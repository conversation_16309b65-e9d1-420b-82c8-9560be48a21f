import {TabsUi} from '@/ui/Tabs';
import styles from './style.module.css';
import {useAutomationTabs} from '@/hooks/useAotomationTab';

export const AutomationContainer = () => {
  const {tabItems, activeTab, handleTabChange} = useAutomationTabs();

  return (
    <div className={styles.automation_container}>
      <h4>Automations</h4>
      <TabsUi items={tabItems} activeKey={activeTab as string} variant="tabs" onChange={handleTabChange} />
    </div>
  );
};
