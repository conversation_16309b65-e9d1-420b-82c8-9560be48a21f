import {Spinner} from 'react-bootstrap';
import styles from './styles.module.css';
import {HtmlHTMLAttributes} from 'react';

interface ButtonUiProps extends HtmlHTMLAttributes<HTMLButtonElement> {
  variant: 'contained' | 'outlined' | 'outlined-bg' | 'text';
  color: 'primary' | 'error' | 'white' | 'gray';
  fullWidth?: boolean;
  rounded?: boolean;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  type?: 'button' | 'submit' | 'reset';
  isLoading?: boolean;
}

export const ButtonUi = ({color, rounded = true, fullWidth, type, variant, ...props}: ButtonUiProps) => {
  const buttonColor = () => {
    switch (color) {
      case 'primary':
        return styles.primary;
      case 'error':
        return styles.error;
      case 'white':
        return styles.white;
      case 'gray':
        return styles.gray;
      default:
        return styles.primary;
    }
  };

  const buttonVariant = () => {
    switch (variant) {
      case 'contained':
        return styles.contained;
      case 'outlined':
        return styles.outlined;
      case 'outlined-bg':
        return styles.outlined_bg;
      case 'text':
        return styles.text;
      default:
        return styles.contained;
    }
  };

  return (
    <div
      {...(props as any)}
      className={`${styles.button} ${buttonVariant()} ${buttonColor()} ${props.size ? styles[props.size] : ''} ${
        fullWidth ? styles.fullWidth : ''
      } ${rounded ? styles.rounded : ''} ${props.disabled ? styles.disabled : ''}`}>
      {props.isLoading ? <Spinner animation="border" size="sm" /> : props.children}
    </div>
  );
};
