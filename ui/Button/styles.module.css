.button {
  display: flex;
  padding: 10px 16px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  line-height: 120%;
  box-shadow: none;
  cursor: pointer;
  width: fit-content;
}

.button.text {
  padding: 0;
}
.rounded {
  border-radius: 8px;
}

.small {
  padding: 6px 16px !important;
}

.contained.primary {
  background: #0055ba;
  color: white;
}

.contained.error {
  background: #d04e4f;
  color: white;
}

.outlined.primary {
  border: 2px solid #0055ba !important;
  color: #0055ba;
  background-color: transparent;
}

.outlined.error {
  border: 2px solid #d04e4f !important;
  color: #d04e4f;
  background-color: transparent;
}

.outlined_bg.error {
  border: 2px solid #d04e4f !important;
  color: #d04e4f;
  background-color: rgba(208, 78, 79, 0.08);
}
.outlined.gray {
  border: 2px solid #d9d9d9 !important;
  background: #fff !important;
  color: #999999 !important;
}

.contained.white {
  background: #fff !important;
  color: #0055ba !important;
}

.text.primary {
  color: #0070f5;
  border: none !important;
  background: transparent !important;
}

.fullWidth {
  width: 100% !important;
}
.disabled {
  pointer-events: none;
  background-color: #c8c3c3 !important;
}
.disabled.outlined.primary {
  border: 2px solid #8f8c8c !important;
  color: #6d6969 !important;
}
.button button {
  border: none;
  background: none;
  cursor: pointer;
  padding: 0;
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  line-height: 120%;
}
