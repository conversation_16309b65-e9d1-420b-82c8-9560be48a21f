import {TabsProps} from 'antd';
import styles from './style.module.css';

interface TabsUiProps {
  variant: 'tabs' | 'button' | 'highlight';
  activeKey: string;
  items: {
    key: string;
    label: string;
    children?: React.ReactNode;
    color?: string;
    backgroundColor?: string;
  }[];
  className?: string;
  onChange?: (key: string) => void;
}

export const TabsUi = ({activeKey, items, variant = 'button', ...props}: TabsUiProps) => {
  const activeItem = items?.find(item => item.key === activeKey);

  return (
    <div className={`${variant === 'button' ? `${styles.customTabs} review-tab` : ''} ${props.className}`}>
      <div className={`${styles.tabHeaders} ${variant === 'highlight' && styles.highlightedHeader}`}>
        {items?.map((item, index) => {
          const isActive = activeKey === item.key;
          const isFirstTab = index === 0;

          const tabStyle = {
            backgroundColor: isFirstTab ? (isActive ? '#0055BA' : 'transparent') : isActive ? '#fff' : 'transparent',
            color: isFirstTab ? (isActive ? '#fff' : '#BABABA') : isActive ? '#0070F5' : '#BABABA',
          };

          return (
            <div
              key={item.key}
              className={`${variant === 'tabs' ? styles.tab : styles.tabButton} ${
                isActive ? styles.activeButtonTab : ''
              } ${variant === 'highlight' ? styles.highlightTab : ''}`}
              onClick={() => props.onChange && props.onChange(item.key)}
              style={variant === 'tabs' ? tabStyle : {}}>
              {item.label}
              <span
                className={
                  variant === 'tabs'
                    ? isFirstTab
                      ? isActive
                        ? styles.firstActiveTabBorder
                        : styles.firstTabBorder
                      : isActive
                      ? styles.activeTabBorder
                      : styles.tabBorder
                    : ''
                }></span>
            </div>
          );
        })}
      </div>
      <div className={styles.tabContent}>{activeItem?.children}</div>
    </div>
  );
};
