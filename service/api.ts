import {getToken} from '@/lib/session';
import axios, {AxiosRequestConfig} from 'axios';

export class ApiService {
  async getHeaders() {
    const session = getToken();
    console.log({session}, 'session');
    return {
      Accept: 'application/json',
      Authorization: 'Bearer ' + session?.token,
    };
  }

  async handleResponse<R = any>(resolver: Promise<{data: R}>) {
    const response = await resolver;
    return response?.data;
  }

  async get<T = any>(endpoint: string, config?: AxiosRequestConfig<any> | undefined) {
    const defaultHeaders = await this.getHeaders();
    const combinedHeaders = {
      ...defaultHeaders,
      ...config?.headers,
    };

    return await this.handleResponse<T>(
      axios.get(endpoint, {
        ...config,
        headers: combinedHeaders,
      }),
    );
  }

  async post<T = any, R = any>(endpoint: string, payload?: T, config?: AxiosRequestConfig<any> | undefined) {
    const defaultHeaders = await this.getHeaders();
    const combinedHeaders = {
      ...defaultHeaders,
      ...config?.headers,
    };

    return await this.handleResponse<R>(
      axios.post<T, {data: R}>(endpoint, payload, {
        headers: combinedHeaders,
        ...config,
      }),
    );
  }

  async delete(endpoint: string, config?: AxiosRequestConfig<any> | undefined) {
    const defaultHeaders = await this.getHeaders();
    const combinedHeaders = {
      ...defaultHeaders,
      ...config?.headers,
    };

    return await this.handleResponse(
      axios.delete(endpoint, {
        ...config,
        headers: combinedHeaders,
      }),
    );
  }

  async patch<T = any, R = any>(endpoint: string, payload: T, config?: AxiosRequestConfig<any> | undefined) {
    const defaultHeaders = await this.getHeaders();
    const combinedHeaders = {
      ...defaultHeaders,
      ...config?.headers,
    };

    return await this.handleResponse<R>(
      axios.patch<T, {data: R}>(endpoint, payload, {
        headers: combinedHeaders,
        ...config,
      }),
    );
  }

  async put<T = any, R = any>(endpoint: string, payload?: T, config?: AxiosRequestConfig<any> | undefined) {
    const defaultHeaders = await this.getHeaders();
    const combinedHeaders = {
      ...defaultHeaders,
      ...config?.headers,
    };

    return await this.handleResponse<R>(
      axios.put(endpoint, payload, {
        headers: combinedHeaders,
        ...config,
      }),
    );
  }
}

export const apiService = new ApiService();
