import {useState} from 'react';
import {useGetFilterData} from './useGetFilterData';
import {Country, Industry, Sector, Skill, User} from '@/lib/types';

interface FilterDataProps {
  countries: Country[];
  skills?: Skill[];
  sectors?: Sector[];
  industries?: Industry[];
  variant?: 'candidate' | 'employers';
}

export const useHandleAction = ({countries, industries, sectors, skills, variant}: FilterDataProps) => {
  const [selectedItem, setSelectedItem] = useState<any>();
  const [updatedQuery, setUpdatedQuery] = useState<any>();
  const {filterData = []} = useGetFilterData({countries, skills, sectors, industries, variant});
  const [chipData, setChipData] = useState<{label: string; value: string[]; key: string}[]>([]);
  const [selectedFilters, setSelectedFilters] = useState<Record<string, any>>({});
  const [selectedUser, setSelectedUser] = useState<User | undefined>();

  const handleFilterApply = (filters: Record<string, any>) => {
    let newUpdatedQuery: any = {...updatedQuery};
    setSelectedUser(undefined);
    const chipsData = Object.entries(filters).flatMap(
      ([key, value]):
        | {label: any; value: any; key: any}[]
        | {label: any; value: any; key: string}
        | null
        | undefined => {
        delete newUpdatedQuery[key];
        if (typeof value === 'string' || typeof value === 'number') {
          // For position, we want to search in the current_position field
          if (key === 'position') {
            newUpdatedQuery[key] = value;
            return {
              label: `Position: ${value}`,
              value: value,
              key: key,
            };
          }

          newUpdatedQuery[key] = value;
          if (key === 'currency') {
            return null;
          }
          return {
            label: value,
            value: value,
            key: key,
          };
        } else if (Array.isArray(value) && value.length > 0) {
          newUpdatedQuery[key] = value.map((val: any) => val?.value);
          switch (key) {
            case 'available_resume_count':
              return value?.map((val: any) => {
                return {
                  label: `Resume: ${val.label}`,
                  value: val.value,
                  key: 'available_resume_count',
                };
              });
            case 'location':
              return {
                label: `Location: ${value.map((val: any) => val.label).join(', ')}`,
                value: value.map((val: any) => val.value),
                key: 'location',
              };
            case 'skills':
              return {
                label: `Skills: ${value.map((val: any) => val.label).join(', ')}`,
                value: value.map((val: any) => val.value),
                key: 'skills',
              };
            case 'sector':
              return {
                label: `Sector: ${value.map((val: any) => val.label).join(', ')}`,
                value: value.map((val: any) => val.value),
                key: 'sector',
              };
            case 'salary':
              const minSalary = filters.salary?.find((el: any) => el.label === 'Min Salary')?.value || '';
              const maxSalary = filters.salary?.find((el: any) => el.label === 'Max Salary')?.value || '';
              const currency = filters.currency || '';
              return {
                label: `${currency}${minSalary}-${currency}${maxSalary}`,
                value: `${minSalary}-${maxSalary}`,
                key: 'salary',
              };
            default:
              return value?.map((val: any) => {
                return {
                  label: val.label,
                  value: val.value,
                  key: key as any,
                };
              });
          }
        }
      },
    );

    setUpdatedQuery(newUpdatedQuery);

    setChipData(
      chipsData
        ?.filter(chip => chip !== null && chip !== undefined)
        ?.map((chip: any) => {
          return {
            label: chip.label,
            value: chip.value,
            key: chip.key,
          };
        }),
    );
  };

  const updateSelectedFilters = (removedChip: {value: string | string[]; key: string; label?: string}) => {
    const {value: removedValue, key: removedKey} = removedChip;

    const updatedFilters = {...selectedFilters};

    Object.entries(updatedFilters).forEach(([key, filterValue]) => {
      if (key === 'currency' || key === 'minSalary' || key === 'maxSalary') {
        delete updatedFilters.currency;
        delete updatedFilters.minSalary;
        delete updatedFilters.maxSalary;
      }

      if (Array.isArray(filterValue) && filterValue?.[0]?.value !== undefined) {
        if (typeof removedValue !== 'object') {
          updatedFilters[key] = filterValue.filter(item => !(removedValue === item.value));
          return;
        }

        updatedFilters[key] = filterValue.filter(item => !removedValue?.includes(item.value));

        if (updatedFilters[key].length === 0) {
          delete updatedFilters[key];
        }
      } else if (Array.isArray(filterValue)) {
        updatedFilters[key] = filterValue.filter(val => !removedValue.includes(val));

        if (updatedFilters[key].length === 0) {
          delete updatedFilters[key];
        }
      } else if (typeof filterValue === 'object' && filterValue.value !== undefined) {
        if (filterValue.value === removedValue || filterValue.value === removedValue[0]) {
          delete updatedFilters[key];
        }
      } else if (typeof filterValue === 'string' || typeof filterValue === 'number') {
        if ((filterValue as any) === removedValue) {
          delete updatedFilters[key];
        }
      }
    });

    setSelectedFilters(updatedFilters);

    return updatedFilters;
  };

  const updatedQueryHasValue = Object.keys(updatedQuery || {}).length > 0;

  const handleRemoveChip = (index: number) => {
    const removedChip = chipData[index];
    if (!removedChip) return;
    const newChipData = chipData.filter((_, idx) => idx !== index);
    setChipData(newChipData);

    const key = removedChip.key;
    const newUpdatedQuery = {...updatedQuery};

    if (newUpdatedQuery[key] && Array.isArray(newUpdatedQuery[key])) {
      if (key === 'salary') {
        delete newUpdatedQuery['currency'];
      }
      delete newUpdatedQuery[key];
    }

    setSelectedFilters(updateSelectedFilters(removedChip));

    setUpdatedQuery(!updatedQueryHasValue ? undefined : newUpdatedQuery);
  };

  return {
    filterData,
    handleFilterApply,
    selectedItem,
    setSelectedItem,
    chipData,
    selectedFilters,
    setSelectedFilters,
    updatedQuery,
    updateSelectedFilters,
    handleRemoveChip,
    setUpdatedQuery,
    selectedUser,
    setSelectedUser,
  };
};
