import {useRouter} from 'next/router';
import {AutomationOverView} from '@/components/Admin/Automation/Overview';
import {AutomationDataPoint} from '@/components/Admin/Automation/DataPoint';
import {AutomationContacts} from '@/components/Admin/Automation/Contacts';
import {TemplateSection} from '@/components/Admin/Automation/TemplateSection';
import {TemplateCreateContainer} from '@/components/Admin/Automation/TemplateSection/TemplateEdit';
import WorkflowSection from '@/components/Admin/Automation/Workflow';
import {AddWorkflowBasicInfo} from '@/components/Admin/Automation/Workflow/AddWorkflow/BasicInfo';
import {WorkflowChooseContainer} from '@/components/Admin/Automation/Workflow/AddWorkflow/workflow';

export const useAutomationTabs = () => {
  const router = useRouter();

  const pathToTabMap = [
    {key: 'overview', path: '/overview'},
    {key: 'data_point', path: '/data_point'},
    {key: 'contacts', path: '/contacts'},
    {key: 'templates', path: '/template-list'},
    {key: 'workflows', path: '/workflows'},
    {key: 'analytics', path: '/analytics'},
  ];

  const determineActiveTab = () => {
    const currentPath = router.pathname;
    const matchedTab = pathToTabMap.find(({path}) => currentPath.startsWith(`/admin/automation${path}`));
    return matchedTab ? matchedTab.key : 'overview';
  };

  const activeTab = determineActiveTab();

  const tabContentMap = {
    overview: {
      label: 'Overview',
      component: <AutomationOverView />,
    },
    data_point: {
      label: 'Data Points',
      component: <AutomationDataPoint />,
    },
    contacts: {
      label: 'Contacts',
      component: <AutomationContacts />,
    },
    templates: {
      label: 'Templates',
      component:
        router.pathname.includes('/create-template') || router.pathname.includes('/edit-template') ? (
          <TemplateCreateContainer variant="template" />
        ) : (
          <TemplateSection variant="template" />
        ),
    },
    workflows: {
      label: 'Workflows',
      component: router.pathname.includes('/basic_info') ? (
        <AddWorkflowBasicInfo />
      ) : router.pathname.includes('/add/workflow') || router.pathname.includes(`/edit/[id]/workflow`) ? (
        <WorkflowChooseContainer />
      ) : router.pathname.includes('/template') ? (
        <TemplateSection variant="workflow" />
      ) : router.pathname.includes('/edit-template') ? (
        <TemplateCreateContainer variant="workflow" />
      ) : (
        <WorkflowSection />
      ),
    },
    analytics: {
      label: 'Analytics',
      component: (
        <div
          style={{
            backgroundColor: 'white',
          }}></div>
      ),
    },
  };

  const tabItems = Object.entries(tabContentMap).map(([key, {label, component}]) => ({
    key,
    label,
    children: component,
  }));

  const handleTabChange = (key: string) => {
    const tab = pathToTabMap.find(tab => tab.key === key);
    if (tab) {
      router.push(`/admin/automation${tab.path}`);
    }
  };

  return {
    tabItems,
    activeTab,
    handleTabChange,
  };
};
