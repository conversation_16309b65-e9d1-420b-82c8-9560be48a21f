import {apiService} from '@/service/api';
import {useMutation} from 'react-query';

interface FileUploadResponse {
  uuid: string;
  name: string;
  size: number;
  type: string;
  description: string;
  source: string;
  thumbnail: string;
  created_at: string;
}

export const useFileUpload = () => {
  return useMutation(async (data: FormData): Promise<FileUploadResponse> => {
    const response = await apiService.post('/file-management/files', data);
    return response;
  });
};
