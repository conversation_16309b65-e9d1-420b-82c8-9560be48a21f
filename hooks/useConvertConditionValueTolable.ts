import {DataPointType} from '@/modules/admin/automation/query/useGetDataPoints';
import {WorkflowSegments} from '@/modules/admin/automation/query/useGetWorkflowById';
import {ConditionData} from '@/modules/admin/automation/types';
import {useGetAllSkills} from '@/modules/common/query/useGetAllSkills';
import {useGetCountries} from '@/modules/common/query/useGetCountries';
import {useGetDataPointOptions} from './useGetDataTypeOptions';

export const useConvertConditionValueToLabel = () => {
  const {data: skills} = useGetAllSkills();
  const {data: locations} = useGetCountries();
  const {data} = useGetDataPointOptions();
  const datapointOptions = data?.dataPoints;

  const convertCondition = (condition: WorkflowSegments[]) => {
    const mappedCondition = condition.map(item => {
      const currentItem = datapointOptions?.find(option => option.value.toString() === item.datapoint_id);
      const location = locations?.find(location => location.id.toString() === item.value);
      const skill = skills?.find(skill => skill.id.toString() === item.value);

      const mappedLabel = () => {
        switch (currentItem?.label as DataPointType) {
          case 'location':
            return location?.country_name;
          case 'skills':
            return skill?.skills;

          default:
            return item.value;
        }
      };

      return {
        ...item,
        value: mappedLabel() ?? item.value,
      };
    });
    return mappedCondition;
  };

  return {convertCondition};
};
