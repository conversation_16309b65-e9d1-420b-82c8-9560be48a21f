import AuthContext from '@/Context/AuthContext';
import {searchJobs} from '@/lib/ApiAdapter';
import <PERSON>rror<PERSON>and<PERSON> from '@/lib/ErrorHandler';
import {toggleFavoriteJob} from '@/lib/frontendapi';
import {Job} from '@/lib/types';
import {QUERY_GET_JOB_LISTS} from '@/modules/common/constants';
import {notification} from 'antd';
import axios from 'axios';
import {useRouter} from 'next/router';
import {useCallback, useContext, useEffect, useState} from 'react';
import {useQueryClient} from 'react-query';

export const useHandleJobSaved = (job: Job, jobs: Job[], favorites: number[]) => {
  const {user} = useContext(AuthContext);
  const [savedJob, setSavedJob] = useState(job?.is_saved);
  const router = useRouter();
  const queryClient = useQueryClient();
  const handleJobSaved = useCallback(() => {
    favorites?.includes(job.id) ? setSavedJob(true) : undefined;
  }, [user, job, setSavedJob]);

  useEffect(() => {
    if (job) {
      handleJobSaved();
    }
  }, [job, handleJobSaved]);

  useEffect(() => {
    setSavedJob(favorites?.includes(job?.id) ? true : false);
  }, [favorites, job]);

  const unSaveJob = (id: any, job_id: any, company_id: any) => {
    const data = {job_id: job_id};
    toggleFavoriteJob(data)
      .then((res: any) => {
        notification.success({message: 'Job unsaved successfully.'});
        setSavedJob(false);
        queryClient.invalidateQueries([QUERY_GET_JOB_LISTS]);
      })
      .catch((err: any) => {
        ErrorHandler.showNotification(err);
      });
  };

  const saveJob = (job_id: any, company_id: any) => {
    if (!user) {
      router.push('/auth/signup');
    }
    const data = {job_id: job_id};
    toggleFavoriteJob(data)
      .then((res: any) => {
        notification.success({message: 'Job saved successfully.'});
        setSavedJob(true);
        queryClient.invalidateQueries([QUERY_GET_JOB_LISTS]);
      })
      .catch((err: any) => {
        ErrorHandler.showNotification(err);
      });
  };

  return {savedJob, unSaveJob, saveJob};
};
