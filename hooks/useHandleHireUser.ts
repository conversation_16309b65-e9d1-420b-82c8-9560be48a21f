import AuthContext from '@/Context/AuthContext';
import {notification} from 'antd';
import {useContext} from 'react';

export const useHandleHire = () => {
  const {user} = useContext(AuthContext);
  const uploadCVLink = (() =>
    user?.id
      ? user?.role == 'admin'
        ? '/admin/employees'
        : user?.role == 'employer'
        ? '/employer/candidates'
        : user?.role == 'employee'
        ? '#'
        : user?.role == 'staff'
        ? '/staff/candidates'
        : '/'
      : '/auth/signup')();

  const handleClickShowErrorMessage = (e: any, message: string) => {
    e.preventDefault();
    notification.error({message: message});
  };

  return {uploadCVLink, handleClickShowErrorMessage};
};
