import {NextRouter, useRouter} from 'next/router';

export const useHandleSelectSaveToQuery = () => {
  const router = useRouter();
  const handleSelectSaveToQuery = (value: string[], key: string) => {
    {
      const filteredOptions = {
        ...router.query,
        [key]: value,
      };
      router
        .push(
          {
            pathname: '/jobs-in-gulf',
            query: filteredOptions,
          },
          undefined,
          {shallow: false},
        )
        .then();
      return;
    }
  };

  return {handleSelectSaveToQuery};
};
