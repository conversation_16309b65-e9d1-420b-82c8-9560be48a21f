import {useEffect, useState} from 'react';
import <PERSON><PERSON>r<PERSON><PERSON><PERSON> from '@/lib/ErrorHandler';
import {getAllCitiesByCountryName} from '@/lib/frontendapi';

export const useGetCityNameByCityId = (countryName?: string, cityId?: number) => {
  const [cities, setCities] = useState<
    {
      id: number;
      city_name: string;
    }[]
  >([]);

  useEffect(() => {
    const fetchCities = async () => {
      try {
        const response = await getAllCitiesByCountryName(countryName);
        setCities(response.data);
      } catch (error) {
        ErrorHandler.showNotification(error);
      }
    };

    if (countryName) {
      fetchCities();
    }
  }, [countryName, cityId]);

  const cityName = cities?.find(city => city?.id === cityId)?.city_name;

  return {cityName};
};
