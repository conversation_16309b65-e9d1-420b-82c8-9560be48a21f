import {useRouter} from 'next/router';

export interface SideBarItems {
  route: string;
  label: string;
  icons?: {
    default: string;
    active: string;
  };
  customIcon?: JSX.Element;
  isActive?: boolean;
  children?: SideBarItems[];
  showChildren?: boolean;
}

export const useAdminSideBarItems = () => {
  const router = useRouter();

  const sideBarItems: SideBarItems[] = [
    {
      route: '/admin/dashboard',
      label: 'Home',
      icons: {
        default: '/images/default/dash-icon-1.svg',
        active: '/images/selected/dash-icon-1.svg',
      },
    },
    {
      route: '/admin/employers',
      label: 'Employers',
      icons: {
        default: '/images/default/dash-icon-7.svg',
        active: '/images/selected/dash-icon-7.svg',
      },
    },
    {
      route: '/admin/jobs',
      label: 'Jobs',
      icons: {
        default: '/images/default/dash-icon-4.svg',
        active: '/images/selected/dash-icon-4.svg',
      },
    },
    {
      route: '/admin/employees',
      label: 'Candidates',
      icons: {
        default: '/images/default/dash-icon-2.svg',
        active: '/images/selected/dash-icon-2.svg',
      },
    },
    {
      route: '/admin/blog',
      label: 'Blogs',
      customIcon: <i className="fa-solid fa-pen"></i>,
    },
    {
      route: '/admin/messages',
      label: 'Messages',
      icons: {
        default: '/images/default/dash-icon-6.svg',
        active: '/images/selected/dash-icon-6.svg',
      },
    },
    {
      label: 'Automations',
      route: '/admin/automation/overview',
      icons: {
        default: '/icons/automation/automation_default.svg',
        active: '/icons/automation/automation_active.svg',
      },
      children: [
        {
          label: 'Overview',
          route: '/admin/automation/overview',
        },
        {
          label: 'Data Points',
          route: '/admin/automation/data_point',
        },
        {
          label: 'contacts',
          route: '/admin/automation/contacts',
        },
        {
          label: 'Templates',
          route: '/admin/automation/template-list',
        },
        {
          label: 'Workflows',
          route: '/admin/automation/workflows',
        },
        {
          label: 'Analytics',
          route: '/admin/automation/analytics',
        },
        {
          label: 'Create Template',
          route: '/admin/automation/template-list/create-template',
        },
        {
          label: 'Create Workflow',
          route: '/admin/automation/workflows/add',
        },
        {
          label: 'Edit Workflow',
          route: '/admin/automation/workflows/edit',
        },
      ],
      showChildren: false,
    },
    {
      route: '/admin/datamanagement/company-review',
      label: 'Data Mgmt',
      icons: {
        default: '/images/default/dash-icon-5.svg',
        active: '/images/selected/dash-icon-5.svg',
      },
      showChildren: true,
      children: [
        {
          label: 'Company Reviews',
          route: '/admin/datamanagement/company-review',
        },
        {
          label: 'Countries',
          route: '/admin/datamanagement/countries',
        },
        {
          label: 'Cities',
          route: '/admin/datamanagement/cities',
        },
        {
          label: 'Industries',
          route: '/admin/datamanagement/industries',
        },
        {
          label: 'Sector',
          route: '/admin/datamanagement/sector',
        },
        {
          label: 'Skills',
          route: '/admin/datamanagement/skills',
        },
        {
          label: 'Author',
          route: '/admin/datamanagement/authors',
        },
      ],
    },
    {
      route: '/admin/settings',
      label: 'Settings',
      customIcon: <i className="fa fa-cog"></i>,
    },
  ];

  return sideBarItems.map(item => ({
    ...item,
    isActive:
      router.pathname.includes(item.route) ||
      (item.children && item.children.some(child => router.pathname.includes(child.route))),
    children: item.children?.map(child => {
      return {
        ...child,
        isActive: router.pathname.includes(child.route),
      };
    }),
  }));
};
