import {Filter} from '@/components/Common/FIlterDrawer';
import {Country, Industry, Sector, Skill} from '@/lib/types';
import {useEffect, useState} from 'react';

interface FilterDataProps {
  countries: Country[];
  skills?: Skill[];
  sectors?: Sector[];
  industries?: Industry[];
  variant?: 'candidate' | 'employers';
}

export const useGetFilterData = ({
  countries = [],
  skills = [],
  sectors = [],
  industries = [],
  variant = 'candidate',
}: FilterDataProps) => {
  const [filterData, setFilterData] = useState<Filter[]>([]);
  useEffect(() => {
    if (variant === 'employers') {
      setFilterData([
        {
          label: 'Account Type',
          type: 'checkbox',
          items: [
            {label: 'Free', value: '1'},
            {label: 'Paid', value: '2'},
          ],
          key: 'account_type',
        },
        {
          label: 'Status',
          type: 'checkbox',
          items: [
            {label: 'Unclaimed', value: 'unclaimed'},
            {label: 'Requested', value: 'requested'},
            {label: 'Claimed', value: 'approved'},
            {label: 'Rejected', value: 'rejected'},
          ],
          key: 'status',
        },
        {
          label: 'Location',
          items: countries?.map(country => ({value: country.id, label: country.country_name})),
          type: 'checkboxWithSearch',
          key: 'location',
        },
        {
          label: 'Activity',
          type: 'buttonSelect',
          items: [
            {value: '24', label: 'Last 24 hours'},
            {value: '3', label: 'Last 3 days'},
            {value: '7', label: 'Last 7 days'},
            {value: '14', label: 'Last 14 days'},
            {value: '30', label: 'Last 30 days'},
            {value: '90', label: 'Last 90 days'},
            // {value: '40', label: 'More than 3 months'}, //TODO: Implement this
          ],
          key: 'activity',
        },
      ]);
    } else {
      setFilterData([
        {
          label: 'Position',
          type: 'input',
          key: 'position',
          items: []
        },
        {
          label: 'Job Search Status',
          type: 'checkbox',
          items: [
            {value: 'ready_to_interview', label: 'Ready to interview'},
            {value: 'open_to_offer', label: 'Open to offers'},
            {value: 'not_looking', label: 'Not looking'},
          ],
          key: 'job_status',
        },
        {
          label: 'Resume Uploaded',
          type: 'buttonSelect',
          items: [
            {value: '1', label: 'Yes'},
            {value: '0', label: 'No'},
          ],
          key: 'available_resume_count',
        },
        {
          label: 'Location',
          type: 'checkboxWithSearch',
          items: countries?.map(country => ({value: country.id, label: country.country_name})),
          key: 'location',
        },
        {
          label: 'Experience',
          type: 'checkbox',
          items: [
            {value: '0', label: 'Internship / No Experience (0 years)'},
            {value: '0-1', label: 'Junior (0 - 1 years)'},
            {value: '2-3', label: 'Associate (2 - 3 years)'},
            {value: '3-5', label: 'Mid Level (3 - 5 years)'},
            {value: '5-7', label: 'Senior (5 - 7 years)'},
            {value: '7-10', label: 'Lead (7 - 10 years)'},
            {value: '10-15', label: 'Principal (10 - 15 years)'},
            {value: '15-20', label: 'Expert (15 - 20 years)'},
            {value: '20+', label: 'Executive Level (20+ years)'},
          ],
          key: 'experience',
        },
        {
          label: 'Salary',
          type: 'dropdown',
          items: [
            {
              label: 'Currency',
              type: 'currency',
              items: [
                {value: 'USD', label: 'USD'},
                {value: 'AED', label: 'AED'},
                {value: 'EUR', label: 'EUR'},
                {value: 'GBP', label: 'GBP'},
              ],
              key: 'currency',
            },
            {
              label: 'Min Salary',
              type: 'minSalary',
              items: [
                {value: 1000, label: '1000'},
                {value: 2000, label: '2000'},
                {value: 3000, label: '3000'},
                {value: 4000, label: '4000'},
                {value: 5000, label: '5000'},
                {value: 6000, label: '6000'},
                {value: 7000, label: '7000'},
                {value: 8000, label: '8000'},
                {value: 9000, label: '9000'},
                {value: 10000, label: '10000'},
              ],
              key: 'salary',
            },
            {
              label: 'Max Salary',
              type: 'maxSalary',
              items: [
                {value: 1000, label: '1000'},
                {value: 2000, label: '2000'},
                {value: 3000, label: '3000'},
                {value: 4000, label: '4000'},
                {value: 5000, label: '5000'},
                {value: 6000, label: '6000'},
                {value: 7000, label: '7000'},
                {value: 8000, label: '8000'},
                {value: 9000, label: '9000'},
                {value: 10000, label: '10000'},
              ],
              key: 'salary',
            },
          ],
          key: 'salary',
        },
        {
          label: 'Skills',
          type: 'checkboxWithSearch',
          items: skills?.map(skill => ({value: skill.id, label: skill.skills})),
          key: 'skills',
        },
        {
          label: 'Designation',
          type: 'checkboxWithSearch',
          items: sectors?.map(sector => ({value: sector.id, label: sector.sector_name})),
          key: 'sector',
        },
        {
          label: 'Industry',
          type: 'checkboxWithSearch',
          items: industries?.map(industry => ({value: industry.id, label: industry.name})),
          key: 'industry',
        },
        {
          label: 'Activity',
          type: 'buttonSelect',
          items: [
            {value: '24', label: 'Last 24 hours'},
            {value: '3', label: 'Last 3 days'},
            {value: '7', label: 'Last 7 days'},
            {value: '14', label: 'Last 14 days'},
            {value: '30', label: 'Last 30 days'},
            {value: '90', label: 'Last 90 days'},
            // {value: '40', label: 'More than 3 months'}, //TODO: Implement this
          ],
          key: 'activity',
        },
      ]);
    }
  }, [countries, skills, sectors, industries]);

  return {filterData};
};
