import {useWorkflowStore} from '@/modules/admin/automation/store/CreateWorkflow';
import {NodesData, WorkflowType} from '@/modules/admin/automation/types';
import {addEdge, applyEdgeChanges, applyNodeChanges, Edge, Position} from '@xyflow/react';
import {useCallback} from 'react';

export type Logic = 'AND' | 'OR';
const NODE_ORIGIN = {x: 0, y: 20};
const HORIZONTAL_OFFSET = 360;
const VERTICAL_OFFSET = 200;
const LOGIC_VERTICAL_SPACING = 200;
type Node = NodesData;

export const centeredPosition = (element: any[]) => {
  return element?.length === 0
    ? NODE_ORIGIN
    : element?.reduce((acc, curr) => {
        console.log(acc?.position?.x, curr.position.x, 'CHECKLOG ss');
        return {
          position: {
            x: acc?.position?.x + curr?.position?.x,
            y: element[element.length - 1]?.position?.y + LOGIC_VERTICAL_SPACING,
          },
        };
      });
};

export const findContinuousLogicNodes = (nodes: Node[], allNodesTypes: Set<WorkflowType>) => {
  const continuousNodes = [];
  const nonContinuousNodes = [];

  const referenceType = nodes[nodes.length - 1]?.data?.type;
  for (let i = nodes.length - 1; i >= 0; i--) {
    if (!nodes[i]?.data?.selectedLogic) {
      nonContinuousNodes.unshift(nodes[i]);
    }

    if (nodes[i]?.data?.selectedLogic && nodes[i]?.data?.type === referenceType) {
      continuousNodes.unshift(nodes[i]);
    } else {
      break;
    }
  }

  const firstNode = allNodesTypes.size >= 4 ? [] : nonContinuousNodes;

  return firstNode.concat(continuousNodes);
};

export const findLastContinuousEdges = (edges: Edge[]) => {
  const continuousEdges = [];

  const referenceType = edges[edges.length - 1]?.type;
  for (let i = edges.length - 1; i >= 0; i--) {
    if ((edges[i]?.label === 'AND' || edges[i]?.label === 'OR') && edges[i]?.type === referenceType) {
      continuousEdges.unshift(edges[i]);
    } else {
      break;
    }
  }
  return continuousEdges;
};

export const useWorkflowNodes = () => {
  const {storeEdges, setStoreEdges, setStoreNodes, storeNodes} = useWorkflowStore();

  const calculatePosition = (prevNode: Node | undefined, logic?: 'AND' | 'OR') => {
    if (!prevNode) {
      return NODE_ORIGIN;
    }
    if (logic) {
      return {
        x: prevNode.position.x + HORIZONTAL_OFFSET,
        y: prevNode.position.y,
      };
    }
    return {
      x: prevNode.position.x,
      y: prevNode.position.y + VERTICAL_OFFSET,
    };
  };

  const onNodesChange = useCallback(
    (changes: any) => {
      setStoreNodes((nds: any) => applyNodeChanges(changes, nds) as any);
    },
    [setStoreNodes],
  );

  const onEdgesChange = useCallback(
    (changes: any) => setStoreEdges(eds => applyEdgeChanges(changes, eds)),
    [setStoreEdges],
  );

  const onConnect = useCallback(
    (connection: any) => setStoreEdges(eds => addEdge({...connection}, eds)),
    [setStoreEdges],
  );

  const deleteNode = (newNode: Node) => {
    setStoreNodes(prev => {
      return prev.filter(el => el.id !== newNode.id);
    });
  };

  const updateNodes = (newNodes: Node[]) => {
    const newNode = newNodes?.[0];
    setStoreNodes(prevNodes => prevNodes.map(node => (node.id === newNode.id ? {...node, ...newNode} : node)));
  };

  const allNodesTypes = new Set(storeNodes.map((node: Node) => node?.data?.type));

  const addNode = (type: string, data: any, logic?: 'AND' | 'OR') => {
    setStoreNodes((prevNodes: any[]) => {
      if (prevNodes.length === 0) {
        const initialNode = {
          id: '1',
          type: type || 'default',
          data: {label: data.label, ...data},
          position: NODE_ORIGIN,
          sourceHandle: logic ? 'r' : 'b',
          index: 0,
        };
        return [initialNode];
      }

      const prevNode = prevNodes[prevNodes.length - 1];

      const updatedPrevNode = {
        ...prevNode,
        sourcePosition: logic ? Position.Right : Position.Bottom,
      };

      const continuousLogic = findContinuousLogicNodes(prevNodes, allNodesTypes);
      console.log({continuousLogic}, 'CHECKLOG ss');

      if (!logic && !!prevNode?.data?.selectedLogic) {
        const newNodePosition = () => {
          const position = centeredPosition(continuousLogic).position;
          return {
            x: position.x / continuousLogic.length,
            y: position.y + VERTICAL_OFFSET,
          };
        };

        const newNode = {
          id: `${prevNodes.length + 1}`,
          type: type || 'default',
          data: {label: data.label, ...data},
          position: newNodePosition(),
          sourceHandle: logic ? 'r' : 'b',
          index: prevNodes.length,
        };

        return [...prevNodes.slice(0, -1), updatedPrevNode, newNode];
      }

      const newNode = {
        id: `${prevNodes.length + 1}`,
        type: type || 'default',
        data: {label: data.label, ...data},
        position: calculatePosition(prevNode, logic),
        sourceHandle: logic ? 'r' : 'b',
        index: prevNodes.length,
      };

      return [...prevNodes.slice(0, -1), updatedPrevNode, newNode];
    });

    setStoreEdges((prevEdges: Edge[]) => {
      const connedWithLogicContinuousLabel = findLastContinuousEdges(prevEdges);

      if (!logic && prevEdges?.[prevEdges.length - 1]?.label) {
        const firstEdge = [
          {
            id: `e${setStoreNodes.length - storeNodes.length}-${storeNodes.length + 1}`,
            source: `${storeNodes[storeNodes.length - connedWithLogicContinuousLabel.length - 1].id}`,
            target: `${storeNodes.length + 1}`,
            label: logic,
            sourceHandle: logic ? 'r' : 'b',
            targetHandle: logic ? 'l' : 't',
            type: type,
          },
        ];

        const mappedEdge = connedWithLogicContinuousLabel.map((edge: Edge, index: number) => {
          return {
            id: `e${Math.random()}-${storeNodes.length + 1}`,
            source: `${storeNodes.length - index}`,
            target: `${storeNodes.length + 1}`,
            label: logic,
            sourceHandle: logic ? 'r' : 'b',
            targetHandle: logic ? 'l' : 't',
            type: type,
          };
        });

        const newEdges = allNodesTypes.size >= 4 ? mappedEdge : firstEdge.concat(mappedEdge);
        return [...prevEdges, ...newEdges];
      }
      if (storeNodes.length > 0) {
        const newEdge = {
          id: `e${storeNodes.length}-${storeNodes.length + 1}`,
          source: `${storeNodes.length}`,
          target: `${storeNodes.length + 1}`,
          label: logic,
          sourceHandle: logic ? 'r' : 'b',
          targetHandle: logic ? 'l' : 't',
          type: type,
        };
        return [...prevEdges, newEdge];
      }
      return prevEdges;
    });
  };

  return {
    nodes: storeNodes,
    edges: storeEdges,
    onNodesChange,
    onEdgesChange,
    addNode,
    onConnect,
    updateNodes,
    deleteNode,
    setStoreNodes,
    setStoreEdges,
  };
};
