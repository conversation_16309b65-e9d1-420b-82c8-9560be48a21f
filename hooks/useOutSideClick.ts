import {RefObject, useEffect} from 'react';

export function useClickOutside(ref: RefObject<HTMLElement>, ignoreRef: any = [], onClickOutside: () => void) {
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      let ignore = false;
      ignoreRef?.forEach((igRef: any) => {
        if (igRef?.current && igRef?.current.contains(event.target)) {
          ignore = true;
        }
      });
      if (ignore) return;
      if (ref.current && !ref.current.contains(event.target as Node)) {
        onClickOutside();
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [ref, onClickOutside]);
}
