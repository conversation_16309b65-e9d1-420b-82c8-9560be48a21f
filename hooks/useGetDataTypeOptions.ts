import {AutomationRoutes} from '@/modules/admin/automation/constants/routes';
import {DataPointTableData} from '@/modules/admin/automation/query/useGetDataPoints';
import {apiService} from '@/service/api';
import {useQuery} from 'react-query';

export const useGetDataPointOptions = () => {
  return useQuery('dataTypes', async () => {
    const response: {
      data: DataPointTableData[];
    } = await apiService.get(AutomationRoutes.QUERY_GET_AUTOMATION_DATA_POINTS, {
      params: {
        per_page: 1000,
      },
    });

    const dataTypes = response.data.map(el => {
      return {
        label: el.data_type,
        value: el.data_type,
      };
    });

    const userTypes = response.data
      .map(el => {
        const userType = el.user_type.split(',');
        return userType.map((t: string) => {
          return {
            label: t === '1' ? 'Candidate' : 'Employer',
            value: t,
          };
        });
      })
      .flat();

    const dataPoints = response.data.map(el => {
      return {
        label: el.data_point_name,
        value: el.id,
      };
    });

    const uniqueDataTypes = dataTypes.filter(
      (el: any, index: number, self: any) => index === self.findIndex((t: any) => t.value === el.value),
    );
    const uniqueUserTypes = userTypes.filter(
      (el: any, index: number, self: any) => index === self.findIndex((t: any) => t.value === el.value),
    );

    const uniqueDataPoints = dataPoints.filter(
      (el: any, index: number, self: any) => index === self.findIndex((t: any) => t.label === el.label),
    );
    return {
      dataTypes: uniqueDataTypes,
      userTypes: uniqueUserTypes,
      dataPoints: uniqueDataPoints,
    };
  });
};
