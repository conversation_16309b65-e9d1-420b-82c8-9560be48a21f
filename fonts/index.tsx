import { Archivo, Open_Sans } from 'next/font/google';
import Head from 'next/head';

export const archivoFont = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
});

export const openSansFont = Open_Sans({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700', '800'],
});

export const FontLoader = () => {
  return (
    <style
      jsx
      global
    >
      {`
        :root {
          --archivo-font: ${archivoFont.style.fontFamily};
          --opensans-font: ${openSansFont.style.fontFamily}
        }
      `}
    </style>
  )
}
