import {getToken} from '@/lib/session';
import axios from 'axios';

export const getUserTestimonials = async () => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/testimonials', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
