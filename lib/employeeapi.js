import { getToken } from "../lib/session";
import axios from "axios";

export const getAllJobs = async () => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/getalljobs', {
      method: 'get',
      headers: {
        'Accept': 'application/json',
        'Authorization': 'Bearer ' + getToken()
      }
    });
    req.then(res => resolve(res.data))
      .catch(err => reject(err));
  });
};

export const uploadResume = async (data, resume_pdf_path) => {
  let formdata = new FormData();
  formdata.append('resume_pdf_path', resume_pdf_path);
  return new Promise((resolve, reject) => {
    const req = axios.post(process.env.NEXT_PUBLIC_API_URL + '/saveresume', formdata, {
      method: 'post',
      headers: {
        'Accept': 'application/json',
        'Content-Type': `multipart/form-data; boundary=${formdata._boundary}`,
        'Authorization': 'Bearer ' + getToken()
      },
      params: {
        ...data
      }
    });
    req.then(res => resolve(res.data))
      .catch(err => reject(err));
  });
};

export const getAllJobsWithId = async (id) => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/getalljobswithid/' + id, {
      method: 'get',
      headers: {
        'Accept': 'application/json',
        'Authorization': 'Bearer ' + getToken()
      }
    });
    req.then(res => resolve(res.data))
      .catch(err => reject(err));
  });
};

export const getSingleOwnResume = async (id) => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/getSingleOwnResume/' + id, {
      method: 'get',
      headers: {
        'Accept': 'application/json',
        'Authorization': 'Bearer ' + getToken()
      }
    });
    req.then(res => resolve(res.data))
      .catch(err => reject(err));
  });
};

export const getDefaultResume = async (id) => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/getUserDefaultResume/' + id, {
      method: 'get',
      headers: {
        'Accept': 'application/json',
        'Authorization': 'Bearer ' + getToken()
      }
    });
    req.then(res => resolve(res.data))
      // .catch(err => reject(err));
      .catch(err=> console.log("getDefaultResumeError",err));
  });
};

export const deleteResume = async (data) => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/resume/' + data, {
      method: 'delete',
      headers: {
        'Accept': 'application/json',
        'Authorization': 'Bearer ' + getToken()
      },
      params: {
        ...data
      },
    });
    req.then(res => resolve(res.data))
      .catch(err => reject(err));
  });
};

export const updateDefaultResume = async (data) => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/updateDefaultResume/' + data, {
      method: 'put',
      headers: {
        'Accept': 'application/json',
        'Authorization': 'Bearer ' + getToken()
      },
      params: {
        ...data
      },
    });
    req.then(res => resolve(res.data))
      .catch(err => reject(err));
  });
};

export const updateJobStatus = async (data) => {
  try {
    const response = await axios.put(
      `${process.env.NEXT_PUBLIC_API_URL}/updatejobstatus/${data.id}/${data.value}`,
      {
        params: {
          ...data
        },
        headers: {
          Accept: "application/json",
          Authorization: "Bearer " + getToken().token,
        },
      }
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const updateShowContact = async (data) => {
  return new Promise((resolve, reject) => {
    const req = axios.request({
      url: process.env.NEXT_PUBLIC_API_URL + '/users/updateShowContact/' + data.userId,
      method: 'put',
      headers: {
        'Accept': 'application/json',
        'Authorization': 'Bearer ' + getToken()
      },
      data: {
        showcontact_no: data.showcontact_no,
      },
    });
    req.then(res => resolve(res.data))
      .catch(err => reject(err));
  });
};

export const updateShowEmail = async (data) => {
  return new Promise((resolve, reject) => {
    const req = axios.request({
      url: process.env.NEXT_PUBLIC_API_URL + '/users/updateShowEmail/' + data.userId,
      method: 'put',
      headers: {
        'Accept': 'application/json',
        'Authorization': 'Bearer ' + getToken()
      },
      data: {
        isShowEmail: data.isShowEmail
      },
    });
    req.then(res => resolve(res.data))
      .catch(err => reject(err));
  });
};

export const getTotalApplicationsForjob = async (id) => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/applications/getTotalApplicationsForjob/' + id, {
      method: 'get',
      headers: {
        'Accept': 'application/json',
        Authorization: "Bearer " + getToken().token,
      }
    });
    req.then(res => resolve(res.data))
      .catch(err => reject(err));
  });
};

export const getTotalSavedjob = async (id) => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/applications/getTotalSavedjob/' + id, {
      method: 'get',
      headers: {
        'Accept': 'application/json',
        Authorization: "Bearer " + getToken().token,
      }
    });
    req.then(res => resolve(res.data))
      .catch(err => reject(err));
  });
};

export const showCompletionPercentage = async (id) => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + "/users/showCompletionPercentage/" + id, {
      method: "post",
      headers: {
        Accept: "application/json",
        Authorization: "Bearer " + getToken().token,
      }
    });
    req.then((res) => resolve(res.data))
      .catch((err) => reject(err));
  })
}

export const updateJobPref = async (id, data) => {
  return new Promise((resolve, reject) => {
    const req = axios.request(
      process.env.NEXT_PUBLIC_API_URL + "/users/updateJobPref/" + id,
      {
        method: "put",
        headers: {
          Accept: "application/json",
          Authorization: "Bearer " + getToken().token,
        },
        data: {
          ...data,
        },
      }
    );
    req.then((res) => resolve(res.data))
      .catch((err) => reject(err));
  });
};
