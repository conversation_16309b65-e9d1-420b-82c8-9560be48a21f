export type Job = {
  id: number;
  user_id: number;
  company_id: number;
  company?: Company;
  country?: Country;
  sector_id: number;
  job_title: string;
  job_slug: string;
  job_description: string;
  type_of_position?: string;
  job_country: string;
  industry: string;
  experience: string;
  skills_required: string;
  monthly_fixed_salary_currency: string;
  monthly_fixed_salary_min: string;
  monthly_fixed_salary_max: string;
  available_vacancies: string;
  deadline: string;
  is_featured: number;
  hide_employer_details: number;
  background_banner_image?: string;
  meta_tag: string;
  meta_desc: string;
  job_type: string;
  job_status: string;
  created_at: string;
  updated_at: string;
  job_city: number;
  saved_id?: number;
  is_applied: boolean;
  //additional
  unlock_instant_apply: any;
  city: any;
  banner: File;
  street_address: string;
  postal_code: string;
  is_saved: boolean;
};
type Role = 'admin' | 'employer' | 'employees';

export type UserMembership = {
  id: number;
  plan_id: number;
  expire_at: string;
  purchase_at: string;
  status: 'active';
  plan: null;
  invoice_number: null;
  information: null;
};

export type User = {
  signup_completed?: boolean;
  available_resume_count: number;
  bio?: string;
  card_cvv: number;
  card_exp_month?: number;
  card_exp_year?: number;
  card_number?: number;
  card_type?: string;
  company_id?: number;
  contact_no?: number;
  country?: Country;
  created_at: string;
  created_by_id: number;
  currency?: string;
  current_position?: string;
  background_banner_image?: string;
  degree?: string;
  current_salary?: string;
  date_of_birth?: string;
  desired_salary?: string;
  email: string;
  email_verified_at?: string;
  facebook_link?: string;
  first_login: number;
  gender?: string;
  google_id?: number;
  id: number;
  industry?: string;
  instagram_link?: string;
  is2FA: boolean;
  isShowEmail: boolean | number;
  is_approved: string;
  job_status: string;
  job_type?: string;
  languages?: Language[];
  linked_id?: number;
  linkedin_link?: string;
  login_count?: number;
  name: string;
  nationality?: number;
  portfolio?: Portfolio[];
  profile_complete_percentage: number;
  profile_image: File;
  role?: string;
  sector?: number;
  showcontact_no: number;
  skills?: string;
  slug?: string;
  status: string;
  twitter_link?: string;
  unlock_instant_apply: boolean;
  updated_at: string;
  view_password?: string;
  work_experience: WorkExperiences[];
  website_url?: string;
  where_currently_based?: string;
  where_job_search?: string;
  years_of_experience?: string;
  //Additional
  description?: string;
  resumedate?: string;
  resume_pdf_path?: string;
  default_resume?: string;
  plan?: number;
  company?: Company;
  country_name?: string;
  otp: number;
  resumes: Resume[];
  jobStatus: string;
  education?: Education[];
  countries?: string;
  cities?: string;
  average_rating: number;
  total_reviews: null;
  team_members: {
    id: number;
    name: string;
    email: string;
    role: Role;
    designation: string;
    available_resume_count: number;
    status: string;
  }[];
  membership: UserMembership | any;
  active_job_count: 0;
  claim_status: {
    id: number;
    status: 'requested' | 'approved' | 'rejected';
    message: null;
    claim_date: string;
    user: {
      id: number;
      name: string;
      email: string;
      role: Role;
    };
  };
};

export interface Language {
  id: number;
  user_id: number;
  language: string;
  proficiency: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export type Resume = {
  created_at: string;
  default_resume: boolean;
  id: number;
  resume_pdf_path: string;
  status: string;
  updated_at: string;
  user_id: number;
  url?: string;
};

export type Author = {
  id: string;
  name: string;
  slug: string;
  profile_image: string;
  gender: string;
  designation: string;
  description: string;
  linkedin: string;
  status: string;
  profile_name: string;
};

export type Blogs = {
  name: string;
  slug: string;
  tag?: string;
  description: string;
  image?: string;
  author?: Author;
  meta_tag?: string;
  meta_desc?: string;
  created_by_id?: string;
  created_at?: string;
  id?: string;
  blog_category_id?: string;
  category_name?: string;
  author_image?: string;
  author_name?: string;
  meta?: PaginationMeta;
};

export type Settings = {
  id: number;
  user_id: number;
  logo: string;
  favicon: string;
  payment_gateway: string;
  payment_mode: string;
  stripe_test_secret_key: string;
  stripe_test_publish_key: string;
  stripe_live_secret_key: string;
  stripe_live_publish_key: string;
  homepage_meta_title: string;
  homepage_meta_description: string;
  jobs_meta_title: string;
  jobs_meta_description: string;
  carrer_meta_title: string;
  carrer_meta_description: string;
  about_meta_title: string;
  about_meta_description: string;
  employer_meta_title: string;
  employer_meta_description: string;
  employee_meta_title: string;
  employee_meta_description: string;
  pricing_meta_title: string;
  pricing_meta_description: string;
  blog_listing_meta_title: string;
  blog_listing_meta_description: string;
  linkedin_link: string;
  twitter_link: string;
  instagram_link: string;
  facebook_link: string;
  website_url: string;
  status: string;
  created_at: string;
  updated_at: string;
};

export type Company = {
  id: number;
  user_id: number;
  company_name: string;
  company_slug: string;
  company_email?: string;
  designation: string;
  company_website?: string;
  company_location: string;
  company_sector: string;
  no_of_employees: string;
  company_description: string; //html
  company_logo?: string;
  company_contact_no?: string;
  available_resume_count: number;
  linkedin_link?: string;
  twitter_link?: string;
  instagram_link?: string;
  facebook_link?: string;
  background_banner_image?: string;
  meta_tag: string;
  meta_desc: string;
  status: string;
  created_at: string;
  updated_at: string;
  country_name: string;
  sector_name: string;
  membership?: Membership;
  logo?: File;
  sector?: Sector;
  country?: Country;
  total_staff?: number;
  total_jobs?: number;
  jobs: any;
  banner: File;
  fk_logo_file_uuid?: string;
};

export type Membership = {
  id: number;
  company_id: number;
  plan_id: number;
  user_id: number;
  status: 'active' | 'pending' | 'deleted';
  purchase_at: string;
  expired_at: string;
  created_at: string;
  updated_at: string;
};

export type Country = {
  id: number;
  country_name: string;
  slug: string;
  flag: string;
  find: any;
  currency: string;
  capital: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
};

export type City = {
  id: string;
  city_name: string;
  country_name: string;
  country_id: string;
  status: string;
};

export type Industry = {
  id: number;
  name: string;
  find: any;
};

export type Sector = {
  id: number;
  find: any;
  sector_name: string;
  status: string;
  created_at: string;
};

export type Skill = {
  id: number;
  skills: string;
  sector_id: number;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
};

export type Experience = {
  id: number;
  name: string;
};

export type ErrorsLogs = {
  error_message: string;
  id: string;
  file_name: string;
  line_number: string;
};

export type PaginationMeta = {
  current_page: number;
  path: string;
  from: number;
  last_page: number;
  per_page: number;
  to: number;
  total: number;
};

export type File = {
  created_at: string;
  name: string;
  size: number;
  source: string;
  thumbnail: string;
  type: string;
  updated_at: string;
  uuid: string;
};

export type CompanyProfileProps = {
  company: Company;
};

export type JobItemProps = {
  jobs_data?: any;
  index?: any;
  isSaved?: boolean;
};

export type MembershippPlan = {
  amount: number | null;
  planId: number | null;
};

export const experienceRanges = [
  {id: 0, label: 'Choose Experience'},
  {id: 1, label: 'Fresher'},
  {id: 2, label: '0-1'},
  {id: 3, label: '2-3'},
  {id: 4, label: '3-5'},
  {id: 5, label: '5-7'},
  {id: 6, label: '7-10'},
  {id: 7, label: '10-15'},
  {id: 8, label: '15-20'},
  {id: 9, label: '20+'},
];

export type ApplyJobFilterProps = {
  sectors?: any;
  onClose?: () => void;
  allCountries?: any;
  onSubmit?: any;
  totalAppliedJobs?: string | number;
  Country?: any;
  skill?: any;
  filterJobData?: any;
  data?: any;
  dataSubmit?: any;
  filterJobId?: any;
  filterJobSectionName?: any;
  job_filter?: any;
  index?: any;
};
export type JobOffersPageProps = {
  resultState: any;
  similarCareerPaths: any;
  latestJobsOpenings: any;
  notFoundMessage: any;
  name: any;
  countries: any;
};

export type carrerProps = {
  industries: any;
};
export type LocationCareerProps = {
  countries: any;
  industries: any;
};

export type Education = {
  education_title: string;
  degree: string;
  start_date: string;
  end_date: string;
  currently_study_here: boolean;
  your_score: string;
  max_score: string;
};
export type Work = {
  title: string;
  company: string;
  start_date: string;
  end_date: string;
  currently_study_here: boolean;
  description: string;
};
export type Portfolio = {
  title: string;
  portfolio_link: string;
  start_date: string;
  end_date: string;
  present: boolean;
  description: string;
};
export type skill = {
  skills: string;
  id: string;
};
export type language = {
  language: string;
  proficiency: string;
};

export type ProfileProrps = {
  userData: any;
  workExperience: any;
};
export type countryIndustryProps = {
  countries: Country;
  industries: Sector;
};

export type filterSearch = {
  countries: any;
  industries: any;
};
export type userData = {
  id: number;
  name: string;
  email: string;
  contact_no: string;
  date_of_birth: string;
  bio: string;
  gender: string;
  years_of_experience: string;
  currency: string;
  current_salary: string;
  desired_salary: string;
  nationality: string;
  countries: string;
  cities: string;
  current_position: string;
  slug: string;
  sector: string;
  industry: string;
};

export type WorkExperiences = {
  title: string;
  company: string;
  start_date: string;
  end_date: string;
  description: string;
};
