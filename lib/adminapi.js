import {getToken} from '../lib/session';
import axios from 'axios';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '@/lib/ErrorHandler';

export const getActiveEmployer = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/users/getActiveEmployer', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getActiveEmployee = async data => {
  const response = await axios.get('users/getActiveEmployee');
  return response.data;
};

export const getActiveJobs = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/jobs/getActiveJobs', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllEmployer = async (data, cancelTokenSource) => {
  return new Promise((resolve, reject) => {
    const req = axios.get('/users/employers', {
      cancelToken: cancelTokenSource?.token,
      params: data,
    });
    req.then(res => resolve(res)).catch(err => reject(err));
  });
};

export const getalljobs = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/getalljobs', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const deleteJob = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/jobs/' + id, {
      method: 'delete',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getSingleJobs = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/getsinglejobs/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        // Authorization: "Bearer " + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllCompany = async search => {
  return new Promise((resolve, reject) => {
    const req = axios.get(process.env.NEXT_PUBLIC_API_URL + '/companies/getcompany', {
      params: {
        search,
      },
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const updateAdminLogo = async (id, profile_image) => {
  try {
    let formdata = new FormData();
    formdata.append('profile_image', profile_image);

    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_API_URL}/users/uploadAdminLogoUpdate/${id}`,
      formdata,
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'multipart/form-data',
          Authorization: 'Bearer ' + getToken().token,
        },
      },
    );

    return response.data;
  } catch (error) {
    throw error;
  }
};

export const deleteuser = async (id, status) => {
  return new Promise((resolve, reject) => {
    const req = axios
      .delete(
        `${process.env.NEXT_PUBLIC_API_URL}/users/user/${id}`,
        {status},
        {
          headers: {
            Accept: 'application/json',
            Authorization: 'Bearer ' + getToken().token,
          },
        },
      )
      .then(res => resolve(res.data))
      .catch(err => reject(err));
  });
};

export const getInActiveEmployee = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/users/getInActiveEmployee', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllinactiveUsers = async () => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/users/getAllinactiveUsers', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllSector = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/sector/getallsectors', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllSectorsForAdmin = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/sector/getallsectorsforadmin', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const editAndSaveSectorData = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/sector/editandsavesector', {
      method: 'post',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const deleteSector = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/sector/sector', {
      method: 'delete',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllCountries = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/countries/countries', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const editAndSaveCountryData = async (data, image) => {
  let formdata = new FormData();
  formdata.append('image', image);
  return new Promise((resolve, reject) => {
    const req = axios.post(process.env.NEXT_PUBLIC_API_URL + '/countries/editandsavecountry', formdata, {
      method: 'post',
      headers: {
        Accept: 'application/json',
        'Content-Type': `multipart/form-data; boundary=${formdata._boundary}`,
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const deleteCountry = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/countries/country', {
      method: 'delete',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllSkillsForAdmin = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/skills/getallskillsforadmin', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const editAndSaveSkillData = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/skills/editandsaveskill', {
      method: 'post',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const deleteSkill = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/skills/skill', {
      method: 'delete',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const updateLogoAndFavicon = async (data, image) => {
  let formdata = new FormData();
  formdata.append('image', image);
  return new Promise((resolve, reject) => {
    const req = axios.put(process.env.NEXT_PUBLIC_API_URL + '/admin/settings/update-logo-and-favicon', formdata, {
      method: 'put',
      headers: {
        Accept: 'application/json',
        'Content-Type': `multipart/form-data; boundary=${formdata._boundary}`,
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllNewAdminSettings = async () => {
  const response = await fetch(process.env.NEXT_PUBLIC_API_URL + '/settings/public');
  return response.json();
};

export const UpdateAdminSettings = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/admin/settings/update-admin-settings', {
      method: 'put',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllIndustries = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/industry/getallindustries', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllIndustriesForAdmin = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/industry/getallindustriesforadmin', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const editAndSaveIndustriesData = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/industry/editandsaveindustries', {
      method: 'post',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const deleteIndustries = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/industry/industries', {
      method: 'delete',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllBlogs = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/blog/get-all-blogs', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllBlogCategories = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/blog/get-blog-category', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const EditAndSaveBlogData = async (data, image) => {
  console.log(data);
  let heading = data.content.split('<h1>')[0];
  let description = data.content.replace(heading, ''); //x.replace(y, '')
  let formdata = new FormData();
  formdata.append('image', image);
  formdata.append('id', data.id);
  // formdata.append('articles',JSON.stringify(data.articles));
  formdata.append('name', data.name);
  formdata.append('slug', data.slug);
  formdata.append('author_id', data.author_id);
  formdata.append('tag', data.tag);
  formdata.append('description', description);
  formdata.append('heading', heading);
  formdata.append('meta_tag', data.meta_tag);
  formdata.append('meta_desc', data.meta_desc);
  formdata.append('created_by_id', data?.created_by_id);
  formdata.append('blog_category_id', data.blog_category_id);
  // formdata.append('job_searches',JSON.stringify(data.job_searches));
  // formdata.append('blog_cta',JSON.stringify(data.blog_cta));
  return new Promise((resolve, reject) => {
    const req = axios.post(process.env.NEXT_PUBLIC_API_URL + '/blog/edit-and-save-blog-data', formdata, {
      method: 'post',
      headers: {
        Accept: 'multipart/form-data',
        'Content-Type': 'multipart/form-data',
        Authorization: 'Bearer ' + getToken(),
      },
    });
    req
      .then(res => resolve(res.data))
      .catch(err => {
        // console.log('777', err.response.data);
        ErrorHandler.showNotification(err);
        reject(err?.response?.data);
      });
  });
};

export const deleteBlog = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/blog/blog', {
      method: 'delete',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getStaffMember = async (id, data) => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/admin/get-staff-member/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getSingleStaffMember = async (id, data) => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/admin/get-single-staff-member/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const updateSingleStaffMember = async (id, data) => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/admin/update-single-staff-member/' + id, {
      method: 'put',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const Activateuser = async (id, status) => {
  return new Promise((resolve, reject) => {
    const req = axios
      .post(
        `${process.env.NEXT_PUBLIC_API_URL}/users/Activateuser/${id}`,
        {status},
        {
          headers: {
            Accept: 'application/json',
            Authorization: 'Bearer ' + getToken().token,
          },
        },
      )
      .then(res => resolve(res.data))
      .catch(err => reject(err));
  });
};

export const getAllCountriesForAdmin = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/countries/getallcountriesforadmin', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getUserAllJobs = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/jobs/getuseralljobs', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllCitiesForAdmin = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/cities/getallcitiesforadmin', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const deleteCity = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/cities/city', {
      method: 'delete',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const editAndSaveCityData = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/cities/editandsavecity', {
      method: 'post',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const updateCityStatus = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/cities/updatecitystatus', {
      method: 'put',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const updateCountryStatus = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/countries/countrystatus', {
      method: 'put',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllErrorLog = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/all-error-log', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const deleteerrorlog = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/error-log', {
      method: 'delete',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const handledeleteAllData = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/all-error-log', {
      method: 'delete',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const createOrUpdateAuthor = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/author/create', {
      method: 'post',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'multipart/form-data',
        Authorization: 'Bearer ' + getToken(),
      },
      data: data,
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const deleteAuthor = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/author/delete/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const searchAuthor = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/author/search', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getHomeTopBlogs = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(process.env.NEXT_PUBLIC_API_URL + '/blog/get-home-top-blogs', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
