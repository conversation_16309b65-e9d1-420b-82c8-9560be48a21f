import Cookies from 'js-cookie';

/**
 * * Remove stored token
 * It should remove the Token into the SessionStorage or LocalStorage
 *
 * @returns {void}
 */
export function removeToken() {
  window.localStorage.removeItem('token');
  window.sessionStorage.removeItem('token');
  window.localStorage.removeItem('exp');
  window.sessionStorage.removeItem('exp');
}

export function removeStorageData() {
  window.localStorage.removeItem('user_id');
  window.localStorage.removeItem('username');
  window.localStorage.removeItem('email');
  window.localStorage.removeItem('user_role');
  window.localStorage.removeItem('contact_no');
  window.localStorage.removeItem('login_count');
  window.localStorage.removeItem('company_id');
  window.localStorage.removeItem('slug');
  window.localStorage.removeItem('company_resume_count');
  window.localStorage.removeItem('user_resume_count');
  window.localStorage.removeItem('membership');
  window.localStorage.removeItem('plan');
  window.localStorage.removeItem('designation');
}

/**
 * * Get the Token if presents.
 *
 * @returns {{exp: string, token: string}}
 */
export function getToken() {
  let token = '';
  if (typeof window !== 'undefined') {
    token = Cookies.get('session_token');
  }
  return {token};
}

export function getCurrentUserData() {
  if (typeof window !== 'undefined') {
    const current_user_data = {
      id: window.localStorage.getItem('user_id'),
      username: window.localStorage.getItem('username'),
      email: window.localStorage.getItem('email'),
      role: window.localStorage.getItem('user_role'),
      where_currently_based: window.localStorage.getItem('where_currently_based'),
      where_job_search: window.localStorage.getItem('where_job_search'),
      login_count: window.localStorage.getItem('login_count'),
      company_id: window.localStorage.getItem('company_id'),
      currentJobStatus: window.localStorage.getItem('currentJobStatus'),
      userProfileImage: window.localStorage.getItem('userProfileImage'),
      company_resume_count: window.localStorage.getItem('company_resume_count'),
      designation: window.localStorage.getItem('designation'),
      user_resume_count: window.localStorage.getItem('user_resume_count'),
      slug: window.localStorage.getItem('slug'),
      otp: window.localStorage.getItem('otp'),
      membership: window.localStorage.getItem('membership'),
      plan: window.localStorage.getItem('plan'),
    };
    //console.log(current_user_data.otp)
    return current_user_data;
  } else {
    const current_user_data = {};
    return current_user_data;
  }
}
