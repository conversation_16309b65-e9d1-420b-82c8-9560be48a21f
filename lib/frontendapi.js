import {getToken} from '@/lib/session';
import axios from 'axios';
import Cookies from 'js-cookie';

export const login = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/login', {
      method: 'post',
      headers: {
        Accept: 'application/json',
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getCurrentUserDetails = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/users/getusersdetails/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getAllStatusCountries = async () => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/countries/countries-all', {
      method: 'get',
      headers: {
        Accept: 'application/json',
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getAllCountries = async () => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/countries/countries', {
      method: 'get',
      headers: {
        Accept: 'application/json',
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getAllCities = async () => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/cities/getallcities', {
      method: 'get',
      headers: {
        Accept: 'application/json',
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getSingleCountryAllCities = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/cities/getsinglecountryallcities/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getAllCitiesByCountryName = async countryName => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/cities/getallcitiesbycountryname/' + countryName, {
      method: 'get',
      headers: {
        Accept: 'application/json',
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const completeUserSignUp = async (password, phoneNumber, role) => {
  const response = await axios.post('/users/signup/complete', {password, phone: phoneNumber, role});
  return response.data;
};

export const addCompanyInfo = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.post('/companies', data, {
      headers: {
        Accept: 'application/json',
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const UpdateSetCompanyInfo = async (id, data) => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/companies/updatecompanyinfo/' + id, {
      method: 'put',
      headers: {
        Accept: 'application/json',
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const addTeamMembers = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/users/addteammembers', {
      method: 'post',
      headers: {
        Accept: 'application/json',
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const savePublishJob = async data => {
  const response = await axios.post('/jobs', data);
  return response.data;
};

// jobs apis
export const getCurrentUserAllJobs = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/jobs/getcurrentuseralljobs', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const updateFirstTimePopupStatus = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/users/firstlogin/' + id, {
      method: 'put',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const updateJob = async (id, data) => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/jobs/updatejobs/' + id, {
      method: 'put',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getSingleJobs = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/jobs/getsinglejobs/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getSingleUserDetails = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/users/getusersdetails/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const updateUserDetails = async (id, data) => {
  return new Promise((resolve, reject) => {
    const req = axios.post('/users/useraccountdetails/' + id, data, {
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const deleteJob = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/jobs/jobs/' + id, {
      method: 'delete',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const changepassword = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/users/changepassword', {
      method: 'post',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getSingleCompanyDetails = async userid => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/companies/getsinglecompanydetails/' + userid, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getSingleEmployerCompanyDetails = async id => {
  const response = await axios.get('/company/' + id);
  return response.data;
};

export const updateAccountAccess = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/settings/updateaccountaccesssettings', {
      method: 'put',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const updateNewsLetterAccess = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/settings/updatenewsletteraccesssettings', {
      method: 'put',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const updateRecommendationsAccess = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/settings/updaterecommendationsaccesssettings', {
      method: 'put',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const updateAnnouncementsAccess = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/settings/updateannouncementsaccesssettings', {
      method: 'put',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const updateMessageFromRecruitersAccess = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/settings/updatemessagefromcandidateaccesssettings', {
      method: 'put',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getSettings = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/settings/getuseraccesssettings', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getCompanyActiveJobs = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/jobs/getcompanyactivejobs', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const updateEmployee = async (id, data, userProfileImage) => {
  let formData = new FormData();
  formData.append('profileImage', userProfileImage);
  const response = await axios.post(`/users/employee/` + id, formData, {
    params: {
      ...data,
    },
  });
  return response.data;
};
export const updateCompanyDetails = async (id, data) => {
  return new Promise((resolve, reject) => {
    const req = axios.put('/companies/updatecompany/' + id, data, {
      method: 'put',
      headers: {
        Accept: 'application/json',
        'Content-Type': `multipart/form-data; boundary=${data._boundary}`,
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getSingleCompanyUserFollowers = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/companyfollower/getsinglecompaniesuserfollow', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const saveFollowRequest = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/companyfollower/createcompanyfollower', {
      method: 'post',
      headers: {
        Accept: 'application/json',
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const deleteUnFollowRequest = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/companyfollower/deletecompanyfollower/', {
      method: 'delete',
      headers: {
        Accept: 'application/json',
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getCompanyFollowers = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/companyfollower/getcompanyfollowersbycompanyid/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const toggleFavoriteJob = async data => {
  const response = await axios.post(`jobs/${data.job_id}/toggle-favorite`);
  return response.data;
};

/**
 * @deprecated
 * @param id
 * @return {Promise<any>}
 */
export const removeFavoriteJob = async id => {
  const response = await axios.post(`jobs/${id}/remove-favorite`);
  return response.data;
};

export const getusersavedjobs = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/jobs/getusersavedjobs', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const applyJob = async data => {
  return new Promise((resolve, reject) => {
    let formdata = new FormData();
    Object.keys(data).forEach(key => {
      formdata.append(key, data[key]);
    });
    const req = axios.post('/applications/createapplication', formdata, {
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
        'Content-Type': `multipart/form-data; boundary=${formdata._boundary}`,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllJobsSearch = async data => {
  return new Promise((resolve, reject) => {
    const session = getToken();
    const req = axios.request('/getalljobssearch', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + session?.token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res)).catch(err => reject(err));
  });
};

export const getApplications = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/applications/getallapplications/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const addEducation = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/education/createeducation', {
      method: 'post',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getEducationsEmployee = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/education/getsingleeducation/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const addWorkExperience = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/workexperience/createworkexperience', {
      method: 'post',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getWorkExperienceEmployee = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/workexperience/getsingleworkexperience/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const addPortfolio = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/portfolio/addportfolio', {
      method: 'post',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getportfolio = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/portfolio/getsingleportfolio/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const updateEducation = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.put('/education/updateeducation', data, {
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getEducationsSingleEmployee = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/education/getsingleeducationid/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const updateWorkExperience = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.put('/workexperience/updateworkexperience', data, {
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const updatePortfolio = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.put('/portfolio/updateportfolio', data, {
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getSinglePortfilio = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/portfolio/getSinglePortfolioid/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getSingleWorkExperience = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request({
      url: '/workexperience/getsingleworkexperienceID/' + id,
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const SearchAllCountries = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request({
      url: '/countries',
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllSectors = async (data, cancelTokenSource = null) => {
  return new Promise((resolve, reject) => {
    const req = axios.get('/sectors/getallsectors', {
      params: data,
      cancelToken: cancelTokenSource?.token,
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getCountries = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request({
      url: '/countries/getcountries',
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const SearchAllSectors = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/sectors/searchallsectors', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getSingleCompanyFollowersByCompanyId = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/companyfollower/getsinglecompanyfollowersbycompanyid/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const updateCompanyProfileSocialLinks = async (id, data) => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/companies/updatecompanysociallinks/' + id, {
      method: 'put',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getAllJobApplications = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/applications/getalljobapplications', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const updateHiringStatus = async (id, data) => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/applications/updatehiringstatus/' + id, {
      method: 'put',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getSingleJobApplications = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/applications/getsinglejobapplications', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllSkills = async () => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/skills/getallskills', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getSingleSkills = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/skills/getsingleskills/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllLanguages = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/languages/getalllanguages/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getSingleEmployerUserDetails = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/users/getemployerusersdetails/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getSingleEmployerActiveJobs = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/jobs/getemployeractivejobs', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getUserSingleJobs = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/jobs/getusersinglejobs', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res)).catch(err => reject(err));
  });
};

export const getJobSkills = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/skills/getjobsskills', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const addInterview = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/interview/insertinterviews', {
      method: 'post',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getAllInterviews = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/interview/getallinterviews', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getSingleInterviews = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/interview/getsingleinterviews/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const updateZoomMeetingLink = async (id, data) => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/interview/updatezoommeetinglink/' + id, {
      method: 'put',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const deleteInterview = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/interview/interviews/' + id, {
      method: 'delete',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const updateAvailibility = async (id, data) => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/interview/updateinterviews/' + id, {
      method: 'put',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const savePlanPayment = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/payment/savepayment', {
      method: 'post',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getCurrentUserPaymentDetails = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/payment/getuserpaymentdetails/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getNotifications = async data => {
  const response = await axios.get('/notifications', {
    headers: {
      'Content-Type': 'application/json',
      Cookie: this.sessionid,
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PATCH, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers':
        'Origin, Content-Type, X-Auth-Token, Authorization, Accept,charset,boundary,Content-Length',
    },
  });
  return response.data;
};

export const getLanguage = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/languages/getalllanguages/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getSingleUserSkill = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/employeeskills/getSingleUserSkill/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const searchSkills = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/skills/searchSkills', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const addEmployeeSkills = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/employeeskills/addemployeeskills', {
      method: 'post',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const addlanguage = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/languages/addlanguage', {
      method: 'post',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getSingleLanguage = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/languages/getsinglelanguages/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const updateLanguage = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.put('/languages/updatelanguages', data, {
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const updateUnlockInstantApply = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/users/updateUnlockInstantApply/' + id, {
      method: 'put',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        unlock_instant_apply: 1,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getSingleUserLanguage = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/languages/getSingleUserLanguage/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const deleteEmployeeSkills = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/employeeskills/employeeskills/' + id, {
      method: 'delete',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const updateUserSocialLinks = async (id, data) => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/users/updateUserSocialLinks/' + id, {
      method: 'put',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getSocialLinks = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/users/getSocialLinks/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const forgetPassword = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(`${process.env.NEXT_PUBLIC_API_URL}/forgetpassword`, {
      email: data,
      method: 'post',
      headers: {
        Accept: 'application/json',
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const resetPassword = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(`${process.env.NEXT_PUBLIC_API_URL}/reset-password`, {
      data,
      method: 'post',
      headers: {
        Accept: 'application/json',
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getReadNotifications = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/notifications/getreadnotifications', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const changeReadUnreadStatus = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/notifications/updatereadunreadnotificationstatus', {
      method: 'put',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const updateCompanyLogo = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.post('/companies/updatecompanylogo', data, {
      method: 'post',
      headers: {
        Accept: 'application/json',
        'Content-Type': `multipart/form-data`,
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const saveProfileViewUser = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/companyprofileview/createcompanyview', {
      method: 'post',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getCompanyProfileViewUserViewCount = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/companyprofileview/getcompanyProfileviewuserviewcount', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getCompanyProfileAllUserViewsCount = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/companyprofileview/getcompanyprofilealluserviewcount', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getTotalCompanyJobApplicationsCount = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/applications/gettotalcompanyjobapplicationscount', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const deleteEducation = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/education/education/' + id, {
      method: 'delete',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const destroyWorkExperience = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/workexperience/workexperience/' + id, {
      method: 'delete',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const deletePortfolio = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/portfolio/portfolio/' + id, {
      method: 'delete',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getUserInterviewss = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/interview/getUserInterviewss/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const updateInterviewStatus = async (id, status) => {
  return new Promise((resolve, reject) => {
    const req = axios
      .put(
        `${process.env.NEXT_PUBLIC_API_URL}/interview/updateInterviewStatus/${id}`,
        {status},
        {
          headers: {
            Accept: 'application/json',
            Authorization: 'Bearer ' + getToken().token,
          },
        },
      )
      .then(res => resolve(res.data))
      .catch(err => reject(err));
  });
};
export const updateSingleJobBackgroundBannerImage = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.put(`/jobs/updatesinglejobbackgroundbannerimage`, data, {
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const updateSingleCompanyBackgroundBannerImage = async (data, companyBannerImage) => {
  let formdata = new FormData();
  formdata.append('company_background_banner_img', companyBannerImage);
  return new Promise((resolve, reject) => {
    const req = axios.post('/companies/updatesinglecompanybackgroundbannerimage', formdata, {
      method: 'put',
      headers: {
        Accept: 'application/json',
        'Content-Type': `multipart/form-data; boundary=${formdata._boundary}`,
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getUserDetailsslug = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/users/getUserDetailsslug/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getFutureInterviewSchedule = async user_id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/interview/getcompanyuserfutureinterviews/' + user_id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const updatebackgroundImage = async (id, formData) => {
  try {
    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_API_URL}/users/updatesingleuserbackgroundbannerimage/${id}`,
      formData,
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'multipart/form-data',
          Authorization: 'Bearer ' + getToken().token,
        },
      },
    );

    return response.data;
  } catch (error) {
    console.error('Error updating background image:', error.message);
    throw new Error('Failed to update background image');
  }
};

export const sendMessage = async (data, attachmentFile) => {
  let formdata = new FormData();
  formdata.append('attachment_path', attachmentFile);
  return new Promise((resolve, reject) => {
    const req = axios.post('/message/savemessage', formdata, {
      method: 'post',
      headers: {
        Accept: 'application/json',
        'Content-Type': `multipart/form-data; boundary=${formdata._boundary}`,
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getAllEmployerReceiverMessages = async user_id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/message/getallemployerreceivermessages/' + user_id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getAllStaffReceiverMessages = async user_id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/message/getallstaffreceivermessages/' + user_id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getAllEmployeesReceiverMessages = async user_id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/message/getallemployeesreceivermessages/' + user_id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getAllEmployerSingleUserMessages = async user_id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/message/getallemployersingleusermessages/' + user_id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getAllEmployeesSingleUserMessages = async user_id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/message/getallemployeessingleusermessages/' + user_id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getAllReceiverUserMessages = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/message/getallreceiverusermessages', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getJobViewUserViewCount = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/jobsview/getjobviewuserviewcount', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const saveJobViewUser = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/jobsview/createjobview', {
      method: 'post',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getJobAllUserViewsCount = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/jobsview/getjoballuserviewcount', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const SearchSkill = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/skills/searchallskill', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const updateArchivedMessages = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/message/updatearchivedmessages', {
      method: 'put',
      headers: {
        Accept: 'application/json',
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const updateUnArchivedMessages = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/message/updateunarchivedmessages', {
      method: 'put',
      headers: {
        Accept: 'application/json',
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getAllEmployerReceiverArchivedMessages = async user_id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/message/getallemployerreceiverarchivedmessages/' + user_id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getAllEmployeesReceiverArchivedMessages = async user_id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/message/getallemployeesreceiverarchivedmessages/' + user_id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getAllEmployerSingleUserArchivedMessages = async user_id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/message/getallemployersingleuserarchivedmessages/' + user_id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getAllEmployeesSingleUserArchivedMessages = async user_id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/message/getallemployeessingleuserarchivedmessages/' + user_id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getAllReceiverUserArchivedMessages = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/message/getallreceiveruserarchivedmessages', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllCompanyAndJobs = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/company/getallcompanyandjobs', {
      method: 'get',
      headers: {
        Accept: 'application/json',
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getUserAllPaymentDetails = async (id, data) => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/payment/getuserallpaymentdetails/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => id && resolve(res.data)).catch(err => reject(err));
  });
};
export const getStripePlanDetails = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/payment/getstripeplandetails', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const updateUserCardDetails = async (id, data) => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/users/updateusercarddetails/' + id, {
      method: 'put',
      headers: {
        Accept: 'application/json',
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getLastPaymentDetails = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/payment/getlastpaymentdetails', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const subscribe = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request(`${process.env.NEXT_PUBLIC_API_URL}/subscribe/subscribe`, {
      data,
      method: 'post',
      headers: {
        Accept: 'application/json',
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAlljobfilter = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/jobs/getalljobfilter/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const UpdateSaveJobFilterData = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/jobs/updatesavejobfilter', {
      method: 'put',
      headers: {
        Accept: 'application/json',
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const DeleteJobFilter = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/jobs/jobfilter', {
      method: 'post',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getSingleBlogBySlug = async slug => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/blog/' + slug + '/content', {
      method: 'get',
      headers: {
        Accept: 'application/json',
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getBlogByTag = async tag => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/blog/blogs/' + tag, {
      method: 'get',
      headers: {
        Accept: 'application/json',
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getExperiance = async () => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/users/experiance', {
      method: 'get',
      headers: {
        Accept: 'application/json',
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
//candidate filter api start

export const getAllCandidateSearch = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/candidate/getallcandidatesearch', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllCandidateFilter = async (id, searchByKeyword = null) => {
  return new Promise((resolve, reject) => {
    const req = axios.request(`/candidate/getallcandidatefilter/${id}/${searchByKeyword}`, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const UpdateSaveCandidateFilterData = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/candidate/updatesavecandidatefilter', {
      method: 'put',
      headers: {
        Accept: 'application/json',
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const DeleteCandidateFilter = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/candidate/candidatefilter', {
      method: 'delete',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getIndustries = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request({
      url: '/industry/getallindustries',
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const updateUserSectorAndIndustry = async (id, data) => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/users/updateUserSectorandIndustry/' + id, {
      method: 'put',
      headers: {
        Accept: 'application/json',
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const updateMessagesReadUnReadStatus = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/message/updatemessagesreadunreadstatus', {
      method: 'put',
      headers: {
        Accept: 'application/json',
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getTotalMessageUnReadCount = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/message/gettotalmessageunreadcount', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const deleteJobType = async (id, jobTypeToDelete) => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/users/jobType/' + id, {
      method: 'delete',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        job_type: jobTypeToDelete,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const checkAndUpdateResumesViewed = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/candidate/checkandupdateresumesviewed', {
      method: 'post',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const AddUpdateStaffMember = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/candidate/addupdatestaffmember', {
      method: 'post',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllStaffMembers = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/candidate/getallstaffmembers/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const DeleteStaff = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/candidate/staff', {
      method: 'delete',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getStaffUserAllJobs = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/staff/getcurrentstaffalljobs', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getCurrentStaffUserAllJobs = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/staff/getcurrentstaffuseralljobs', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const updateJobByStaff = async (id, data) => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/staff/updatejobsbystaff/' + id, {
      method: 'put',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getFirstMessageCheckCount = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/message/gettotalmessagecount', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllCandidateHeaderSearch = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/candidate/getallcandidateheadersearch', {
      method: 'get',
      headers: {
        Accept: 'application/json',
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getApplyJobInterview = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/interview/getapplyjobinterview', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const updateTwoFactorAuth = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request({
      url: '/users/updatetwofactorauth/' + data.userId,
      method: 'put',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      data: {
        is2FA: data.is2FA,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const OtpMatch = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/match-otp', {
      method: 'post',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      params: {
        ...data,
      },
    });
    req
      .then(res => {
        const session_token = Cookies.get('temp_token');
        if (session_token) {
          // registerSessionToken(session_token);
          axios.defaults.headers.common.Authorization = 'Bearer ' + session_token;
          Cookies.set('session_token', session_token);
          // Cookies.remove('temp_token');
        }
        return resolve(res.data);
      })
      .catch(err => reject(err));
  });
};

export const deleteLanguage = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/languages/language/' + id, {
      method: 'delete',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getUserAllTeamMembers = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/users/getuserallteammembers/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getSingleTeamMember = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/users/getsingleteammember/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const deleteTeamMember = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/users/teammember/' + id, {
      method: 'delete',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const updateTeamMember = async (id, data) => {
  return new Promise((resolve, reject) => {
    const req = axios.request({
      url: '/users/updateteammember/' + id,
      method: 'put',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken(),
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getCompanyFirstInsightsChart = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/company/getcompanyfirstinsightschart', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getCompanySecondInsightsChart = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/company/getcompanysecondinsightschart', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getSingleUserDetailsByEmail = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/users/getuserdetailsbyemail', {
      method: 'get',
      headers: {
        Accept: 'application/json',
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllMemeberShipDetails = async id => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/membership/get-all-memebership-details/' + id, {
      method: 'get',
      headers: {
        Accept: 'application/json',
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getJobViewUsers = async jobId => {
  return new Promise((resolve, reject) => {
    const req = axios.request(`${process.env.NEXT_PUBLIC_API_URL}/jobsview/get-jobview-users`, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        job_id: jobId,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const updateNotificationStatus = async (id, data) => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/notifications/updatenotificationstatus/' + id, {
      method: 'put',
      headers: {
        Accept: 'application/json',
      },
      data: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getCountriesCitiesLocations = async () => {
  return new Promise((resolve, reject) => {
    const req = axios.request({
      url: '/countries/getcountriesselectedids',
      method: 'get',
      headers: {
        Accept: 'application/json',
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const SearchCities = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request({
      url: '/cities/searchallcities',
      method: 'get',
      headers: {
        Accept: 'application/json',
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getSingleCityByName = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/cities/single-city-by-name', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getSingleSkillByName = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/skills/singleskillbyname', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
export const getSingleCountryByName = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/countries/single-country-by-name', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const jobAppliedOrNot = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/checkjobapplied', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllAuthors = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/author', {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      params: {
        ...data,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAuthorsBySlug = async slugOrId => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/author/' + slugOrId, {
      method: 'get',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const updateOrCreateClaimRequest = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/company/claim-company', {
      method: 'post',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data,
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const createUserSettings = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/settings/create', {
      method: 'post',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data,
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const purchasePlan = async data => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/plan/purchase', {
      method: 'post',
      headers: {
        Accept: 'application/json',
        Authorization: 'Bearer ' + getToken().token,
      },
      data,
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};

export const getAllNationality = async () => {
  return new Promise((resolve, reject) => {
    const req = axios.request('/countries/nationality', {
      method: 'get',
      headers: {
        Accept: 'application/json',
      },
    });
    req.then(res => resolve(res.data)).catch(err => reject(err));
  });
};
