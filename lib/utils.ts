import { jsPDF } from "jspdf";
import * as htmlToImage from "html-to-image";

export async function exportMultipleChartsToPdf() {
  const doc = new jsPDF("p", "px");

  const elements = document.getElementsByClassName("chart_section");

  // Get chart data for statistics
  const chartData = (window as Window & { chartData?: any }).chartData || {};
  const labels = chartData.labels || [];
  const datasets = chartData.datasets || [];

  await createPdf({ doc, elements, chartData: { labels, datasets } });

  doc.save(`charts.pdf`);
}

async function createPdf({
  doc,
  elements,
  chartData
}: {
  doc: jsPDF;
  elements: HTMLCollectionOf<Element>;
  chartData: { labels: string[], datasets: any[] }
}) {
  const padding = 10;
  const marginTop = 20;
  let top = marginTop;

  // Add title
  doc.setFontSize(18);
  doc.setTextColor(0, 85, 186); // #0055ba
  doc.text("Dashboard Analytics Report", padding, top);
  top += 30;

  // Add date
  doc.setFontSize(12);
  doc.setTextColor(100, 100, 100);
  doc.text(`Generated on: ${new Date().toLocaleDateString()}`, padding, top);
  top += 30;

  for (let i = 0; i < elements.length; i++) {
    const el = elements.item(i) as HTMLElement;
    const imgData = await htmlToImage.toPng(el);

    let elHeight = el.offsetHeight;
    let elWidth = el.offsetWidth;

    const pageWidth = doc.internal.pageSize.getWidth();

    if (elWidth > pageWidth) {
      const ratio = pageWidth / elWidth;
      elHeight = elHeight * ratio - padding * 2;
      elWidth = elWidth * ratio - padding * 2;
    }

    const pageHeight = doc.internal.pageSize.getHeight();

    if (top + elHeight > pageHeight) {
      doc.addPage();
      top = marginTop;
    }

    doc.addImage(imgData, "PNG", padding, top, elWidth, elHeight, `image${i}`);
    top += elHeight + marginTop;

    // Add statistical breakdown after the chart
    if (chartData.labels.length > 0 && chartData.datasets.length > 0) {
      // Add a new page for statistics
      doc.addPage();
      top = marginTop;

      // Add statistics title
      doc.setFontSize(16);
      doc.setTextColor(0, 85, 186);
      doc.text("Statistical Breakdown", padding, top);
      top += 25;

      // Add table headers
      doc.setFontSize(12);
      doc.setTextColor(0, 0, 0);
      const headers = ["Period", ...chartData.datasets.map(ds => ds.label)];
      const columnWidth = (pageWidth - (padding * 2)) / headers.length;

      headers.forEach((header, index) => {
        doc.text(header, padding + (columnWidth * index), top);
      });

      top += 15;

      // Add horizontal line
      doc.setDrawColor(200, 200, 200);
      doc.line(padding, top - 5, pageWidth - padding, top - 5);

      // Add data rows
      doc.setFontSize(10);
      chartData.labels.forEach((label, labelIndex) => {
        if (top > pageHeight - 30) {
          doc.addPage();
          top = marginTop;

          // Repeat headers on new page
          doc.setFontSize(12);
          headers.forEach((header, index) => {
            doc.text(header, padding + (columnWidth * index), top);
          });
          top += 15;
          doc.setDrawColor(200, 200, 200);
          doc.line(padding, top - 5, pageWidth - padding, top - 5);
          doc.setFontSize(10);
        }

        // Add period label
        doc.text(label, padding, top);

        // Add values for each dataset
        chartData.datasets.forEach((dataset, datasetIndex) => {
          const value = dataset.data[labelIndex] || 0;
          doc.text(value.toString(), padding + (columnWidth * (datasetIndex + 1)), top);
        });

        top += 12;
      });

      // Add summary statistics
      top += 20;
      doc.setFontSize(14);
      doc.setTextColor(0, 85, 186);
      doc.text("Summary", padding, top);
      top += 20;

      // Calculate totals, averages, min, max for each dataset
      chartData.datasets.forEach((dataset, datasetIndex) => {
        if (top > pageHeight - 60) {
          doc.addPage();
          top = marginTop;
        }

        const data = dataset.data || [];
        const total = data.reduce((sum: number, val: number) => sum + val, 0);
        const avg = total / (data.length || 1);
        const min = Math.min(...data);
        const max = Math.max(...data);

        doc.setFontSize(12);
        doc.setTextColor(0, 0, 0);
        doc.text(`${dataset.label}:`, padding, top);
        top += 15;

        doc.setFontSize(10);
        doc.text(`Total: ${total}`, padding + 20, top);
        top += 12;
        doc.text(`Average: ${avg.toFixed(2)}`, padding + 20, top);
        top += 12;
        doc.text(`Minimum: ${min}`, padding + 20, top);
        top += 12;
        doc.text(`Maximum: ${max}`, padding + 20, top);
        top += 20;
      });
    }
  }
}
