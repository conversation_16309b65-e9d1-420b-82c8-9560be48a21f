import axios, {CancelTokenSource} from 'axios';
import {Country, Job, Sector, Industry} from '@/lib/types';

export const getCountries = async (
  searchKeywords?: string,
  data?: any,
  cancelTokenSource?: CancelTokenSource,
): Promise<Country[]> => {
  const response = await axios.get('/countries', {
    params: {
      keywords: searchKeywords,
      ...data,
    },
    cancelToken: cancelTokenSource?.token,
  });
  return response.data;
};

export const getSectorsList = async (): Promise<Sector> => {
  const response = await axios.get('sectors/searchallsectors?order_by=desc');
  return response.data.data;
};

export const getIndustries = async (
  searchKeywords?: string,
  data?: any,
  cancelTokenSource?: CancelTokenSource,
): Promise<Industry[]> => {
  const response = await axios.get('/industry/getallindustries', {
    params: {
      keywords: searchKeywords,
      ...data,
    },
    cancelToken: cancelTokenSource?.token,
  });
  return response.data.data;
};

export const getSalaryInsights = async (country_id: any, sector_id: any) => {
  const response = await axios.get('/salaries/insights', {
    params: {
      sector_id: sector_id,
      country_id: country_id,
    },
  });
  return response.data;
};

export const getSimilarCarrerPaths = async (country_id: any, sector_id: any) => {
  const response = await axios.get('/salaries/career-paths', {
    params: {
      sector_id: sector_id,
      country_id: country_id,
      num_items: '3',
    },
  });
  return response.data;
};
export const getLatestJobOpenings = async (country_id: any, sector_id: any) => {
  const response = await axios.get('/salaries/latest-openings', {
    params: {
      sector_id: sector_id,
      country_id: country_id,
      num_items: '3',
    },
  });
  return response.data;
};

export const getSectors = async (
  searchKeywords?: string,
  data?: any,
  cancelTokenSource?: CancelTokenSource,
): Promise<Sector[]> => {
  const response = await axios.get('/sectors', {
    params: {
      keywords: searchKeywords,
      ...data,
    },
    cancelToken: cancelTokenSource?.token,
  });
  return response.data;
};

export const searchJobs = async (data?: any, cancelTokenSource?: CancelTokenSource): Promise<any> => {
  try {
    let response;
    if (data) {
      if ('value' in data) {
        response = await axios.get(`/jobs?${data.value}`, {
          cancelToken: cancelTokenSource?.token,
        });
      } else {
        response = await axios.get('/jobs', {
          params: data,
          cancelToken: cancelTokenSource?.token,
        });
      }
    }

    return response?.data;
  } catch (error) {
    console.log('error', error);
    return {data: [], faq: []};
  }
};
