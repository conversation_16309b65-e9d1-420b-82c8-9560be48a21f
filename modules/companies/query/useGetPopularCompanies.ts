import {useQuery} from 'react-query';
import {QUERY_GET_POPULAR_COMPANIES} from '../constants';
import {PopularCompanies} from '@/components/Company';
import {apiService} from '@/service/api';
import {CompanyApiRoutes} from '../constants/routes';
import {consoleLogger} from '@/utils/logger';
import {notification} from 'antd';

export const useGetPopularCompanies = () => {
  return useQuery(
    QUERY_GET_POPULAR_COMPANIES,
    async (): Promise<PopularCompanies[]> => {
      const response = await apiService.get(CompanyApiRoutes.POPULAR_COMPANIES);
      return response?.data;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
    },
  );
};
