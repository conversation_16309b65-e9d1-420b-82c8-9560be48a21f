import {useQuery} from 'react-query';
import {PopularCompanies} from '@/components/Company';
import {apiService} from '@/service/api';
import {CompanyApiRoutes} from '../constants/routes';
import {notification} from 'antd';
import {QUERY_GET_SINGLE_COMPANY_REVIEW} from '../constants/cacheKey';
import {Reviews} from './useGetCompanyDetail';

export interface SingleCompaniesReview {
  total_reviews: number;
  average_rating: number;
  five_star_reviews: number;
  four_star_reviews: number;
  three_star_reviews: number;
  two_star_reviews: number;
  one_star_reviews: number;
  reviews: Reviews[];
}

export const useGetSingleCompanyReviews = ({id, params, user_id}: {id: number; params?: any; user_id?: number}) => {
  return useQuery(
    [QUERY_GET_SINGLE_COMPANY_REVIEW, params],
    async (): Promise<SingleCompaniesReview> => {
      const response = await apiService.get(CompanyApiRoutes.singleCompanyReview, {
        params: {
          ...params,
          id: id,
          user_id: user_id,
        },
      });
      console.log(response, 'res');
      return response?.data;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
      enabled: !!id,
    },
  );
};
