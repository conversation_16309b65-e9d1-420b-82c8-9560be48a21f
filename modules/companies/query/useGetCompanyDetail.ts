import {useQuery} from 'react-query';
import {QUERY_GET_POPULAR_COMPANIES} from '../constants';
import {Company, Job} from '@/lib/types';
import {getAllJobs} from '@/lib/employeeapi';
import {getAllJobsSearch} from '@/lib/frontendapi';
import {apiService} from '@/service/api';
import {notification} from 'antd';
import {QUERY_GET_COMPANY_DETAILS} from '../constants/cacheKey';
import {useGetCompanyJobs} from './useGetCompanyJobs';

export interface Reviews {
  id: number;
  rating: string;
  review_title: string;
  review_description: string;
  helpful_yes_count: number;
  helpful_no_count: number;
  created_at: string;
  user: {
    id: number;
    current_position: string;
    country: string;
  };

  work_experience?: {
    job_title: string;
    country: string;
    city: string;
    start_date: string;
    end_date: string;
  };
  detail_review?: {
    work_environment: string;
    work_life_balance: string;
    career_growth: string;
    compensation_benefit: string;
    job_security: string;
  };
  helpful_yes_flag: boolean;
  helpful_no_flag: boolean;
  report_flag: boolean;
}

export interface CompanyDetail {
  company: Company;
  recent_reviews: Reviews[];
  claim_flag: boolean;
  follow_flag: boolean;
  jobs: Job[];
}

export const useGetCompanyDetails = ({slug, user_id}: {slug: string; user_id: number}) => {
  const {data: jobs} = useGetCompanyJobs({
    params: {
      company_id: slug ? parseInt(slug) : 0,
    },
  });
  return useQuery(
    [QUERY_GET_COMPANY_DETAILS, user_id],
    async (): Promise<CompanyDetail> => {
      const response = await apiService.get(`/company/${slug}`, {
        params: {
          user_id: user_id,
        },
      });
      return {
        ...(response?.data ?? []),
        overview: {
          jobs: jobs ?? [],
        },
        jobs: jobs ?? [],
      };
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
      enabled: !!slug,
    },
  );
};
