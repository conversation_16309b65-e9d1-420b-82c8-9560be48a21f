import {useQuery} from 'react-query';
import {Job} from '@/lib/types';
import {getAllJobsSearch} from '@/lib/frontendapi';
import {notification} from 'antd';
import {QUERY_GET_COMPANY_JOB} from '../constants/cacheKey';
import {apiService} from '@/service/api';
import {CompanyApiRoutes} from '../constants/routes';
import {useContext} from 'react';
import AuthContext from '@/Context/AuthContext';

export const useGetCompanyJobs = ({
  params,
}: {
  params: {
    company_id: number;
    sector?: number;
    location?: number;
  };
}) => {
  return useQuery(
    [QUERY_GET_COMPANY_JOB, params],
    async (): Promise<Job[]> => {
      const response = await apiService.get(CompanyApiRoutes.companyActiveJobs, {
        params: params,
      });
      return response?.data;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
      enabled: !!params.company_id,
    },
  );
};
