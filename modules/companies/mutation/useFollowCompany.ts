import {apiService} from '@/service/api';
import {useMutation, useQueryClient} from 'react-query';
import {CompanyApiRoutes} from '../constants/routes';
import {notification} from 'antd';
import {QUERY_GET_COMPANY_DETAILS} from '../constants/cacheKey';

export const useFollowCompany = () => {
  const queryClient = useQueryClient();
  const mutation = useMutation(
    async (props: {user_id: number; company_id: number}) => {
      const response = await apiService.post(CompanyApiRoutes.followCompany, props);
      return response?.data;
    },
    {
      onError: (error: any) => {
        console.error('Error fetching companies:', error);
        notification.error(error);
      },
      onSuccess() {
        queryClient.invalidateQueries(QUERY_GET_COMPANY_DETAILS);
      },
    },
  );

  return {
    ...mutation,
  };
};
