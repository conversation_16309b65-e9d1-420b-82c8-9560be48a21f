import {apiService} from '@/service/api';
import {useState} from 'react';
import {useMutation, useQuery} from 'react-query';
import {CompanyApiRoutes} from '../constants/routes';
import {notification} from 'antd';
import {QUERY_GET_SEARCH_LIST} from '../constants/cacheKey';
import {consoleLogger} from '@/utils/logger';

export interface SearchedCompanies {
  average_rating: null;
  company_logo: string;
  company_name: string;
  company_sector: string;
  id: number;
  total_reviews: number;
}

export const useGetSearchedCompanies = (props: {keyword?: string; type: 'view' | 'suggestion'}) => {
  console.log(props.keyword, 'ss');
  const mutation = useQuery(
    [QUERY_GET_SEARCH_LIST, props?.type, props?.keyword],
    async (): Promise<SearchedCompanies[] | string[] | null> => {
      const response = await apiService.get(CompanyApiRoutes.SearchedCompanies, {
        params: {
          query: props.keyword,
          type: props.type,
        },
      });
      consoleLogger(response);
      return response?.data;
    },
    {
      onError: (error: any) => {
        console.error('Error fetching companies:', error);
        notification.error(error);
      },
      enabled: !!props?.keyword,
    },
  );

  return {
    ...mutation,
  };
};
