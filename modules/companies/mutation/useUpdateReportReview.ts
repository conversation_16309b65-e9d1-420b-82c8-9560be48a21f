import {apiService} from '@/service/api';
import {useState} from 'react';
import {useMutation} from 'react-query';
import {CompanyApiRoutes} from '../constants/routes';
import {notification} from 'antd';

export interface SearchedCompanies {
  average_rating: null;
  company_logo: string;
  company_name: string;
  company_sector: string;
  id: number;
  total_reviews: number;
}

export const useUpdateReportReview = () => {
  const mutation = useMutation(
    async (props: {user_id: string; review_id: string; report_reason: string}) => {
      const response = await apiService.post(CompanyApiRoutes.ReportReview, props);

      return response?.data;
    },
    {
      onError: (error: any) => {
        console.error('Error fetching companies:', error);
        notification.error(error);
      },
    },
  );

  return {
    ...mutation,
  };
};
