import {apiService} from '@/service/api';
import {useMutation, useQueryClient} from 'react-query';
import {notification} from 'antd';
import {QUERY_GET_POPULAR_COMPANIES} from '../constants';
import {QUERY_GET_SINGLE_COMPANY_REVIEW} from '../constants/cacheKey';

export interface SearchedCompanies {
  average_rating: null;
  company_logo: string;
  company_name: string;
  company_sector: string;
  id: number;
  total_reviews: number;
}

export const useUpdateCompanyHelpful = () => {
  const queryClient = useQueryClient();
  const mutation = useMutation(
    async (props: {review_id: string; user_id: string; reviewType: 'yes' | 'no'}) => {
      const response = await apiService.post(`companyreview/helpful_${props.reviewType}`, {
        review_id: props.review_id,
        user_id: props.user_id,
      });
      return response?.data;
    },
    {
      onError: (error: any) => {
        console.error('Error fetching companies:', error);
        notification.error(error);
      },
      onSuccess: () => {
        notification.success({
          message: 'Review helpful status updated',
        });
        queryClient?.invalidateQueries(QUERY_GET_POPULAR_COMPANIES);
        queryClient?.invalidateQueries(QUERY_GET_SINGLE_COMPANY_REVIEW);
      },
    },
  );

  return {
    ...mutation,
  };
};
