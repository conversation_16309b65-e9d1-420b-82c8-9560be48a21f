import {apiService} from '@/service/api';
import {useState} from 'react';
import {useMutation} from 'react-query';
import {CompanyApiRoutes} from '../constants/routes';
import {notification} from 'antd';

export interface ReviewCreateProps {
  user_id: number;
  company_id: number;
  review_title: string;
  review_description: string;
  work_environment: string;
  work_life_balance: string;
  career_growth: string;
  compensation_benefit: string;
  job_security: string;
  job_title: string;
  country: string;
  city: string;
  start_date: string;
  end_date: string;
}

export const useWriteReviewMutation = () => {
  const mutation = useMutation(
    async (props: ReviewCreateProps) => {
      const response = await apiService.post(CompanyApiRoutes.writeReview, props);

      return response?.data;
    },
    {
      onError: (error: any) => {
        console.error('Error fetching companies:', error);
        notification.error(error);
      },
      onSuccess: () => {
        notification.success({
          message: 'Your Report Submitted SuccessFully',
        });
      },
    },
  );
  return {
    ...mutation,
  };
};
