import {useMutation} from 'react-query';
import {CompanyApiRoutes} from '../constants/routes';
import {apiService} from '@/service/api';
import {notification} from 'antd';

interface CompanyClaimProps {
  company_id: number;
  user_id: number;
  status: ClaimStatus;
}

export enum ClaimStatus {
  requested = 'requested',
  approved = 'approved',
  rejected = 'rejected',
}

interface CompanyClaimResponse {
  id: number;
  user_id: number;
  company_id: number;
  message: null;
  status: ClaimStatus;
  created_at: string;
  updated_at: string;
}

export const useClaimCompany = () => {
  const mutation = useMutation(
    async (props: CompanyClaimProps): Promise<CompanyClaimResponse> => {
      const response = await apiService.post(CompanyApiRoutes.companyClaim, props);

      return response?.data;
    },
    {
      onError: (error: any) => {
        console.error('Error fetching companies:', error);
        notification.error(error);
      },
    },
  );
  return {
    ...mutation,
  };
};
