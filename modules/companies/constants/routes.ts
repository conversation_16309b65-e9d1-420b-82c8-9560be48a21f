export class CompanyApiRoutes {
  public static get POPULAR_COMPANIES() {
    return '/companyprofileview/getpopularcompany';
  }
  public static get SearchedCompanies() {
    return '/companysearch/companies';
  }

  public static get ReportReview() {
    return '/companyreview/report_review';
  }

  public static get writeReview() {
    return '/companyreview/createcompanyreview';
  }

  public static get singleCompanyReview() {
    return '/companyreview/getsinglecompanyreview';
  }

  public static get followCompany() {
    return '/companyfollower/createcompanyfollower';
  }

  public static get companyClaim() {
    return `/company/claim-company`;
  }

  public static get deleteFollowCompany() {
    return '/companyfollower/deletecompanyfollower';
  }

  public static get companyActiveJobs() {
    return '/getalljobssearch?';
  }
}
