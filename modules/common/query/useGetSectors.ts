import {useQuery} from 'react-query';
import {notification} from 'antd';
import {Sector} from '@/lib/types';
import {QUERY_GET_SECTORS} from '../../companies/constants/cacheKey';
import {getAllSector} from '@/lib/adminapi';

export const useGetSectors = () => {
  return useQuery(
    [QUERY_GET_SECTORS],
    async (): Promise<Sector[]> => {
      const response = await getAllSector();
      return response?.sectors ?? [];
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
    },
  );
};
