import {useQuery} from 'react-query';
import {notification} from 'antd';
import {City} from '@/lib/types';
import {QUERY_GET_CITY_BY_COUNTRY, QUERY_GET_COUNTRIES} from '../../companies/constants/cacheKey';
import {getAllCitiesByCountryName} from '@/lib/frontendapi';

export const useGetCountryByCity = ({countryName}: {countryName: string}) => {
  return useQuery(
    [QUERY_GET_CITY_BY_COUNTRY, countryName],
    async (): Promise<City[]> => {
      const response = await getAllCitiesByCountryName(countryName);
      return response.data;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
      enabled: !!countryName,
    },
  );
};
