import {useQuery} from 'react-query';
import {QUERY_GET_POPULAR_COMPANIES} from '../../companies/constants';
import {notification} from 'antd';
import axios from 'axios';
import {getCountries} from '@/lib/ApiAdapter';
import {Country} from '@/lib/types';
import {QUERY_GET_COUNTRIES} from '../../companies/constants/cacheKey';

export const useGetCountries = () => {
  return useQuery(
    [QUERY_GET_COUNTRIES],
    async (): Promise<Country[]> => {
      const cancelTokenSource = axios.CancelToken.source();
      const response = await getCountries(undefined, undefined, cancelTokenSource);
      return response;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
    },
  );
};
