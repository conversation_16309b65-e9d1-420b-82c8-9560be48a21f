import {useQuery} from 'react-query';
import {QUERY_GET_JOB_LISTS} from '../constants';
import {searchJobs} from '@/lib/ApiAdapter';
import axios from 'axios';
import {Job, PaginationMeta} from '@/lib/types';

export const useGetJobLists = (props: any) => {
  const cancelTokenSource = axios.CancelToken.source();
  return useQuery(
    [QUERY_GET_JOB_LISTS, props],
    async (): Promise<{
      trending: any[];
      faq: any[];
      data: Job[];
      meta: PaginationMeta;
      favorites: number[];
    }> => {
      return await searchJobs(props, cancelTokenSource);
    },
  );
};
