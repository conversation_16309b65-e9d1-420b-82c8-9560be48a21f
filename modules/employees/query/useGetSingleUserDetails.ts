import {useQuery} from 'react-query';
import {getSingleUserDetails} from '@/lib/frontendapi';
import {QUERY_GET_SINGLE_USER_DETAILS} from '../constants';

export const useGetSingleUserDetails = (userId?: number) => {
  return useQuery(
    [QUERY_GET_SINGLE_USER_DETAILS, userId],
    async () => {
      return (await getSingleUserDetails(userId))?.user;
    },
    {
      enabled: !!userId,
    },
  );
};
