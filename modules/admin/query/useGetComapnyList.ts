import {useQuery} from 'react-query';
import {notification} from 'antd';
import axios from 'axios';
import {QUERY_GET_COMPANY_REVIEW_LIST} from '../constants';
import {apiService} from '@/service/api';
import {AdminApiRoutes} from '../constants/routes';

interface GetEmployerListProps {
  page: number;
  pageSize: number;
  searchName: string;
  star?: string;
  order?: 'asc' | 'desc';
  sort_by?: 'rating' | 'approval' | 'reported';
}

export interface AllCompaniesReview {
  company_id: number;
  company_name: string;
  total_reviews: number;
  average_rating: number;
  approved_reviews: string;
  reported_reviews: string;
  claim_status: 'approved' | 'unclaimed' | 'rejected';
  membership: 1 | 0;
  expire: string;
}

export const useGetCompanyReviewList = ({page, pageSize, searchName, order, sort_by, star}: GetEmployerListProps) => {
  return useQuery(
    [QUERY_GET_COMPANY_REVIEW_LIST, page, pageSize, searchName, order, sort_by, star],
    async (): Promise<{
      data: AllCompaniesReview[];
      meta: {
        total: number;
        per_page: number;
        current_page: number;
        last_page: number;
        from: number;
        to: number;
      };
    }> => {
      const response = await apiService.get(AdminApiRoutes.QUERY_GET_EMPLOYER_LIST, {
        params: {sort_by, page, company_name: searchName, pageSize: pageSize, star, order},
      });
      return response;
    },
    {
      onError: (error: any) => {
        notification.error(error);
        return notification.error(error);
      },
    },
  );
};
