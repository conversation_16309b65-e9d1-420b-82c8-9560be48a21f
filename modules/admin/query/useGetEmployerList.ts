import {useQuery} from 'react-query';
import {notification} from 'antd';
import axios from 'axios';
import {getAllEmployer} from '@/lib/adminapi';
import {QUERY_GET_EMPLOYER_LIST} from '../constants';
import {AllEmployer} from '@/components/Admin/Employers/Employers';

export type RatingValue = '4_and_above' | '3_and_above' | '2_and_above' | '1_and_above' | '5';

export type SortingValue = 'high_to_low' | 'low_to_high';

interface GetEmployerListProps {
  page: number;
  pageSize: number;
  searchName: string;
  account_type?: 'free' | 'pro';
  params?: any;
  rating?: RatingValue;
  job_post?: SortingValue;
  team_member?: SortingValue;
  status?: 'unclaimed' | 'requested';
}

export const useGetEmployerList = ({
  page,
  pageSize,
  searchName,
  account_type,
  params,
  job_post,
  rating,
  team_member,
  ...props
}: GetEmployerListProps, options?: any) => {
  return useQuery(
    [QUERY_GET_EMPLOYER_LIST, page, pageSize, searchName, account_type, params, rating, team_member, job_post],
    async (): Promise<AllEmployer> => {
      const cancelTokenSource = axios.CancelToken.source();
      const response = await getAllEmployer(
        {
          page,
          name: searchName,
          pageSize: pageSize,
          account_type: params?.account_type?.[0],
          rating,
          job_post,
          team_member,
          status: params?.status?.[0],
          location: params.location,
          activity: params.activity,
        },
        cancelTokenSource,
      );
      return response?.data;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
      ...options
    },
  );
};
