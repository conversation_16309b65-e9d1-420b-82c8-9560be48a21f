import {useQuery} from 'react-query';
import {notification} from 'antd';
import {QUERY_GET_CANDIDATE_LIST} from '../constants';
import {AllEmployer} from '@/components/Admin/Employers/Employers';
import {apiService} from '@/service/api';
import {AdminApiRoutes} from '../constants/routes';
import {PaginationMeta, User} from '@/lib/types';

interface GetCandidateList {
  config: any;
}

export const useGetCandidateList = ({config}: GetCandidateList) => {
  return useQuery(
    [QUERY_GET_CANDIDATE_LIST, config],
    async (): Promise<{
      data: User[];
      meta: PaginationMeta;
    }> => {
      // If position is in the params, ensure it's used to filter by current_position
      if (config.params.position) {
        config.params.current_position = config.params.position;
      }

      const response = await apiService.get(AdminApiRoutes.QUERY_GET_CANDIDATE_LIST, config);
      return response;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
    },
  );
};
