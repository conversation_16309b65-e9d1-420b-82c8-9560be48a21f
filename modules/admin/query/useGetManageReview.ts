import {useQuery} from 'react-query';
import {notification} from 'antd';
import {QUERY_GET_MANAGE_REVIEW} from '../constants';
import {apiService} from '@/service/api';
import {AdminApiRoutes} from '../constants/routes';

interface GetEmployerListProps {
  company_id: number;
  type?: 'all' | 'pending' | 'approved' | 'rejected' | 'reported';
  sort_by?: string;
  rating?: string;
  'location[]'?: string;
  'date_range[start]'?: string;
  'date_range[end]'?: string;
  'job_title[]'?: string;
}

export interface WorkExperience {
  job_title: string;
  country: string;
  city: string;
  start_date: string;
  end_date: string;
  company_name?: string;
}

export interface ManageReview {
  review_title: string;
  review_description: string;
  review_id: number;
  review_rating: string;
  detailed_review: {
    work_environment: string;
    work_life_balance: string;
    career_growth: string;
    compensation_benefit: string;
    job_security: string;
  };
  work_experience: WorkExperience;
  helpful_yes_count: number;
  helpful_no_count: number;
  report_reviews: {
    id: number;
    report_reason: string;
    user_id: string;
  }[];
  user_work_experiences: WorkExperience[];
  user_id?: number;
  user_name?: string;
  is_approve: 0 | 1 | 2;
  is_reported?: 0 | 1;
}

export interface ManageReviewResponse {
  company_avg_rating: number;
  total_reviews: null;
  company_sector: string;
  company_location: string;
  no_of_employees: string;
  reviews: ManageReview[];
}

export const useGetCompanyManageReview = (props: GetEmployerListProps) => {
  return useQuery(
    [QUERY_GET_MANAGE_REVIEW, props],
    async (): Promise<ManageReviewResponse> => {
      const response = await apiService.get(AdminApiRoutes.QUERY_GET_MANAGE_REVIEW, {
        params: props,
      });
      return response?.data;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
      enabled: !!props.company_id,
    },
  );
};
