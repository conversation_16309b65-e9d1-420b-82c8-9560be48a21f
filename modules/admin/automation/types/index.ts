import {Position} from '@xyflow/react';

export interface DropdownOption {
  label: string;
  value: string | number | number[] | string[];
}

export type WorkflowType = 'condition' | 'contact' | 'frequency' | 'templates';

export type LogicSelection = 'AND' | 'OR';

export interface ConditionData {
  label?: string;
  condition: {
    when: DropdownOption;
    operator: DropdownOption;
    becomes: DropdownOption;
    to: DropdownOption;
    from: DropdownOption;
  };
  type: WorkflowType;
  selectedLogic?: LogicSelection;
}

export interface ContactCondition {
  conditionType: DropdownOption;
  conditionKey: DropdownOption;
  conditionValue: DropdownOption;
}
export interface ContactData {
  label?: string;
  contact: {
    sendEmailTo: DropdownOption;
    applySegment: boolean;
    condition: ContactCondition[];
  };
  type: WorkflowType;
  selectedLogic?: LogicSelection;
}
export interface FrequencyData {
  frequency: {
    every: {
      periodValue: string;
      periodType: string;
    };
    time: {
      timeValue: string;
      timeType: string;
    };
    startTime: {
      dateTime: {
        date: string;
        time: string;
      };
      delayTime: {
        delayValue: string;
        delayType: string;
      };
      option: string;
    };
    oneTime: boolean;
  };
  type: WorkflowType;
  selectedLogic?: LogicSelection;
}

export interface TemplateData {
  templates: {
    subject: string;
    name: string;
    templateId: number;
  };
  type: WorkflowType;
  selectedLogic?: LogicSelection;
}

export interface NodesData {
  id?: string;
  type: WorkflowType;
  data: ConditionData | ContactData | FrequencyData | TemplateData;
  position: {
    x: number;
    y: number;
  };
  sourceHandle?: 't' | 'b' | 'l' | 'r';
  index?: number;
  measured: {
    width: number;
    height: number;
  };
  selected?: boolean;
  sourcePosition?: Position.Bottom | Position.Left | Position.Right | Position.Top;
}

export interface EdgeData {
  source?: string;
  target?: string;
  sourceHandle: 't' | 'b' | 'l' | 'r';
  targetHandle: 't' | 'b' | 'l' | 'r';
  type: WorkflowType;
  label?: string;
  id?: string;
}

export type Operators = '=' | 'IS NULL' | 'IS NOT NULL' | 'LIKE';

export type Connector = 'AND' | 'OR';

export interface ApiWorkflow {
  id?: string;
  name: string;
  instance: string;
  conditions: {
    datapoint_id: number;
    operator: Operators;
    value: string;
    connector: Connector;
  }[];
  execution_type: string;
  contacts: string;
  segments: [
    {
      datapoint_id: Number;
      operator: Operators;
      value: string;
      connector: Connector;
    },
  ];
  frequency_value: string;
  frequency_period: 'days';
  frequency_time: '10:30';
  AM_PM: 'AM';
  start_date: '2024-12-31';
  start_time: '09:00';
  email_sent: 'no';
  status: 1;
  delay_value: '15';
  email_template_id: '3';
  delay_unit: 'minutes';
}
[];

export interface WorkflowSendApiPayload {
  workflows: ApiWorkflow[];
}
