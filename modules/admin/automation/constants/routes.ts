export class AutomationRoutes {
  public static get QUERY_GET_EMAIL_STATISTICS(): string {
    return '/automation/overview-graph-data';
  }

  public static get QUERY_GET_AUTOMATION_DATA_POINTS(): string {
    return '/automation/datapoint-list';
  }

  public static get QUERY_GET_AUTOMATION_CONTACT_LISTS(): string {
    return '/automation/contact-list';
  }

  public static get QUERY_GET_AUTOMATION_TEMPLATES(): string {
    return '/automation/email-templates';
  }

  public static get QUERY_GET_WORKFLOW_LIST(): string {
    return '/automation/workflow-list';
  }

  public static get QUERY_GET_WORKFLOW_BY_ID(): string {
    return '/automation/workflow';
  }

  public static get QUERY_GET_INSTANCE_LIST(): string {
    return '/automation/instance-list';
  }

  public static get MUTATION_CREATE_WORKFLOW(): string {
    return '/automation/workflow-save';
  }

  public static get MUTATION_CREATE_TEMPLATE_LIST(): string {
    return '/automation/email-templates';
  }

  public static get QUERY_EXPORT_CONTACTS(): string {
    return '/automation/export-contacts';
  }

  public static get MUTATION_DELETE_WORKFLOW(): string {
    return '/automation/workflow';
  }

  public static get MUTATION_UPDATE_WORKFLOW(): string {
    return '/automation/workflow';
  }

  public static get MUTATION_UPDATE_WORKFLOW_STATUS(): string {
    return '/automation/workflow';
  }
}
