import {useGetDataPointOptions} from '@/hooks/useGetDataTypeOptions';
import {WorkFLowByIdResponse, WorkflowDetailResponse} from '../query/useGetWorkflowById';
import {ConditionData, ContactCondition, EdgeData, NodesData, WorkflowType} from '../types';
import {DataPointType} from '../query/useGetDataPoints';
import {getAllOperatorOptions} from '@/utils/getAllOperatorOptions';
import {useGetTemplatesList} from '../query/useGetTemplates';
import {TemplateDetails} from '../query/useGetTemplatesById';
import {Position} from '@xyflow/react';
import {useConvertConditionValueToLabel} from '@/hooks/useConvertConditionValueTolable';

interface ConvertWorkflowToNodeProps {
  dataPoint?: {
    label: DataPointType;
    value: number;
  }[];
  workflow?: WorkflowDetailResponse;
  templates?: TemplateDetails[];
}

export type SourceHandlePosition = 't' | 'r' | 'b' | 'l';

export const continuousCondition = (continuousType: WorkflowType, nodes: NodesData[]) => {
  const continuousNodes = [];

  for (let i = nodes.length - 1; i >= 0; i--) {
    if (nodes[i].type === continuousType) {
      continuousNodes.push(nodes[i]);
    }
  }

  return continuousNodes;
};

const convertWorkflowToNodes = ({workflow, dataPoint, templates}: ConvertWorkflowToNodeProps) => {
  const operator = getAllOperatorOptions();
  const currentTemplates = templates?.find(template => template.id === workflow?.email_template_id);

  let currentX = 0;
  let currentY = 0;

  const conditions = workflow?.conditions?.reduce((acc, condition, index) => {
    const currentDataPoint = dataPoint?.find(dp => dp?.value.toString() === condition.datapoint_id.toString());
    const operatorValue = operator?.find(op => op.value === condition.operator);

    acc.push({
      type: 'condition',
      position: {
        x: currentX + index * 300,
        y: currentY,
      },
      measured: {
        width: 212,
        height: 109,
      },
      sourceHandle: index === workflow?.conditions?.length - 1 ? 'b' : 'r',
      data: {
        condition: {
          when: {
            label: currentDataPoint?.label ?? '',
            value: currentDataPoint?.value.toString() ?? '',
          },
          becomes: {
            label: condition?.value ?? '',
            value: condition?.value,
          },
          operator: {
            label: operatorValue?.label ?? '',
            value: operatorValue?.value ?? '',
          },
          from: {
            label: '',
            value: '',
          },
          to: {
            label: '',
            value: '',
          },
        },
        type: 'condition',
      },
      sourcePosition: index === workflow?.conditions?.length - 1 ? Position.Bottom : Position.Right,
    });

    return acc;
  }, [] as NodesData[]);

  currentY += 200;

  const contacts: NodesData = {
    type: 'contact',
    data: {
      contact: {
        sendEmailTo: {
          label: workflow?.contacts === '1' ? 'Candidate' : workflow?.contacts === '2' ? 'Employer' : '',
          value: workflow?.contacts ?? '',
        },
        applySegment: workflow?.segments && workflow?.segments?.length > 0 ? true : false,
        condition:
          workflow?.segments?.reduce((acc, segment) => {
            const currentDataPoint = dataPoint?.find(dp => dp?.value.toString() === segment.datapoint_id.toString());
            const operatorValue = operator?.find(op => op.value === segment.operator);

            acc.push({
              conditionType: {
                label: operatorValue?.label ?? '',
                value: operatorValue?.value ?? '',
              },
              conditionKey: {
                label: currentDataPoint?.label ?? '',
                value: currentDataPoint?.value.toString() ?? '',
              },
              conditionValue: {
                label: segment?.value,
                value: segment?.value,
              },
            });
            return acc;
          }, [] as ContactCondition[]) ?? [],
      },
      type: 'contact',
    },
    position: {
      x: conditions
        ? conditions?.reduce((acc, condition) => acc + condition.position.x, 0) / conditions.length
        : currentX,
      y: currentY,
    },
    measured: {
      width: 212,
      height: 109,
    },
    sourceHandle: 'b',
  };

  currentY += 200;

  const frequency: NodesData = {
    data: {
      frequency: {
        every: {
          periodType: workflow?.frequency_period ?? '',
          periodValue: workflow?.frequency_value ?? '',
        },
        startTime: {
          delayTime: {
            delayType: workflow?.delay_unit ?? '',
            delayValue: workflow?.delay_value ?? '',
          },
          option: workflow?.execution_type ?? '',
          dateTime: {
            date: '',
            time: '',
          },
        },
        time: {
          timeType: workflow?.AM_PM ?? '',
          timeValue: workflow?.frequency_time ?? '',
        },
        oneTime: workflow?.frequency_one_time === 'one_time',
      },
      type: 'frequency',
    },
    type: 'frequency',
    measured: {
      width: 212,
      height: 109,
    },
    sourceHandle: 'b',
    position: {
      x: contacts.position.x,
      y: currentY,
    },
  };

  currentY += 200;

  const templateData: NodesData | undefined = currentTemplates
    ? {
        data: {
          templates: {
            templateId: currentTemplates?.id,
            name: currentTemplates?.template_name,
            subject: currentTemplates?.subject,
          },
          type: 'templates',
        },
        measured: {
          width: 212,
          height: 109,
        },
        position: {
          x: frequency.position.x,
          y: currentY,
        },
        sourceHandle: 'r',
        type: 'templates',
      }
    : undefined;

  return [...(conditions ?? []), contacts, , frequency, templateData].filter(Boolean) as NodesData[];
};

const convertWorkflowToEdges = ({workflow, nodes}: {workflow?: WorkflowDetailResponse; nodes: NodesData[]}) => {
  const edges: EdgeData[] = [];

  const addEdge = ({
    sourceId,
    targetId,
    sourceHandle,
    targetHandle,
    label,
    type,
  }: {
    sourceId?: string;
    targetId?: string;
    sourceHandle: SourceHandlePosition;
    targetHandle: SourceHandlePosition;
    label?: string;
    type: WorkflowType;
  }) => {
    edges.push({
      id: `e${sourceId}-${targetId}`,
      source: sourceId,
      target: targetId,
      sourceHandle,
      targetHandle,
      type: type,
      label: label || '',
    });
  };

  const nodeMap = new Map(nodes.map(node => [node.type, node]));

  if (workflow?.conditions?.length) {
    workflow.conditions.forEach((condition, index) => {
      const sourceNode = nodes.find(
        node =>
          node.type === 'condition' && (node.data as ConditionData).condition.when.value === condition.datapoint_id,
      );
      const targetNode = nodes.find(
        node =>
          node.type === 'condition' && (node.data as ConditionData).condition.when.value === condition.datapoint_id,
      );

      if (sourceNode && targetNode) {
        const label = index === 0 ? 'AND' : 'OR';
        addEdge({
          sourceId: sourceNode.id,
          targetId: targetNode.id,
          sourceHandle: 'r',
          targetHandle: 'l',
          label,
          type: 'condition',
        });
      }
    });
  }

  const lastConditionNode = nodes.find(
    node =>
      node.type === 'condition' &&
      (node.data as ConditionData)?.condition.when.value ===
        workflow?.conditions[workflow.conditions.length - 1]?.datapoint_id,
  );
  const contactNode = nodeMap.get('contact');

  if (lastConditionNode && contactNode) {
    addEdge({
      sourceId: lastConditionNode.id,
      targetId: contactNode.id,
      sourceHandle: 'b',
      targetHandle: 't',
      type: 'condition',
    });
  }

  const frequencyNode = nodeMap.get('frequency');
  if (contactNode && frequencyNode) {
    addEdge({
      sourceId: contactNode.id,
      targetId: frequencyNode.id,
      sourceHandle: 'b',
      targetHandle: 't',
      type: 'contact',
    });
  }

  const templateNode = nodeMap.get('templates');
  if (frequencyNode && templateNode) {
    addEdge({
      sourceId: frequencyNode.id,
      targetId: templateNode.id,
      sourceHandle: 'b',
      targetHandle: 't',
      type: 'frequency',
    });
  }

  return edges;
};

export const useConvertApiToWorkflow = ({workflow}: WorkFLowByIdResponse) => {
  const {data} = useGetDataPointOptions();
  const {data: templates} = useGetTemplatesList({
    template_name: '',
  });

  const {convertCondition} = useConvertConditionValueToLabel();

  const mappedWorkflow = {
    ...workflow,
    conditions: convertCondition(workflow?.conditions ?? []),
  };

  const dataPoint = data?.dataPoints;

  const processWorkflowNodesAndEdges = (workflow: WorkflowDetailResponse | undefined, offsetX = 0, offsetY = 0) => {
    if (!workflow) return {nodes: [], edges: []};

    const nodes = convertWorkflowToNodes({
      workflow,
      dataPoint,
      templates,
    }).map(node => ({
      ...node,
      position: {
        x: node.position.x + offsetX,
        y: node.position.y + offsetY,
      },
    }));

    const edges = convertWorkflowToEdges({
      workflow,
      nodes,
    });

    const childResults = workflow.children?.map((child, index) => {
      const childOffsetX = offsetX + 300 * (index + 1);
      const childOffsetY = offsetY + 600;

      const childEl = {
        ...child,
        conditions: convertCondition(child.conditions ?? []),
      };

      return processWorkflowNodesAndEdges(childEl, childOffsetX, childOffsetY);
    });

    const childNodes: NodesData[] = childResults?.flatMap(result => result.nodes) ?? [];
    const childEdges: EdgeData[] = childResults?.flatMap(result => result.edges) ?? [];

    return {
      nodes: [...nodes, ...childNodes],
      edges: [...edges, ...childEdges],
    };
  };

  const {nodes, edges} = processWorkflowNodesAndEdges(mappedWorkflow as WorkflowDetailResponse);

  const uniqueNodes = nodes.map((node, index) => ({
    ...node,
    index,
    id: (index + 1).toString(),
  }));

  const continuousConditionNodes = continuousCondition('condition', uniqueNodes);

  const extraEdges = continuousConditionNodes.reduce((acc, node, index) => {
    if (index === continuousConditionNodes.length - 1) return acc;

    acc.push({
      id: `e${index + 1}-${uniqueNodes[continuousConditionNodes.length].id}`,
      source: (index + 1).toString(),
      target: uniqueNodes[continuousConditionNodes.length].id,
      sourceHandle: 'b',
      targetHandle: 't',
      type: 'condition',
    });

    return acc;
  }, [] as EdgeData[]);

  const uniqueEdges = edges
    .map((edge, index) => ({
      ...edge,
      source: index.toString(),
      target: (index + 1).toString(),
      id: `e${index}-${index + 1}`,
    }))
    .concat((extraEdges.length > 1 ? extraEdges : []) as any[]);

  return {nodes: uniqueNodes, edges: uniqueEdges};
};
