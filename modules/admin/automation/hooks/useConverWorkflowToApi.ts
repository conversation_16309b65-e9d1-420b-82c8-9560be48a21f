import {
  ApiWorkflow,
  ConditionData,
  ContactCondition,
  ContactData,
  FrequencyData,
  NodesData,
  TemplateData,
  WorkflowSendApiPayload,
} from '../types';

interface BasicInfo {
  title: string;
  instance: string;
}

interface ConvertWorkflowToApiProps {
  nodes: NodesData[];
  basicInfo: BasicInfo;
  id?: number;
}

function createWorkflows({
  basicInfo,
  nodes,
  id,
}: {
  nodes: NodesData[];
  basicInfo: BasicInfo;
  id?: number;
}): ApiWorkflow[] {
  const workflows: ApiWorkflow[] = [];

  const extractData = (node: NodesData): any => {
    switch (node?.type) {
      case 'condition':
        const conditionData = (node.data as ConditionData)?.condition;
        return {
          datapoint_id: conditionData?.when?.value,
          operator: conditionData?.operator?.value,
          value:
            conditionData?.becomes?.value ||
            `${conditionData?.from?.value} ${conditionData.from && conditionData.to && ','} ${conditionData?.to
              ?.value}`,
          connector: 'AND',
        };
      case 'contact':
        const contactData = (node.data as ContactData).contact;
        return {
          datapoint_id: contactData.condition,
          operator: 'IS NULL',
          value: null,
          connector: 'AND',
        };
      case 'frequency':
        const frequencyData = (node.data as FrequencyData).frequency;
        return {
          frequency_value: frequencyData.oneTime ? '' : frequencyData.every.periodValue,
          frequency_period: frequencyData.oneTime ? '' : frequencyData.every.periodType,
          frequency_time: frequencyData.time.timeValue,
          AM_PM: frequencyData.time.timeType,
          start_date: frequencyData.startTime.dateTime.date,
          start_time: frequencyData.startTime.dateTime.time,
          delay_value: frequencyData.startTime.delayTime.delayValue,
          delay_unit: frequencyData.startTime.delayTime.delayType,
          execution_type: frequencyData.startTime.option,
          frequency_one_time: frequencyData.oneTime ? 'one_time' : '',
        };
      case 'templates':
        return {
          email_template_id: (node.data as TemplateData).templates.templateId.toString(),
        };
      default:
        return null;
    }
  };

  let currentWorkflow: ApiWorkflow | null = null;

  const createNewWorkflow = (): any => ({
    id: id,
    name: basicInfo.title || `Workflow ${workflows.length + 1}`,
    instance: basicInfo.instance || `Instance ${workflows.length + 1}`,
    conditions: [],
    execution_type: null,
    contacts: null,
    segments: [],
    frequency_value: null,
    frequency_period: null,
    frequency_time: null,
    AM_PM: null,
    start_date: null,
    start_time: null,
    email_sent: 'yes',
    status: 1,
    delay_value: null,
    email_template_id: extractData(nodes.find(node => node.type === 'templates') as any)?.email_template_id,
    delay_unit: null,
  });

  nodes.forEach(node => {
    if (!currentWorkflow) {
      currentWorkflow = createNewWorkflow();
    }

    const extracted = extractData(node);
    if (!extracted) return;

    if (node.type === 'templates') {
      if (currentWorkflow) {
        workflows.push(currentWorkflow);
      }
      currentWorkflow = createNewWorkflow();
    } else if (currentWorkflow) {
      switch (node.type) {
        case 'condition':
          currentWorkflow.conditions = [...currentWorkflow.conditions, extracted];
          break;
        case 'contact':
          currentWorkflow.segments = (node.data as ContactData).contact.condition.map(
            (condition: ContactCondition) => ({
              datapoint_id: condition.conditionKey.value,
              operator: condition.conditionType.value,
              value: condition.conditionValue?.value,
              connector: 'AND',
            }),
          ) as any;
          currentWorkflow.contacts = (node.data as ContactData).contact.sendEmailTo.value.toString();
          break;
        case 'frequency':
          Object.assign(currentWorkflow, extracted);
          break;
      }
    }
  });

  if (currentWorkflow && (currentWorkflow as ApiWorkflow).conditions.length > 0) {
    workflows.push(currentWorkflow);
  }

  return workflows;
}

export const useConvertWorkflowToApi = ({nodes, basicInfo, id}: ConvertWorkflowToApiProps): WorkflowSendApiPayload => {
  const output = createWorkflows({nodes, basicInfo, id});

  return {
    workflows: output,
  };
};
