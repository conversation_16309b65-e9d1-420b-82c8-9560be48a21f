import {create, useStore} from 'zustand';
import {persist} from 'zustand/middleware';
import {WorkflowSectionStore} from '../type';
import {Edge} from '@xyflow/react';
import {SetStateAction} from 'react';
import {NodesData} from '../../types';

const initialBasicInfo = {
  title: '',
  instance: '',
};

type Node = NodesData;

const initialNodes: Node[] = [];
const initialEdges: Edge[] = [];

const workflowStore = create<WorkflowSectionStore>()(
  persist(
    set => ({
      basicInfo: initialBasicInfo,
      storeNodes: initialNodes,
      storeEdges: initialEdges,

      setBasicInfo: (basicInfo: WorkflowSectionStore['basicInfo']) => {
        set(state => ({
          ...state,
          basicInfo: {
            ...state.basicInfo,
            ...basicInfo,
          },
        }));
      },
      setStoreNodes: (storeNodes: SetStateAction<Node[]>) => {
        set(state => ({
          ...state,
          storeNodes: typeof storeNodes === 'function' ? storeNodes(state.storeNodes) : storeNodes,
        }));
      },

      setStoreEdges: (storeEdges: SetStateAction<Edge[]>) => {
        set(state => ({
          ...state,
          storeEdges: typeof storeEdges === 'function' ? storeEdges(state.storeEdges) : storeEdges,
        }));
      },
      reset: async () => {
        set({
          basicInfo: initialBasicInfo,
          storeNodes: initialNodes,
        });
      },
    }),
    {
      name: 'create-workflow-store',
    },
  ),
);

function useWorkflowStore(): WorkflowSectionStore;
function useWorkflowStore<T>(selector: (state: WorkflowSectionStore) => T): T;
function useWorkflowStore<T>(selector?: (state: WorkflowSectionStore) => T) {
  return useStore(workflowStore, selector!);
}

export {useWorkflowStore};
