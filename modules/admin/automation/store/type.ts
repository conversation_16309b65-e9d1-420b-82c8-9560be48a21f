import {Edge} from '@xyflow/react';
import {SetStateAction} from 'react';
import {NodesData} from '../types';

type Node = NodesData;

export interface WorkflowSectionStore {
  basicInfo: {
    title: string;
    instance: string;
  };

  storeNodes: Node[];
  storeEdges: Edge[];
  reset: () => void;

  setBasicInfo: (basicInfo: WorkflowSectionStore['basicInfo']) => void;
  setStoreNodes: (value: SetStateAction<Node[]>) => void;
  setStoreEdges: (value: SetStateAction<Edge[]>) => void;
}
