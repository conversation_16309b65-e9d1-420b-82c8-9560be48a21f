import {useMutation} from 'react-query';
import {apiService} from '@/service/api';
import {AutomationRoutes} from '../constants/routes';
import {QUERY_GET_AUTOMATION_CONTACT_LISTS} from '../constants';

export type ContactExportType = 'candidates' | 'employers' | 'all';

interface AutomationContactsExportProps {
  contact_type: ContactExportType;
  location: number | null;
}

export const useExportContacts = () => {
  return useMutation([QUERY_GET_AUTOMATION_CONTACT_LISTS], async (props: AutomationContactsExportProps) => {
    const response = await apiService.get(AutomationRoutes.QUERY_EXPORT_CONTACTS, {
      params: props,
    });
    return response;
  });
};
