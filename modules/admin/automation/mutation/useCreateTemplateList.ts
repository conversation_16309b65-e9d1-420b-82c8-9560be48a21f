import {apiService} from '@/service/api';
import {notification} from 'antd';
import {useMutation, useQueryClient} from 'react-query';
import {AutomationRoutes} from '../constants/routes';
import {QUERY_GET_AUTOMATION_TEMPLATES} from '../constants';

export interface TemplatePayloads {
  template_name: string;
  template_html: string;
  template_json: string;
  status: number;
  bookmark: number;
  subject: string;
}

interface CreateTemplateListProps {
  props: TemplatePayloads;
}

export const useCreateTemplateList = () => {
  const queryClient = useQueryClient();
  const mutation = useMutation(
    async ({props}: CreateTemplateListProps) => {
      const response = await apiService.post(AutomationRoutes.MUTATION_CREATE_TEMPLATE_LIST, props);
      return response?.data;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
      onSuccess: () => {
        queryClient.invalidateQueries([QUERY_GET_AUTOMATION_TEMPLATES]);
      },
    },
  );
  return {
    ...mutation,
  };
};
