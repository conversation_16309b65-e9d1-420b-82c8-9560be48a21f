import {ClaimStatus} from '@/modules/companies/mutation/useClaimCompany';
import {apiService} from '@/service/api';
import {notification} from 'antd';
import {useMutation, useQueryClient} from 'react-query';
import {AutomationRoutes} from '../constants/routes';
import {QUERY_GET_AUTOMATION_TEMPLATES} from '../constants';
import {TemplatePayloads} from './useCreateTemplateList';

interface UpdateTemplateListProps {
  props: TemplatePayloads;
  id: number;
}

export const useUpdateTemplateList = () => {
  const queryClient = useQueryClient();
  const mutation = useMutation(
    async ({props, id}: UpdateTemplateListProps) => {
      const response = await apiService.put(`${AutomationRoutes.MUTATION_CREATE_TEMPLATE_LIST}/${id}`, props);
      return response?.data;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
      onSuccess: () => {
        queryClient.invalidateQueries([QUERY_GET_AUTOMATION_TEMPLATES]);
      },
    },
  );
  return {
    ...mutation,
  };
};
