import {apiService} from '@/service/api';
import {notification} from 'antd';
import {useMutation, useQueryClient} from 'react-query';
import {AutomationRoutes} from '../constants/routes';
import {QUERY_GET_WORKFLOW_LIST} from '../constants';

export const useUpdateWorkflowStatus = () => {
  const queryClient = useQueryClient();
  const mutation = useMutation(
    async ({id, status}: {id: number; status: number}) => {
      const response = await apiService.patch(
        `${AutomationRoutes.MUTATION_UPDATE_WORKFLOW_STATUS}/${id}/status?status=${status}`,
        {},
      );
      return response?.data;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
      onSuccess: () => {
        queryClient.invalidateQueries([QUERY_GET_WORKFLOW_LIST]);
      },
    },
  );
  return {
    ...mutation,
  };
};
