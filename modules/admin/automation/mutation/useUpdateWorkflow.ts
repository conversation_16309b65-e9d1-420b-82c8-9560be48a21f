import {apiService} from '@/service/api';
import {notification} from 'antd';
import {useMutation} from 'react-query';
import {AutomationRoutes} from '../constants/routes';
import {WorkflowPayload} from './useCreateWorkflow';

interface UpdateWorkflowProps {
  workflows: WorkflowPayload[];
  id: number;
}

export const useUpdateWorkflows = () => {
  const mutation = useMutation(
    async ({id, workflows}: UpdateWorkflowProps) => {
      const response = await apiService.put(`${AutomationRoutes.MUTATION_UPDATE_WORKFLOW}/${id}`, {
        workflows: workflows,
      });
      return response?.data;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
    },
  );
  return {
    ...mutation,
  };
};
