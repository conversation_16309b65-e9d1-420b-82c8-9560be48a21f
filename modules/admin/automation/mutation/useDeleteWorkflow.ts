import {apiService} from '@/service/api';
import {notification} from 'antd';
import {useMutation, useQueryClient} from 'react-query';
import {AutomationRoutes} from '../constants/routes';
import {QUERY_GET_WORKFLOW_LIST} from '../constants';

export const useDeleteWorkflows = () => {
  const queryClient = useQueryClient();
  const mutation = useMutation(
    async ({id}: {id: number}) => {
      const response = await apiService.delete(`${AutomationRoutes.MUTATION_DELETE_WORKFLOW}/${id}`);
      return response?.data;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
      onSuccess: () => {
        queryClient.invalidateQueries([QUERY_GET_WORKFLOW_LIST]);
      },
    },
  );
  return {
    ...mutation,
  };
};
