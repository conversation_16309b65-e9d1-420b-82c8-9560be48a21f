import {apiService} from '@/service/api';
import {notification} from 'antd';
import {useMutation, useQueryClient} from 'react-query';
import {AutomationRoutes} from '../constants/routes';
import {WorkflowConditions, WorkflowSegments} from '../query/useGetWorkflowById';
import {QUERY_GET_WORKFLOW_LIST} from '../constants';

export interface WorkflowPayload {
  name: string;
  instance?: string;
  conditions: WorkflowConditions[];
  execution_type: string;
  contacts: string;
  segments: WorkflowSegments[];
  frequency_value: string;
  frequency_period: string;
  frequency_time: string;
  AM_PM: string;
  start_date?: string;
  start_time?: string;
  email_sent?: string;
  status: number;
  delay_value: string;
  email_template_id: string;
  delay_unit: string;
}

export const useCreateWorkflow = () => {
  const queryClient = useQueryClient();
  const mutation = useMutation(
    async (props: {workflows: WorkflowPayload[]}) => {
      const response = await apiService.post(AutomationRoutes.MUTATION_CREATE_WORKFLOW, props);
      return response?.data;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
      onSuccess: () => {
        queryClient.invalidateQueries([QUERY_GET_WORKFLOW_LIST]);
      },
    },
  );
  return {
    ...mutation,
  };
};
