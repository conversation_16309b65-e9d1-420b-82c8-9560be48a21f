import {ClaimStatus} from '@/modules/companies/mutation/useClaimCompany';
import {apiService} from '@/service/api';
import {notification} from 'antd';
import {useMutation, useQueryClient} from 'react-query';
import {AutomationRoutes} from '../constants/routes';
import {QUERY_GET_AUTOMATION_TEMPLATES} from '../constants';

interface UpdateTemplateListProps {
  id: number;
}

export const useDeleteTemplateList = () => {
  const queryClient = useQueryClient();
  const mutation = useMutation(
    async ({id}: UpdateTemplateListProps) => {
      const response = await apiService.delete(`${AutomationRoutes.MUTATION_CREATE_TEMPLATE_LIST}/${id}`);
      return response?.data;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
      onSuccess: () => {
        queryClient.invalidateQueries([QUERY_GET_AUTOMATION_TEMPLATES]);
      },
    },
  );
  return {
    ...mutation,
  };
};
