import {useQuery} from 'react-query';
import {AutomationRoutes} from '../constants/routes';
import {QUERY_GET_WORKFLOW_LIST} from '../constants';
import {apiService} from '@/service/api';

export interface WorkFLowList {
  id: number;
  name: string;
  instance: null;
  parent_id: null;
  AND_OR: null;
  condition_data_point: number;
  condition_status: number;
  contacts: string;
  segment: null;
  segment_condition: null;
  segment_name: null;
  segment_value: null;
  frequency_value: string;
  frequency_period: string;
  frequency_time: string;
  AM_PM: string;
  start_date: null;
  start_time: null;
  email_sent: null;
  status: number;
  created_at: string;
  updated_at: string;
}

export const useGetWorkflowList = () => {
  return useQuery([QUERY_GET_WORKFLOW_LIST], async (): Promise<WorkFLowList[]> => {
    const response = await apiService.get(AutomationRoutes.QUERY_GET_WORKFLOW_LIST);
    return response?.data;
  });
};
