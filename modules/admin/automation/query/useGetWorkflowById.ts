import {useQuery} from 'react-query';
import {AutomationRoutes} from '../constants/routes';
import {QUERY_GET_WORKFLOW_BY_ID} from '../constants';
import {apiService} from '@/service/api';

export interface WorkFLowByIdResponse {
  workflow?: WorkflowDetailResponse;
}

export interface WorkflowDetailResponse {
  id: number;
  name: string;
  contacts: string;
  frequency_value: string;
  frequency_period: string;
  frequency_time: string;
  AM_PM: string;
  execution_type: string;
  delay_value: string;
  delay_unit: string;
  email_template_id: number;
  segments: WorkflowSegments[];
  conditions: WorkflowConditions[];
  children: WorkflowDetailResponse[];
  frequency_one_time: string;
}
export interface WorkflowSegments {
  datapoint_id: string;
  operator: string;
  value: string;
}

export type WorkflowConditions = WorkflowSegments;

export const useGetWorkflowById = ({id}: {id: number}) => {
  return useQuery(
    [QUERY_GET_WORKFLOW_BY_ID, id],
    async (): Promise<WorkFLowByIdResponse> => {
      const response = await apiService.get(`${AutomationRoutes.QUERY_GET_WORKFLOW_BY_ID}/${id}`);
      return response;
    },
    {
      enabled: !!id,
    },
  );
};
