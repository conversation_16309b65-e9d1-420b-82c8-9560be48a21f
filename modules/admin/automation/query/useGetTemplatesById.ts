import {useQuery} from 'react-query';
import {QUERY_GET_TEMPLATES_BY_ID} from '../constants';
import {apiService} from '@/service/api';
import {AutomationRoutes} from '../constants/routes';
import {notification} from 'antd';

export interface TemplateDetails {
  id: 1;
  template_name: string;
  template_html: string;
  template_json: any;
  status: number;
  bookmark: number;
  created_at: string;
  updated_at: string;
  subject: string;
}

export const useGetTemplatesById = ({id}: {id?: number}) => {
  return useQuery(
    [QUERY_GET_TEMPLATES_BY_ID, id],
    async (): Promise<TemplateDetails> => {
      try {
        const response = await apiService.get(`${AutomationRoutes.QUERY_GET_AUTOMATION_TEMPLATES}/${id}`);
        return response?.data;
      } catch (error: any) {
        notification.error(error);
        return error;
      }
    },
    {
      enabled: !!id,
      keepPreviousData: false,
    },
  );
};
