import {useQuery} from 'react-query';
import {QUERY_GET_AUTOMATION_TEMPLATES} from '../constants';
import {apiService} from '@/service/api';
import {AutomationRoutes} from '../constants/routes';
import {TemplateDetails} from './useGetTemplatesById';
import {notification} from 'antd';

interface TemplateListProps {
  template_name: string;
}

export const useGetTemplatesList = ({template_name}: TemplateListProps) => {
  return useQuery([QUERY_GET_AUTOMATION_TEMPLATES, template_name], async (): Promise<TemplateDetails[]> => {
    try {
      const response = await apiService.get(AutomationRoutes.QUERY_GET_AUTOMATION_TEMPLATES, {
        params: {
          template_name: template_name,
        },
      });
      return response?.data;
    } catch (error: any) {
      notification.error(error);
      return error;
    }
  });
};
