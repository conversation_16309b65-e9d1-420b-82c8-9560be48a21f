import {useQuery} from 'react-query';
import {notification} from 'antd';
import {apiService} from '@/service/api';
import {QUERY_GET_EMAIL_STATISTICS} from '../constants';
import {AutomationRoutes} from '../constants/routes';

export interface EmailStatisticData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
    borderColor?: string;
    backgroundColor?: string;
  }>;
}

export interface OverviewData {
  year: string;
  start_date: string;
  end_date: string;
  total_emails: number;
  delivered: {
    count: number;
    percentage: number;
  };
  opened: {
    count: number;
    percentage: number;
  };
  spam: {
    count: number;
    percentage: number;
  };
  graph_data: {
    date: string;
    opened: number;
    delivered: number;
    spam: number;
  }[];
  total_candidates: 0;
  total_employers: 0;
  total_active_workflows: 0;
  message: 'Statistics with date filter fetched successfully.';
}
interface EmailStatisticProps {
  year: number;
  start_date: string;
  end_date: string;
}

export const useGetOverview = ({end_date, start_date, year}: EmailStatisticProps) => {
  return useQuery([QUERY_GET_EMAIL_STATISTICS, start_date, end_date], async (): Promise<OverviewData> => {
    const response = await apiService.get(AutomationRoutes.QUERY_GET_EMAIL_STATISTICS, {
      params: {
        year,
        start_date,
        end_date,
      },
    });
    return response;
  });
};
