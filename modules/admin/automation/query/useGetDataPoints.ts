import {useInfiniteQuery} from 'react-query';
import {apiService} from '@/service/api';
import {AutomationRoutes} from '../constants/routes';
import {QUERY_GET_AUTOMATION_DATA_POINTS} from '../constants';

export type DataPointType =
  | 'name'
  | 'profile-image'
  | 'contact-number'
  | 'resume'
  | 'location'
  | 'gender'
  | 'social-linkedin'
  | 'skills'
  | 'profile-completion-percentage'
  | 'experience-level'
  | 'current-job-location'
  | 'current-job-role'
  | 'expected-salary-range'
  | 'languages-known'
  | 'last-login-date'
  | 'last-application-date'
  | 'application-status'
  | 'job-saved'
  | 'job-viewed'
  | 'notification-read'
  | 'activity-status'
  | 'registered-date'
  | 'job-title'
  | 'job-location'
  | 'job-type'
  | 'salary-range-max'
  | 'experience-level-required'
  | 'job-deadline'
  | 'job-status'
  | 'account-type'
  | 'subscription-expiry'
  | 'industry-type'
  | 'company-size'
  | 'company-location'
  | 'jobs-posted'
  | 'jobs-expired';

export type DataPointOperator = keyof typeof Operators;

export enum Operators {
  EQUAL = '=',
  NOT_EQUAL = '!=',
  GREATER_THAN = '>',
  GREATER_THAN_OR_EQUAL = '>=',
  LESS_THAN = '<',
  LESS_THAN_OR_EQUAL = '<=',
  BETWEEN = 'between',
}

export interface DataPointTableData {
  id: number;
  data_point_name: DataPointType;
  user_type: string;
  data_type: string;
  status: string;
  created_at: string;
  updated_at: string;
}

interface AutomationDataPointResponse {
  data: DataPointTableData[];
}

export const useGetDataPoints = ({
  search,
  selectedUserType,
  selectedDataType,
  per_page,
}: {
  search: string;
  selectedUserType?: number;
  selectedDataType?: string;
  per_page?: number;
}) => {
  return useInfiniteQuery(
    [QUERY_GET_AUTOMATION_DATA_POINTS, {search, selectedUserType, selectedDataType, per_page}],
    async ({pageParam = 1}) => {
      const response = await apiService.get(AutomationRoutes.QUERY_GET_AUTOMATION_DATA_POINTS, {
        params: {
          user_type: selectedUserType,
          data_type: selectedDataType,
          name: search,
          per_page: per_page,
          page: pageParam,
        },
      });

      return response.data;
    },
    {
      getNextPageParam: (lastPage, allPages) => {
        if (lastPage?.length === per_page) {
          return allPages.length + 1;
        }
        return undefined;
      },
    },
  );
};
