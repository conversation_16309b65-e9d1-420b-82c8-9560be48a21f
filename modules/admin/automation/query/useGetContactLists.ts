import {useInfiniteQuery, useQuery} from 'react-query';
import {notification} from 'antd';
import {apiService} from '@/service/api';
import {AutomationRoutes} from '../constants/routes';
import {QUERY_GET_AUTOMATION_CONTACT_LISTS} from '../constants';

export interface ContactTableData {
  email: string;
  name: string;
  location: string;
  created_at: string;
}

interface AutomationContactsResponse {
  data: ContactTableData[];
}

export type ContactType = 'candidates' | 'employers' | 'test';

interface AutomationContactsProps {
  per_page: number;
  name: string;
  type: ContactType;
  location: string;
  sort_by?: string;
  sort_order?: string;
}

export const useGetContactList = (props: AutomationContactsProps) => {
  const {name, type, location, sort_by, sort_order} = props;
  return useInfiniteQuery(
    [QUERY_GET_AUTOMATION_CONTACT_LISTS, {name, type, location, sort_by, sort_order}],
    async ({pageParam = 1}) => {
      const response = await apiService.get(AutomationRoutes.QUERY_GET_AUTOMATION_CONTACT_LISTS, {
        params: props,
      });
      return response?.data;
    },
    {
      getNextPageParam: (lastPage, allPages) => {
        if (lastPage?.data?.length === props.per_page) {
          return allPages.length + 1;
        }
        return undefined;
      },
      enabled: !!type,
    },
  );
};
