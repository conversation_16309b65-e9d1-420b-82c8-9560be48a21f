import {useQuery} from 'react-query';
import {QUERY_GET_INSTANCE_LIST} from '../constants';
import {apiService} from '@/service/api';
import {AutomationRoutes} from '../constants/routes';

export interface AutomationInstanceList {
  title: string;
  options: {
    label: string;
    value: string;
  }[];
}

export const useGetAutomationInstance = () => {
  return useQuery([QUERY_GET_INSTANCE_LIST], async (): Promise<AutomationInstanceList[]> => {
    try {
      const response = await apiService.get(AutomationRoutes.QUERY_GET_INSTANCE_LIST);
      return response;
    } catch (error) {
      return [
        {
          title: 'CANDIDATE INSTANCES',
          options: [
            {
              label: 'Profile Completion',
              value: 'Profile Completion',
            },
            {
              label: 'Reminders',
              value: 'Reminders',
            },
          ],
        },
        {
          title: 'EMPLOYER INSTANCES',
          options: [
            {
              label: 'Applicant Notification',
              value: 'Applicant Notification',
            },
            {
              label: 'Subscription Renewal',
              value: 'Subscription Renewal',
            },
          ],
        },
        {
          title: 'GENERAL INSTANCES',
          options: [
            {
              label: 'Invitations',
              value: 'Invitations',
            },
            {
              label: 'Promotional',
              value: 'Promotional',
            },
            {
              label: 'Announcements',
              value: 'Announcements',
            },
            {
              label: 'Feedback',
              value: 'Feedback',
            },
          ],
        },
        {
          title: 'PLATFORM INSTANCES',
          options: [
            {
              label: 'System/Platform Issues',
              value: 'System/Platform Issues',
            },
            {
              label: 'Service Downtime/Maintenance Updates',
              value: 'Service Downtime/Maintenance Updates',
            },
            {
              label: 'Policy/Term Updates',
              value: 'Policy/Term Updates',
            },
            {
              label: 'Expiring Listings Alert',
              value: 'Expiring Listings Alert',
            },
          ],
        },
        {
          title: 'CUSTOM INSTANCES',
          options: [
            {
              label: 'Specific Campaigns Workflow',
              value: 'Specific Campaigns Workflow',
            },
            {
              label: 'Referral Program Workflow',
              value: 'Referral Program Workflow',
            },
          ],
        },
      ];
    }
  });
};
