import {ClaimStatus} from '@/modules/companies/mutation/useClaimCompany';
import {apiService} from '@/service/api';
import {notification} from 'antd';
import {useMutation, useQueryClient} from 'react-query';
import {AdminApiRoutes} from '../constants/routes';
import {QUERY_GET_EMPLOYER_LIST, QUERY_GET_MANAGE_REVIEW} from '../constants';
import {QUERY_GET_COMPANIES} from '@/modules/companies/constants/cacheKey';

interface CompanyClaimRejectProps {
  action: 'approve' | 'reject';
  company_id: number;
  user_id: number;
}

export const useAcceptRejectClaimCompany = () => {
  const queryClient = useQueryClient();
  const mutation = useMutation(
    async (props: CompanyClaimRejectProps) => {
      const response = await apiService.post(AdminApiRoutes.MUTATION_CLAIM_COMPANY_ACTION, props);

      return response?.data;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
      onSuccess: () => {
        queryClient.invalidateQueries(QUERY_GET_EMPLOYER_LIST);
      },
    },
  );
  return {
    ...mutation,
  };
};
