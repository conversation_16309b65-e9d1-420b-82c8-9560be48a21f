import {ClaimStatus} from '@/modules/companies/mutation/useClaimCompany';
import {apiService} from '@/service/api';
import {notification} from 'antd';
import {useMutation, useQueryClient} from 'react-query';
import {AdminApiRoutes} from '../constants/routes';
import {QUERY_GET_EMPLOYER_LIST, QUERY_GET_MANAGE_REVIEW} from '../constants';

interface EmployerDeleteProps {
  usersId: number[];
}

export const useDeleteEmployer = () => {
  const queryClient = useQueryClient();
  const mutation = useMutation(
    async (props: EmployerDeleteProps) => {
      const response = await apiService.post(`${AdminApiRoutes.MUTATION_DELETE_EMPLOYER}`, props);
      return response?.data;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
      onSuccess: () => {
        queryClient.invalidateQueries(QUERY_GET_EMPLOYER_LIST);
      },
    },
  );
  return {
    ...mutation,
  };
};
