import {ClaimStatus} from '@/modules/companies/mutation/useClaimCompany';
import {apiService} from '@/service/api';
import {notification} from 'antd';
import {useMutation, useQueryClient} from 'react-query';
import {AdminApiRoutes} from '../constants/routes';
import {QUERY_GET_MANAGE_REVIEW} from '../constants';

interface CompanyClaimRejectProps {
  review_id: number;
}

export const useDeleteCompanyReview = () => {
  const queryClient = useQueryClient();
  const mutation = useMutation(
    async (props: CompanyClaimRejectProps) => {
      const response = await apiService.delete(`${AdminApiRoutes.MUTATION_DELETE_COMPANY_REVIEW}/${props.review_id}`);
      return response?.data;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
      onSuccess: () => {
        queryClient.invalidateQueries(QUERY_GET_MANAGE_REVIEW);
      },
    },
  );
  return {
    ...mutation,
  };
};
