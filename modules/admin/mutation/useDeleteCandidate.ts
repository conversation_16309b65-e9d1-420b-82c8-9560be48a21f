import {ClaimStatus} from '@/modules/companies/mutation/useClaimCompany';
import {apiService} from '@/service/api';
import {notification} from 'antd';
import {useMutation, useQueryClient} from 'react-query';
import {AdminApiRoutes} from '../constants/routes';
import {QUERY_GET_CANDIDATE_LIST} from '../constants';

export type ExportFormat = 'csv' | 'xls';

interface DeleteCandidateProps {
  usersId: number[];
}

export const useDeleteCandidate = () => {
  const queryClient = useQueryClient();
  const mutation = useMutation(
    async (props: DeleteCandidateProps) => {
      const response = await apiService.post(`${AdminApiRoutes.MUTATION_DELETE_CANDIDATE}`, props);
      return response?.data;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
      onSuccess: () => {
        queryClient.invalidateQueries(QUERY_GET_CANDIDATE_LIST);
      },
    },
  );
  return {
    ...mutation,
  };
};
