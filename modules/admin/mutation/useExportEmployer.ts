import {ClaimStatus} from '@/modules/companies/mutation/useClaimCompany';
import {apiService} from '@/service/api';
import {notification} from 'antd';
import {useMutation, useQueryClient} from 'react-query';
import {AdminApiRoutes} from '../constants/routes';
import {QUERY_GET_EMPLOYER_LIST, QUERY_GET_MANAGE_REVIEW} from '../constants';

export type ExportFormat = 'csv' | 'xls';

interface ExportEmployerProps {
  user_ids: number[];
  format: ExportFormat;
}

export const useExportEmployer = () => {
  const queryClient = useQueryClient();
  const mutation = useMutation(
    async (props: ExportEmployerProps) => {
      const response = await apiService.post(`${AdminApiRoutes.MUTATION_EXPORT_EMPLOYER}`, props, {
        responseType: 'blob',
      });
      return response;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
      onSuccess: () => {
        queryClient.invalidateQueries(QUERY_GET_EMPLOYER_LIST);
      },
    },
  );
  return {
    ...mutation,
  };
};
