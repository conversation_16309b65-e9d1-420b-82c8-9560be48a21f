import {ClaimStatus} from '@/modules/companies/mutation/useClaimCompany';
import {apiService} from '@/service/api';
import {notification} from 'antd';
import {useMutation, useQueryClient} from 'react-query';
import {AdminApiRoutes} from '../constants/routes';
import {QUERY_GET_MANAGE_REVIEW} from '../constants';

interface CompanyClaimRejectProps {
  action: 'approve' | 'remove';
  company_id: number;
  review_id: number;
}

export const useApproveRejectReview = () => {
  const queryClient = useQueryClient();
  const mutation = useMutation(
    async (props: CompanyClaimRejectProps) => {
      const response = await apiService.post(AdminApiRoutes.MUTATION_APPROVE_REJECT_REVIEW, props);

      return response?.data;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
      onSuccess: () => {
        queryClient.invalidateQueries(QUERY_GET_MANAGE_REVIEW);
      },
    },
  );
  return {
    ...mutation,
  };
};
