import {apiService} from '@/service/api';
import {notification} from 'antd';
import {useMutation, useQueryClient} from 'react-query';
import {AdminApiRoutes} from '../constants/routes';
import {QUERY_GET_EMPLOYER_LIST} from '../constants';

interface CreateCompanyProps {
  user_id?: number;
  company_name: string;
  company_slug: string;
  designation?: string;
  company_website?: string;
  company_location: string;
  company_sector: string;
  no_of_employees?: string;
  company_description?: string;
  company_logo?: string;
  company_contact_no: string;
  fk_logo_file_uuid?: string;
  linkedin_link?: string;
  twitter_link?: string;
  instagram_link?: string;
  facebook_link?: string;
}

export const useCreateCompany = (props: CreateCompanyProps) => {
  const queryClient = useQueryClient();
  const mutation = useMutation(
    async (props: CreateCompanyProps) => {
      const res = await apiService.post(AdminApiRoutes.MUTATION_CREATE_COMPANY, props);
      return res?.data;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
      onSuccess: () => {
        queryClient.invalidateQueries(QUERY_GET_EMPLOYER_LIST);
      },
    },
  );
  return {
    ...mutation,
  };
};
