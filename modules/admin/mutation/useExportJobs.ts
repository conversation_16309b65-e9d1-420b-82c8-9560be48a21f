import {apiService} from '@/service/api';
import {useMutation} from 'react-query';
import {AdminApiRoutes} from '../constants/routes';
interface JobExportProps {
  format: 'csv' | 'xlsx';
}

export const useExportJobs = () => {
  return useMutation(async (props: JobExportProps) => {
    const response = await apiService.post(`${AdminApiRoutes.MUTATION_EXPORT_JOBS}`, null, {
      responseType: 'blob',
      params: {
        format: props.format,
      },
    });

    return response;
  });
};
