import {apiService} from '@/service/api';
import {notification} from 'antd';
import {useMutation, useQueryClient} from 'react-query';
import {AdminApiRoutes} from '../constants/routes';
import {QUERY_GET_EMPLOYER_LIST} from '../constants';

interface UpdateCompanyProps {
  edit_company_name: string;
  edit_company_email: string;
  edit_designation: string;
  edit_company_website: string;
  edit_company_location: number;
  edit_company_sector: string;
  edit_no_of_employees: number;
  edit_company_description: string;
  edit_company_contact_no: string;
  linkedin_link?: string;
  twitter_link?: string;
  instagram_link?: string;
  facebook_link?: string;
}

export const useCreateCompany = (props: UpdateCompanyProps) => {
  const queryClient = useQueryClient();
  const mutation = useMutation(
    async (props: UpdateCompanyProps) => {
      const res = await apiService.put(AdminApiRoutes.MUTATION_UPDATE_COMPANIES, props);
      return res?.data;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
      onSuccess: () => {
        queryClient.invalidateQueries(QUERY_GET_EMPLOYER_LIST);
      },
    },
  );
  return {
    ...mutation,
  };
};
