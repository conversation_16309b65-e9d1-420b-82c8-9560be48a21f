import {ClaimStatus} from '@/modules/companies/mutation/useClaimCompany';
import {apiService} from '@/service/api';
import {notification} from 'antd';
import {useMutation, useQueryClient} from 'react-query';
import {AdminApiRoutes} from '../constants/routes';
import {QUERY_GET_MANAGE_REVIEW} from '../constants';

export type ExportFormat = 'csv' | 'xls';

interface ExportCCandidateProps {
  user_ids: number[];
  format: ExportFormat;
}

export const useExportCandidate = () => {
  const mutation = useMutation(
    async (props: ExportCCandidateProps) => {
      const response = await apiService.post(`${AdminApiRoutes.MUTATION_EXPORT_CANDIDATE}`, props, {
        responseType: 'blob',
      });
      return response;
    },
    {
      onError: (error: any) => {
        notification.error(error);
      },
    },
  );
  return {
    ...mutation,
  };
};
