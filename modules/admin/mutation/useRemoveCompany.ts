import {ClaimStatus} from '@/modules/companies/mutation/useClaimCompany';
import {apiService} from '@/service/api';
import {notification} from 'antd';
import {useMutation, useQueryClient} from 'react-query';
import {AdminApiRoutes} from '../constants/routes';
import {QUERY_GET_MANAGE_REVIEW} from '../constants';

interface CompanyRemoveProps {
  company_id: number;
  user_id: number;
}

export const useRemoveCompany = () => {
  const queryClient = useQueryClient();
  const mutation = useMutation(
    async (props: CompanyRemoveProps) => {
      const response = await apiService.post(AdminApiRoutes.MUTATION_REMOVE_COMPANY, props);

      return response?.data;
    },
    {
      onError: (error: any) => {
        notification.error(error ?? 'Error removing company');
      },
      onSuccess: () => {
        queryClient.invalidateQueries(QUERY_GET_MANAGE_REVIEW);
      },
    },
  );
  return {
    ...mutation,
  };
};
