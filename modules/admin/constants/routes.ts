export class AdminApiRoutes {
  public static get QUERY_GET_EMPLOYER_LIST(): string {
    return '/companyreview/reviews/grouped-by-company';
  }
  public static get MUTATION_CREATE_COMPANY(): string {
    return '/companies';
  }
  public static get QUERY_GET_USER_ALL_JOBS(): string {
    return '/jobs/getuseralljobs';
  }
  public static get MUTATION_REVIEW_HANDLE(): string {
    return '/companyreview/remove-reject-report';
  }
  public static get QUERY_GET_MANAGE_REVIEW(): string {
    return '/companyreview/reviews/manage-review';
  }

  public static get MUTATION_REMOVE_COMPANY(): string {
    return '/companies/remove-company';
  }

  public static get MUTATION_CLAIM_COMPANY_ACTION(): string {
    return '/company/accept-reject-claim-company';
  }

  public static get MUTATION_DELETE_COMPANY_REVIEW(): string {
    return '/companyreview/delete-review';
  }

  public static get MUTATION_APPROVE_REJECT_REVIEW(): string {
    return '/companyreview/approve-remove-review';
  }
  public static get MUTATION_UPDATE_COMPANIES(): string {
    return '/companies/updatecompany';
  }
  public static get MUTATION_EXPORT_CANDIDATE(): string {
    return '/users/export';
  }
  public static get MUTATION_DELETE_CANDIDATE(): string {
    return '/users/delete-bulkusers';
  }
  public static get MUTATION_DELETE_EMPLOYER(): string {
    return '/users/delete-bulkemployers';
  }
  public static get MUTATION_EXPORT_EMPLOYER(): string {
    return '/users/export-employer';
  }
  public static get QUERY_GET_CANDIDATE_LIST(): string {
    return '/users/employees/filter';
  }
  public static get MUTATION_EXPORT_JOBS(): string {
    return '/jobs/export';
  }
}
