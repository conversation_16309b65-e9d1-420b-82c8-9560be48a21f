import {NextResponse} from 'next/server';
import type {NextRequest} from 'next/server';

export function middleware(request: NextRequest) {
  const protectedRoutes = ['/write', '/claim', '/claim-thanks', '/review-thanks'];
  const url = new URL(request.url);
  const slug = url.pathname.split('/')[2];
  const token = request.cookies.get('session_token');

  // Check if the user is accessing a protected route
  if (url.pathname.startsWith('/companies/') && slug) {
    const isProtectedRoute = protectedRoutes.some(route => url.pathname === `/companies/${slug}${route}`);

    if (isProtectedRoute && !token) {
      return NextResponse.redirect(new URL('/auth/login', request.url));
    }
  }

  // Handle redirection for UTM parameters and location filters
  if (url.pathname === '/' && ['utm_campaign', 'country', 'city'].some(param => url.searchParams.has(param))) {
    const urlClone = new URL(request.url);
    urlClone.search = '';
    return NextResponse.redirect(urlClone.toString());
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico, sitemap.xml, robots.txt (metadata files)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt).*)',
  ],
};
