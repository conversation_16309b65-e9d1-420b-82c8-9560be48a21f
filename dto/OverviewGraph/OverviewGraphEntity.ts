import moment from 'moment';

interface OverviewGraphEntityProps {
  date: string;
  opened: number;
  delivered: number;
  spam: number;
}

export class OverviewGraphEntity {
  labels: string[];
  data: {
    opened: number[];
    delivered: number[];
    spam: number[];
  };

  constructor(props: OverviewGraphEntityProps[]) {
    const showUptoTenDates = (props: OverviewGraphEntityProps[], item: OverviewGraphEntityProps) => {
      const dates = props.map(el => moment(el.date).format('YYYY-MM-DD'));
      if (new Set([dates]).size > 10) {
        return;
      } else {
        const noOfGap = Number((dates?.length / 10).toFixed(0));
        let temp = [];

        for (let i = 0; i < dates.length; i += noOfGap) {
          temp?.push(dates[i]);
        }

        return temp;
      }
    };
    this.labels = props.reduce((acc: string[], item) => {
      if (props.some(el => moment(el.date).format('YYYY') !== moment(item.date).format('YYYY'))) {
        acc.push(moment(item.date).format('YYYY'));
      } else if (props.some(el => moment(el.date).format('YYYY-MM') !== moment(item.date).format('YYYY-MM'))) {
        acc.push(moment(item.date).format('MMM'));
      } else if (props.some(el => moment(el.date).format('YYYY-MM-DD') !== moment(item.date).format('YYYY-MM-DD'))) {
        const dates = showUptoTenDates(props, item);
        if (dates) {
          acc = dates;
        } else {
          acc.push(moment(item.date).format('YYYY-MM-DD'));
        }
      } else {
        acc.push(moment(item.date).format('YYYY-MM-DD'));
      }

      return [...(new Set(acc) as any)];
    }, []);
    this.data = props?.reduce(
      (acc: {opened: number[]; delivered: number[]; spam: number[]}, item) => {
        acc.opened.push(item.opened);
        acc.delivered.push(item.delivered);
        acc.spam.push(item.spam);
        return acc;
      },
      {opened: [], delivered: [], spam: []},
    );
  }
}
