import React, { useState, useEffect, useContext } from 'react';
import moment from 'moment';
import swal from 'sweetalert';
import Link from 'next/link';
import { paginate } from "@/helpers/paginate";
import { getStaffUserAllJobs, deleteJob } from '../../../lib/frontendapi';
import Pagination from "../../../components/Common/Pagination";
import AuthContext from '@/Context/AuthContext';
import { notification } from 'antd';
import ModalForm from '@/components/Common/ModalForm';
import CreateJobForm from '@/components/Jobs/CreateJobForm';
import { Job } from '@/lib/types';
import LoadingIndicator from '@/components/Common/LoadingIndicator';
import Image from 'next/image';

interface SwalOptions {
  title?: string;
  text?: string;
  icon?: string;
  dangerMode?: boolean;
  buttons?: string[];
  confirmButtonColor?: string;
}

export default function Jobs() {
  const { user } = useContext(AuthContext)
  const [modalConfirmPostJob, setModalConfirmPostJob] = useState(false);
  const [jobs, setJobs] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalJobs, setTotalJobs] = useState([]);
  const [reload, setReload] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [editJobData, setEditJobData] = useState<Job[] | any>([])

  const pageSize = 8;

  const modalConfirmOpenPostJob = () => {
    setModalConfirmPostJob(true);
  }
  const modalConfirmClosePostJob = () => {
    setModalConfirmPostJob(false);
  }

  const onPageChange = (page: any) => {
    setIsLoading(true)
    setCurrentPage(page);

    const data = {
      user_id: user?.id,
      job_sort: ''
    }
    getStaffUserAllJobs(data)
      .then(res => {
        if (res.status == true) {
          setTotalJobs(res.data);
          const paginatedPosts = paginate(res.data, page, pageSize);
          setJobs(paginatedPosts);
          setIsLoading(false)
        } else {
          setJobs([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  useEffect(() => {
    setIsLoading(true)
    const data = {
      user_id: user?.id,
      job_sort: ''
    }
    getStaffUserAllJobs(data)
      .then(res => {
        if (res.status == true) {
          setTotalJobs(res.data);
          const paginatedPosts = paginate(res.data, currentPage, pageSize);
          setJobs(paginatedPosts);
          setIsLoading(false)
        } else {
          setJobs([]);
        }
      })
      .catch(err => {
        console.log(err);
      });

  }, [reload]);

  const handleDeleteJob = (id: any) => {
    const options: Partial<SwalOptions> = {
      title: "Are you sure?",
      text: "You want to delete the job",
      icon: "warning",
      dangerMode: true,
      buttons: ['Cancel', 'Yes, I am sure!'],
      confirmButtonColor: '#062B60',
    };
    swal(options)
      .then((willDelete) => {
        if (willDelete) {
          deleteJob(id)
            .then(res => {
              if (res.status == true) {
                swal("Your Job has been deleted!", {
                  icon: "success",
                });
                $('#data_' + id).hide();
              } else {
              }
            })
            .catch(err => {

            });
        } else {
        }
      });
  }
  const handleChangeJobSorting = (e: any) => {

    const data = {
      user_id: user?.id,
      job_sort: e.target.value,
    }

    getStaffUserAllJobs(data)
      .then(res => {
        if (res.status == true) {
          setTotalJobs(res.data);
          const paginatedPosts = paginate(res.data, currentPage, pageSize);
          setJobs(paginatedPosts);
        } else {
          setJobs([]);
        }
      })
      .catch(err => {

      });
  }

  const planerrormsg = () => {
    let msg;
    if (user?.membership == false) {
      msg = 'Hi, memebership plan has been expired. kindly contact with company admin';
    } else {
      if (user?.plan == 1) {
        msg = 'Hi there!,job posting is not available right now. kindly contact with company admin.';
      } else if (user?.plan == 2) {
        msg = 'Hi, you can post only 25 jobs with current plan';
      } else {
        msg = 'Hi, you can post only 30 jobs with current plan';
      }

    }
     notification.error({ message: msg })

  };

  const editJob = (job: any) => {
    setEditJobData(job);
    setModalConfirmPostJob(true);
  };


  return (
    <>
      <div className='dash-right'>
        <h1>Jobs</h1>
        <div className='row mt-4'>
          <div className='col-sm-4'>
            <div className='sort-d-flex mt-2'>
              <p className='sort-by'>Sort By:</p>
              <select className='all-recent' onChange={handleChangeJobSorting}>
                <option value=""> All</option>
                <option value="active">Active</option>
                <option value="closed">Closed</option>
                <option value="expired">Expired</option>
              </select>
            </div>
          </div>
          <div className='col-sm-8 text-right'>
            <ul className='blue-text-line text-right m-center mobole-add-sp'>
              {user?.membership == false ? (
                user?.plan == 1 || (user?.plan == 2 && jobs.length > 25) || (user?.plan == 3 && jobs.length > 30) ? (
                  <li>
                    <button className="btn-a primary-size-16 btn-bg-0055BA" onClick={planerrormsg}>
                      <i className="fa-solid fa-plus"></i> &nbsp; Post A New Job
                    </button>
                  </li>
                ) : (
                  <li>
                    <button className="btn-a primary-size-16 btn-bg-0055BA" onClick={modalConfirmOpenPostJob}>
                      <i className="fa-solid fa-plus"></i> &nbsp; Post A New Job
                    </button>
                  </li>
                )
              ) : (
                <li>
                  <button className="btn-a primary-size-16 btn-bg-0055BA" onClick={() => { setEditJobData([]); setModalConfirmPostJob(true); }}>
                    <i className="fa-solid fa-plus"></i> &nbsp; Post A New Job
                  </button>
                </li>
              )}
            </ul>
          </div>
        </div>
        <ModalForm
          open={modalConfirmPostJob}
          title={editJobData.job_title ? `Edit ${editJobData.job_title}` : 'Post a Job'}
          description={'Enter your basic job details information & get started right away.'}
          onCancel={modalConfirmClosePostJob}>
          <CreateJobForm
            onCompleted={() => {
              setModalConfirmPostJob(false);
              setReload(!reload);
            }}
            job={editJobData}
          />
        </ModalForm>
        <div className="table-part mt-4 admin-tab-table">
          <table className="rwd-table">
            <tbody>
              <tr>
                <th>JOB TITLE<i className="fa-solid fa-align-left"></i></th>
                <th className='w-18'>IMPRESSIONS <i className="fa-solid fa-align-left"></i></th>
                <th>POSTED ON <i className="fa-solid fa-align-left"></i></th>
                <th>ALL RESPONSES <i className="fa-solid fa-align-left"></i></th>
                <th>STATUS  </th>
                <th>MANAGE  </th>
              </tr>
              {
                isLoading ?
                  <LoadingIndicator visible={isLoading} />
                  :
                  jobs.length > 0
                    ?
                    jobs.map((jobs_data: any, index: any) => {
                      return (
                        <tr id={`data_${jobs_data.id}`} key={index}>
                          <td data-th="JOB TITLE">
                            <p className='c-0070F5 f-18 w-600'><a target="_blank" href={'/job/' + jobs_data.job_slug}>{jobs_data.job_title}</a></p>
                            <p className='c-999999 f-16 '>{jobs_data.country_name}</p>
                            {jobs_data.is_featured == '1' ? <button className="pro mt-3 mb-3">Featured<img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/pro.png'} alt="Avatars-2" className='w-25' /> </button> : ''}
                            <p className='f-16 c-4D4D4D'> <b>{jobs_data.jobs_applicants}</b> Applicants <b>{jobs_data.shortlisted_jobs_count}</b> Shortlisted<b><br />{jobs_data.rejected_jobs_count}</b> Rejected <br /></p>
                            {/* <small className='mt-1'>Posted by : {current_user_id == jobs_data.job_created_by_id ? "Me" : jobs_data.job_created_name}</small> */}
                          </td>
                          <td data-th="IMPRESSIONS">
                            <div className='row'>
                              <div className='col-6'><p className='f-22 c-2C2C2C w-500'>{jobs_data.totaljobimpression}</p></div>
                              <div className='col-6'><p className='f-16 c-3D9F79 w-600'>{parseFloat(jobs_data.jobsImpressionpercentage).toFixed(2) + "%"}</p></div>
                            </div>
                          </td>
                          <td data-th="POSTED ON">
                            <p className='location'>{moment(jobs_data.created_at).format('LL')}</p>
                          </td>
                          <td data-th="ALL RESPONSES">
                            <p className='team-m'>{jobs_data.jobs_view_count} <span className='f-12'>(View)</span></p>
                          </td>
                          <td data-th="STATUS">
                            {jobs_data.job_status == 'active' && (
                              <button className="btn-app bg-3D9F79-app">{jobs_data.job_status}</button>
                            )}
                            {jobs_data.job_status == 'closed' && (
                              <button className="btn-app bg-bababa-app">{jobs_data.job_status}</button>
                            )}
                            {jobs_data.job_status == 'expired' && (
                              <button className="btn-app bg-D04E4F-app">{jobs_data.job_status}</button>
                            )}
                          </td>
                          <td data-th="MANAGE">
                            <a href="#" onClick={() => editJob(jobs_data)}><i className="fa-solid fa-pencil edit-pencil"></i></a>
                            <a href="#" onClick={(e) => handleDeleteJob(jobs_data.id)}><i className="fa-regular fa-trash-can del-trash"></i></a>
                          </td>
                        </tr>
                      )
                    })
                    :
                    <tr>
                      <td colSpan={6} className='work-experience-fieild m-p-10 text-center'>
                        <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blank-1.jpg'} alt="blank-1" className='' />
                        <p className='f-22 c-BABABA mb-1'>You don't seem to have any active jobs.</p>
                        <p className='f-18'>
                          <Link href="/employer/jobs" className='c-0070F5'>Post A Job</Link>
                        </p>
                      </td>
                    </tr>
              }
            </tbody>
          </table>
          <Pagination
            items={totalJobs.length}
            currentPage={currentPage}
            pageSize={pageSize}
            onPageChange={onPageChange}
          />
        </div>
      </div >
    </>
  )
}
