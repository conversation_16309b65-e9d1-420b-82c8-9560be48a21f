import React, { useState, useEffect } from 'react';
import { getCurrentUserData } from "../../../lib/session";
import { getSingleUserDetails, updateUserDetails, updateTwoFactorAuth } from '../../../lib/frontendapi';
import { useForm } from 'react-hook-form';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import moment from 'moment';
import PopupModal from '../../../components/Common/PopupModal';
import Link from 'next/link';
import SuccessToast from "../../Common/showSuccessTostrMessage";
import ErrorToast from "../../Common/showErrorTostrMessage";
import Image from 'next/image';


export default function Account() {
    const [userProfileImage, setUserProfileImage] = useState('');
    const [userEmail, setUserEmail] = useState('');
    const [userName, setUserName] = useState('');
    const [uploadError, setUploadError] = useState('');
    const [selectedFileName, setSelectedFileName] = useState('');
    const [addProfileImage, setAddProfileImage] = useState('');
    const [previewProfileImage, setPreviewProfileImage] = useState('');
    const [twofactorauth, setTwoFactorAuth] = useState<any>('');
    const [showPopupunsave, setShowPopupunsave] = useState(false);
    const [showPopupunerror, setShowPopupunerror] = useState(false);
    const [showmessage, setShowmessage] = useState([]);

    useEffect(() => {
        const current_user_data: any = getCurrentUserData();
        getSingleUserDetails(current_user_data.id)
            .then(res => {
                if (res.status == true) {
                    setUserProfileImage(res.user.profile_image);
                    setUserEmail(res.user.email);
                    setUserName(res.user.name);
                    setTwoFactorAuth(res.user.is2FA)
                } else {
                    setUserProfileImage('');
                    setUserEmail('');
                    setUserName('');
                    setTwoFactorAuth('')
                }
            })
            .catch(err => {
                console.log(err);
            });
    }, []);
    const submitForm = (event: any) => {
        event.preventDefault()
        const current_user_data: any = getCurrentUserData();
        const data = {
            name: userName
        }
        updateUserDetails(current_user_data.id, data) // Image upload pending
            .then(res => {
                if (res.status == true) {
                    setShowmessage(res.message);
                    setShowPopupunsave(true);
                    setTimeout(() => {
                        setShowPopupunsave(false)
                    }, 10000)
                    setTimeout(function () {
                        window.location.reload();
                    }, 3000);
                } else {
                    setShowmessage(res.message);
                    setShowPopupunerror(true);
                    setTimeout(() => {
                        setShowPopupunerror(false)
                    }, 10000)
                }
            })
            .catch(err => {
                console.log(err);
            });
    }
    const handleImageChange = (event: any) => {
        if (event.target.files && event.target.files[0]) {
            if (event.target.files[0].type.includes('image')) {
                const selectedFile = event.target.files[0];
                setPreviewProfileImage(URL.createObjectURL(event.target.files[0]));
                setAddProfileImage(selectedFile);
                setSelectedFileName(selectedFile.name);
                setUploadError('');
            }
            else {
                setUploadError('Please upload an image file (JPEG,JPG,PNG).')
            }
        }
    };

    const isSubmitDisabled = !!uploadError;

    const [modalConfirm7, setModalConfirm7] = useState(false);

    const openModalConfirm7 = () => {
        setModalConfirm7(true);
    };

    const modalConfirmClose7 = () => {
        setModalConfirm7(false);
    };
    const handleToggle2Fa = () => {
        const updatedValue: any = !twofactorauth;
        const current_user_data: any = getCurrentUserData();
        const data = {
            userId: current_user_data.id,
            is2FA: updatedValue ? 1 : 0,
        };
        updateTwoFactorAuth(data)
            .then(response => {
                setTwoFactorAuth(updatedValue);
                //localStorage.setItem('displayEmail', String(updatedValue));
            })
            .catch(error => {
                console.error('Error setting default resume:', error);
            });
    };
    return (
        <>
            <div className="dash-right">
                <h1>Settings </h1>
                <div className='row '>
                    <div className='col-sm-12'>
                        <ul className='list-loc m-m-0 mt-4'>
                            <li className='active'><Link href="/staff/settings">Account</Link></li>
                            <li><Link href="/staff/settings/changepassword">Password </Link></li>
                            <li><Link href="/staff/settings/notifications">Notification </Link></li>
                        </ul>
                    </div>
                </div>
                <div className='data-management m-p-10'>
                    <div className='work-experience-fieild m-p-10'>
                        <form className='form-experience-fieild' onSubmit={submitForm}>
                            <div className='row'>
                                <div className='col-lg-2 col-md-3 col-12'>
                                    <div className="dash-profile-img mb-4 m-auto">
                                        <p className='f-12 c-2C2C2C m-center'>Profile Picture</p>
                                        {previewProfileImage ? (
                                            <img src={previewProfileImage} alt="Preview Image" style={{ "height": "118px" }} />
                                        ) : userProfileImage ? (
                                            <img
                                                src={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/userprofileImg/${userProfileImage}`}
                                                alt="Avatars-4" style={{ "height": "118px" }}
                                            />
                                        ) : (
                                            <img
                                                src={`${process.env.NEXT_PUBLIC_BASE_URL}images/Profile_Picture.png`}
                                                alt="Avatars-4"
                                                className="Profile_Picture"
                                                style={{ "width": "100%", "height": "118px", objectFit: "contain" }}
                                            />
                                        )
                                        }
                                        {/* {userProfileImage
                                            ?
                                                <img src={process.env.NEXT_PUBLIC_IMAGE_URL+'images/userprofileImg/'+userProfileImage} alt="Avatars-4" style={{"height": "118px"}} />
                                            :
                                                <img src={process.env.NEXT_PUBLIC_BASE_URL+'images/Avatars-4.png'} alt="Avatars-4"  />
                                        } */}
                                    </div>
                                </div>
                                <div className='col-lg-10 col-md-9 col-12'>
                                    <div className='uploade-btn'>
                                        <div
                                            className="btn-a primary-size-16 btn-bg-0055BA mt-5 mb-4 mobile-m-0 max-340"
                                            onClick={openModalConfirm7}>
                                            <i className="fa-solid fa-upload"></i> Upload A New Photo
                                        </div>
                                        <PopupModal
                                            show={modalConfirm7}
                                            handleClose={modalConfirmClose7}
                                            customclass={"header-remove body-sp-0"}>
                                            <div className="popup-body">
                                                <h5 className="f-26 c-0055BA w-700 text-center mb-4">
                                                    Upload Your Profile Picture
                                                </h5>
                                                <div className="upload-file" id='upload-file1'>
                                                    <div className="file-up">
                                                        <input type='file' className='account_page_upload_btn' name='profile_image' id="doctor_profile" onChange={handleImageChange} accept=".jpg, .png" />
                                                        <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/cemra.png'} alt="cemra" className='cemra' />
                                                    </div>
                                                    <p className="upload-text">
                                                        Browse and choose the files you want to upload from your
                                                        computer.
                                                    </p>
                                                    <p className="max-size">Maximum upload size is 1MB.</p>
                                                </div>
                                                {uploadError && <p className='error mt-2 ml-auto' style={{ color: "red" }}>{uploadError}</p>}
                                                {selectedFileName && <p className='text-dark'>Selected File: {selectedFileName}</p>}
                                                <div className="modal-footer">
                                                    <button
                                                        type="button"
                                                        className="cancel-btn"
                                                        data-bs-dismiss="modal"
                                                        onClick={modalConfirmClose7}
                                                    >
                                                        Cancel
                                                    </button>
                                                    <button type="submit" className="update-btn" disabled={isSubmitDisabled}>
                                                        Update
                                                    </button>
                                                </div>
                                            </div>
                                        </PopupModal>
                                    </div>
                                </div>
                            </div>
                            <div className='row'>
                                <div className='col-sm-8'>
                                    <div className='form_field_sec'>
                                        <input type='email' placeholder='Email' className='fild-des' value={userEmail} readOnly />
                                        <label>Email ID</label>
                                    </div>
                                    <div className='form_field_sec'>
                                        <input type='text' placeholder='Name' className='fild-des' value={userName} onChange={(e: any) => setUserName(e.target.value)} />
                                        <label>Name</label>
                                    </div>
                                </div>
                                <div className="col-sm-12">
                                    <div className="form-experience-fieild">
                                        <div className="row mt-4">
                                            <div className="col-sm-10 col-8">
                                                <p className="f-22 c-2C2C2C w-500 mb-1">
                                                    Two-Factor Authentication (2FA)
                                                </p>
                                                <p className="f-16 c-747474">
                                                    2FA adds an extra layer of protection, making it more challenging for hackers to compromise user accounts.
                                                </p>
                                            </div>
                                            <div className="col-sm-2 col-4 text-right">
                                                <label className="switch btn-swith">
                                                    <input
                                                        type="checkbox"
                                                        checked={twofactorauth}
                                                        onChange={handleToggle2Fa}
                                                    />
                                                    <span className="slider round"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='col-sm-12'>
                                    <div className='text-right mt-5'>
                                        <button className='save' type="submit">Save</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <ToastContainer />
            </div>
            {showPopupunsave &&
                <SuccessToast message={showmessage} />
            }
            {showPopupunerror &&
                <ErrorToast message={showmessage} />
            }
        </>
    )
}
