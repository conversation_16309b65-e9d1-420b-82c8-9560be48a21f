import React, { useState, useEffect } from 'react';
import { getCurrentUserData } from "../../../lib/session";
import { getSingleUserDetails, changepassword } from '../../../lib/frontendapi';
import { useForm } from 'react-hook-form';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Link from 'next/link';
import SuccessToast from "../../Common/showSuccessTostrMessage";
import ErrorToast from "../../Common/showErrorTostrMessage";

export default function ChangePassword() {
    const { register, handleSubmit, watch, formState: { errors } }: any = useForm();
    const [currentPassword, setCurrentPassword]: any = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [retypePassword, setRetypePassword] = useState('');
    const [showPopupunsave, setShowPopupunsave] = useState(false);
    const [showPopupunerror, setShowPopupunerror] = useState(false);
    const [showmessage, setShowmessage] = useState([]);

    const submitForm = (data: any) => {
        const current_user_data: any = getCurrentUserData();
        const password_data = {
            user_id: current_user_data.id,
            currentPassword: data.current_password,
            newPassword: data.new_password,
            retypePassword: data.retype_password
        }
        changepassword(password_data)
            .then(res => {
                if (res.status == true) {
                    setShowmessage(res.message);
                    setShowPopupunsave(true);
                    setTimeout(() => {
                        setShowPopupunsave(false)
                    }, 10000)
                } else {
                    setShowmessage(res.message);
                    setShowPopupunerror(true);
                    setTimeout(() => {
                        setShowPopupunerror(false)
                    }, 10000)
                }
            })
            .catch(err => {
                console.log(err);
            });
    }
    return (
        <>
            <div className="dash-right">
                <h1>Settings </h1>
                <div className='row '>
                    <div className='col-sm-12'>
                        <ul className='list-loc m-m-0 mt-4'>
                            <li><Link href="/staff/settings">Account</Link></li>
                            <li className='active'><Link href="/staff/settings/changepassword">Password </Link></li>
                            <li><Link href="/staff/settings/notifications">Notification </Link></li>
                        </ul>
                    </div>
                </div>
                <div className='data-management m-p-10'>
                    <div className='work-experience-fieild m-p-10'>
                        <p className='f-12 c-2C2C2C m-center w-700'>RESET PASSWORD</p>
                        <div className='row'>
                            <form className='form-experience-fieild' onSubmit={handleSubmit(submitForm)}>
                                <div className='col-sm-3'>
                                    <div className='form_field_sec'>
                                        <input type='password' placeholder='Current Password' className='fild-des' {...register('current_password', { required: true, minLength: 8 })} onChange={(e: any) => setCurrentPassword(e.target.value)} />
                                        <label>Current Password</label>
                                    </div>
                                    {errors.current_password && errors.current_password.type === 'required' && <p className="text-danger" style={{ "textAlign": "left" }}>Current Password is required.</p>}
                                    {errors.current_password && errors.current_password.type === 'minLength' && (<p className="text-danger" style={{ textAlign: 'left' }}>Please enter a minimum length of 8.</p>)}
                                    <div className='form_field_sec'>
                                        <input type='password' placeholder='New Password' className='fild-des' {...register('new_password', { required: true, minLength: 8 })} onChange={(e: any) => setNewPassword(e.target.value)} />
                                        <label>New Password</label>
                                    </div>
                                    {errors.new_password && errors.new_password.type === 'required' && <p className="text-danger" style={{ "textAlign": "left" }}>New Password is required.</p>}
                                    {errors.new_password && errors.new_password.type === 'minLength' && (<p className="text-danger" style={{ textAlign: 'left' }}>Please enter a minimum length of 8.</p>)}

                                    <div className='form_field_sec'>
                                        <input type='password'
                                            placeholder='Re-type Password' className='fild-des'
                                            {...register("retype_password", {
                                                required: 'Re-type password is required.',
                                                validate: (val: string) =>
                                                    val === watch('new_password') || 'Your password do not match',
                                            })}
                                            onChange={(e: any) => setRetypePassword(e.target.value)}
                                        />
                                        <label>Re-type Password</label>
                                    </div>
                                    {errors.retype_password && (<p className="text-danger" style={{ "textAlign": "left" }}>
                                        {errors.retype_password.message}
                                    </p>)}
                                </div>
                                <div className='col-sm-12'>
                                    <div className='text-right mt-5'>
                                        <button className='cancel'>Cancel</button>
                                        <button className='save' type="submit">Save</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <ToastContainer />
            </div>
            {showPopupunsave &&
                <SuccessToast message={showmessage} />
            }
            {showPopupunerror &&
                <ErrorToast message={showmessage} />
            }
        </>
    )
}