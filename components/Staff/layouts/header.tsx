import React, { useState, useEffect, useRef, useContext } from 'react';
import { useRouter } from 'next/router';
import {
  getCurrentUserDetails,
  savePlanPayment,
  getCurrentUserPaymentDetails,
  getNotifications,
  changeReadUnreadStatus,
  getReadNotifications,
  updateCompanyLogo,
  getAllCandidateHeaderSearch,
  checkAndUpdateResumesViewed,
} from '../../../lib/frontendapi';
import { useSession, signOut } from 'next-auth/react';
import moment from 'moment';
import Link from 'next/link';
import { getAllNewAdminSettings } from '../../../lib/adminapi';
import AuthContext from '@/Context/AuthContext';
import Image from 'next/image';
import { Popover } from 'antd';
import AuthUserMenu from '@/components/Common/AuthUserMenu';

export default function Header() {
  interface AdminSettings {
    logo: string;
    favicon: string;
  }
  const initialSettings: AdminSettings = {
    logo: '',
    favicon: '',
  };
  const anchorRef = useRef<HTMLAnchorElement | null>(null);
  const [sessionstatus, setSession] = useState('');
  const [userProfileImage, setUserProfileImage] = useState('');
  const { data: session } = useSession();
  const [current_user_id, setCurrentUserId] = useState('');
  const [current_user_name, setCurrentUserName] = useState('');
  const [current_user_role, setCurrentUserRole] = useState('');
  const [notifications, setNotifications] = useState([]);
  const [readNotifications, setReadNotifications] = useState([]);
  const router = useRouter();
  const [belliconshow, setBellIconShow] = useState(false);
  const [userPaymentId, setUserPaymentId] = useState('');
  const [currentUserData, setCurrentUserData]: any = useState([]);
  const [currentUserName, setCurrentUsername] = useState('');
  const [companyLogo, setCompanyLogo] = useState('');
  const [modalConfirm7, setModalConfirm7] = useState(false);
  const [editCompanyLogo, setEditCompanyLogo] = useState('');
  const [companyName, setCompanyName] = useState('');
  const [companyDesignation, setCompanyDesignation] = useState('');
  const [companyId, setCompanyId] = useState('');
  const [planType, setPlanType] = useState('Month');
  const { user } = useContext(AuthContext)

  const [searchInputValue, setSearchInputValue] = useState('');

  const [employeedata, setEmployeeData] = useState([]);

  const [settings, SetAdminSetting] = useState<AdminSettings>(initialSettings);

  const inputRef = useRef<HTMLInputElement>(null);
  const searchRef = useRef<HTMLDivElement | null>(null);

  const showBellIconOnclick = (showVal: any) => {
    setBellIconShow(showVal);
  };

  const [usercvcount, setUserCvCount] = useState(0);

  const [showPopupunsave, setShowPopupunsave] = useState(false);
  const [showPopupunerror, setShowPopupunerror] = useState(false);
  const [showmessage, setShowmessage] = useState('');

  const [membershipstatus, setMemberShipStatus] = useState('');
  const [membershipplan, setMemberShipPlan] = useState('');
  const [belliconshow2, setBellIconShow2] = useState(false);
  const showBellIconOnclick2 = (showVal: any, status: any) => {
    setBellIconShow2(showVal);
    if (status == 'read') {
      const data = {
        notify_to: user?.id,
      };
      changeReadUnreadStatus(data)
        .then(res => {
          if (res.status == true) {
            console.log(res);
          } else {
          }
        })
        .catch(err => {
          setShowmessage(err);
          setShowPopupunerror(true);
          setTimeout(() => {
            setShowPopupunerror(false);
          }, 10000);
        });

      getNotifications(data).then(res => {
        if (res.status == true) {
          setReadNotifications(res.data);
        } else {
          setReadNotifications([]);
        }
      });
    } else {
    }
  };
  const [modalConfirm, setModalConfirm] = useState(false);
  const modalConfirmOpen = () => {
    setModalConfirm(true);
  };
  function redirectToLogin() {
    window.location.href = '/auth/login';
  }
  function handleLogout(e: any) {
    e.preventDefault();
    redirectToLogin();
  }
  function handleLogouttwo() {
    //removeToken();
    signOut({ redirect: false });
    router.push('/');
  }
  const modalConfirmOpen7 = () => {
    setModalConfirm7(true);
  };
  const modalConfirmClose7 = () => {
    setModalConfirm7(false);
  };
  const submitUploaCompanyLogo = (e: any) => {
    e.preventDefault();
    const data = {
      company_id: companyId,
    };
    updateCompanyLogo(data)
      .then(res => {
        if (res.status == true) {
          setShowmessage(res.message);
          setShowPopupunsave(true);
          setTimeout(() => {
            setShowPopupunsave(false);
          }, 10000);
          modalConfirmClose7();
          setTimeout(function () {
            window.location.reload();
          }, 3000);
        } else {
          setShowmessage(res.message);
          setShowPopupunerror(true);
          setTimeout(() => {
            setShowPopupunerror(false);
          }, 10000);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  //Function to check if a JWT token has expired
  // function isTokenExpired(token_exp:any) {
  //     const currentTime = Math.floor(Date.now() / 1000); // Get the current time in seconds
  //     const tokenExp = token_exp; // Assuming the token payload has an 'exp' field
  //     return tokenExp < currentTime;
  // }

  //Check if the token is expired when the page loads
  // const authtoken:any = getToken();
  // let token_exp = '';
  // if (authtoken && authtoken.exp) {
  //     token_exp = authtoken.exp;
  // }
  // if (isTokenExpired(token_exp)) {
  //     // Redirect the user to the login page
  //     if(typeof window !== 'undefined'){
  //         window.location.href = '/auth/login';
  //     }
  // }
  useEffect(() => {
    fetchAdminSettingData();
    const datas = {
      notify_to: user?.id,
    };

    getNotifications(datas)
      .then(res => {
        if (res.status == true) {
          setReadNotifications(res.data);
        } else {
          setReadNotifications([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
    const data = {
      notify_to: user?.id,
    };
    getReadNotifications(data)
      .then(res => {
        if (res.status == true) {
          setNotifications(res.data);
        } else {
          setNotifications([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  }, [session]);

  const fetchAdminSettingData = async () => {
    try {
      const response = await getAllNewAdminSettings();
      SetAdminSetting(response);
    } catch (error) {
      console.error(error);
    }
  };

  let username_letter = '';
  if (currentUserName) {
    username_letter = currentUserName
      .split(/\s/)
      .reduce((response: any, word: any) => (response += word.slice(0, 1)), '');
  } else {
    username_letter = '';
  }
  function handleSearchInputChange(event: any) {
    const value = event.target.value.trim();
    setSearchInputValue(value);
    if (value.length >= 3) {
      const data = {
        value: value,
        user_id: user?.id,
        company_id: user?.company_id,
      };
      getAllCandidateHeaderSearch(data)
        .then(response => {
          if (response.status == true) {
            setEmployeeData(response.employee);
          }
        })
        .catch(error => {
          console.error(error);
        });
    }
  }

  const handleViewCandidateProfile = (id: any) => {

    const data = {
      user_id: user?.id,
      user_role: user?.role,
      company_id: user?.company_id,
      applicant_id: id,
    };

    checkAndUpdateResumesViewed(data)
      .then(res => {
        if (res.status === true) {
          setSearchInputValue('');
          window.localStorage.setItem('user_resume_count', res.user_resume_count);
          setUserCvCount(res.user_resume_count);
        } else {
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  const handleCvCountErrorMsg = () => {
    let msg;
    if (membershipstatus == 'true') {
      msg = 'Hi,memebership plan has been expired. Please upgrade your plan to see more candidates';
    } else {
      msg = 'Hi, you have consumed all CV count. Please upgrade your plan to see more candidates';
    }

    setShowmessage(msg);
    setShowPopupunerror(true);
    setTimeout(() => {
      setShowPopupunerror(false);
    }, 10000);
  };

  useEffect(() => {
    window.addEventListener('click', handleClick);
    return () => {
      window.addEventListener('click', handleClick);
    };
  }, []);
  const handleClick = (event: MouseEvent) => {
    if (anchorRef.current && event.target instanceof Node && !anchorRef.current.contains(event.target)) {
      showBellIconOnclick2(false, '');
    }
  };
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  const handleMenuItemClick = () => {
    closeMenu();
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (inputRef.current && !inputRef.current.contains(event.target as Node) && (searchRef.current && !searchRef.current.contains(event.target as Node))) {
        setSearchInputValue('');
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);


  return (
    <>
      <header className="head-part mar-bot-55">
        {membershipstatus} df
        <nav className="navbar navbar-expand-lg navbar-light  fixed-top bg-fff">
          <div className="container-fluid full-width">
            {settings && settings.logo ? (
              <div className="logo-width">
                <Link className="navbar-brand" href="/">
                  <img
                    src={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/' + settings.logo}
                    alt="logo"
                    className="logo-head"
                  />
                </Link>
              </div>
            ) : (
              <div className="logo-width">
                <Link className="navbar-brand" href="/">
                  <img
                    src={'/images/Avatars-4.png'}
                    alt="logo"
                    className="logo-head"
                  />
                </Link>
              </div>
            )}
            <button
              className={`navbar-toggler collapsed ${isMenuOpen ? 'active' : ''}`}
              type="button"
              data-bs-toggle="collapse"
              data-bs-target="#navbarSupportedContent"
              aria-controls="navbarSupportedContent"
              aria-expanded="false"
              aria-label="Toggle navigation"
              onClick={toggleMenu}>
              <span className="navbar-toggler-icon">
                <i className="fa-solid fa-bars"></i>
                <i className="fa-solid fa-x close-x"></i>
              </span>
            </button>
            <div className={`collapse navbar-collapse j-end ${isMenuOpen ? 'show' : ''}`} id="navbarSupportedContent">
              <form className="d-flex mobile-single tab-scroll-view tab-flex-none">
                <div className="search-in tab-none">
                  <input
                    className="form-control me-2"
                    type="search"
                    placeholder="Search candidate"
                    aria-label="Search"
                    value={searchInputValue}
                    onChange={handleSearchInputChange}
                    ref={inputRef}
                  />
                  <i className="fa-solid fa-magnifying-glass glass-ser"></i>
                  {searchInputValue && (
                    <div className="company_jobs_search" ref={searchRef}>
                      <div id="search-results" className="companysas">
                        <p className="title_heading text-start">Candidates</p>
                        <ul
                          className="list-unstyled"
                          id="company"
                          style={{ height: employeedata.length >= 3 ? '150px' : 'auto' }}>
                          {employeedata.length > 0 ? (
                            employeedata.map((employee: any, index: any) => (
                              <React.Fragment key={index}>
                                {membershipstatus == 'false' && (usercvcount != 0 || employee.applicant_id) ? (
                                  <a
                                    target="_blank"
                                    href={`/candidate-profile/${employee.slug}`}
                                    className="search_result_para 1"
                                    onClick={() => handleViewCandidateProfile(employee.user_id)}>
                                    <li>
                                      {employee.name}{' '}
                                      {employee.applicant_id && (
                                        <span className="badge bg-success float-end">
                                          Seen by{' '}
                                          {employee.viewed_user_id == current_user_id
                                            ? 'me'
                                            : employee.viewed_user_name.split(' ')[0]}
                                        </span>
                                      )}
                                    </li>
                                  </a>
                                ) : (
                                  <li className="search_result_para 2" onClick={handleCvCountErrorMsg} role="button">
                                    {employee.name}{' '}
                                    {employee.applicant_id && (
                                      <span className="badge bg-success">
                                        Seen by{' '}
                                        {employee.viewed_user_id == current_user_id
                                          ? 'me'
                                          : employee.viewed_user_name.split(' ')[0]}
                                      </span>
                                    )}
                                  </li>
                                )}
                              </React.Fragment>
                            ))
                          ) : (
                            <li className="search_result_para">
                              No candidate records found or enter at least three characters to get the results
                            </li>
                          )}
                        </ul>
                      </div>
                    </div>
                  )}
                </div>
              </form>

              <div className="dask-tab-mobile-d-flex m-top-add-sp ">
                <div className="dask-none d-flex user-profile-mobile ">
                  <div className="img-box-1">
                    {userProfileImage ? (
                      <img
                        src={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/userprofileImg/' + userProfileImage}
                        alt="Avatars-4"
                        className="w-40"
                      />
                    ) : (
                      <img
                        src={'/images/Avatars-1.png'}
                        alt="Avatars-4"
                        className="w-40"
                      />
                    )}
                  </div>
                  <div className="text-name-mobile">
                    <h4 className="name-text">{companyName}</h4>
                    <h5 className="roll">{current_user_name}</h5>
                  </div>
                </div>
                <p className="head-icon notifications mobile-nati">
                  {belliconshow2 ? (
                    <>
                      <a href="#" className="pog-r" onClick={e => showBellIconOnclick2(false, '')} ref={anchorRef}>
                        <i className="fa-solid fa-bell fill-bell"></i>
                        <span className="round-bell"></span>
                      </a>
                      <div className="box-noti">
                        <div className="bell-box">
                          <div className="row">
                            <div className="col-7">
                              <h4 className="not-title">Notifications</h4>
                            </div>
                            <div className="col-5">
                              <p onClick={e => showBellIconOnclick2(true, 'read')} className="mark-as">
                                Mark as Read
                              </p>
                            </div>
                          </div>
                          {readNotifications.length > 0
                            ? readNotifications.slice(0, 3).map((read_notification_data: any, index: any) => {
                              const created_at = moment.utc(read_notification_data.created_at);
                              const currentTime = moment();
                              const yesterday = moment().subtract(1, 'day');
                              let timeFormatted;

                              if (created_at.isSame(currentTime, 'day')) {
                                timeFormatted = created_at.local().format('hh:mm A');
                              } else if (created_at.isSame(yesterday, 'day')) {
                                timeFormatted = 'Yesterday';
                              } else {
                                timeFormatted = created_at.local().format('MMMM D');
                              }
                              return (
                                <div className="row mt-4" key={index}>
                                  <div className="col-2 pr-0">
                                    {read_notification_data.profile_image ? (
                                      <img
                                        src={`${process.env.NEXT_PUBLIC_IMAGE_URL}images/userprofileImg/${read_notification_data.profile_image}`}
                                        alt="Avatars-4"
                                        className="w-24"
                                      />
                                    ) : (
                                      <small
                                        title={read_notification_data.name}
                                        className="text-uppercase w-24 notfication_name">
                                        {read_notification_data.name.split(' ').length === 1
                                          ? read_notification_data.name.substring(0, 2)
                                          : read_notification_data.name
                                            .split(' ')
                                            .map((word: any, index: any) =>
                                              index === 0 || index === 1 ? word[0] : '',
                                            )
                                            .join('')}
                                      </small>
                                    )}
                                  </div>
                                  <div className="col-10 text-left pl-0">
                                    {read_notification_data.is_read == 0 ? (
                                      <>
                                        <p
                                          className="f-16 notification_font_weight"
                                          dangerouslySetInnerHTML={{ __html: read_notification_data.notification }}></p>
                                      </>
                                    ) : (
                                      <>
                                        <p
                                          className="f-16"
                                          dangerouslySetInnerHTML={{ __html: read_notification_data.notification }}></p>
                                      </>
                                    )}
                                    <p className="f-12">
                                      {/* {moment.utc(read_notification_data.created_at).local().startOf('seconds').fromNow()} */}
                                      {timeFormatted}
                                    </p>
                                  </div>
                                </div>
                              );
                            })
                            : ''}
                          <p>
                            <Link href="/staff/notifications" className="view-all">
                              View All
                            </Link>
                          </p>
                        </div>
                      </div>
                    </>
                  ) : (
                    <a href="#" onClick={e => showBellIconOnclick2(true, '')} className="pog-r">
                      <i className="fa-regular fa-bell"></i>
                      <span className="round-bell"></span>
                    </a>
                  )}
                </p>

                <div className="dropdown w-400-list pog-none d-flex" id="heaedr1">
                  <p
                    className="user-img tab-none"
                    id="headerdropdownMenuButton1"
                    data-bs-toggle="dropdown"
                    aria-expanded="false">
                    {userProfileImage ? (
                      <img
                        src={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/userprofileImg/' + userProfileImage}
                        alt="Avatars-4"
                        className="w-32"
                      />
                    ) : (
                      <img
                        src={'/images/Avatars-1.png'}
                        alt="Avatars-4"
                        className="w-40"
                      />
                    )}
                  </p>
                  {/* <div className="dropdown w-400-list pog-none"> */}
                  <p
                    className="head-icon  dots-three"
                    id="headerdropdownMenuButton1"
                    data-bs-toggle="dropdown"
                    aria-expanded="false">
                    <i className="fa-solid fa-ellipsis-vertical"></i>
                  </p>
                  <ul className="dropdown-menu m-w-100 " aria-labelledby="dropdownMenuButton1">
                    <div className="candidate-box">
                      <div className="row tab-none">
                        <div className="col-sm-3 pr-0">
                          {userProfileImage ? (
                            <img
                              src={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/userprofileImg/' + userProfileImage}
                              alt="Avatars-4"
                              className="w-32"
                            />
                          ) : (
                            <img src={'/images/Avatars-1.png'} alt="Avatars-4" />
                          )}
                        </div>
                        <div className="col-sm-9 ">
                          <h6 className="name-user">{currentUserName.substring(0, 10)}</h6>
                        </div>
                      </div>
                      <ul className="can-list">
                        <li>
                          <Link
                            className="dropdown-item item-2"
                            href="/staff/settings"
                            style={{ padding: '0px' }}
                            onClick={handleMenuItemClick}>
                            <i className="fa-solid fa-gear"></i> Settings
                          </Link>
                        </li>
                        <li>
                          <Link
                            className="dropdown-item item-2"
                            href="/staff/notifications"
                            style={{ padding: '0px' }}
                            onClick={handleMenuItemClick}>
                            <i className="fa-regular fa-bell"></i> Notifications
                          </Link>
                        </li>
                        <li>
                          <Link
                            className="dropdown-item item-2"
                            href="#"
                            style={{ padding: '0px' }}
                            onClick={handleMenuItemClick}>
                            <i className="fa-regular fa-life-ring"></i> Help
                          </Link>
                        </li>
                        {sessionstatus == 'ok' ? (
                          <li className="log-out">
                            <a
                              className="dropdown-item item-2 log-out"
                              href="#"
                              onClick={handleLogouttwo}
                              style={{ padding: '0px' }}>
                              <i className="fa-solid fa-right-from-bracket"></i> Logout
                            </a>
                          </li>
                        ) : (
                          <li className="log-out">
                            <a
                              className="dropdown-item item-2"
                              href="#"
                              onClick={e => handleLogout(e)}
                              style={{ padding: '0px' }}>
                              <i className="fa-solid fa-right-from-bracket"></i> Logout
                            </a>
                          </li>
                        )}
                      </ul>
                    </div>
                  </ul>
                  {/* </div> */}
                </div>
              </div>
              <div className="left-bar dask-none">
                <div className="text-center mt-4">
                  <ul className="side-menu-left mt-4">
                    <li className={router.pathname == '/staff/dashboard' ? 'active' : ''}>
                      <Link href="/staff/dashboard" onClick={handleMenuItemClick}>
                        <img
                          src={'/images/dash/icon-4a.png'}
                          alt="icon-4a"
                          className="icon-a"
                        />
                        <img
                          src={'/images/dash/icon-4.png'}
                          alt="icon-4"
                          className="icon-hover"
                        />
                        Home
                      </Link>
                    </li>
                    <li className="m-none">
                      <li
                        className={
                          router.pathname == '/staff/company' ||
                            router.pathname == '/staff/company/profile' ||
                            router.pathname == '/staff/company/followers' ||
                            router.pathname == '/staff/company/insights'
                            ? 'active'
                            : ''
                        }>
                        <Link href="/staff/companyprofile" onClick={handleMenuItemClick}>
                          <img
                            src={'/images/dash/icon-3a.png'}
                            alt="icon-3a"
                            className="icon-a"
                          />
                          <img
                            src={'/images/dash/icon-3.png'}
                            alt="icon-3"
                            className="icon-hover"
                          />
                          Company Profile
                        </Link>
                      </li>
                    </li>
                    <li
                      className={
                        router.pathname == '/staff/jobs' || router.pathname == '/staff/jobs/editjobs' ? 'active' : ''
                      }>
                      <Link href="/staff/jobs" onClick={handleMenuItemClick}>
                        <img
                          src={'/images/dash/icon-5a.png'}
                          alt="icon-5a"
                          className="icon-a"
                        />
                        <img
                          src={'/images/dash/icon-5.png'}
                          alt="icon-5"
                          className="icon-hover"
                        />
                        Jobs
                      </Link>
                    </li>
                    <li className={router.pathname == '/staff/applicants' ? 'active' : ''}>
                      <Link href="/staff/applicants" onClick={handleMenuItemClick}>
                        <img
                          src={'/images/dash/icon-7a.png'}
                          alt="icon-7a"
                          className="icon-a"
                        />
                        <img
                          src={'/images/dash/icon-7.png'}
                          alt="icon-7"
                          className="icon-hover"
                        />
                        Applications
                      </Link>
                    </li>
                    <li className={router.pathname == '/staff/messages' ? 'active' : ''}>
                      <a href="/staff/messages" onClick={handleMenuItemClick}>
                        <img
                          src={'/images/dash/icon-1a.png'}
                          alt="icon-1a"
                          className="icon-a"
                        />
                        <img
                          src={'/images/dash/icon-1.png'}
                          alt="icon-1"
                          className="icon-hover"
                        />
                        Messages
                      </a>
                    </li>
                    {user && (
                      <li className="nav-item">
                        <div className='d-block d-md-none'>
                          <Popover content={<AuthUserMenu />} trigger={['click']}>
                            <a
                              className={
                                router.pathname == '/auth/login'
                                  ? 'nav-link active dropdown-toggle single-menu-space'
                                  : 'nav-link dropdown-toggle single-menu-space'
                              }
                              id="dropdownMenuButton1"
                              data-bs-toggle="dropdown"
                              aria-expanded="false"
                              style={{ cursor: 'pointer' }}>
                              {user.name}
                            </a>
                          </Popover>
                        </div>
                      </li>
                    )}
                  </ul>
                  <a href="#">
                    <button className="btn-a primary-size-16 btn-bg-0055BA mr-1 tab-w-100 dask-none m-none">
                      Upgrade
                    </button>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </nav>
      </header>
    </>
  );
}
