import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic'
import { useRouter } from "next/router";
import PopupModal from '../../../components/Common/PopupModal';
import { removeToken, removeStorageData, getToken, getCurrentUserData } from "../../../lib/session";
import { getCurrentUserDetails, savePlanPayment, getCurrentUserPaymentDetails, getNotifications, changeReadUnreadStatus, getReadNotifications } from '../../../lib/frontendapi';
import { useSession, signOut } from 'next-auth/react';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Swal from 'sweetalert2';
import moment from 'moment';
import Image from 'next/image';


export default function Header() {
    const [sessionstatus, setSession] = useState("");
    const [userProfileImage, setUserProfileImage] = useState('');
    const { data: session } = useSession();
    const [current_user_id, setCurrentUserId] = useState(false);
    const [current_user_name, setCurrentUserName] = useState('');
    const [current_user_role, setCurrentUserRole] = useState('');
    const [notifications, setNotifications] = useState([]);
    const [readNotifications, setReadNotifications] = useState([]);
    const router = useRouter();
    const [belliconshow, setBellIconShow] = useState(false);
    const [userPaymentId, setUserPaymentId] = useState('');
    const [currentUserData, setCurrentUserData]: any = useState([]);
    const [currentUserName, setCurrentUsername] = useState('');
    const showBellIconOnclick = (showVal: any) => {
        setBellIconShow(showVal);
    }

    const [belliconshow2, setBellIconShow2] = useState(false);
    const showBellIconOnclick2 = (showVal: any, status: any) => {
        setBellIconShow2(showVal);
        if (status == 'read') {
            const current_user_data: any = getCurrentUserData();
            const data = {
                notify_to: current_user_data.id
            }
            changeReadUnreadStatus(data)
                .then(res => {
                    if (res.status == true) {
                        console.log(res);
                    } else {

                    }
                })
                .catch(err => {
                    toast.error(err, {
                        position: toast.POSITION.TOP_RIGHT,
                        toastId: 'error',
                    });
                });
        } else {

        }
    }
    const [modalConfirm, setModalConfirm] = useState(false);
    const modalConfirmOpen = () => {
        setModalConfirm(true);
    }
    const modalConfirmClose = () => {
        setModalConfirm(false);
    }
    function redirectToLogin() {
        window.location.href = '/auth/login';
    }
    function handleLogout(e: any) {
        e.preventDefault();
        //removeToken();
        removeStorageData();
        redirectToLogin();
    }
    function handleLogouttwo() {
        //removeToken();
        removeStorageData();
        signOut({ redirect: false });
        router.push('/');
    }
    //Function to check if a JWT token has expired
    // function isTokenExpired(token_exp:any) {
    //     const currentTime = Math.floor(Date.now() / 1000); // Get the current time in seconds
    //     const tokenExp = token_exp; // Assuming the token payload has an 'exp' field
    //     return tokenExp < currentTime;
    // }

    //Check if the token is expired when the page loads
    // const authtoken:any = getToken();
    // let token_exp = '';
    // if (authtoken && authtoken.exp) {
    //     token_exp = authtoken.exp;
    // }
    // if (isTokenExpired(token_exp)) {
    //     // Redirect the user to the login page
    //     if(typeof window !== 'undefined'){
    //         window.location.href = '/auth/login';
    //     }
    // }
    useEffect(() => {
        if (session) {
            setSession("ok");
        }
        const current_user_data: any = getCurrentUserData();
        current_user_data.username ? setCurrentUserName(current_user_data.username) : setCurrentUserName('');
        current_user_data.role ? setCurrentUserRole(current_user_data.role) : setCurrentUserRole('');
        current_user_data.id ? setCurrentUserId(true) : setCurrentUserId(false);
        getCurrentUserDetails(current_user_data.id)
            .then(res => {
                if (res.status == true) {
                    setUserProfileImage(res.user.profile_image);
                    setCurrentUserData(res.user);
                    setCurrentUsername(res.user.name);
                } else {
                    setUserProfileImage('');
                    setCurrentUserData([]);
                    setCurrentUsername('');
                }
            })
            .catch(err => {
                console.log(err);
            });
        getCurrentUserPaymentDetails(current_user_data.id)
            .then(res => {
                if (res.status == true) {
                    setUserPaymentId(res.data.id);
                } else {
                    setUserPaymentId('');
                }
            })
            .catch(err => {
                console.log(err);
            });
        const datas = {
            notify_to: current_user_data.id
        }
        getNotifications(datas)
            .then(res => {
                if (res.status == true) {
                    setReadNotifications(res.data);
                } else {
                    setReadNotifications([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
        const data = {
            notify_to: current_user_data.id
        }
        getReadNotifications(data)
            .then(res => {
                if (res.status == true) {
                    setNotifications(res.data);
                } else {
                    setNotifications([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
    }, [session]);
    let username_letter = currentUserName.split(/\s/).reduce((response: any, word: any) => response += word.slice(0, 1), '');
    const handleClickSubmitPament = (e: any, amount: any) => {
        const current_user_data: any = getCurrentUserData();
        const data = {
            user_id: current_user_data.id,
            company_id: current_user_data.company_id,
            amount: amount
        }
        savePlanPayment(data)
            .then(res => {
                if (res.status == true) {
                    toast.success(res.message, {
                        position: toast.POSITION.TOP_RIGHT,
                        toastId: 'success',
                    });
                    modalConfirmClose();
                    Swal.fire({
                        //position: 'top-end',
                        icon: 'success',
                        title: '<strong>Thank You</strong>',
                        text: 'Your payment completed successfully!',
                        showConfirmButton: true,
                        //timer: 1500
                    }).then(function () {
                        // Redirect the user
                        window.location.reload();
                    });
                } else {
                    toast.error(res.message, {
                        position: toast.POSITION.TOP_RIGHT,
                        toastId: 'error',
                    });
                }
            })
            .catch(err => {
                toast.error(err, {
                    position: toast.POSITION.TOP_RIGHT,
                    toastId: 'error',
                });
            });
    }
    return (
        <>
            <header className='head-part mar-bot-75'>
                <nav className="navbar navbar-expand-lg navbar-light  fixed-top bg-fff">
                    <div className="container-fluid full-width">
                        <a className="navbar-brand" href="/"><img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/logo.png'} alt="logo" className='logo-head' /></a>
                        <button className="navbar-toggler collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                            <span className="navbar-toggler-icon">
                                <i className="fa-solid fa-bars"></i>
                                <i className="fa-solid fa-x close-x"></i>
                            </span>
                        </button>
                        <div className="collapse navbar-collapse j-end " id="navbarSupportedContent">
                            <form className="d-flex mobile-single ">
                                <div className='search-in'>
                                    <input className="form-control me-2" type="search" placeholder="Search for jobs, companies or location" aria-label="Search" />
                                    <i className="fa-solid fa-magnifying-glass glass-ser"></i>
                                </div>
                            </form>
                            {userPaymentId
                                ?
                                ''
                                :
                                <button className="btn signup mobile-w-100 mt-1 m-r-l-sp" onClick={modalConfirmOpen}>Upgrade</button>
                            }
                            <PopupModal show={modalConfirm} handleClose={modalConfirmClose} customclass={'modal-xl pricing-pop head-d-none'}>
                                <section className='pricing-part pt-0 pb-2 mt-0'>
                                    <div className='container'>
                                        <h2>Hire Smarter With Our <br /><span> Flexible Pricing Plans</span></h2>
                                        <h3 className='choose-plan'>Choose a plan that’s right for you</h3>
                                        <div className='pay-d-flex mb-4'>
                                            <p>PAY MONTHLY</p>
                                            <label className="switch switch-sp">
                                                <input type="checkbox" />
                                                <span className="slider round"></span>
                                            </label>
                                            <p>PAY YEARLY  <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/save-25.png'} alt="save-25" className='save-25' /> </p>
                                        </div>
                                        <div className='row mt-5'>
                                            <div className='col-lg-4 col-md-6'>
                                                <div className='plan-box'>
                                                    <h4>Free</h4>
                                                    <p>Ideal for small businesses or startups with limited hiring needs and a tight budget.</p>
                                                    <h3><small>AED</small> 0 <sup>/ Month</sup></h3>
                                                    <button className='started-part mt-3 mb-3' onClick={(e: any) => handleClickSubmitPament(e, '0')}>Get Started Now</button>
                                                    <ul className='check-close'>
                                                        <li><i className="fa-solid fa-check"></i> Lorem ipsum dolor sit amet consectetur.</li>
                                                        <li><i className="fa-solid fa-check"></i> Lorem ipsum dolor sit amet consectetur.</li>
                                                        <li className='dis'><i className="fa-solid fa-xmark"></i> Lorem ipsum dolor sit amet consectetur.</li>
                                                        <li className='dis'><i className="fa-solid fa-xmark"></i> Lorem ipsum dolor sit amet consectetur.</li>
                                                        <li className='dis'><i className="fa-solid fa-xmark"></i> Lorem ipsum dolor sit amet consectetur.</li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <div className='col-lg-4 col-md-6'>
                                                <div className='plan-box blue-plan'>
                                                    <h4>Professional</h4>
                                                    <p>Ideal for businesses with specific hiring needs and advanced search features.</p>
                                                    <h3><small>AED</small> 25 <sup>/ Month</sup></h3>
                                                    <button className='started-part mt-3 mb-3' onClick={(e: any) => handleClickSubmitPament(e, '25')}>Get Started Now</button>
                                                    <ul className='check-close'>
                                                        <li><i className="fa-solid fa-check"></i> Lorem ipsum dolor sit amet consectetur.</li>
                                                        <li><i className="fa-solid fa-check"></i> Lorem ipsum dolor sit amet consectetur.</li>
                                                        <li><i className="fa-solid fa-check"></i>  Lorem ipsum dolor sit amet consectetur.</li>
                                                        <li className='dis'><i className="fa-solid fa-xmark"></i> Lorem ipsum dolor sit amet consectetur.</li>
                                                        <li className='dis'><i className="fa-solid fa-xmark"></i> Lorem ipsum dolor sit amet consectetur.</li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <div className='col-lg-4 col-md-6'>
                                                <div className='plan-box'>
                                                    <h4>Enterprise</h4>
                                                    <p>Ideal for large organizations with extensive hiring needs and dedicated support.</p>
                                                    <h3><small>AED</small>100 <sup>/ Month</sup></h3>
                                                    <button className='started-part mt-3 mb-3' onClick={(e: any) => handleClickSubmitPament(e, '100')}>Get Started Now</button>
                                                    <ul className='check-close'>
                                                        <li><i className="fa-solid fa-check"></i> Lorem ipsum dolor sit amet consectetur.</li>
                                                        <li><i className="fa-solid fa-check"></i> Lorem ipsum dolor sit amet consectetur.</li>
                                                        <li><i className="fa-solid fa-check"></i>  Lorem ipsum dolor sit amet consectetur.</li>
                                                        <li><i className="fa-solid fa-check"></i> Lorem ipsum dolor sit amet consectetur.</li>
                                                        <li><i className="fa-solid fa-check"></i> Lorem ipsum dolor sit amet consectetur.</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </section>
                            </PopupModal>
                            <div className='dask-tab-mobile-d-flex'>
                                <p className='head-icon notifications'>
                                    {belliconshow2
                                        ?
                                        <>
                                            <a href="#" onClick={(e) => showBellIconOnclick2(false, '')}><i className="fa-solid fa-bell fill-bell"></i><span className='round-bell'></span></a>
                                            <div className='box-noti'>
                                                <div className='bell-box'>
                                                    <div className='row'>
                                                        <div className='col-7'>
                                                            <h4 className='not-title'>Notifications</h4>
                                                        </div>
                                                        <div className='col-5'><p className='mark-as'>Mark as Read</p></div>
                                                    </div>
                                                    {readNotifications.length > 0
                                                        ?
                                                        readNotifications.slice(0, 3).map((read_notification_data: any, index: any) => {
                                                            return (
                                                                <div className='row mt-4' key={index}>
                                                                    <div className='col-2 pr-0'>
                                                                        <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/Avatars-3.png'} alt="Avatars-3" className='w-24' />
                                                                    </div>
                                                                    <div className='col-10 text-left pl-0'>
                                                                        <p className='f-16' dangerouslySetInnerHTML={{ __html: read_notification_data.notification }}></p>
                                                                        <p className='f-12'>{moment.utc(read_notification_data.created_at).local().startOf('seconds').fromNow()}</p>
                                                                    </div>
                                                                </div>
                                                            )
                                                        })
                                                        :
                                                        ''
                                                    }
                                                    <p><a href="/staff/notifications" className='view-all'>View All</a></p>
                                                </div>
                                            </div>
                                        </>
                                        :
                                        <a href="#" onClick={(e) => showBellIconOnclick2(true, 'read')}><i className="fa-regular fa-bell"></i><span className='round-bell'></span></a>
                                    }

                                </p>
                                {userProfileImage
                                    ?
                                    <p className='user-img'>
                                        <img src={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/userprofileImg/' + userProfileImage} alt="Avatars-4" className='w-32' />
                                    </p>
                                    :
                                    <p className='user-img'>
                                        <span className="user_name_letter">{username_letter}</span>
                                    </p>
                                }
                                <div className="dropdown w-400-list ">
                                    <p className="head-icon  dots-three" id="headerdropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i className="fa-solid fa-ellipsis-vertical"></i>
                                    </p>
                                    <ul className="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                        <div className='candidate-box'>
                                            <div className='row'>
                                                <div className='col-sm-3 pr-0'>
                                                    {userProfileImage
                                                        ?
                                                        <img src={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/userprofileImg/' + userProfileImage} alt="Avatars-4" className='w-32' />
                                                        :
                                                        <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/Avatars-1.png'} alt="Avatars-4" />
                                                    }
                                                </div>
                                                <div className='col-sm-9 pl-0'>
                                                    <h6 className='name-user'>{currentUserData.name}</h6>
                                                    {/* <p className='interview' style={{"margin":"0px", "textAlign":"left"}}>Ready to Interview</p> */}
                                                </div>
                                            </div>
                                            <ul className='can-list'>
                                                <li><a className="dropdown-item item-2" href="/staff/settings" style={{ "padding": "0px" }}><i className="fa-solid fa-gear"></i> Settings</a></li>
                                                <li><a className="dropdown-item item-2" href="/staff/notifications" style={{ "padding": "0px" }}><i className="fa-regular fa-bell"></i> Notifications</a></li>
                                                <li><a className="dropdown-item item-2" href="#" style={{ "padding": "0px" }}><i className="fa-regular fa-life-ring"></i> Help</a></li>
                                                {sessionstatus == "ok" ? <li className='log-out'><a className="dropdown-item item-2 log-out" href="#" onClick={handleLogouttwo} style={{ "padding": "0px" }}><i className="fa-solid fa-right-from-bracket"></i> Logout</a></li> : <li className='log-out'><a className="dropdown-item item-2" href="#" onClick={(e) => handleLogout(e)} style={{ "padding": "0px" }}><i className="fa-solid fa-right-from-bracket"></i> Logout</a></li>}
                                            </ul>
                                        </div>
                                    </ul>
                                </div>
                                {/*<p className='head-icon  dots-three'>
                            <a href="#"><i className="fa-solid fa-ellipsis-vertical"></i></a>
                            </p>*/}
                            </div>
                        </div>
                    </div>
                </nav>
                <ToastContainer />
            </header>
        </>
    )
}
