import React, {useState, useContext} from 'react';
import {useRouter} from 'next/router';
import {updateJobStatus} from '../../../lib/employeeapi';
import Link from 'next/link';
import Image from 'next/image';
import AuthContext from '@/Context/AuthContext';
import UserProfileImage from '@/components/Common/UserProfileImage';
import {message} from 'antd';

export default function Sidebar() {
  const router = useRouter();
  const {user} = useContext(AuthContext);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [jobStatus, setJobStatus] = useState(user?.jobStatus);

  const handleJobStatusUpdate = (e: any, status: any) => {
    e.preventDefault();
    if (user?.id) {
      const data = {
        id: user?.id,
        value: status,
      };
      updateJobStatus(data)
        .then(() => {
          setIsDropdownOpen(false);
          setJobStatus(status);
          message.success('Saved');
        })
        .catch(error => {
          console.error(error);
        });
    }
  };

  return (
    <>
      <div className="left-bar tab-none">
        <div className="text-center mt-4">
          <div className="dash-profile-img mb-2 m-auto">
            <div className="pro-diamond">
              <UserProfileImage user={user} showModel={false} company={user?.company} />
            </div>
          </div>
          <h4 className="name-text">{user?.name}</h4>
          <h5 className="roll">{user?.current_position && user?.current_position}</h5>
          <ul className="side-menu-left mt-4 tab-none">
            <li className={router.pathname == '/staff/dashboard' ? 'active' : ''}>
              <Link href="/staff/dashboard">
                <img
                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/default/dash-icon-1.svg'}
                  alt="icon-4a"
                  className="icon-a"
                />
                <img
                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/selected/dash-icon-1.svg'}
                  alt="icon-4"
                  className="icon-hover"
                />
                Home
              </Link>
            </li>

            <li
              className={router.pathname == '/staff/jobs' || router.pathname == '/staff/jobs/editjobs' ? 'active' : ''}>
              <Link href="/staff/jobs">
                <img
                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/default/dash-icon-4.svg'}
                  alt="icon-4a"
                  className="icon-a"
                />
                <img
                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/selected/dash-icon-4.svg'}
                  alt="icon-4"
                  className="icon-hover"
                />
                Jobs
              </Link>
            </li>

            <li className={router.pathname == '/staff/candidates' ? 'active' : ''}>
              <Link href="/staff/candidates">
                <img
                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/default/dash-icon-2.svg'}
                  alt="icon-4a"
                  className="icon-a"
                />
                <img
                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/selected/dash-icon-2.svg'}
                  alt="icon-4"
                  className="icon-hover"
                />{' '}
                Candidates
              </Link>
            </li>

            <li className={router.pathname == '/staff/applicants' ? 'active' : ''}>
              <Link href="/staff/applicants">
                <img
                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/default/dash-icon-5.svg'}
                  alt="icon-7a"
                  className="icon-a"
                />
                <img
                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/selected/dash-icon-5.svg'}
                  alt="icon-7"
                  className="icon-hover"
                />
                Applications
              </Link>
            </li>
            <li className={router.pathname == '/staff/messages' ? 'active' : ''}>
              <Link href="/staff/messages">
                <img
                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/default/dash-icon-6.svg'}
                  alt="icon-1a"
                  className="icon-a"
                />
                <img
                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/selected/dash-icon-6.svg'}
                  alt="icon-1"
                  className="icon-hover"
                />
                Messages
              </Link>
            </li>
          </ul>
        </div>
      </div>
    </>
  );
}
