import React, { useState, useEffect } from 'react';
import { getCurrentUserData } from "../../../lib/session";
import { getAllEmployerReceiverMessages, getAllInterviews, getAllEmployerReceiverArchivedMessages, updateMessagesReadUnReadStatus, getTotalMessageUnReadCount } from '../../../lib/frontendapi';
import 'react-toastify/dist/ReactToastify.css';
import Link from 'next/link';
import { useRouter } from 'next/router';
import moment from 'moment';
import Image from 'next/image';

export default function Inbox() {
    const [messagesData, setMessagesData]: any = useState([]);
    const router = useRouter();
    const [archivedMessagesData, setArchivedMessagesData]: any = useState([]);
    const [interviews, setInterviews] = useState([]);
    const [totalUnReadMessageCount, setTotalUnReadMessageCount] = useState(0);
    const current_user_data: any = getCurrentUserData();
    useEffect(() => {
        const current_user_data: any = getCurrentUserData();
        getAllEmployerReceiverMessages(current_user_data.id)
            .then(res => {
                if (res.status == true) {
                    setMessagesData(res.data);
                } else {
                    setMessagesData([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
        const data = {
            user_id: current_user_data.id
        }
        getAllInterviews(data)
            .then(res => {
                if (res.status == true) {
                    setInterviews(res.data);
                } else {
                    setInterviews([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
        getAllEmployerReceiverArchivedMessages(current_user_data.id)
            .then(res => {
                if (res.status == true) {
                    setArchivedMessagesData(res.data);
                } else {
                    setArchivedMessagesData([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
        const message_data = {
            sender_id: current_user_data.id
        }
        getTotalMessageUnReadCount(message_data)
            .then(res => {
                if (res.status == true) {
                    setTotalUnReadMessageCount(res.total_unread_message_count);
                } else {
                    setTotalUnReadMessageCount(0);
                }
            })
            .catch(err => {
                console.log(err);
            });

    }, []);
    const handleClickMessageStatusUpdate = (e: any, sender_id: any) => {
        const current_user_data: any = getCurrentUserData();
        const data = {
            sender_id: current_user_data.id,
            receiver_id: sender_id
        }
        updateMessagesReadUnReadStatus(data)
            .then(res => {
                if (res.status == true) {
                    // toast.success(res.message, {
                    //     position: toast.POSITION.TOP_RIGHT,
                    //     closeButton: true,
                    //     hideProgressBar: false,
                    //     style: {
                    //         background: '#dcf2ea',
                    //         color: '#0c5a14',
                    //         "--toastify-icon-color-success": "#3D9F79",
                    //         maxWidth: "300px",
                    //         padding: 0,
                    //         fontSize: "15px",
                    //         fontFamily: "var(--opensans-font)",
                    //     } as React.CSSProperties,
                    //     progressStyle: {
                    //         background: '#dcf2ea',
                    //     },
                    // });
                    router.push("/staff/messages/inbox/" + sender_id);
                } else {
                    // toast.error(res.message, {
                    //     position: toast.POSITION.TOP_RIGHT,
                    //     closeButton: true,
                    //     hideProgressBar: false,
                    //     style: {
                    //         background: '#ffe6e6',
                    //         color: '#d04e4f',
                    //         maxWidth: "300px",
                    //         padding: 0,
                    //         margin: 0,
                    //         fontSize: "15px",
                    //         fontFamily: "var(--opensans-font)",
                    //         paddingRight: "10px",
                    //         paddingLeft: "10px",
                    //         },
                    //     progressStyle: {
                    //         background: '#ffe6e6',
                    //     },
                    //     icon: () => <i className="fa-solid fa-circle-xmark" style={{ color: "#d04e4f", fontSize: "16px", }}></i>,
                    // });
                    router.push("/staff/messages/inbox/" + sender_id);
                }
            })
            .catch(err => {
                console.log(err);
            });
    }
    return (
        <>

            <div className="dash-right">
                <h1>Messages</h1>
                <div className='row '>
                    <div className='col-sm-9'>
                        <ul className='list-loc m-m-0 mt-4'>
                            <li className='active'>
                                <Link href="/staff/messages">Inbox <span className="tab-span-sa c-0070F5">{messagesData.length}</span></Link>
                            </li>
                            <li>
                                <Link href="/staff/messages/interviews">Interviews <span className="tab-span-sa">{interviews.length}</span></Link>
                            </li>
                            <li>

                                <Link href="/staff/messages/archived">Archived <span className="tab-span-sa">{archivedMessagesData.length}</span></Link>
                            </li>
                        </ul>
                    </div>
                    <div className="col-sm-3">
                        {/* <ul className='blue-text-line mt-4 text-right'>
                            <li><a href="/staff/messages/emptyinbox">Empty Inbox</a></li>
                        </ul> */}
                    </div>
                </div>
                <div className='data-management m-p-10'>
                    <div className='work-experience-fieild m-p-10'>
                        <div className='row'>
                            <div className='col-sm-6'>
                                <p className='f-22 m-center'>Inbox</p>
                            </div>
                            <div className='col-sm-6 text-right'>
                                <p className='f-16 c-BABABA m-center'>
                                    You have {messagesData.length} {messagesData.length !== 1 ? 'chats' : 'chat'} and {totalUnReadMessageCount} unread {totalUnReadMessageCount !== 1 ? 'messages' : 'message'}
                                </p>
                            </div>
                        </div>
                        {messagesData.length > 0
                            ?
                            messagesData.map((messages_data: any, index: any) => {
                                let file_url = '';
                                if (messages_data.attachment_path) {
                                    file_url = process.env.NEXT_PUBLIC_IMAGE_URL + 'images/messageAttachmentFile/' + messages_data.attachment_path;
                                }
                                const filename = file_url.substring(file_url.lastIndexOf('/') + 1);
                                const extension = filename.split('.').pop();
                                return (
                                    messages_data.message_status == 'unread'
                                        ?
                                        <div className='box-text-img bg-CFE5FF  mb-2' key={index}>
                                            <div className='row'>
                                                <div className='col-sm-1 text-center'>
                                                    {messages_data.candidate_profile_image
                                                        ?
                                                        <img src={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/userprofileImg/' + messages_data.candidate_profile_image} alt="Avatars-1" className="w-40" />
                                                        :
                                                        <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/Avatars-1.png'} alt="Avatars-1" className="w-40" />
                                                    }
                                                </div>
                                                <div className='col-sm-9'>
                                                    {
                                                        messages_data.receiver_id == current_user_data.id
                                                            ?
                                                            messages_data.user_role == 'admin'
                                                                ?
                                                                <a href="#" onClick={(e: any) => handleClickMessageStatusUpdate(e, messages_data.sender_id)}><p className='f-16 c-4D4D4D w-700 '>Talent Point</p></a>
                                                                :
                                                                messages_data.user_role == 'staff'
                                                                    ?
                                                                    <a href="#" onClick={(e: any) => handleClickMessageStatusUpdate(e, messages_data.sender_id)}><p className='f-16 c-4D4D4D w-700 '>{messages_data.candidate_name}</p></a>
                                                                    :
                                                                    <a href="#" onClick={(e: any) => handleClickMessageStatusUpdate(e, messages_data.sender_id)}><p className='f-16 c-4D4D4D w-700 '>{messages_data.candidate_name}.{messages_data.candidate_position}</p></a>
                                                            :
                                                            messages_data.user_role == 'admin'
                                                                ?
                                                                <a href="#" onClick={(e: any) => handleClickMessageStatusUpdate(e, messages_data.receiver_id)}><p className='f-16 c-4D4D4D w-700 '>Talent Point</p></a>
                                                                :
                                                                messages_data.user_role == 'staff'
                                                                    ?
                                                                    <a href="#" onClick={(e: any) => handleClickMessageStatusUpdate(e, messages_data.receiver_id)}><p className='f-16 c-4D4D4D w-700 '>{messages_data.candidate_name}</p></a>
                                                                    :
                                                                    <a href="#" onClick={(e: any) => handleClickMessageStatusUpdate(e, messages_data.receiver_id)}><p className='f-16 c-4D4D4D w-700 '>{messages_data.candidate_name}.{messages_data.candidate_position}</p></a>
                                                    }
                                                    {/* <p className='f-16 w-600'>{messages_data.message_description}</p> */}
                                                    <p className='f-16 w-600'>
                                                        {messages_data.message_description ? (
                                                            messages_data.message_status === 'unread' ? (
                                                                <span className='message2'>{messages_data.message_description}</span>
                                                            ) : (
                                                                <span className='message1'>{messages_data.message_description}</span>
                                                            )
                                                        ) : messages_data.attachment_path ? (
                                                            extension == 'pdf' || extension == 'xlxs' || extension == 'xlx' || extension == 'docx' || extension == 'doc' ? (
                                                                <a href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${messages_data.attachment_path}`} target='_blank' style={{ "fontSize": "14px" }}><i className="fa-solid fa-file" style={{ "fontSize": "16px" }}></i> {messages_data.attachment_path}</a>
                                                            ) :
                                                                <a href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${messages_data.attachment_path}`} target='_blank' style={{ "fontSize": "14px" }}><i className="fa-solid fa-image" style={{ "fontSize": "16px" }}></i> {messages_data.attachment_path}</a>
                                                        ) : (
                                                            ''
                                                        )
                                                        }
                                                    </p>
                                                </div>
                                                <div className='col-sm-2 text-right'>
                                                    <p className='f-16 c-4D4D4D'>{moment.utc(messages_data.created_at).local().startOf('seconds').fromNow()}</p>
                                                </div>
                                            </div>
                                        </div>
                                        :
                                        <div className='box-text-img  mb-2' key={index}>
                                            <div className='row'>
                                                <div className='col-sm-1 text-center'>
                                                    {messages_data.candidate_profile_image
                                                        ?
                                                        <img src={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/userprofileImg/' + messages_data.candidate_profile_image} alt="Avatars-1" className="w-40" />
                                                        :
                                                        <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/Avatars-1.png'} alt="Avatars-1" className="w-40" />
                                                    }
                                                </div>
                                                <div className='col-sm-9'>
                                                    {
                                                        messages_data.receiver_id == current_user_data.id
                                                            ?
                                                            messages_data.user_role == 'admin'
                                                                ?
                                                                <a href="#" onClick={(e: any) => handleClickMessageStatusUpdate(e, messages_data.sender_id)}><p className='f-16 c-4D4D4D w-700 '>Talent Point</p></a>
                                                                :
                                                                messages_data.role == 'staff'
                                                                    ?
                                                                    <a href="#" onClick={(e: any) => handleClickMessageStatusUpdate(e, messages_data.sender_id)}><p className='f-16 c-4D4D4D w-700 '>{messages_data.candidate_name}</p></a>
                                                                    :
                                                                    <a href="#" onClick={(e: any) => handleClickMessageStatusUpdate(e, messages_data.sender_id)}><p className='f-16 c-4D4D4D w-700 '>{messages_data.candidate_name}.{messages_data.candidate_position}</p></a>
                                                            :
                                                            messages_data.user_role == 'admin'
                                                                ?
                                                                <a href="#" onClick={(e: any) => handleClickMessageStatusUpdate(e, messages_data.receiver_id)}><p className='f-16 c-4D4D4D w-700 '>Talent Point</p></a>
                                                                :
                                                                messages_data.role == 'staff'
                                                                    ?
                                                                    <a href="#" onClick={(e: any) => handleClickMessageStatusUpdate(e, messages_data.receiver_id)}><p className='f-16 c-4D4D4D w-700 '>{messages_data.candidate_name}</p></a>
                                                                    :
                                                                    <a href="#" onClick={(e: any) => handleClickMessageStatusUpdate(e, messages_data.receiver_id)}><p className='f-16 c-4D4D4D w-700 '>{messages_data.candidate_name}.{messages_data.candidate_position}</p></a>
                                                    }
                                                    {/* <p className='f-16 w-600'>{messages_data.message_description}</p> */}
                                                    <p className='f-16 w-600'>
                                                        {messages_data.message_description ? (
                                                            messages_data.message_status === 'unread' ? (
                                                                <span className='message2'>{messages_data.message_description}</span>
                                                            ) : (
                                                                <span className='message1'>{messages_data.message_description}</span>
                                                            )
                                                        ) : messages_data.attachment_path ? (
                                                            extension == 'pdf' || extension == 'xlxs' || extension == 'xlx' || extension == 'docx' || extension == 'doc' ? (
                                                                <a href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${messages_data.attachment_path}`} target='_blank' style={{ "fontSize": "14px" }}><i className="fa-solid fa-file" style={{ "fontSize": "16px" }}></i> {messages_data.attachment_path}</a>
                                                            ) :
                                                                <a href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${messages_data.attachment_path}`} target='_blank' style={{ "fontSize": "14px" }}><i className="fa-solid fa-image" style={{ "fontSize": "16px" }}></i> {messages_data.attachment_path}</a>
                                                        ) : (
                                                            ''
                                                        )
                                                        }
                                                    </p>
                                                </div>
                                                <div className='col-sm-2 text-right'>
                                                    <p className='f-16 c-4D4D4D'>{moment.utc(messages_data.created_at).local().startOf('seconds').fromNow()}</p>
                                                </div>
                                            </div>
                                        </div>

                                )
                            })
                            :
                            <div className='work-experience-fieild m-p-10 text-center'>
                                <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blank-3.png'} alt="blank-3" className='' />
                                <p className='f-22 c-BABABA mb-1'>You don't seem to have any messages</p>
                                <p className='f-18 c-BABABA'>Go to  <a href="#" className='c-0070F5' > Messages</a></p>
                            </div>
                        }
                    </div>
                </div>
                {/* <ToastContainer /> */}
            </div>
        </>
    )
}
