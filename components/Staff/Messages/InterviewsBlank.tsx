import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

export default function InterviewsBlank() {
    return (
        <>
            <div className="dash-right">
                <h1>Messages</h1>
                <div className='row '>
                    <div className='col-sm-12'>
                        <ul className='list-loc m-m-0 mt-4'>
                            <li>
                                <Link href="/employees/messages">Inbox <span className="tab-span-sa">12</span></Link>
                            </li>
                            <li className='active'>
                                <Link href="/employees/messages/interviews">Interviews <span className="tab-span-sa c-0070F5">12</span></Link>
                            </li>
                            <li>
                                <Link href="/employees/messages/archived">Archived <span className="tab-span-sa">12</span></Link>
                            </li>
                        </ul>
                    </div>
                </div>

                <div className='data-management m-p-10'>
                    <div className='work-experience-fieild m-p-10'>
                        <div className='row'>
                            <div className='col-sm-9'>
                                <p className='f-22 m-center c-191919'>Interviews Scheduled</p>
                            </div>
                            <div className='col-sm-3'><button className="download mb-3 w-100"><i className="fa-solid fa-pen-to-square"></i> Availability</button></div>
                        </div>

                        <div className='text-center'>
                            <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blank-4.png'} alt="blank-4" />
                            <p className='f-22 c-BABABA mb-2'>You don't seem to have any interviews</p>
                            <p className='f-18 c-BABABA'>Go to <a href="#" className='c-0055BA'>Jobs</a></p>
                        </div>

                    </div>

                </div>
            </div>
        </>
    )
}
