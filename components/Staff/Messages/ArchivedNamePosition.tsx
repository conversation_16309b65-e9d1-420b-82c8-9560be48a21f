import React, { useState, useEffect, useRef } from 'react';
import { getCurrentUserData } from "../../../lib/session";
import { getAllEmployerSingleUserArchivedMessages, getAllReceiverUserArchivedMessages, sendMessage, updateUnArchivedMessages, getAllEmployerReceiverMessages, getAllInterviews, getAllEmployerReceiverArchivedMessages } from '../../../lib/frontendapi';
import { useForm } from 'react-hook-form';
import 'react-toastify/dist/ReactToastify.css';
import moment from 'moment';
import Link from 'next/link';
import SuccessToast from "../../Common/showSuccessTostrMessage";
import ErrorToast from "../../Common/showErrorTostrMessage";
import Image from 'next/image';

export default function ArchivedNamePosition(props: any) {
    let candidate_user_id = props.userId;
    const chatWindowRef: any = useRef(null);
    const {
        register,
        formState: { errors },
        handleSubmit,
    } = useForm({
        mode: "onChange",
    });
    const [messageDesc, setMessageDesc] = useState('');
    const current_user_data: any = getCurrentUserData();
    const [messagesData, setMessagesData]: any = useState([]);
    const [chatMessagesData, setChatMessagesData]: any = useState([]);
    const [allInboxMessagesData, setAllInboxMessagesData]: any = useState([]);
    const [archivedMessagesData, setArchivedMessagesData]: any = useState([]);
    const [showPopupunsave, setShowPopupunsave] = useState(false);
    const [showPopupunerror, setShowPopupunerror] = useState(false);
    const [showmessage, setShowmessage] = useState([]);

    const [interviews, setInterviews] = useState([]);
    useEffect(() => {
        getAllEmployerSingleUserArchivedMessages(candidate_user_id)
            .then(res => {
                if (res.status == true) {
                    setMessagesData(res.data);
                } else {
                    setMessagesData([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
        refreshChatMessages();
        // setInterval(async () => {
        //     refreshChatMessages();
        // }, 5000);
        getAllEmployerReceiverMessages(current_user_data.id)
            .then(res => {
                if (res.status == true) {
                    setAllInboxMessagesData(res.data);
                } else {
                    setAllInboxMessagesData([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
        const data = {
            user_id: current_user_data.id
        }
        getAllInterviews(data)
            .then(res => {
                if (res.status == true) {
                    setInterviews(res.data);
                } else {
                    setInterviews([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
        getAllEmployerReceiverArchivedMessages(current_user_data.id)
            .then(res => {
                if (res.status == true) {
                    setArchivedMessagesData(res.data);
                } else {
                    setArchivedMessagesData([]);
                }
            })
            .catch(err => {
                console.log(err);
            });

    }, [candidate_user_id]);
    const refreshChatMessages = () => {
        const messages_chat_section = document.getElementById('messages_chat_section');
        let shouldScroll = 0;
        if (messages_chat_section) {
            let shouldScroll = messages_chat_section.scrollTop + messages_chat_section.clientHeight === messages_chat_section.scrollHeight;
        }
        if (!shouldScroll) {
            if (messages_chat_section) {
                messages_chat_section.scrollTop = messages_chat_section.scrollHeight;
            }
        }
        const data = {
            sender_id: current_user_data.id,
            receiver_id: candidate_user_id
        }
        getAllReceiverUserArchivedMessages(data)
            .then(res => {
                if (res.status == true) {
                    setChatMessagesData(res.data);
                } else {
                    setChatMessagesData([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
    }
    // const submitMessageForm = (data:any) => {
    //     const messages_chat_section = document.getElementById('messages_chat_section');
    //     let shouldScroll = 0;
    //     if (messages_chat_section) {
    //         let shouldScroll = messages_chat_section.scrollTop + messages_chat_section.clientHeight === messages_chat_section.scrollHeight;
    //     }
    //     if (!shouldScroll) {
    //         if (messages_chat_section) {
    //             messages_chat_section.scrollTop = messages_chat_section.scrollHeight;
    //         }
    //     }
    //     $(".message_send_filed").val('');
    //     const current_user_data:any = getCurrentUserData();
    //     let applicants_id = $(".hd_applicants_id").val();
    //     let candidate_id = candidate_user_id;
    //     let job_id = $(".hd_job_id").val();
    //     let current_user_id = current_user_data.id;
    //     const datas = {
    //         applicants_id: applicants_id,
    //         candidate_id: candidate_id,
    //         job_id: job_id,
    //         current_user_id: current_user_id,
    //         message_title: null,
    //         message_description: data.message_desc,
    //         attachment_path: null,
    //         message_type: 'applyJob',
    //         message_status: 'unread',
    //     }
    //     sendMessage(datas)
    //     .then(res => {
    //         if(res.status==true){
    //             // toast.success(res.message, {
    //             //     position: toast.POSITION.TOP_RIGHT,
    //             //     toastId: 'success',
    //             // });
    //         } else {
    //             // toast.error(res.message, {
    //             //     position: toast.POSITION.TOP_RIGHT,
    //             //     toastId: 'error',
    //             // });
    //         }
    //     })
    //     .catch(err => {
    //         toast.error(err, {
    //             position: toast.POSITION.TOP_RIGHT,
    //             toastId: 'error',
    //         });
    //     });
    // }
    const handleClickUnArchived = () => {
        const data = {
            sender_id: current_user_data.id,
            receiver_id: candidate_user_id
        }
        updateUnArchivedMessages(data)
            .then(res => {
                if (res.status == true) {
                    setShowmessage(res.message);
                    setShowPopupunsave(true);
                    setTimeout(() => {
                        setShowPopupunsave(false)
                    }, 10000)
                } else {
                    setShowmessage(res.message);
                    setShowPopupunerror(true);
                    setTimeout(() => {
                        setShowPopupunerror(false)
                    }, 10000)
                }
            })
            .catch(err => {
                setShowmessage(err);
                setShowPopupunerror(true);
                setTimeout(() => {
                    setShowPopupunerror(false)
                }, 10000)
            });
    }
    return (
        <>
            <div className="dash-right">
                <h1>My <span className='span-color'> Messages</span></h1>
                <div className='row '>
                    <div className='col-sm-12'>
                        <ul className='list-loc m-m-0 mt-4'>
                            <li>
                                <Link href="/staff/messages">Inbox <span className="tab-span-sa c-0070F5">{allInboxMessagesData.length}</span></Link>
                            </li>
                            <li>
                                <Link href="/staff/messages/interviews">Interviews <span className="tab-span-sa">{interviews.length}</span></Link>
                            </li>
                            <li className='active'>
                                <Link href="/staff/messages/archived">Archived <span className="tab-span-sa">{archivedMessagesData.length}</span></Link>
                            </li>
                        </ul>
                    </div>
                </div>
                <div className='work-experience-fieild m-p-10 mt-2 p-0'>
                    <div className='head-message'>
                        <div className='row'>
                            <div className='col-sm-4 m-center'>
                                <Link href='/staff/messages/archived'><i className="fa-solid fa-arrow-left arrow-icon f-18 c-4D4D4D"></i></Link>
                            </div>
                            <div className='col-sm-4 text-center'>
                                <p className='f-22 c-191919 w-700 mb-2 '>{messagesData.job_title}</p>
                                <p className='f-18 c-0070F5 w-600'>{messagesData.company_name}</p>
                            </div>
                            <div className='col-sm-4 text-right  m-center'>
                                <ul className='list-message'>
                                    <li><a href="#"> <i className="fa-regular fa-flag"></i> Report</a></li>
                                    <li><a href="#" onClick={handleClickUnArchived}><i className="fa-solid fa-file-arrow-down"></i> Unarchive</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div className='body-message'>
                        {/* <div className='search-center-box'>
                            <div className='row'>
                                <div className='col-sm-9'>
                                    <p className='m-0 pt-2'>You applied to this position on {moment(messagesData.applied_date).format("MMMM D, YYYY")}. </p>
                                </div>
                                <div className='col-sm-3 text-right m-center'>
                                    <a href={'/jobs/' + messagesData.job_slug}><button className="btn-a border-primary-size-16 border-0055BA w-100">View Job</button></a>
                                </div>
                            </div>
                        </div> */}
                        <div className='messages_chat_section' id='messages_chat_section' ref={chatWindowRef}>
                            {chatMessagesData.length > 0
                                ?
                                chatMessagesData.map((chat_message_data: any, index: any) => {
                                    let file_url = '';
                                    if (chat_message_data.attachment_path) {
                                        file_url = process.env.NEXT_PUBLIC_IMAGE_URL + 'images/messageAttachmentFile/' + chat_message_data.attachment_path;
                                    }
                                    const filename = file_url.substring(file_url.lastIndexOf('/') + 1);
                                    const extension = filename.split('.').pop();
                                    return (
                                        <>
                                            <div key={index} className={`single_message_section ${chat_message_data.sender_id == current_user_data.id ? 'sender' : 'receiver'}`}>
                                                {chat_message_data.sender_id == current_user_data.id
                                                    ?
                                                    <>
                                                        <div className='row mt-3'>
                                                            <div className='col-sm-6 text-left mt-2'>
                                                                <p className='f-16  open-sans '>{moment(chat_message_data.created_at).format("MMMM D, hh:mmA ")}</p>
                                                            </div>
                                                            <div className='col-sm-6'>
                                                                <p className='f-16  open-sans w-600'> {chat_message_data.sender_name}/{chat_message_data.company_name} &nbsp;{chat_message_data.sender_profile_image ? <img src={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/userprofileImg/' + chat_message_data.sender_profile_image} alt="Avatars-1" className='w-40' /> : <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/Avatars-1.png'} alt="Avatars-1" className='w-40' />}</p>
                                                            </div>
                                                        </div>
                                                        <div className='mt-4 mb-4'>
                                                            {/* <p className='f-16 c-4D4D4D'>{chat_message_data.message_description}</p> */}
                                                            <p className='f-16 c-4D4D4D' style={{ "whiteSpace": "pre-wrap" }}>
                                                                {chat_message_data.message_description ? (
                                                                    chat_message_data.message_description
                                                                ) : chat_message_data.attachment_path ? (
                                                                    extension == 'pdf' || extension == 'xlxs' || extension == 'xlx' || extension == 'docx' || extension == 'doc' ? (
                                                                        <a href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${chat_message_data.attachment_path}`} target='_blank'><i className="fa-solid fa-file" style={{ "fontSize": "85px" }}></i></a>
                                                                    ) :
                                                                        <a href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${chat_message_data.attachment_path}`} target='_blank'><img
                                                                            src={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${chat_message_data.attachment_path}`}
                                                                            alt="attachment" style={{ "width": "200px", "height": "150px", "borderRadius": "10px" }}
                                                                        /></a>
                                                                ) : (
                                                                    ''
                                                                )
                                                                }
                                                            </p>
                                                        </div>
                                                    </>

                                                    :
                                                    <>
                                                        <div className='row mt-3'>
                                                            <div className='col-sm-6'>
                                                                <p className='f-16  open-sans w-600'>{chat_message_data.sender_profile_image ? <img src={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/userprofileImg/' + chat_message_data.sender_profile_image} alt="Avatars-1" className='w-40' /> : <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/Avatars-1.png'} alt="Avatars-1" className='w-40' />}&nbsp; {chat_message_data.sender_name}.{chat_message_data.sender_current_postion}</p>
                                                            </div>
                                                            <div className='col-sm-6 text-right mt-2'>
                                                                <p className='f-16  open-sans '>{moment(chat_message_data.created_at).format("MMMM D, hh:mmA ")}</p>
                                                            </div>
                                                        </div>
                                                        <div className='mt-4 mb-4'>
                                                            {/* <p className='f-16 c-4D4D4D'>{chat_message_data.message_description}</p> */}
                                                            <p className='f-16 c-4D4D4D' style={{ "whiteSpace": "pre-wrap" }}>
                                                                {chat_message_data.message_description ? (
                                                                    chat_message_data.message_description
                                                                ) : chat_message_data.attachment_path ? (
                                                                    extension == 'pdf' || extension == 'xlxs' || extension == 'xlx' || extension == 'docx' || extension == 'doc' ? (
                                                                        <a href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${chat_message_data.attachment_path}`} target='_blank'><i className="fa-solid fa-file" style={{ "fontSize": "85px" }}></i></a>
                                                                    ) :
                                                                        <a href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${chat_message_data.attachment_path}`} target='_blank'><img
                                                                            src={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${chat_message_data.attachment_path}`}
                                                                            alt="attachment" style={{ "width": "200px", "height": "150px", "borderRadius": "10px" }}
                                                                        /></a>
                                                                ) : (
                                                                    ''
                                                                )
                                                                }
                                                            </p>
                                                        </div>
                                                    </>
                                                }
                                            </div>
                                        </>
                                    )
                                })
                                :
                                <div className='mt-4 mb-4'>
                                    <p className='f-16 c-4D4D4D'>No any messages found on this conversation.</p>
                                </div>
                            }
                        </div>
                        <div className='send-fild'>
                            {/* <form className='form-experience-fieild' onSubmit={handleSubmit(submitMessageForm)}>  */}
                            <form className='form-experience-fieild'>
                                <div className='row m-center'>
                                    <input type="hidden" name='hd_job_id' className='hd_job_id' value={messagesData.job_id} />
                                    <input type="hidden" name='hd_applicants_id' className='hd_applicants_id' value={messagesData.applied_id} />
                                    <div className='col-sm-1  col-12 '>
                                        <button className="btn-a border-primary-size-16 border-0055BA p-3 mobile-w-100" disabled><i className="fa-solid fa-paperclip"></i></button>
                                    </div>
                                    <div className='col-sm-9  col-12 '>
                                        <textarea placeholder='Your message goes here...' className='message-send mobile-w-100 mobile-add-sp message_send_filed' {...register('message_desc', { required: true })} onChange={(e: any) => setMessageDesc(e.target.value)} disabled></textarea>
                                        {errors.message_desc && errors.message_desc.type === 'required' && <p className="text-danger" style={{ "textAlign": "left" }}>Message Field is required.</p>}
                                    </div>
                                    <div className='col-sm-2 col-12 text-right'>
                                        <button className="btn-a primary-size-16 btn-bg-0055BA mobile-w-100" type='submit' disabled>Send</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            {showPopupunsave &&
                <SuccessToast message={showmessage} />
            }
            {showPopupunerror &&
                <ErrorToast message={showmessage} />
            }
        </>
    )
}
