import React, { useState, useEffect } from 'react';
import { getCurrentUserData } from "../../../lib/session";
import { getAllInterviews, getSingleInterviews, updateZoomMeetingLink, deleteInterview, updateAvailibility, getAllEmployerReceiverMessages, getAllEmployerReceiverArchivedMessages } from '../../../lib/frontendapi';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import PopupModal from '../../../components/Common/PopupModal';
import Link from 'next/link';
import moment from 'moment';
import $ from 'jquery';
import SuccessToast from "../../Common/showSuccessTostrMessage";
import ErrorToast from "../../Common/showErrorTostrMessage";
import Image from 'next/image';

interface SwalOptions {
    title?: string;
    text?: string;
    icon?: string;
    dangerMode?: boolean;
    buttons?: string[];
    confirmButtonColor?: string;
}
export default function Interviews() {
    const [interviews, setInterviews] = useState([]);
    const [zoomMeetingPopupInterviewId, setZoomMeetingPopupInterviewId] = useState('');
    const [updateAvalibilityPopupInterviewId, setUpdateAvalibilityPopupInterviewId] = useState('');
    const [singleInterviews, setSingleInterviews]: any = useState([]);
    const [zoomMeetingLink, setZoomMeetingLink] = useState('');
    const [modalUpdateMeetingLinkPopup, setModalUpdateMeetingLinkPopup] = useState(false);
    const [modalUpdateAvailabilityPopup, setModalUpdateAvailabilityPopup] = useState(false);
    const [modalDeleteInterviewPopup, setModalDeleteInterviewPopup] = useState(false);
    const [interviewScheduleDate, setInterviewScheduleDate] = useState('');
    const [interviewScheduleFromTime, setInterviewScheduleFromTime] = useState('');
    const [interviewScheduleToTime, setInterviewScheduleToTime] = useState('');
    const [deleteInterviewId, setDeleteInterviewId] = useState('');
    const [messagesData, setMessagesData]: any = useState([]);
    const [archivedMessagesData, setArchivedMessagesData]: any = useState([]);
    const [showPopupunsave, setShowPopupunsave] = useState(false);
    const [showPopupunerror, setShowPopupunerror] = useState(false);
    const [showmessage, setShowmessage] = useState('');
    const [showmessagestat, setShowmessagestat] = useState('');


    useEffect(() => {
        const current_user_data: any = getCurrentUserData();
        getAllEmployerReceiverMessages(current_user_data.id)
            .then(res => {
                if (res.status == true) {
                    setMessagesData(res.data);
                } else {
                    setMessagesData([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
        const data = {
            user_id: current_user_data.id
        }
        getAllInterviews(data)
            .then(res => {
                if (res.status == true) {
                    setInterviews(res.data);
                } else {
                    setInterviews([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
        getAllEmployerReceiverArchivedMessages(current_user_data.id)
            .then(res => {
                if (res.status == true) {
                    setArchivedMessagesData(res.data);
                } else {
                    setArchivedMessagesData([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
    }, []);
    const modalConfirmDeleteInterviewPopupOpen = (e: any, interview_id: any) => {
        setModalDeleteInterviewPopup(true);
        setDeleteInterviewId(interview_id);
    }
    const modalConfirmDeleteInterviewPopupClose = () => {
        setModalDeleteInterviewPopup(false);
    }
    const modalConfirmUpdateMeetingLinkPopupOpen = (e: any, interview_id: any) => {
        setModalUpdateMeetingLinkPopup(true);
        setZoomMeetingPopupInterviewId(interview_id);
        getSingleInterviews(interview_id)
            .then(res => {
                if (res.status == true) {
                    setZoomMeetingLink(res.data.meeting_link);
                } else {
                    setZoomMeetingLink('');
                }
            })
            .catch(err => {
                console.log(err);
            });
    }
    const modalConfirmUpdateMeetingLinkPopupClose = () => {
        setModalUpdateMeetingLinkPopup(false);
    }
    const submitZoomMeetingLinkUpdateForm = (e: any) => {
        let interview_id = zoomMeetingPopupInterviewId;
        e.preventDefault();
        const data = {
            edit_zoom_meeting_link: zoomMeetingLink
        }
        updateZoomMeetingLink(interview_id, data)
            .then(res => {
                if (res.status == true) {
                    setShowmessage(res.message);
                    setShowPopupunsave(true);
                    setTimeout(() => {
                        setShowPopupunsave(false)
                    }, 10000)
                    modalConfirmUpdateMeetingLinkPopupClose();
                    window.location.reload();
                } else {
                    setShowmessage(res.message);
                    setShowPopupunerror(true);
                    setTimeout(() => {
                        setShowPopupunerror(false)
                    }, 10000)
                }
            })
            .catch(err => {
                setShowmessage(err);
                setShowPopupunerror(true);
                setTimeout(() => {
                    setShowPopupunerror(false)
                }, 10000)
            });
    }
    const deleteSingleInterview = () => {
        deleteInterview(deleteInterviewId)
            .then(res => {
                if (res.status == true) {
                    modalConfirmDeleteInterviewPopupClose();
                    setShowmessage('Your interview has been deleted!');
                    setShowPopupunsave(true);
                    setTimeout(() => {
                        setShowPopupunsave(false)
                    }, 10000)
                    // toast.success('Your interview has been deleted!', {
                    //     position: toast.POSITION.TOP_RIGHT,
                    //     closeButton: true,
                    //     hideProgressBar: false,
                    //     style: {
                    //         background: '#dcf2ea',
                    //         color: '#0c5a14',
                    //         "--toastify-icon-color-success": "#3D9F79",
                    //         maxWidth: "300px",
                    //         padding: 0,
                    //         fontSize: "15px",
                    //         fontFamily: "var(--opensans-font)",
                    //     } as React.CSSProperties,
                    //     progressStyle: {
                    //         background: '#dcf2ea',
                    //     },
                    // });

                    $('#data_' + deleteInterviewId).hide();
                } else {
                }
            })
            .catch(err => {
                setShowmessage(err.message);
                setShowPopupunerror(true);
                setTimeout(() => {
                    setShowPopupunerror(false)
                }, 10000)
            });
        // const options: Partial<SwalOptions> = {
        //     title: "Are you sure?",
        //     text: "You want to delete the interview",
        //     icon: "warning",
        //     dangerMode: true,
        //     buttons: ['Cancel', 'Yes, I am sure!'],
        //     confirmButtonColor: '#062B60',
        // };
        // swal(options)
        // .then((willDelete) => {
        //     if (willDelete) {
        //         deleteInterview(id)
        //         .then(res => {
        //             if(res.status == true){
        //                 swal("Your interview has been deleted!", {
        //                     icon: "success",
        //                 });
        //                 $('#data_'+id).hide();
        //             } else {
        //             }
        //         })
        //         .catch(err => {

        //         });
        //     } else {
        //     }
        // });
    }
    const modalConfirmUpdateAvailabilityPopupOpen = (e: any, interview_id: any) => {
        setModalUpdateAvailabilityPopup(true);
        setUpdateAvalibilityPopupInterviewId(interview_id);
        getSingleInterviews(interview_id)
            .then(res => {
                if (res.status == true) {
                    setInterviewScheduleDate(res.data.interview_schedule_date);
                    setInterviewScheduleFromTime(res.data.interview_schedule_from_time);
                    setInterviewScheduleToTime(res.data.interview_schedule_to_time);
                } else {
                    setInterviewScheduleDate('');
                    setInterviewScheduleFromTime('');
                    setInterviewScheduleToTime('');
                }
            })
            .catch(err => {
                console.log(err);
            });
    }
    const modalConfirmUpdateAvailabilityPopupClose = () => {
        setModalUpdateAvailabilityPopup(false);
    }
    const submitUpdateAvailabilityForm = (e: any) => {
        let interview_id = updateAvalibilityPopupInterviewId;
        e.preventDefault();
        const data = {
            edit_interview_schedule_date: interviewScheduleDate,
            edit_interview_schedule_from_time: interviewScheduleFromTime,
            edit_interview_schedule_to_time: interviewScheduleToTime
        }
        updateAvailibility(interview_id, data)
            .then(res => {
                if (res.status == true) {
                    setShowmessage(res.message);
                    setShowPopupunsave(true);
                    setTimeout(() => {
                        setShowPopupunsave(false)
                    }, 10000)
                    modalConfirmUpdateAvailabilityPopupClose();
                    window.location.reload();
                } else {
                    setShowmessage(res.message);
                    setShowPopupunerror(true);
                    setTimeout(() => {
                        setShowPopupunerror(false)
                    }, 10000)
                }
            })
            .catch(err => {
                setShowmessage(err.message);
                setShowPopupunerror(true);
                setTimeout(() => {
                    setShowPopupunerror(false)
                }, 10000)
            });
    }
    let current_date = new Date().toJSON().slice(0, 10);
    return (
        <>
            <div className="dash-right">
                <h1>My <span className='span-color'>Messages</span></h1>
                <div className='row '>
                    <div className='col-sm-9'>
                        <ul className='list-loc m-m-0 mt-4'>
                            <li>
                                <Link href="/staff/messages">Inbox <span className="tab-span-sa">{messagesData.length}</span></Link>
                            </li>
                            <li className='active'>
                                <Link href="/staff/messages/interviews">Interviews <span className="tab-span-sa c-0070F5">{interviews.length}</span></Link>
                            </li>
                            <li>
                                <Link href="/staff/messages/archived">Archived <span className="tab-span-sa">{archivedMessagesData.length}</span></Link>
                            </li>
                        </ul>
                    </div>
                    <div className="col-sm-3">
                        {/* <ul className='blue-text-line mt-4 text-right'>
                            <li><a href="/staff/messages/emptyinterviews">Empty Interviews</a></li>
                        </ul> */}
                    </div>
                </div>
                <div className='work-experience-fieild m-p-10 mt-2'>
                    <div className='row'>
                        <div className='col-sm-9'>
                            <p className='f-22 m-center c-191919 mt-3'>Interviews Scheduled</p>
                        </div>
                        <div className='col-sm-3 text-right m-center'>
                            {/* <button className="download mb-3 w-100"><i className="fa-solid fa-pen-to-square"></i> Availability</button> */}
                        </div>
                    </div>
                    <div className='row'>
                        {interviews.length > 0
                            ?
                            interviews.map((interviews_data: any, index: any) => {
                                return (
                                    <div id={`data_${interviews_data.id}`} className='col-sm-6' key={index}>
                                        <div className='p-3 bg-EBF4FF'>
                                            <div className='row'>
                                                <div className='col-sm-3'>
                                                    <div className='bg-D9D9D9 work-senior p-2 text-center'>
                                                        <p className='f-12 c-2C2C2C w-700 mb-sp'>{moment(interviews_data.interview_schedule_date).format('MMMM')}</p>
                                                        <h3 className='f-54 c-191919 mt-3 mb-3'>{moment(interviews_data.interview_schedule_date).format('D')}</h3>
                                                        <p className='f-12 c-2C2C2C w-700  mb-sp'>{moment(interviews_data.interview_schedule_date).format('dddd')}</p>
                                                    </div>
                                                </div>
                                                <div className='col-sm-6'>
                                                    <div className='Senior-div work-senior'>
                                                        <p className='f-18 c-191919'>{interviews_data.job_title}</p>
                                                        <p className='f-16 c-0070F5 w-600'>{interviews_data.company_name}</p>
                                                        <p className='f-12 c-2C2C2C mb-sp mt-sp-add w-600'><i className="fa-regular fa-clock icon-w-12"></i> {moment(interviews_data.interview_schedule_from_time, 'hh:mm A').format('hh:mm A')} - {moment(interviews_data.interview_schedule_to_time, 'hh:mm A').format('hh:mm A')}</p>
                                                        <p className='f-12 c-2C2C2C mb-sp mt-sp-add w-600'>
                                                            <i className="fa-regular fa-file icon-w-12"></i> Here’s your interview link:<br />
                                                            <a href={interviews_data.meeting_link} className='c-0070F5 ' target='_blank'> {interviews_data.meeting_link} </a>
                                                            &nbsp; <a href="#" className='c-0070F5 ' onClick={(e: any) => modalConfirmUpdateMeetingLinkPopupOpen(e, interviews_data.id)}>Edit <i className="fa-solid fa-pencil"></i></a>
                                                        </p>
                                                    </div>
                                                </div>
                                                <div className='col-sm-3'>
                                                    <div className='Senior-div work-senior'>
                                                        {interviews_data.interview_status == "accepted" && (
                                                            <p className='bg-3D9F79-app w-100 w-700 border-radius-4 text-center' style={{ "padding": "8px 8px" }}>Accepted</p>
                                                        )}
                                                        {interviews_data.interview_status == "rejected" && (
                                                            <p className='bg-D04E4F-app w-100 w-700 border-radius-4 text-center' style={{ "padding": "8px 8px" }}>Rejected</p>
                                                        )}
                                                        {interviews_data.interview_status == "scheduled" && (
                                                            <p className='bg-3D9F79-app w-100 w-700 border-radius-4 text-center' style={{ "padding": "8px 8px" }}>Scheduled</p>
                                                        )}
                                                        {interviews_data.interview_status == "canceled" && (
                                                            <p className='bg-D04E4F-app w-100 w-700 border-radius-4 text-center' style={{ "padding": "8px 8px" }}>Canceled</p>
                                                        )}
                                                        {interviews_data.interview_status == "completed" && (
                                                            <p className='bg-3D9F79-app w-100 w-700 border-radius-4 text-center' style={{ "padding": "8px 8px" }}>Completed</p>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                            <div className='row mt-3'>
                                                <div className='col-sm-4'>
                                                    <button className="download mb-3 w-100" onClick={(e: any) => modalConfirmDeleteInterviewPopupOpen(e, interviews_data.id)}><i className="fa-solid fa-trash-can"></i> Delete</button>
                                                </div>
                                                <div className='col-sm-8'>
                                                    <button className="btn-a primary-size-16 btn-bg-0055BA w-100" onClick={(e: any) => modalConfirmUpdateAvailabilityPopupOpen(e, interviews_data.id)}><i className="fa-regular fa-calendar"></i> Reschedule</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                )
                            })
                            :
                            <div className='work-experience-fieild m-p-10 text-center'>
                                <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blank-4.png'} alt="blank-4" className='' />
                                <p className='f-22 c-BABABA mb-1'>You don't seem to have any interviews</p>
                                <p className='f-18 c-BABABA'>Go to  <a href="#" className='c-0070F5' > Job</a></p>
                            </div>
                        }
                        <PopupModal show={modalUpdateMeetingLinkPopup} handleClose={modalConfirmUpdateMeetingLinkPopupClose} customclass={'header-remove body-sp-0'}>
                            <div className='popup-body'>
                                <p className='f-22 c-191919 text-left'>Update Interview Zoom Meeting Link</p>
                                <hr className="hr-line"></hr>
                                <form className='form-experience-fieild' onSubmit={submitZoomMeetingLinkUpdateForm}>
                                    <p className="f-12 c-2C2C2C">Interview Zoom Meeting Link:</p>
                                    <input type='text' placeholder='linke.linke.zooooom' className='fild-des' value={zoomMeetingLink} onChange={(e: any) => setZoomMeetingLink(e.target.value)} />
                                    <div className="text-right mt-3">
                                        <div className='row'>
                                            <div className='col-4'>
                                                <button className="cancel  w-100" onClick={modalConfirmUpdateMeetingLinkPopupClose} type='button'>Cancel</button>
                                            </div>
                                            <div className='col-8'>
                                                <button className="save w-100" type='submit'>Update</button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </PopupModal><br /><br />
                        <PopupModal show={modalUpdateAvailabilityPopup} handleClose={modalConfirmUpdateAvailabilityPopupClose} customclass={'   modal-md-size body-sp-0 '}>
                            <div className='popup-body'>
                                {/* <div className="row">
                                    <div className="col-sm-12 text-right">
                                        <button type="button" className="close-x bg-none close-pog" data-bs-dismiss="modal" aria-label="Close"><i className="fa-solid fa-xmark"></i></button>
                                    </div>
                                </div> */}
                                <p className='f-31 c-191919 text-left mb-2'>Availability</p>
                                <p className='f-22'>Mark the days and time you are available to interview.</p>
                                <hr className="hr-line"></hr>
                                <p className='f-16'>You may choose multiple days at once by clicking on them and mark if you are available.</p>
                                <form className='form-experience-fieild' onSubmit={submitUpdateAvailabilityForm}>
                                    <div className='row mt-2'>
                                        <div className='col-sm-7'>
                                            <input type="date" className='big-input' name="edit_schedule_date" id='edit_schedule_date' value={interviewScheduleDate} onChange={(e: any) => setInterviewScheduleDate(e.target.value)} min={current_date} />
                                        </div>
                                        <div className='col-sm-5'>
                                            <p className='f-18 c-0055BA w-600'>Available?</p>
                                            <label className="switch switch-sp mt-2" style={{ "width": "60px" }}>
                                                <input type="checkbox" checked /> <span className="slider round"></span>
                                            </label>
                                            <p className='f-18 c-0055BA w-600 mt-4 mb-2'>From Time</p>
                                            <input type="time" className='big-input' value={interviewScheduleFromTime} onChange={(e: any) => setInterviewScheduleFromTime(e.target.value)} />
                                            <p className='f-18 c-0055BA w-600 mt-4'>To Time</p>
                                            <input type="time" className='big-input' value={interviewScheduleToTime} onChange={(e: any) => setInterviewScheduleToTime(e.target.value)} />
                                        </div>
                                    </div>
                                    <center className='mt-3'>
                                        <button className="btn-a primary-size-18 btn-bg-0055BA" type='submit'>Update</button>
                                    </center>
                                </form>
                            </div>
                        </PopupModal><br /><br />
                        <PopupModal show={modalDeleteInterviewPopup} handleClose={modalConfirmDeleteInterviewPopupClose} customclass={'header-remove body-sp-0'}>
                            <div className='popup-body'>
                                <div className='row'>
                                    <div className='col-12 pr-0'>
                                        <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/alt.png'} alt="alt" />
                                    </div>
                                    <div className='col-12'>
                                        <h5 className='Unsaved-text'>Are you sure?</h5>
                                        <h6 className='discard'>You want to delete the interview?</h6>
                                    </div>
                                </div>
                                <div className="text-right mt-3">
                                    <div className='row'>
                                        <div className='col-4'>
                                            <button className="cancel  w-100" onClick={modalConfirmDeleteInterviewPopupClose} type='button'>Cancel</button>
                                        </div>
                                        <div className='col-8'>
                                            <button className="save w-100" onClick={deleteSingleInterview}>Yes, I am sure!</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </PopupModal><br /><br />
                    </div>
                </div>
                <ToastContainer />
            </div>
            {showPopupunsave &&
                <SuccessToast message={showmessage} />
            }
            {showPopupunerror &&
                <ErrorToast message={showmessage} />
            }

        </>
    )
}
