import React, { useState, useEffect, useContext } from 'react';
import PopupModal from '../../../components/Common/PopupModal';
import moment from 'moment';
import { getStaffUserAllJobs, updateFirstTimePopupStatus, getCurrentUserDetails, getFutureInterviewSchedule, getNotifications, getTotalMessageUnReadCount } from '../../../lib/frontendapi';
import Link from 'next/link'
import AuthContext from '@/Context/AuthContext';
import Image from 'next/image';

export default function Dashboard() {
    const [jobs, setJobs] = useState([]);
    const [futureInterviewsSchedule, setFutureInterviewSchedule] = useState([]);
    const [modalConfirm6, setModalConfirm6] = useState(false);
    const [totalNotifications, setTotalNotifications] = useState([]);
    const [totalUnReadMessageCount, setTotalUnReadMessageCount] = useState('');
    const [cvCount, setStaffCvCount] = useState('');
    const [tooltipVisible, setTooltipVisible] = useState(false);
    const { user } = useContext(AuthContext)


    const toggleTooltip = () => {
        setTooltipVisible(!tooltipVisible);
    };

    interface TooltipProps {
        uniqueKey: number;
    }

    const Tooltip: React.FC<TooltipProps> = ({ uniqueKey }) => {
        const [tooltipVisible, setTooltipVisible] = useState(false);

        const toggleTooltip = () => {
            setTooltipVisible(!tooltipVisible);
        };

        return (
            <span
                className="custom-tooltip-container"
                onMouseEnter={toggleTooltip}
                onMouseLeave={toggleTooltip}
                style={{ position: 'relative', display: 'inline-block' }}
            >
                <i
                    className="fa-solid fa-circle-info c-D9D9D9"

                ></i>
                {/* Tooltip content */}
                {tooltipVisible && (
                    <div
                        className="custom-tooltip"
                        style={{
                            position: 'absolute',
                            top: '150%',
                            left: 'calc(100% + 10px)',
                            transform: 'translateX(-58%)',
                            backgroundColor: '#EBF4FF',
                            color: '#4D4D4D',
                            boxShadow: '0 3px 10px rgb(0 0 0 / 0.2)',
                            width: 'auto',
                            whiteSpace: 'nowrap',
                            borderRadius: '8px',
                            textAlign: 'center',
                            lineHeight: '1.4',
                            fontSize: '12px',
                            padding: '9px',
                            zIndex: 1000,
                        }}
                    >
                        <span>how this feature works in a <br></br>friendly tone & concise manner.</span>
                        <div
                            className="tooltip-arrow"
                            style={{
                                position: 'absolute',
                                top: '-10px',
                                left: '50%',
                                transform: 'translateX(-50%)',
                                borderBottom: '10px solid #EBF4FF',
                                borderLeft: '10px solid transparent',
                                borderRight: '10px solid transparent',
                            }}
                        ></div>
                    </div>
                )}
            </span>
        );
    };

    const modalConfirmOpen6 = () => {
        setModalConfirm6(true);
    }
    const modalConfirmClose6 = () => {
        setModalConfirm6(false);
    }
    const updateFirstTimePopup = () => {

        updateFirstTimePopupStatus(user?.id)
            .then(res => {
                if (res.status == true) {
                    setModalConfirm6(false);
                } else {
                    setModalConfirm6(true);
                }
            })
            .catch(err => {
                console.log(err);
            });
    }
    useEffect(() => {
        const data = {
            user_id: user?.id
        }

        getStaffUserAllJobs(data)
            .then(res => {
                if (res.status == true) {
                    setJobs(res.data);
                } else {
                    setJobs([]);
                }
            })
            .catch(err => {
                console.log(err);
            });

        getCurrentUserDetails(user?.id)
            .then(res => {
                if (res.status == true) {
                    setStaffCvCount(res.user.available_resume_count);
                    if (res.user.first_login == '0') {
                        setTimeout(() => {
                            modalConfirmOpen6();
                        }, 2000);
                    }
                } else {

                }
            })
            .catch(err => {
                console.log(err);
            });

        const noti_data = {
            notify_to: user?.id
        }
        getNotifications(noti_data)
            .then(res => {
                if (res.status == true) {
                    setTotalNotifications(res.data.length);
                } else {
                    setTotalNotifications([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
        getFutureInterviewSchedule(user?.id)
            .then(res => {
                if (res.status == true) {
                    setFutureInterviewSchedule(res.interviews);
                } else {
                    setFutureInterviewSchedule([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
        const message_data = {
            sender_id: user?.id
        }
        getTotalMessageUnReadCount(message_data)
            .then(res => {
                if (res.status == true) {
                    setTotalUnReadMessageCount(res.total_unread_message_count);
                } else {
                    setTotalUnReadMessageCount('');
                }
            })
            .catch(err => {
                console.log(err);
            });
    }, [user]);
    return (
        <>
            <div className="dash-right">
                <h1>Welcome <span className='span-color'>{user?.name && user?.name}</span></h1>
                <div className='row mt-4'>
                    <div className='col-lg-3 col-md-6 col-6'>
                        <Link href="/staff/jobs">
                            <div className='dash-card d-c-1'>
                                <div className='row'>
                                    <div className='col-4'>
                                        <h5 className='dash-card-h5'>{jobs.length}</h5>
                                    </div>
                                    <div className='col-8'>
                                        <div className='text-right'>
                                            <h6>Active Jobs</h6>
                                            <p className='f-12'>View All</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </Link>
                    </div>
                    <div className='col-lg-3 col-md-6 col-6'>
                        <div className='dash-card d-c-2'>
                            <div className='row'>
                                <div className='col-4'>
                                    <h5 className='dash-card-h5'>{cvCount}</h5>
                                </div>
                                <div className='col-8'>
                                    <div className='text-right'>
                                        <h6>Cv Count</h6>
                                        <p className='f-12'>View All</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className='col-lg-3 col-md-6 col-6'>
                        <div className='dash-card d-c-4'>
                            <div className='row'>
                                <div className='col-4'>
                                    <h5 className='dash-card-h5'>{totalUnReadMessageCount}</h5>
                                </div>
                                <div className='col-8'>
                                    <div className='text-right'>
                                        <h6>Messages</h6>
                                        <Link href="/staff/messages"><p className='f-12'>View All</p></Link>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className='col-lg-3 col-md-6 col-6'>
                        <div className='dash-card d-c-3'>
                            <div className='row'>
                                <div className='col-4'>
                                    <h5 className='dash-card-h5'>{totalNotifications}</h5>
                                </div>
                                <div className='col-8'>
                                    <div className='text-right'>
                                        <h6>Notifications</h6>
                                        <Link href="/staff/notifications"><p className='f-12'>View All</p></Link>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <p className="over mt-4 mb-4">Recent Job Posts</p>
                <div className='row'>
                    <div className='col-lg-8 col-md-12'>
                        {jobs.length > 0
                            ?
                            jobs.slice(0, 2).map((jobs_data: any, index: any) => {
                                return (
                                    <div className='filter filter-sp m-center border-r-16 mb-3' key={index}>
                                        <div className='row'>
                                            <div className='col-sm-8'>
                                                <p className='p-18'><a target="_blank" href={'/job/' + jobs_data.job_slug}>{jobs_data.job_title}</a></p>
                                                <p className='p-16 mt-1'><a target="_blank" href={'/companies/' + jobs_data.company_slug}>{jobs_data.company_name}</a></p>
                                                {jobs_data.is_featured == '1' ? <button className='pro'>Featured <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/pro.png'} alt=" pro" className='w-25 ' /></button> : ''}
                                                <ul className='full-time f-12-list'>
                                                    <li><i className="fa-solid fa-business-time"></i>
                                                        {jobs_data.job_type === 'parttime' ? "Part-Time" : jobs_data.job_type === 'fulltime' ? "Full-Time" : jobs_data.job_type === 'contract' ? "Contract" : jobs_data.job_type === 'freelance' ? "Freelance" : " "}{" "}
                                                    </li>
                                                    <li><i className="fa-solid fa-location-dot"></i> {jobs_data.country_name}</li>
                                                    <li>Created: {moment(jobs_data.created_at).format("LL")}</li>
                                                    <li>Deadline: {moment(jobs_data.deadline).format("LL")}</li>
                                                </ul>
                                                <p className='f-18 w-600 mt-2 c-4D4D4D'>Job Insights <Tooltip uniqueKey={index} /></p>
                                                <div className='row mt-3'>
                                                    <div className='col-lg-3 col-md-4'>
                                                        <div className='right-border'>
                                                            <p className='f-12 c-4D4D4D m-sp-0'>Applicants</p>
                                                            <h3 className='f-37 c-2C2C2C'>{jobs_data.jobs_applicants}</h3>
                                                        </div>
                                                    </div>
                                                    <div className='col-lg-4 col-md-4'>
                                                        <div className='right-border'>
                                                            <p className='f-12 c-4D4D4D m-sp-0'>Impressions</p>
                                                            <h3 className='f-37 c-2C2C2C'>{jobs_data.jobViewCountslastweek > jobs_data.jobViewCountslasttwoweek ? <i className="fa-solid fa-arrow-up-long c-3D9F79 up-icon"></i> : <i className="fa-solid fa-arrow-up-long c-3D9F79 up-icon"></i>} {parseInt(jobs_data.jobsImpressionpercentage) + "%"}</h3>
                                                        </div>
                                                    </div>
                                                    <div className='col-lg-3 col-md-4'>
                                                        <div className=''>
                                                            <p className='f-12 c-4D4D4D m-sp-0'>Shortlisted</p>
                                                            <h3 className='f-37 c-2C2C2C'>{jobs_data.shortlisted_jobs_count}</h3>
                                                        </div>
                                                    </div>
                                                    <small className='mt-1'>Posted by : {user?.id == jobs_data.job_created_by_id ? "Me" : jobs_data.job_created_name}</small>
                                                </div>
                                            </div>
                                            <div className='col-sm-4 text-right'>
                                                <div className="dropdown w-400-list ">
                                                    <button className="dropdown-toggle btn-a primary-size-16 btn-bg-0055BA" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i className="fa-solid fa-circle-chevron-down sp-right"></i> Actions
                                                    </button>
                                                    <ul className="dropdown-menu view-right" aria-labelledby="dropdownMenuButton1">
                                                        <li>
                                                            <Link className="dropdown-item item-2" href={'/job/' + jobs_data.job_slug}>
                                                                View public job post
                                                            </Link>
                                                        </li>
                                                        <li>
                                                            <Link className="dropdown-item item-2" href="/staff/applicants">View all applicants</Link>
                                                        </li>
                                                        <li>
                                                            <Link className="dropdown-item item-2" href="/staff/jobs">Manage job post</Link>
                                                        </li>
                                                        <li>
                                                            <Link className="dropdown-item item-2" href="#">Post as featured (a/n)</Link>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                )
                            })

                            :
                            <div className='filter filter-sp m-center border-r-16 p-5'>
                                <div className='row'>
                                    <div className='col-sm-12'>
                                        <p className='p-18 text-center'>Recent Jobs Post Not Found!</p>
                                    </div>
                                </div>
                            </div>
                        }
                        <center><p className='explore-jobs mt-4'>
                            <Link href="/staff/jobs">View All Job Posts</Link>
                        </p></center>
                    </div>
                    <div className='col-lg-4 col-md-12'>
                        <div className='blue-box'>
                            <div className='row'>
                                <div className='col-10'>
                                    <p className='f-26 c-fff w-700'>Interviews</p>
                                    <p className='f-22  c-fff'>Coming up...</p>
                                </div>
                                <div className='col-2 text-center'>
                                    {/* <i className="fa-solid fa-circle-info c-CFE5FF"></i> */}
                                    <Tooltip uniqueKey={-100} />
                                </div >
                            </div>
                            {futureInterviewsSchedule.length > 0
                                ?
                                futureInterviewsSchedule.slice(0, 2).map((interviews_data: any, index: any) => {
                                    return (
                                        <div key={index}>
                                            <div className='bg-D9D9D9 work-senior p-2 text-center mt-2 mb-3'>
                                                <p className='f-12 c-2C2C2C w-700 mb-sp c-0070F5'>{moment(interviews_data.interview_schedule_date).format('MMMM')}</p>
                                                <h3 className='f-54 c-191919 mt-3 mb-3 c-0070F5'>{moment(interviews_data.interview_schedule_date).format('D')}</h3>
                                                <p className='f-12 c-2C2C2C w-700  mb-sp c-0070F5'>{moment(interviews_data.interview_schedule_date).format('dddd')}</p>
                                            </div>
                                            <p className='f-18 c-fff w-600 '>{interviews_data.job_title}</p>
                                            <p className='f-16 w-600 c-fff'>{interviews_data.applicants_name}</p>
                                            <ul className='full-time f-12-list'>
                                                <li><i className="fa-regular fa-clock"></i> {moment(interviews_data.interview_schedule_from_time, 'hh:mm A').format('hh:mm A')} - {moment(interviews_data.interview_schedule_to_time, 'hh:mm A').format('hh:mm A')}</li>
                                            </ul>
                                        </div>
                                    )
                                })
                                :
                                ''
                            }
                            <p className='mb-0 mt-4'>
                                <Link href="/staff/messages/interviews" className='f-16 w-700 c-fff'> View All <i className="fa-solid fa-arrow-right"></i></Link>
                            </p>
                        </div>
                    </div>
                </div>
                <PopupModal show={modalConfirm6} handleClose={modalConfirmClose6} customclass={'add_company_signup_popup modal-lg big-size body-sp-0 b-r-30'} closebtnclass={'d-none close-x mt-2'} closebtnicon={'icon'}>
                    <div className='popup-modal'>
                        <div className='row'>
                            <div className='col-sm-4 popup-right border-left popup-left-2'></div>
                            <div className='col-sm-8'>
                                <div className="popup-left-text p-25 text-center">
                                    <h3>Welcome  <span className="span-color"> to The Talent Point! </span></h3>
                                    <p className='f-22 c-2C2C2C w-500'>The gateway to finding your dream job</p>
                                    <p className='f-18 c-4D4D4D'>Complete your profile to level up your job search and unlock the true potential of our platform.</p>
                                    <button className="btn login  mt-4" onClick={updateFirstTimePopup}>Get Started</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </PopupModal><br /><br />
            </div>
        </>
    )
}
