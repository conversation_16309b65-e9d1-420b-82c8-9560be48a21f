import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
export default function Insights() {
    return (
        <>
            <div className="dash-right">
                <h1>Company <span className='span-color'>Profile</span></h1>
                <div className='row m-column-reverse'>
                    <div className='col-sm-7'>
                        <ul className='list-loc m-m-0 mt-4'>
                            <li>
                                <Link href="/staff/companyprofile">Overview</Link>
                            </li>
                            <li>
                                <Link href="/staff/companyprofile/profile">Profile <i className="fa-solid fa-circle circle-round"></i></Link>
                            </li>
                            <li>
                                <Link href="/staff/companyprofile/followers">Followers</Link>
                            </li>
                            <li className='active'>
                                <Link href="/staff/companyprofile/insights">Insights</Link>
                            </li>
                        </ul>
                    </div>
                    <div className='col-sm-5'>
                        <ul className='blue-text-line mt-4 text-right'>
                            <li>
                                <Link href="/profileemployer">View Public Profile</Link>
                            </li>
                        </ul>
                    </div>
                </div>

                <div className='data-management bg-fff  m-p-10'>
                    <div className='row'>
                        <div className='col-lg-9'>
                            <div className='left-text-fieild'>
                                <h3 className=' m-center'>Profile Performance</h3>
                            </div>
                        </div>
                        <div className='col-lg-3 text-right'>
                            <select className="last-fields">
                                <option>Last 7 days</option>
                                <option>Last 7 days</option>
                            </select>
                        </div>
                    </div>

                    <div className="table-part  mt-1">
                        <div className='row mt-2'>
                            <div className='col-lg-3 col-md-6'>
                                <div className='dash-card b-fff'>
                                    <p className='f-12 c-747474 w-700'>Profile views</p>
                                    <div className='row'>
                                        <div className='col-7'>
                                            <p className='f-22 c-2C2C2C mb-0'>500</p>
                                        </div>
                                        <div className='col-5'>
                                            <div className='text-right'>
                                                <p className='f-16 c-3D9F79 w-600'><i className="fa-solid fa-caret-up"></i> 14%</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className='col-lg-3 col-md-6'>
                                <div className='dash-card b-fff'>
                                    <p className='f-12 c-747474 w-700'>Followers</p>
                                    <div className='row'>
                                        <div className='col-7'>
                                            <p className='f-22 c-2C2C2C mb-0'>50</p>
                                        </div>
                                        <div className='col-5'>
                                            <div className='text-right'>
                                                <p className='f-16 c-FD7373 w-600'><i className="fa-solid fa-caret-down"></i> 14%</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className='col-lg-3 col-md-6'>
                                <div className='dash-card b-fff'>
                                    <p className='f-12 c-747474 w-700'>Active job posts</p>
                                    <div className='row'>
                                        <div className='col-7'>
                                            <p className='f-22 c-2C2C2C mb-0'>5</p>
                                        </div>
                                        <div className='col-5'>
                                            <div className='text-right'>
                                                <p className='f-16 c-3D9F79 w-600'><i className="fa-solid fa-caret-up"></i> 14%</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className='col-lg-3 col-md-6'>
                                <div className='dash-card b-fff'>
                                    <p className='f-12 c-747474 w-700'>Total Applicants</p>
                                    <div className='row'>
                                        <div className='col-7'>
                                            <p className='f-22 c-2C2C2C mb-0'>100</p>
                                        </div>
                                        <div className='col-5'>
                                            <div className='text-right'>
                                                <p className='f-16 c-3D9F79 w-600'><i className="fa-solid fa-caret-up"></i> 14%</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/graf-3.png'} alt="graf-3" className='w-100 mt-4 mb-4' />

                    <form className='form-experience-fieild'>
                        <div className='text-right mt-3'>
                            <button className='cancel'>Cancel</button>
                            <button className='save'>Save</button>
                        </div>
                    </form>


                </div>


            </div>
        </>
    )
}
