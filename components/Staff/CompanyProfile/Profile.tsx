import React, { useState, useEffect } from 'react';
import { getCurrentUserData } from '../../../lib/session';
import Link from 'next/link';
import {
  getAllCountries,
  getSingleCompanyDetails,
  updateCompanyDetails,
  updateCompanyProfileSocialLinks,
  addTeamMembers,
} from '../../../lib/frontendapi';
import { useForm } from 'react-hook-form';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { HtmlEditor } from '../../Common/HtmlEditor';
import SuccessToast from '../../Common/showSuccessTostrMessage';
import ErrorToast from '../../Common/showErrorTostrMessage';
import Image from 'next/image';

export default function Profile() {
  //const {register, handleSubmit, formState: { errors },}:any = useForm();
  const {
    register,
    formState: { errors },
    handleSubmit,
  } = useForm({
    mode: 'onChange',
  });
  const {
    register: register2,
    formState: { errors: errors2 },
    handleSubmit: handleSubmit2,
  } = useForm({
    mode: 'onChange',
  });
  const [Country, setCountry] = useState([]);
  const [companyId, setCompanyId] = useState('');
  const [companyLogo, setCompanyLogo] = useState('');
  const [companyName, setCompanyName] = useState('');
  const [companyDesignation, setCompanyDesignation] = useState('');
  const [companyEmail, setCompanyEmail] = useState('');
  const [companyContactNo, setCompanyContactNo] = useState('');
  const [companyWebsite, setCompanyWebsite] = useState('');
  const [companyLocation, setCompanyLocation] = useState('');
  const [companySector, setCompanySector] = useState('');
  const [numberOfEmployees, setNumberOfEmployees] = useState('');
  const [companyDescription, setCompanyDescription] = useState('');
  const [linkedInLink, setLinkedInLink] = useState('');
  const [twitterLink, setTwitterLink] = useState('');
  const [instagramLink, setInstagramLink] = useState('');
  const [facebookLink, setFacebookLink] = useState('');
  const [previewCompanyLogo, setPreviewCompanyLogo] = useState('');
  const [addCompanyLogo, setAddCompanyLogo] = useState('');
  const [showPopupunsave, setShowPopupunsave] = useState(false);
  const [showPopupunerror, setShowPopupunerror] = useState(false);
  const [showmessage, setShowmessage] = useState([]);
  const [inputFields, setInputFields] = useState([
    {
      team_member_email: '',
    },
  ]);
  const [companySlug, setCompanySlug] = useState('');
  useEffect(() => {
    const current_user_data: any = getCurrentUserData();
    getSingleCompanyDetails(current_user_data.id)
      .then(res => {
        if (res.status == true) {
          setCompanyId(res.data.id);
          console.log(res.data.id);

          setCompanyLogo(res.data.company_logo);
          setCompanyName(res.data.company_name);
          setCompanyDesignation(res.data.designation);
          setCompanyEmail(res.data.company_email);
          setCompanyContactNo(res.data.company_contact_no);
          setCompanyWebsite(res.data.company_website);
          setCompanyLocation(res.data.company_location);
          setCompanySector(res.data.company_sector);
          setNumberOfEmployees(res.data.no_of_employees);
          setCompanyDescription(res.data.company_description);
          setLinkedInLink(res.data.linkedin_link);
          setTwitterLink(res.data.twitter_link);
          setInstagramLink(res.data.instagram_link);
          setFacebookLink(res.data.facebook_link);
          setCompanySlug(res.data.company_slug);
        } else {
          setCompanyId('');
          setCompanyLogo('');
          setCompanyName('');
          setCompanyDesignation('');
          setCompanyEmail('');
          setCompanyContactNo('');
          setCompanyWebsite('');
          setCompanyLocation('');
          setCompanySector('');
          setNumberOfEmployees('');
          setCompanyDescription('');
          setLinkedInLink('');
          setTwitterLink('');
          setInstagramLink('');
          setFacebookLink('');
          setCompanySlug('');
        }
      })
      .catch(err => {
        console.log(err);
      });
    getAllCountries()
      .then(res => {
        if (res) {
          setCountry(res);
        } else {
          setCountry([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  }, []);
  const submitForm = (data: any) => {
    //event.preventDefault()
    const datas = {
      edit_company_name: data.company_name,
      edit_company_email: data.company_email,
      edit_designation: data.company_designation,
      edit_company_contact_no: data.company_contact_number,
      edit_company_website: data.company_website,
      edit_company_location: data.company_location,
      edit_company_sector: data.company_sector,
      edit_no_of_employees: data.company_no_of_employees,
      edit_company_description: companyDescription,
    };
    updateCompanyDetails(companyId, datas)
      .then(res => {
        if (res.status == true) {
          setShowmessage(res.message);
          setShowPopupunsave(true);
          setTimeout(() => {
            setShowPopupunsave(false);
          }, 10000);
          setTimeout(function () {
            window.location.reload();
          }, 3000);
        } else {
          setShowmessage(res.message);
          setShowPopupunerror(true);
          setTimeout(() => {
            setShowPopupunerror(false);
          }, 10000);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  const SubmitSocialLinks = (data: any) => {
    //e.preventDefault()
    const datas = {
      edit_linkedin_link: data.linkedin_link,
      edit_twitter_link: data.twitter_link,
      edit_instagram_link: data.instagram_link,
      edit_facebook_link: data.facebook_link,
    };
    updateCompanyProfileSocialLinks(companyId, datas)
      .then(res => {
        if (res.status == true) {
          setShowmessage(res.message);
          setShowPopupunsave(true);
          setTimeout(() => {
            setShowPopupunsave(false);
          }, 10000);
          setTimeout(function () {
            window.location.reload();
          }, 3000);
        } else {
          setShowmessage(res.message);
          setShowPopupunerror(true);
          setTimeout(() => {
            setShowPopupunerror(false);
          }, 10000);
          setTimeout(function () {
            window.location.reload();
          }, 3000);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  const addInputField = () => {
    setInputFields([
      ...inputFields,
      {
        team_member_email: '',
      },
    ]);
  };
  const removeInputFields = (index: any) => {
    const rows = [...inputFields];
    rows.splice(index, 1);
    setInputFields(rows);
  };
  const handleChange = (index: any, evnt: any) => {
    const { name, value } = evnt.target;
    const list: any = [...inputFields];
    list[index][name] = value;
    setInputFields(list);
  };
  const submitTeamMembersForm = (event: any) => {
    event.preventDefault();
    let company_id = window.localStorage.getItem('company_id');
    const user_data = {
      company_id: company_id,
      team_members_email: inputFields,
    };
    addTeamMembers(user_data)
      .then(res => {
        if (res.status == true) {
          setShowmessage(res.message);
          setShowPopupunsave(true);
          setTimeout(() => {
            setShowPopupunsave(false);
          }, 10000);
          setTimeout(function () {
            window.location.reload();
          }, 3000);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  const handleImageChange = (event: any) => {
    if (event.target.files && event.target.files[0]) {
      setPreviewCompanyLogo(URL.createObjectURL(event.target.files[0]));
      setAddCompanyLogo(event.target.files);
    }
  };
  const handleChangeEditorCompanyDesc = (name: any, value: any) => {
    setCompanyDescription(value);
  };
  return (
    <>
      <div className="dash-right company_profile_page_section">
        <h1>
          My <span className="span-color">Profile</span>
        </h1>
        <div className="row m-column-reverse">
          <div className="col-sm-7">
            <ul className="list-loc m-m-0 mt-4">
              <li>
                <Link href="/staff/companyprofile">Overview</Link>
              </li>
              <li className="active">
                <Link href="/staff/companyprofile/profile">
                  Profile <i className="fa-solid fa-circle circle-round"></i>
                </Link>
              </li>
              <li>
                <Link href="/staff/companyprofile/followers">Followers</Link>
              </li>
              <li>
                <Link href="/staff/companyprofile/insights">Insights</Link>
              </li>
            </ul>
          </div>
          <div className="col-sm-5">
            <ul className="blue-text-line mt-4 text-right">
              <li>
                <Link href={'/companies/' + companySlug}>View Public Profile</Link>
              </li>
            </ul>
          </div>
        </div>
        <div className="m-p-10 mt-2">
          <div className="work-experience-fieild m-p-10 mb-3 rounded">
            <div className="row">
              <div className="col-lg-3 col-md-3">
                <div className="left-text-fieild">
                  <h3 className=" m-center">About</h3>
                  <p className="c-747474  m-center">Tell us about yourself</p>
                </div>
              </div>
              <div className="col-lg-9 col-md-9">
                <form className="form-experience-fieild" key={1} onSubmit={handleSubmit(submitForm)}>
                  <div className="row">
                    <div className="col-lg-2 col-md-3 col-12">
                      <div className="dash-profile-img mb-4 m-auto">
                        <p className="f-12 c-2C2C2C m-center">Profile Picture</p>
                        {previewCompanyLogo ? (
                          <img src={previewCompanyLogo} alt="Preview Image" className="img-circle" />
                        ) : companyLogo ? (
                          <img
                            src={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/companylogo/${companyLogo}`}
                            alt="Avatars-5"
                            className="img-circle"
                          />
                        ) : (
                          <img
                            src={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-5.png`}
                            alt="Avatars-5"
                            className="img-circle"
                          />
                        )}
                      </div>
                    </div>
                    <div className="col-lg-10 col-md-9 col-12">
                      <div className="uploade-btn">
                        {/* <input type='file' name="company_logo" onChange={(e:any)=>setCompanyLogo(e.target.files)} accept="jpg,png"/>  */}
                        <input
                          type="file"
                          name="company_logo"
                          onChange={handleImageChange}
                          accept=".jpg, .png"
                          style={{ top: '45px' }}
                        />
                        <button
                          className="btn-a primary-size-16 btn-bg-0055BA mt-5 mb-4 mobile-m-0 max-340"
                          type="button">
                          <i className="fa-solid fa-upload"></i> Upload A New Photo
                        </button>
                      </div>
                    </div>
                  </div>
                  <label>Company Name*</label>
                  <input
                    type="text"
                    placeholder="Alan Moore"
                    className="fild-des"
                    value={companyName}
                    {...register('company_name', { required: true })}
                    onChange={(e: any) => setCompanyName(e.target.value)}
                  />
                  {errors.company_name && errors.company_name.type === 'required' && (
                    <p className="text-danger" style={{ textAlign: 'left' }}>
                      Company Name is required.
                    </p>
                  )}
                  <label>Your Designation</label>
                  <input
                    type="text"
                    placeholder="HR Manager"
                    className="big-input mb-4"
                    value={companyDesignation}
                    {...register('company_designation', { required: true })}
                    onChange={(e: any) => setCompanyDesignation(e.target.value)}
                  />
                  {errors.company_designation && errors.company_designation.type === 'required' && (
                    <p className="text-danger" style={{ textAlign: 'left' }}>
                      Company Designation is required.
                    </p>
                  )}
                  <div className="row">
                    <div className="col-sm-6">
                      <label>Email ID*</label>
                      <input
                        type="email"
                        placeholder="<EMAIL>"
                        className="fild-des"
                        value={companyEmail}
                        {...register('company_email', { required: true })}
                        onChange={(e: any) => setCompanyEmail(e.target.value)}
                      />
                      {errors.company_email && errors.company_email.type === 'required' && (
                        <p className="text-danger" style={{ textAlign: 'left' }}>
                          Company Email is required.
                        </p>
                      )}
                    </div>
                    <div className="col-sm-6">
                      <label>Contact Number*</label>
                      <input
                        type="number"
                        placeholder="(+971) 123 – 456 – 7890"
                        className="fild-des"
                        value={companyContactNo}
                        {...register('company_contact_number', { required: true })}
                        onChange={(e: any) => setCompanyContactNo(e.target.value)}
                      />
                      {errors.company_contact_number && errors.company_contact_number.type === 'required' && (
                        <p className="text-danger" style={{ textAlign: 'left' }}>
                          Company Contact Number is required.
                        </p>
                      )}
                    </div>
                  </div>
                  <label>Website*</label>
                  <div className="d-flex-icons">
                    <i className="fa-solid fa-paperclip  glass-search mt-1"></i>
                    <input
                      type="text"
                      placeholder="https://www.website.com"
                      className="fild-des left-sp"
                      value={companyWebsite}
                      {...register('company_website', {
                        required: true,
                        pattern:
                          /^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/,
                      })}
                      onChange={(e: any) => setCompanyWebsite(e.target.value)}
                    />
                    {errors.company_website && errors.company_website.type === 'required' && (
                      <p className="text-danger" style={{ textAlign: 'left' }}>
                        Company Website is required.
                      </p>
                    )}
                    {errors.company_website?.type === 'pattern' && (
                      <p className="text-danger" style={{ textAlign: 'left' }}>
                        Enter a valid website url!
                      </p>
                    )}
                  </div>
                  <label>Location*</label>
                  <select
                    className="big-select"
                    {...register('company_location', { required: true })}
                    onChange={(e: any) => setCompanyLocation(e.target.value)}>
                    {Country.length > 0 ? (
                      Country.map((CountryData: any, index) => {
                        if (CountryData.status == 'active') {
                          return (
                            <option value={CountryData.id} key={index} selected={CountryData.id == companyLocation}>
                              {CountryData.country_name}
                            </option>
                          );
                        }
                      })
                    ) : (
                      <option value="">Select Location</option>
                    )}
                  </select>
                  {errors.company_location && errors.company_location.type === 'required' && (
                    <p className="text-danger" style={{ textAlign: 'left' }}>
                      Company Location is required.
                    </p>
                  )}
                  {/* <input type='text' placeholder='United Arab Emirates' className='fild-des' value={companyName} onChange={(e:any) => setCompanyLocation(e.target.value)}/> */}
                  <label>Sector*</label>
                  <select
                    className="big-select"
                    {...register('company_sector', { required: true })}
                    onChange={(e: any) => setCompanySector(e.target.value)}>
                    <option value="1" selected={companySector === '1'}>
                      IT
                    </option>
                    <option value="2" selected={companySector === '2'}>
                      Software
                    </option>
                  </select>
                  {errors.company_sector && errors.company_sector.type === 'required' && (
                    <p className="text-danger" style={{ textAlign: 'left' }}>
                      Company Sector is required.
                    </p>
                  )}
                  {/* <input type='text' placeholder='Software' className='fild-des' onChange={(e:any) => setCompanySector(e.target.value)}/> */}
                  <label>Number of Employees*</label>
                  <select
                    className="big-select"
                    {...register('company_no_of_employees', { required: true })}
                    onChange={(e: any) => setNumberOfEmployees(e.target.value)}>
                    <option value="0-50" selected={numberOfEmployees === '0-50'}>
                      0-50
                    </option>
                    <option value="51-100" selected={numberOfEmployees === '51-100'}>
                      51-100
                    </option>
                    <option value="101-1000" selected={numberOfEmployees === '101-1000'}>
                      101-1000{' '}
                    </option>
                  </select>
                  {errors.company_no_of_employees && errors.company_no_of_employees.type === 'required' && (
                    <p className="text-danger" style={{ textAlign: 'left' }}>
                      Company No Of Employees is required.
                    </p>
                  )}
                  {/* <input type='text' placeholder='51-100' className='fild-des' onChange={(e:any) => setNumberOfEmployees(e.target.value)}/> */}
                  <label>About Our Company</label>
                  <HtmlEditor
                    name="company_description"
                    value={companyDescription}
                    onChange={(name: any, val: any) => {
                      handleChangeEditorCompanyDesc(name, val);
                    }}
                  />
                  {/* <textarea placeholder='B. Tech from Georgia Institute of Technology, Coding wizard, working on an AI tool to assist developers, previously worked at Meta.' className='fild-des' value={companyDescription} {...register('company_bio', { required: true})} onChange={(e:any) => setCompanyDescription(e.target.value)}></textarea>
                                    {errors.company_bio && errors.company_bio.type === 'required' && <p className="text-danger" style={{"textAlign": "left"}}>Company Bio is required.</p>} */}
                  <div className="text-right mt-3">
                    <button className="cancel">Cancel</button>
                    <button className="save" type="submit">
                      Save
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <div className="work-experience-fieild m-p-10 mb-3 rounded">
            <div className="row">
              <div className="col-lg-3 col-md-3">
                <div className="left-text-fieild">
                  <h3>Social Links</h3>
                  <p className="c-747474">
                    Where can talent find you <br /> online?
                  </p>
                </div>
              </div>
              <div className="col-lg-9 col-md-9">
                <form className="form-experience-fieild" key={2} onSubmit={handleSubmit2(SubmitSocialLinks)}>
                  <label>LinkedIn*</label>
                  <div className="form-fild">
                    <input
                      type="text"
                      placeholder="LinkedIn"
                      value={linkedInLink}
                      className="fild-des"
                      {...register2('linkedin_link', {
                        required: true,
                        pattern:
                          /^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/,
                      })}
                      onChange={(e: any) => setLinkedInLink(e.target.value)}
                    />
                    <i className="fa-brands fa-linkedin right-line"></i>
                  </div>
                  {errors2.linkedin_link && errors2.linkedin_link.type === 'required' && (
                    <p className="text-danger" style={{ textAlign: 'left' }}>
                      Linkedin Link is required.
                    </p>
                  )}
                  {errors2.linkedin_link?.type === 'pattern' && (
                    <p className="text-danger" style={{ textAlign: 'left' }}>
                      Enter a valid linkedin Link!
                    </p>
                  )}
                  <div className="left-text-fieild">
                    <h3>Additional Links</h3>
                  </div>
                  <label>Twitter</label>
                  <div className="form-fild">
                    <input
                      type="text"
                      placeholder="Twitter"
                      className="fild-des"
                      value={twitterLink}
                      {...register2('twitter_link', {
                        required: true,
                        pattern:
                          /^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/,
                      })}
                      onChange={(e: any) => setTwitterLink(e.target.value)}
                    />
                    <i className="fa-brands fa-x-twitter right-line"></i>
                  </div>
                  {errors2.twitter_link && errors2.twitter_link.type === 'required' && (
                    <p className="text-danger" style={{ textAlign: 'left' }}>
                      Twitter Link is required.
                    </p>
                  )}
                  {errors2.twitter_link?.type === 'pattern' && (
                    <p className="text-danger" style={{ textAlign: 'left' }}>
                      Enter a valid twitter Link!
                    </p>
                  )}
                  <label>Instagram</label>
                  <div className="form-fild">
                    <input
                      type="text"
                      placeholder="Instagram"
                      className="fild-des"
                      value={instagramLink}
                      {...register2('instagram_link', {
                        required: true,
                        pattern:
                          /^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/,
                      })}
                      onChange={(e: any) => setInstagramLink(e.target.value)}
                    />
                    <i className="fa-brands fa-instagram right-line"></i>
                  </div>
                  {errors2.instagram_link && errors2.instagram_link.type === 'required' && (
                    <p className="text-danger" style={{ textAlign: 'left' }}>
                      Instagram Link is required.
                    </p>
                  )}
                  {errors2.instagram_link?.type === 'pattern' && (
                    <p className="text-danger" style={{ textAlign: 'left' }}>
                      Enter a valid instagram Link!
                    </p>
                  )}
                  <label>Facebook</label>
                  <div className="form-fild">
                    <input
                      type="text"
                      placeholder="Facebook"
                      className="fild-des"
                      value={facebookLink}
                      {...register2('facebook_link', {
                        required: true,
                        pattern:
                          /^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/,
                      })}
                      onChange={(e: any) => setFacebookLink(e.target.value)}
                    />
                    <i className="fa-brands fa-facebook-f right-line"></i>
                  </div>
                  {errors2.facebook_link && errors2.facebook_link.type === 'required' && (
                    <p className="text-danger" style={{ textAlign: 'left' }}>
                      Facebook Link is required.
                    </p>
                  )}
                  {errors2.facebook_link?.type === 'pattern' && (
                    <p className="text-danger" style={{ textAlign: 'left' }}>
                      Enter a valid facebook Link!
                    </p>
                  )}
                  <div className="text-right mt-3">
                    <a className="cancel">Cancel</a>
                    <button className="save" type="submit">
                      Save
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <div className="work-experience-fieild m-p-10 mb-3 rounded">
            <div className="row">
              <div className="col-lg-3 col-md-3">
                <div className="left-text-fieild">
                  <h3>Team Members</h3>
                  <p className="c-747474">
                    Add your team members <br /> to your company profile
                  </p>
                </div>
              </div>
              <div className="col-lg-9 col-md-9">
                <form className="form-experience-fieild" onSubmit={submitTeamMembersForm}>
                  <label>Add Team Member</label>
                  {inputFields.map((data: any, index) => {
                    const { team_member_email } = data;
                    return (
                      <div className="row my-3" key={index}>
                        <div className="col-sm-10">
                          <div className="form-group">
                            <input
                              type="email"
                              placeholder="<EMAIL>"
                              className="fild-des mb-4"
                              onChange={evnt => handleChange(index, evnt)}
                              value={team_member_email}
                              name={'team_member_email'}
                            />
                            {/* <input type="text" placeholder='<EMAIL>' className='form-control big-input mb-4' onChange={(evnt)=>handleChange(index, evnt)} value={team_member_email} name={"team_member_email"}/>
                            <input type="text" placeholder='<EMAIL>' className='form-control big-input mb-4' onChange={(evnt)=>handleChange(index, evnt)} value={team_member_email} name={"team_member_email"}/> */}
                          </div>
                        </div>
                        <div className="col-sm-2">
                          {inputFields.length !== 1 ? (
                            <button className="btn btn-outline-danger" onClick={removeInputFields}>
                              x
                            </button>
                          ) : (
                            ''
                          )}
                        </div>
                      </div>
                    );
                  })}
                  <p className="f-16-form add-member">
                    <a href="#" onClick={addInputField}>
                      <i className="fa-solid fa-plus"></i> Add Team Member
                    </a>
                  </p>
                  <div className="text-right mt-3">
                    <a className="cancel">Cancel</a>
                    <button className="save">Save</button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
        <ToastContainer />
      </div>

      {showPopupunsave && <SuccessToast message={showmessage} />}
      {showPopupunerror && <ErrorToast message={showmessage} />}
    </>
  );
}
