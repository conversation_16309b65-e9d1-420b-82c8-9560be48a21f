import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import PlanPopup from '../../../components/Common/PlanPopup';
import { getCurrentUserData } from "../../../lib/session";
import { getCompanyProfileAllUserViewsCount, getCompanyFollowers, getCompanyActiveJobs, getTotalCompanyJobApplicationsCount, getSingleEmployerCompanyDetails, AddUpdateStaffMember, getAllStaffMembers, DeleteStaff } from '../../../lib/frontendapi';
import PopupModal from '../../../components/Common/PopupModal';
import swal from "sweetalert";
import SuccessToast from "../../Common/showSuccessTostrMessage";
import ErrorToast from "../../Common/showErrorTostrMessage";
import Image from 'next/image';

export default function Overview() {
    const [companyProfileAllUsersViewsCount, setCompanyProfileAllUsersViewsCount] = useState('');
    const [currentUserCompanyId, setCurrentUserCompanyId] = useState('');
    const [companyFollowers, setCompanyFollowers] = useState([]);
    const [companyActiveJobs, setCompanyActiveJobs] = useState([]);
    const [currentCompany, setCurrentCompany]: any = useState([]);
    const [totalCompanyJobApplicationsCount, setTotalCompanyJobApplicationsCount] = useState('');

    const [allstaffmember, setAllStaffMember] = useState([]);

    const [staffname, setStaffName] = useState("");
    const [staffemail, setStaffEmail] = useState("");
    const [staffcvcount, setStaffCvCount] = useState("");
    const [staffid, setStaffId] = useState("");

    const [showPopupunsave, setShowPopupunsave] = useState(false);
    const [showPopupunerror, setShowPopupunerror] = useState(false);
    const [showmessage, setShowmessage] = useState('');

    const [currentstaffcvcount, setcurrentStaffCvCount] = useState("");

    const [modalConfirmStaff, setModalConfirmStaff] = useState(false);

    const modalConfirmCloseStaff = () => {
        setModalConfirmStaff(false);
    }

    useEffect(() => {
        const current_user_data: any = getCurrentUserData();
        current_user_data.company_id ? setCurrentUserCompanyId(current_user_data.company_id) : setCurrentUserCompanyId('');
        const data = {
            company_id: current_user_data.company_id
        }
        getCompanyProfileAllUserViewsCount(data)
            .then(res => {
                if (res.status == true) {
                    setCompanyProfileAllUsersViewsCount(res.company_profile_views_count);
                } else {
                    setCompanyProfileAllUsersViewsCount('');
                }
            })
            .catch(err => {
                console.log(err);
            });
        getCompanyFollowers(current_user_data.company_id)
            .then(res => {
                if (res.status == true) {
                    setCompanyFollowers(res.companies_followers);
                } else {
                    setCompanyFollowers([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
        getCompanyActiveJobs(data)
            .then(res => {
                if (res.status == true) {
                    setCompanyActiveJobs(res.data);
                } else {
                    setCompanyActiveJobs([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
        const applications_data = {
            company_id: current_user_data.company_id,
            user_id: current_user_data.id
        }
        getTotalCompanyJobApplicationsCount(applications_data)
            .then(res => {
                if (res.status == true) {
                    setTotalCompanyJobApplicationsCount(res.data);
                } else {
                    setTotalCompanyJobApplicationsCount('');
                }
            })
            .catch(err => {
                console.log(err);
            });


        fetchSingleEmployerCompanyDetails(current_user_data.company_id);

        fetchStaffMemeber(current_user_data.id);

    }, []);
    const [modalConfirm, setModalConfirm] = useState(false);
    const modalConfirmOpen = () => {
        setModalConfirm(true);
    }

    const fetchStaffMemeber = async (id: any) => {
        try {
            const response = await getAllStaffMembers(id);
            setAllStaffMember(response.data);

        } catch (error) {
            console.error(error);
        }
    };

    const fetchSingleEmployerCompanyDetails = async (id: any) => {
        try {
            const res = await getSingleEmployerCompanyDetails(id);
            if (res.status == true) {
                setCurrentCompany(res.data);

            } else {
                setCurrentCompany([]);
            }

        } catch (error) {
            console.error(error);
        }
    };


    const handleStaffSubmit = (e: any) => {
        e.preventDefault();
        const current_user_data: any = getCurrentUserData();

        let company_cv_count;
        let updatecount;

        if (staffid) {

            updatecount = Number(currentstaffcvcount) - Number(staffcvcount);
            company_cv_count = Number(updatecount) + Number(currentCompany.available_resume_count);

        } else {

            company_cv_count = staffcvcount < currentCompany.available_resume_count ? Number(currentCompany.available_resume_count) - Number(staffcvcount) : Number(staffcvcount) - Number(currentCompany.available_resume_count);
        }

        const data = {
            id: staffid,
            name: staffname,
            email: staffemail,
            company_id: current_user_data.company_id,
            role: 'staff',
            available_resume_count: staffcvcount,
            created_by_id: current_user_data.id,
            company_cv_count: company_cv_count
        };

        if (staffcvcount > currentCompany.available_resume_count && !staffid) {

            if (currentCompany.available_resume_count == 0) {

                setShowmessage('You cannot add a team member because your CV count is not available.');
                setShowPopupunerror(true);
                setTimeout(() => {
                    setShowPopupunerror(false)
                }, 10000)

            } else {
                setShowmessage('Cv count value must be less than  and equal to' + currentCompany);
                setShowPopupunerror(true);
                setTimeout(() => {
                    setShowPopupunerror(false)
                }, 10000)
            }

        } else {

            if (Number(staffcvcount) > (Number(currentstaffcvcount) + Number(currentCompany.available_resume_count))) {
                setShowmessage('Cv count value must be less than and equal to' + (Number(currentstaffcvcount) + Number(currentCompany.available_resume_count)));
                setShowPopupunerror(true);
                setTimeout(() => {
                    setShowPopupunerror(false)
                }, 10000)
            } else {

                AddUpdateStaffMember(data)
                    .then((res) => {
                        if (res.status == true) {
                            setShowmessage(res.message);
                            setShowPopupunsave(true);
                            setTimeout(() => {
                                setShowPopupunsave(false)
                            }, 10000)
                            fetchSingleEmployerCompanyDetails(current_user_data.company_id);
                            fetchStaffMemeber(current_user_data.id);
                            setModalConfirmStaff(false);


                        } else {

                            const errors = res.message;
                            let errorMessage = '';
                            for (const error in errors) {
                                errorMessage += errors[error];
                            }
                            setShowmessage(res.message);
                            setShowPopupunerror(true);
                            setTimeout(() => {
                                setShowPopupunerror(false)
                            }, 10000)
                        }
                    })
                    .catch((err) => {
                        console.log(err);
                    });

            }

        };


    }

    const resetForm = () => {
        setStaffName("");
        setStaffEmail("");
        setStaffCvCount("");
        setStaffId("");
        setcurrentStaffCvCount("");

    };

    const handleStaffEdit = (staff: any) => {
        setStaffName(staff.name);
        setStaffEmail(staff.email);
        setStaffCvCount(staff.available_resume_count);
        setStaffId(staff.id);
        setcurrentStaffCvCount(staff.available_resume_count)
        setModalConfirmStaff(true);
    };

    const handleStaffDelete = (staff: any) => {
        const current_user_data: any = getCurrentUserData();
        swal({
            title: "Are you sure?",
            text: "You want to delete the staff",
            icon: "warning",
            dangerMode: true,
            buttons: ["Cancel", "Yes, I am sure!"],
        }).then((willDelete) => {
            if (willDelete) {

                const data = {
                    id: staff.id,
                    company_id: current_user_data.company_id,
                    company_cv_count: Number(currentCompany.available_resume_count) + Number(staff.available_resume_count),
                }


                DeleteStaff(data)
                    .then((res) => {
                        if (res.status === true) {

                            setShowmessage(res.message);
                            setShowPopupunsave(true);
                            setTimeout(() => {
                                setShowPopupunsave(false)
                            }, 10000)

                            fetchSingleEmployerCompanyDetails(current_user_data.company_id);
                            fetchStaffMemeber(current_user_data.id)

                        } else {
                            console.log("Deletion failed");
                        }
                    })
                    .catch((err) => {
                        // Handle error
                        console.log(err);
                    });

            } else {

            }
        });
    };


    return (
        <>
            <div className="dash-right">
                <h1>Company <span className='span-color'>Profile</span></h1>
                <div className='row m-column-reverse'>
                    <div className='col-sm-7'>
                        <ul className='list-loc m-m-0 mt-4 blue-active'>
                            <li className='active'>
                                <Link href="/staff/companyprofile">Overview</Link>
                            </li>
                            <li>
                                <Link href="/staff/companyprofile/profile">Profile <i className="fa-solid fa-circle circle-round"></i></Link>
                            </li>
                            <li>
                                <Link href="/staff/companyprofile/followers">Followers</Link>
                            </li>
                            <li>
                                <Link href="/staff/companyprofile/insights">Insights</Link>
                            </li>
                        </ul>
                    </div>
                    <div className='col-sm-5'>
                        <ul className='blue-text-line mt-4 text-right'>
                            <li>
                                <Link href="/profileemployer">View Public Profile</Link>
                            </li>
                        </ul>
                    </div>
                </div>
                <div className='data-management bg-0055BA border-0'>
                    <div className="row">
                        <div className="col-sm-8">
                            <p className="f-22 m-center c-fff">Profile Highlights <i className="fa-solid fa-circle-info c-D9D9D9"></i></p>
                        </div>
                        <div className="col-sm-4 text-right">
                            <p className="16 c-fff">EXPAND &nbsp;&nbsp; <i className="fa-solid fa-up-right-and-down-left-from-center c-D9D9D9"></i></p>
                        </div>
                    </div>
                    <div className='row mt-2'>
                        <div className='col-lg-3 col-md-6'>
                            <div className='dash-card b-fff'>
                                <p className='f-12 c-747474 w-700 text-upper'>Profile views</p>
                                <div className='row'>
                                    <div className='col-7'>
                                        <p className='f-22 c-2C2C2C mb-0'>{companyProfileAllUsersViewsCount}</p>
                                    </div>
                                    <div className='col-5'>
                                        <div className='text-right'>
                                            <p className='f-16 c-3D9F79 w-600'><i className="fa-solid fa-caret-up"></i> 14%</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className='col-lg-3 col-md-6'>
                            <div className='dash-card b-fff'>
                                <p className='f-12 c-747474 w-700 text-upper'>Followers</p>
                                <div className='row'>
                                    <div className='col-7'>
                                        <p className='f-22 c-2C2C2C mb-0'>{companyFollowers.length}</p>
                                    </div>
                                    <div className='col-5'>
                                        <div className='text-right'>
                                            <p className='f-16 c-FD7373 w-600'><i className="fa-solid fa-caret-down"></i> 14%</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className='col-lg-3 col-md-6'>
                            <div className='dash-card b-fff'>
                                <p className='f-12 c-747474 w-700 text-upper'>Active job posts</p>
                                <div className='row'>
                                    <div className='col-7'>
                                        <p className='f-22 c-2C2C2C mb-0'>{companyActiveJobs.length}</p>
                                    </div>
                                    <div className='col-5'>
                                        <div className='text-right'>
                                            <p className='f-16 c-3D9F79 w-600'><i className="fa-solid fa-caret-up"></i> 14%</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className='col-lg-3 col-md-6'>
                            <div className='dash-card b-fff'>
                                <p className='f-12 c-747474 w-700 text-upper'>Total Applicants</p>
                                <div className='row'>
                                    <div className='col-7'>
                                        <p className='f-22 c-2C2C2C mb-0'>{totalCompanyJobApplicationsCount}</p>
                                    </div>
                                    <div className='col-5'>
                                        <div className='text-right'>
                                            <p className='f-16 c-3D9F79 w-600'><i className="fa-solid fa-caret-up"></i> 14%</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className='data-management border-0 mt-1'>
                    <div className='row '>
                        <div className='col-sm-3 pr-0'>
                            <p className='f-22 c-191919 mb-1'>Featured Job Posts <i className="fa-solid fa-circle-info c-D9D9D9" data-bs-toggle="tooltip" data-bs-placement="right" title=" Give users more information  about how this feature works in a friendly tone & concise manner."></i></p>
                            <p className='f-16 c-747474 w-600'>A glimpse into your available featured job posts</p>
                        </div>
                        <div className='col-sm-8 pr-0 pl-sp'>
                            <div className='row'>
                                <div className='col-sm-8'>
                                    <h4 className='big-short mt-1 '><big>0</big> featured job posts available</h4>
                                    <p className='f-12 c-747474 m-sp-0'>Available from 01 Mar 2023 - 31 Mar 2023</p>
                                </div>
                                <div className='col-sm-4 text-right m-center'>
                                    <button className="btn-a primary-size-16 btn-bg-0055BA  tab-add-sp mt-3 w-100" onClick={modalConfirmOpen}><i className="fa-solid fa-bolt"></i> Upgrade</button>
                                    <PlanPopup show={modalConfirm} setModalConfirm={(bool: any) => setModalConfirm(bool)} />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className='data-management border-0 mt-1'>
                    <div className='row '>
                        <div className='col-sm-3 pr-0'>
                            <p className='f-16 c-747474 w-600'>A glimpse into your available featured job posts</p>
                        </div>
                        <div className='col-sm-8 pr-0 pl-sp'>
                            <div className='row'>
                                <div className='col-sm-12'>
                                    <div className="progress">
                                        <div className="progress-bar progress-w" role="progressbar" aria-valuenow={92} aria-valuemin={0} aria-valuemax={100}></div>
                                    </div>
                                </div>
                                <div className='col-sm-8'>
                                    <h4 className='big-short mt-5 '><big>49</big> featured job posts available</h4>
                                    <p className='f-12 c-747474 m-sp-0'>Available from 01 Mar 2023 - 31 Mar 2023</p>
                                </div>
                                <div className='col-sm-4 text-right m-center'>
                                    <Link href='/staff/billing/billinghistory'><button className="download mt-4 w-100">Billing History</button></Link>
                                    <button className="btn-a primary-size-16 btn-bg-0055BA  tab-add-sp mt-3  w-100" onClick={modalConfirmOpen}><i className="fa-solid fa-bolt"></i> Upgrade</button>
                                    <PlanPopup show={modalConfirm} setModalConfirm={(bool: any) => setModalConfirm(bool)} />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className='data-management border-0 mt-1'>
                    <div className='row'>
                        <div className='col-sm-3 pr-0'>
                            <p className='f-22 c-191919 mb-1'>Team CV View <i className="fa-solid fa-circle-info c-D9D9D9" data-bs-toggle="tooltip" data-bs-placement="right" title=" Give users more information  about how this feature works in a friendly tone & concise manner."></i></p>
                            <p className='f-16 c-747474 w-600'>Manage your teams CV viewing breakdown</p>
                        </div>
                        <div className='col-sm-4 pr-0 pl-sp'>
                            <h4 className='big-short mt-1 '><big>{currentCompany.available_resume_count}</big> CV Views Available</h4>
                            <p className='f-12 c-747474 m-sp-0'>Available from 01 Mar 2023 - 31 Mar 2023</p>
                        </div>
                        <div className='col-sm-5 '>
                            <p className='f-18 w-600 c-2C2C2C mb-4'>Team Members ({allstaffmember.length})</p>
                            <div className='team_memebr'>
                                {allstaffmember.length > 0
                                    ?
                                    allstaffmember.map((staff: any, index: any) => {
                                        return (
                                            <div className='row mt-1' key={index}>
                                                <div className='col-7'>
                                                    <p className='f-16 c-4D4D4D '>{staff.name}</p>

                                                </div>
                                                <div className='col-5 pt-2'>
                                                    <p className='f-16 w-700 c-4D4D4D  '>{staff.available_resume_count} CV’s <i className="fa-solid fa-pencil pencil-12" onClick={() => handleStaffEdit(staff)} role="button"></i><i className="fa fa-trash pencil-12 mx-2" onClick={() => handleStaffDelete(staff)} role="button"></i></p>

                                                </div>
                                            </div>
                                        )
                                    })
                                    :
                                    <p>
                                        No Team Member Found
                                    </p>
                                }
                            </div>
                            <p className="add mt-2" onClick={() => {
                                setModalConfirmStaff(true);
                                resetForm();
                            }} role="button"><i className="fa-solid fa-plus" > </i> Add Team Member</p>
                        </div>
                    </div>
                </div>
                <div className='data-management border-0 mt-1'>
                    <div className='row'>
                        <div className='col-sm-3 pr-0'>
                            <p className='f-22 c-191919 mb-1'>What candidates will <br /> see</p>
                            <p className='f-16 c-747474 w-600'>A glimpse into what talent <br /> will look at on your profile</p>
                        </div>
                        <div className='col-sm-7 '>
                            <div className='row pb-4'>
                                <div className='col-sm-3 pr-0'>
                                    <div className="dash-profile-img m-auto over-h-none">
                                        {currentCompany.company_logo
                                            ?
                                            <img src={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/companylogo/' + currentCompany.company_logo} alt="Avatars-5" style={{ "width": "100px", "height": "100px" }} />
                                            :
                                            <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/Avatars-5.png'} alt="Avatars-5" style={{ "width": "100px", "height": "100px" }} />
                                        }
                                    </div>
                                </div>
                                <div className='col-sm-9'>
                                    <div className='col-sm-12 col-12 m-center'>
                                        <h4 className='em-name f-32 mb-2 c-0055BA'>{currentCompany.company_name}</h4>
                                        <p className='f-16 c-0070F5'><i className="fa-solid fa-globe"></i>
                                            <a href={currentCompany.company_website} target="_blank">{currentCompany.company_website}</a></p>
                                        <ul className='skills mt-1 mb-1'>
                                            {currentCompany.country_name ? <li style={{ "paddingRight": "10px" }}><p className='f-12 c-999999'><i className="fa-solid fa-location-dot"></i> {currentCompany.country_name}</p></li> : ''}
                                            {currentCompany.company_email ? <li style={{ "paddingRight": "10px" }}><p className='f-12 c-999999'><i className="fa-regular fa-envelope"></i> {currentCompany.company_email}</p></li> : ''}
                                            {currentCompany.company_contact_no ? <li style={{ "paddingRight": "10px" }}><p className='f-12 c-999999'><i className="fa-solid fa-phone-volume"></i> +{currentCompany.company_contact_no}</p></li> : ''}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div className='row mb-3'>
                                <p className='f-14 c-000'>About Our Company</p>
                                <p className='f-16 w-400 c-4D4D4D' dangerouslySetInnerHTML={{ __html: currentCompany.company_description }}></p>
                            </div>
                            <div className='row mb-3'>
                                <p className='f-14 c-000 mb-2'>Sector</p>
                                <p className='f-16 w-400 c-4D4D4D'>{currentCompany.sector_name}</p>
                            </div>
                            <div className='row'>
                                <p className='f-14 c-000 mb-2'>Company Size</p>
                                <p className='f-16 w-400 c-4D4D4D'>{currentCompany.no_of_employees}+ employees</p>
                            </div>
                        </div>
                        <div className='col-sm-2'>
                            <div className='text-right link-right-icons'>
                                <p className='mt-2'>
                                    {currentCompany.twitter_link && (
                                        <a href={currentCompany.twitter_link} target='_blank'><i className="fa-brands fa-x-twitter"></i></a>
                                    )}
                                    {currentCompany.facebook_link && (
                                        <a href={currentCompany.facebook_link} target='_blank'><i className="fa-brands fa-facebook-f"></i></a>
                                    )}
                                    {currentCompany.instagram_link && (
                                        <a href={currentCompany.instagram_link} target='_blank'><i className="fa-brands fa-instagram"></i></a>
                                    )}
                                    {currentCompany.linkedin_link && (
                                        <a href={currentCompany.linkedin_link} target='_blank'><i className="fa-brands fa-linkedin"></i></a>
                                    )}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <PopupModal show={modalConfirmStaff} handleClose={modalConfirmCloseStaff} customclass={'add_company_signup_popup modal-lg body-sp-0 '} closebtnclass={'close-x  bg-0055BA border-design'} closebtnicon={'icon'}>
                <div className='head-box'>
                    <div className='row'>
                        <div className='col-sm-10'>
                            <p className='f-26 mb-2 mt-2'>{staffid ? 'Edit Team Member' : 'Add Team Member'}</p>

                        </div>
                        <div className='col-sm-2 text-right'>
                            <button type="button" className="close-x  bg-0055BA border-design" data-bs-dismiss="modal" aria-label="Close">
                                <i className="fa-solid fa-xmark"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div className='popup-body'>
                    <form className='form-experience-fieild' onSubmit={(e: any) => handleStaffSubmit(e)}>

                        <label>Name</label>
                        <input type="text" placeholder='Enter name' className='big-input' value={staffname} onChange={(e) => setStaffName(e.target.value)} required />

                        <label>Email</label>
                        <input type="email" placeholder='Enter email' className='big-input' value={staffemail} onChange={(e) => setStaffEmail(e.target.value)} required disabled={!!staffid} />

                        <label>Cv Count</label>
                        <input type="number" placeholder='Enter cv count' className='big-input mb-4' value={staffcvcount} onChange={(e) => {
                            const enteredValue = e.target.value;
                            // Ensure the entered value is a non-negative number
                            const nonNegativeValue = Math.max(0, parseInt(enteredValue));
                            setStaffCvCount(nonNegativeValue.toString());
                        }} required />
                        <small style={{ color: '#dc3545' }}>Note : {currentCompany.available_resume_count} cv count are available</small>
                        <div className='text-right mt-3'>
                            <button className='save' type="submit">Submit</button>
                        </div>
                    </form>
                </div>

            </PopupModal>

            {showPopupunsave &&
                <SuccessToast message={showmessage} />
            }
            {showPopupunerror &&
                <ErrorToast message={showmessage} />
            }
        </>
    )
}
