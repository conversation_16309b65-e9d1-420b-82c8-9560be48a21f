import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { getCurrentUserData } from '../../../lib/session';
import { getCompanyFollowers } from '../../../lib/frontendapi';
import moment from 'moment';
import Image from 'next/image';

export default function Followers() {
  const [companyFollowers, setCompanyFollowers] = useState([]);
  useEffect(() => {
    const current_user_data: any = getCurrentUserData();
    getCompanyFollowers(current_user_data.company_id)
      .then(res => {
        if (res.status == true) {
          setCompanyFollowers(res.companies_followers);
        } else {
          setCompanyFollowers([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  }, []);
  return (
    <>
      <div className="dash-right">
        <h1>
          My <span className="span-color">Profile</span>
        </h1>
        <div className="row m-column-reverse">
          <div className="col-sm-7">
            <ul className="list-loc m-m-0 mt-4">
              <li>
                <Link href="/staff/companyprofile">Overview</Link>
              </li>
              <li>
                <Link href="/staff/companyprofile/profile">
                  Profile <i className="fa-solid fa-circle circle-round"></i>
                </Link>
              </li>
              <li className="active">
                <Link href="/staff/companyprofile/followers">Followers</Link>
              </li>
              <li>
                <Link href="/staff/companyprofile/insights">Insights</Link>
              </li>
            </ul>
          </div>
          <div className="col-sm-5">
            <ul className="blue-text-line mt-4 text-right">
              <li>
                <Link href="/profileemployer">View Public Profile</Link>
              </li>
            </ul>
          </div>
        </div>
        <div className="data-management m-p-10">
          <div className="row">
            <div className="col-lg-12">
              <div className="left-text-fieild">
                <h3 className=" m-center">Your Followers</h3>
              </div>
            </div>
          </div>
          <div className="table-part mt-1">
            <table className="rwd-table">
              <tbody>
                <tr>
                  <th>PROFILE</th>
                  <th>
                    <div className="text-right">TIME</div>{' '}
                  </th>
                </tr>
                {companyFollowers.length > 0 ? (
                  companyFollowers.map((company_followers: any, index: any) => {
                    return (
                      <tr key={index}>
                        <td data-th="PROFILE">
                          <div className="row">
                            <div className="col-sm-2 col-3 pr-0">
                              {company_followers.profile_image ? (
                                <img
                                  src={
                                    process.env.NEXT_PUBLIC_IMAGE_URL +
                                    '/images/userprofileImg/' +
                                    company_followers.profile_image
                                  }
                                  alt="Avatars-1"
                                  className="w-48 round-img-200"
                                />
                              ) : (
                                <img
                                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/Avatars-1.png'}
                                  alt="Avatars-1"
                                  className="w-48 round-img-200"
                                />
                              )}
                            </div>
                            <div className="col-sm-10 col-9 pl-0 name-candi">
                              <p className="c-n mb-0">
                                <Link href={'/candidate-profile/' + company_followers.candidate_profile_slug}>
                                  {company_followers.candidate_name}{' '}
                                </Link>
                              </p>
                              <p className="f-16 w-600 c-2C2C2C">
                                {company_followers.candidate_current_position} @
                                <Link href={'/companies/' + company_followers.company_slug}>
                                  {company_followers.company_name}
                                </Link>
                              </p>
                            </div>
                          </div>
                        </td>
                        <td data-th="TIME">
                          <div className="text-right right-time">
                            <p className="f-16 w-600 c-999999">
                              {moment(company_followers.created_at).format('HH:mm A')}
                            </p>
                          </div>
                        </td>
                      </tr>
                    );
                  })
                ) : (
                  <tr>
                    <td data-th="TIME">
                      <div className="text-right right-time">
                        <p className="f-16 w-600 c-999999">No any followers found</p>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </>
  );
}
