import React, { useState, useEffect } from 'react';
import { useRouter } from "next/router";
import Link from 'next/link';
import PopupModal from '../../../components/Common/PopupModal';
import { getLastPaymentDetails, getSingleUserDetails, getUserAllPaymentDetails, updateUserCardDetails } from '../../../lib/frontendapi';
import { getCurrentUserData } from "../../../lib/session";
import Pagination from "../../../components/Common/Pagination";
import { paginate } from "../../../helpers/paginate";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import moment from "moment";
import SuccessToast from "../../Common/showSuccessTostrMessage";
import ErrorToast from "../../Common/showErrorTostrMessage";
import Image from 'next/image';

export default function BillingSummary() {
  const [lastPaymentData, setLastPaymentData]: any = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPaymentTransaction, setTotalPaymentTransaction] = useState([]);
  const [userData, SetUserData]: any = useState([]);
  const [cardNumber, setCardNumber] = useState('');
  const [cvc, setCvc] = useState('');
  const [cardError, setCardError] = useState('');
  const [cvcError, setCvcError] = useState('');
  const [expiryMonth, setExpiryMonth] = useState('');
  const [expiryYear, setExpiryYear] = useState('');
  const [expiry, setExpiry] = useState('');
  const [expiryDateError, setExpiryDateError] = useState('');
  const [expiryMonthError, setExpiryMonthError] = useState('');
  const [modalConfirmUpdateCardDetails, setModalConfirmUpdateCardDetails] = useState(false);
  const [paymentTransaction, setPaymentTransaction] = useState([]);
  const [nextPaymentTransaction, setNextPaymentTransaction] = useState([]);
  const [totalPendingAmount, setTotalPendingAmount]: any = useState('');
  const [filterStartDate, setFilterStartDate] = useState('');
  const [filterEndDate, setFilterEndDate] = useState('');
  const [filterStartDateError, setFilterStartDateError] = useState('');
  const [filterEndDateError, setFilterEndDateError] = useState('');
  const [showPopupunsave, setShowPopupunsave] = useState(false);
  const [showPopupunerror, setShowPopupunerror] = useState(false);
  const [showmessage, setShowmessage] = useState([]);
  const pageSize = 5;

  const onPageChange = (page: any) => {
    const current_user_data: any = getCurrentUserData();
    setCurrentPage(page);
    const data = {
      filterStartDate: '',
      filterEndDate: ''
    }
    getUserAllPaymentDetails(current_user_data.id, data)
      .then(res => {
        if (res.status == true) {
          setTotalPaymentTransaction(res.data);
          const paginatedPosts = paginate(res.data, page, pageSize);
          setPaymentTransaction(paginatedPosts);
          setNextPaymentTransaction(paginatedPosts);
        } else {
          setPaymentTransaction([]);
          setNextPaymentTransaction([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  useEffect(() => {
    const current_user_data: any = getCurrentUserData();
    const data = {
      filterStartDate: '',
      filterEndDate: ''
    }
    getUserAllPaymentDetails(current_user_data.id, data)
      .then(res => {
        if (res.status == true) {
          setTotalPaymentTransaction(res.data);
          const paginatedPosts = paginate(res.data, currentPage, pageSize);
          setPaymentTransaction(paginatedPosts);
          setTotalPendingAmount(res.total_amount);
          setNextPaymentTransaction(paginatedPosts);
        } else {
          setPaymentTransaction([]);
          setNextPaymentTransaction([]);
        }
      })
      .catch(err => {
        setShowmessage(err);
        setShowPopupunerror(true);
        setTimeout(() => {
          setShowPopupunerror(false)
        }, 1000)
      })
    const payment_data = {
      user_id: current_user_data.id
    }
    getLastPaymentDetails(payment_data)
      .then(res => {
        if (res.status === true) {
          setLastPaymentData(res.data);
        } else {
          setLastPaymentData([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
    getSingleUserDetails(current_user_data.id)
      .then(res => {
        if (res.status === true) {
          SetUserData(res.user);
          setCardNumber(res.user.card_number);
          setExpiryMonth(res.user.card_exp_month);
          setExpiryYear(res.user.card_exp_year);
          setCvc(res.user.card_cvv);
        } else {
          SetUserData([]);
          setCardNumber('');
          setExpiryMonth('');
          setExpiryYear('');
          setCvc('');
        }
      })
      .catch(err => {
        console.log(err);
      });
  }, []);
  const modalConfirmUpdateCardDetailsPopupOpen = () => {
    setModalConfirmUpdateCardDetails(true);
  }
  const modalConfirmUpdateCardDetailsPopupClose = () => {
    setModalConfirmUpdateCardDetails(false);
  }
  const handleCardNumberChange = (event: any) => {
    setCardNumber(event.target.value);
  };
  const handleExpiryMonthChange = (event: any) => {
    const value = event.target.value;
    setExpiryMonth(value);
    if (/^((0[1-9])|(1[0-2]))$/.test(value)) {
      setExpiryMonthError('');
    } else {
      setExpiryMonthError('Invalid expiry month');
    }
  };
  const handleExpiryYearChange = (event: any) => {
    const value = event.target.value;
    setExpiryYear(value);
    if (/^\d{4}$/.test(value)) {
      setExpiryDateError('');
    } else {
      setExpiryDateError('Invalid expiry date');
    }
  };
  const handleCvcChange = (event: any) => {
    const value = event.target.value;
    setCvc(value);
    if (/^\d{3}$/.test(value)) {
      setCvcError('');
    } else {
      setCvcError('Invalid CVC');
    }
  };
  const handleSubmit = (event: any) => {
    const current_user_data: any = getCurrentUserData();
    event.preventDefault();
    if (!cardNumber) {
      setCardError('Please enter your card number');
    } else if (!/^\d{16}$/.test(cardNumber)) {
      setCardError('Invalid card number');
    } else {
      setCardError('');
    }
    if (!expiryMonth || !expiryYear) {
      setExpiryDateError('Please enter your card expiry date');
      setExpiryMonthError('Please enter your card expiry month');
    } else if (!/^((0[1-9])|(1[0-2]))$/.test(expiryMonth)) {
      setExpiryMonthError('Invalid expiry month');
      setExpiryDateError('');
    } else if (!/^\d{4}$/.test(expiryYear)) {
      setExpiryMonthError('');
      setExpiryDateError('Invalid expiry year');
    } else {
      setExpiryMonthError('');
      setExpiryDateError('');
    }
    if (!cvc) {
      setCvcError('Please enter your card security code (CVC)');
    } else if (!/^\d{3}$/.test(cvc)) {
      setCvcError('Invalid CVC');
    } else {
      setCvcError('');
    }
    if (cardNumber && cvc && expiryMonth && expiryYear && !cardError && !cvcError && !expiryDateError && !expiryMonthError) {
      // visa
      let card_type = "";
      var re = new RegExp("^4");
      if (cardNumber.match(re) != null) {
        card_type = "Visa";
      }
      // Mastercard
      // Updated for Mastercard 2017 BINs expansion
      if (/^(5[1-5][0-9]{14}|2(22[1-9][0-9]{12}|2[3-9][0-9]{13}|[3-6][0-9]{14}|7[0-1][0-9]{13}|720[0-9]{12}))$/.test(cardNumber)) {
        card_type = "Mastercard";
      }
      // AMEX
      re = new RegExp("^3[47]");
      if (cardNumber.match(re) != null) {
        card_type = "AMEX";
      }
      // Discover
      re = new RegExp("^(6011|622(12[6-9]|1[3-9][0-9]|[2-8][0-9]{2}|9[0-1][0-9]|92[0-5]|64[4-9])|65)");
      if (cardNumber.match(re) != null) {
        card_type = "Discover";
      }
      // Diners
      re = new RegExp("^36");
      if (cardNumber.match(re) != null) {
        card_type = "Diners";
      }
      // Diners - Carte Blanche
      re = new RegExp("^30[0-5]");
      if (cardNumber.match(re) != null) {
        card_type = "Diners - Carte Blanche";
      }
      // JCB
      re = new RegExp("^35(2[89]|[3-8][0-9])");
      if (cardNumber.match(re) != null) {
        card_type = "JCB";
      }
      // Visa Electron
      re = new RegExp("^(4026|417500|4508|4844|491(3|7))");
      if (cardNumber.match(re) != null) {
        card_type = "Visa Electron";
      }

      const data = {
        card_number: cardNumber,
        card_exp_month: expiryMonth,
        card_exp_year: expiryYear,
        card_cvv: cvc,
        card_type: card_type
      }
      updateUserCardDetails(current_user_data.id, data)
        .then(res => {
          if (res.status == true) {
            setShowmessage(res.message);
            setShowPopupunsave(true);
            setTimeout(() => {
              setShowPopupunsave(false)
            }, 10000)
          } else {
            setShowmessage(res.message);
            setShowPopupunerror(true);
            setTimeout(() => {
              setShowPopupunerror(false)
            }, 10000)
          }
        })
        .catch(err => {
          console.log(err);
        });
    }
  }
  function addZeroes(num: any) {
    const dec = num.split('.')[1]
    const len = dec && dec.length > 2 ? dec.length : 2
    return Number(num).toFixed(len)
  }
  const handleSubmitFilter = (event: any) => {
    event.preventDefault();
    const current_user_data: any = getCurrentUserData();
    if (filterStartDate > filterEndDate) {
      setFilterEndDateError('please select end date more than of start date.');
    } else if (filterStartDate == '') {
      setFilterStartDateError('please select start date');
    } else if (filterEndDate == '') {
      setFilterEndDateError('please select end date');
    } else {
      setFilterStartDateError('');
      setFilterEndDateError('');
      const data = {
        filterStartDate: filterStartDate,
        filterEndDate: filterEndDate
      }
      getUserAllPaymentDetails(current_user_data.id, data)
        .then(res => {
          if (res.status == true) {
            setTotalPaymentTransaction(res.data);
            const paginatedPosts = paginate(res.data, currentPage, pageSize);
            setPaymentTransaction(paginatedPosts);
          } else {
            setPaymentTransaction([]);
          }
        })
        .catch(err => {
          setShowmessage(err);
          setShowPopupunerror(true);
          setTimeout(() => {
            setShowPopupunerror(false)
          }, 1000)
        });
    }
  }
  return (
    <>
      <section className='card-box p-4 pb-2   '>
        <ul className="list-loc m-m-0 mt-4">
          <li className="active"><a href="#" className='c-4D4D4D'>Billing Summary</a></li>
        </ul>
      </section>
      <div className='dash-right'>
        <div className='row'>
          <div className='col-lg-9'>
            <div className='card-box p-4 b-r-8'>
              <p className='f-18 c-2C2C2C w-700 mb-2'>Payment Due</p>
              <p className='f-16 c-4D4D4D w-400  '>Your payment method will be charged by the due date. Your payment method will be charged by the due date.</p>
              <div className='pay-due mt-3'>
                <div className='row '>
                  <div className='col-sm-3'>
                    <p className='f-18 c-4D4D4D w-600'>due date</p>
                  </div>
                  <div className='col-sm-6'></div>
                  <div className='col-sm-3 text-right m-text-left'>
                    <p className='f-22 c-4D4D4D w-500 mt-3 '>AED {totalPendingAmount ? addZeroes(new Intl.NumberFormat('ar-AE').format(totalPendingAmount / 100)) : ''}</p>
                    {/* <p className='f-22 c-4D4D4D w-500 mt-3 '>AED 255.<small className='f-16 w-600'>00</small></p> */}
                  </div>
                </div>
                <table>
                  <thead>
                    <tr>
                      <th scope="col" className='w-15-par'>Due Date</th>
                      <th scope="col" className='w-60-par'>Activity</th>
                      <th scope="col" className='w-25 text-right'>Amount</th>
                    </tr>
                  </thead>
                  <tbody>
                    {nextPaymentTransaction.length > 0
                      ?
                      nextPaymentTransaction.map((payment_tansaction: any, index: any) => {
                        return (
                          <tr key={index}>
                            <td data-label="Account">{moment.unix(payment_tansaction.current_period_end).format("MM/DD/YYYY")}</td>
                            <td data-label="Due Date">Your Next Month plan payment Pending. </td>
                            <td data-label="Amount" className='text-right f-18 '>AED {addZeroes(new Intl.NumberFormat('ar-AE').format(payment_tansaction.amount / 100))} </td>
                          </tr>
                        )
                      })
                      :
                      <tr><td colSpan={6} style={{ "textAlign": "center" }}>No Any Transactions Found</td></tr>
                    }
                    {/*<tr>
                      <td data-label="Account">04/01/2016</td>
                      <td data-label="Due Date">Your payment method <a href="#" className='c-0055BA'><u>will be charged by the</u>  </a></td>
                      <td data-label="Amount" className='text-right f-18 '><b>$1,190</b></td>
                    </tr>
                    <tr>
                      <td data-label="Account">04/01/2016</td>
                      <td data-label="Due Date">Your payment method <a href="#" className='c-0055BA'><u>will be charged by the</u>  </a></td>
                      <td data-label="Amount" className='text-right f-18 '><b>$1,190</b></td>
                    </tr> */}
                  </tbody>
                </table>
              </div>
              <div className='row mt-3'>
                <div className='col-sm-4'>
                  <p className='f-14 c-4D4D4D w-600  '>Payment method</p>
                  <p className='f-14 c-4D4D4D w-400 '><img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/visa-2.png'} alt="visa-2" className='w-30' />   {userData.card_type} ending in {userData.card_number ? userData.card_number.substr(-4) : ''} &nbsp; <a href="#" className='c-0055BA w-600' onClick={modalConfirmUpdateCardDetailsPopupOpen}>Edit</a></p>
                </div>
                <div className='col-sm-2'>
                  <p className='f-14 c-4D4D4D w-600  '>Currency <i className="fa-solid fa-circle-info c-0055BA"> </i></p>
                  <p className='f-14 c-4D4D4D w-600   '>AED</p>
                </div>
                <div className='col-sm-6'>
                  <p className='f-14 c-4D4D4D w-600  '>Payment method</p>
                  <p className='f-14 c-4D4D4D w-600   '>{userData.email}</p>
                </div>
              </div>
            </div>
            <div className='card-box p-4 b-r-8 mt-4'>
              <div className='row'>
                <div className='col-lg-6 col-md-4'>
                  <p className='f-22 c-191919 w-800'>Transaction History</p>
                  <div className="btn-group m-center-j" role="group" aria-label="Basic outlined example mt-2">
                    <button type="button" className="btn btn-outline-primary active pad-spac">All</button>
                    <button type="button" className="btn btn-outline-primary pad-spac">Payments</button>
                    <button type="button" className="btn btn-outline-primary pad-spac">Invoices</button>
                  </div>
                  <br />
                  <br />
                </div>
                <div className='col-lg-6 col-md-4'>
                  <form onSubmit={handleSubmitFilter} className='form-in-3'>
                    <div className='row'>
                      <div className='col-sm-4'>
                        <input type='date' placeholder='9/1/2023' className='date-viza w-100 b-r-8 p-2' onChange={(e: any) => setFilterStartDate(e.target.value)} />
                        {filterStartDateError && <span className="error text-danger">{filterStartDateError}</span>}
                      </div>
                      <div className='col-sm-4'>
                        <input type='date' placeholder='9/1/2023' className='date-viza w-100 b-r-8 p-2' onChange={(e: any) => setFilterEndDate(e.target.value)} />
                        {filterEndDateError && <span className="error text-danger">{filterEndDateError}</span>}
                      </div>
                      <div className='col-sm-4'>
                        <button className="btn-a border-primary-size-16 border-99C8FF b-4  b-r-8 p-2 w-100" type='submit'>Update</button>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
              <div className='pay-due'>
                <table>
                  <thead>
                    <tr>
                      <th scope="col" >Due Date</th>
                      <th scope="col" className='w-60-par'>Activity</th>
                      <th scope="col" className='text-right'>Amount (AED) <i className="fa-solid fa-circle-info c-0055BA"></i></th>
                      <th scope="col" className='text-right'>Blance (AED) <i className="fa-solid fa-circle-info c-0055BA"></i></th>
                    </tr>
                  </thead>
                  <tbody>
                    {paymentTransaction.length > 0
                      ?
                      paymentTransaction.map((payment_tansaction: any, index: any) => {
                        return (
                          <tr key={index}>
                            <td data-label="Account">{moment.unix(payment_tansaction.current_period_start).format("MM/DD/YYYY")}</td>
                            <td data-label="Due Date">Your {payment_tansaction.plan_type == 'Year' ? 'Yearly' : 'Monthly'} Plan Payment Successfull!</td>
                            <td data-label="Amount" className='text-right f-16 '>AED {addZeroes(new Intl.NumberFormat('ar-AE').format(payment_tansaction.amount / 100))} </td>
                            <td data-label="Amount" className='text-right f-16 '>AED {addZeroes(new Intl.NumberFormat('ar-AE').format(payment_tansaction.amount / 100))} </td>
                          </tr>
                        )
                      })
                      :
                      <tr><td colSpan={6} style={{ "textAlign": "center" }}>No Any Transactions Found</td></tr>
                    }
                    {/* <tr>
                      <td data-label="Account">04/01/2016</td>
                      <td data-label="Due Date">Your payment method <a href="#" className='c-0055BA'><u>will be charged by the</u>  </a></td>
                      <td data-label="Amount" className='text-right f-16 c-cc6666 '> $1,190 </td>
                      <td data-label="Amount" className='text-right f-16 '> $1,190 </td>
                    </tr>
                    <tr>
                      <td data-label="Account">04/01/2016</td>
                      <td data-label="Due Date">Your payment method <a href="#" className='c-0055BA'><u>will be charged by the</u>  </a></td>
                      <td data-label="Amount" className='text-right f-16 c-cc6666 '> $1,190 </td>
                      <td data-label="Amount" className='text-right f-16 '> $1,190 </td>
                    </tr>
                    <tr>
                      <td data-label="Account">04/01/2016</td>
                      <td data-label="Due Date">Your payment method  will be charged by the  </td>
                      <td data-label="Amount" className='text-right f-16  '> $1,190 </td>
                      <td data-label="Amount" className='text-right f-16 '> $1,190 </td>
                    </tr>
                    <tr>
                      <td data-label="Account">04/01/2016</td>
                      <td data-label="Due Date">Your payment method <a href="#" className='c-0055BA'><u>will be charged by the</u>  </a></td>
                      <td data-label="Amount" className='text-right f-16 c-cc6666 '> $1,190 </td>
                      <td data-label="Amount" className='text-right f-16 '> $1,190 </td>
                    </tr>
                    <tr>
                      <td data-label="Account">04/01/2016</td>
                      <td data-label="Due Date">Your payment method  will be charged by the </td>
                      <td data-label="Amount" className='text-right f-16   '> $1,190 </td>
                      <td data-label="Amount" className='text-right f-16 '> $1,190 </td>
                    </tr>
                    <tr>
                      <td data-label="Account">04/01/2016</td>
                      <td data-label="Due Date">Your payment method <a href="#" className='c-0055BA'><u>will be charged by the</u>  </a></td>
                      <td data-label="Amount" className='text-right f-16   '> $1,190 </td>
                      <td data-label="Amount" className='text-right f-16 '> $1,190 </td>
                    </tr>
                    <tr>
                      <td data-label="Account">04/01/2016</td>
                      <td data-label="Due Date">Your payment method <a href="#" className='c-0055BA'><u>will be charged by the</u>  </a></td>
                      <td data-label="Amount" className='text-right f-16 c-cc6666 '> $1,190 </td>
                      <td data-label="Amount" className='text-right f-16 '> $1,190 </td>
                    </tr>
                    <tr>
                      <td data-label="Account">04/01/2016</td>
                      <td data-label="Due Date">Your payment method <a href="#" className='c-0055BA'><u>will be charged by the</u>  </a></td>
                      <td data-label="Amount" className='text-right f-16   '> $1,190 </td>
                      <td data-label="Amount" className='text-right f-16 '> $1,190 </td>
                    </tr> */}
                  </tbody>
                </table>
                <Pagination
                  items={totalPaymentTransaction.length}
                  currentPage={currentPage}
                  pageSize={pageSize}
                  onPageChange={onPageChange}
                />
                {/* <nav aria-label="Page navigation example">
                  <ul className="pagination justify-content-center mt-4">
                    <li className="page-item active"><a className="page-link" href="#">1</a></li>
                    <li className="page-item"><a className="page-link" href="#">2</a></li>
                    <li className="page-item"><a className="page-link" href="#">3</a></li>
                    <li className="page-item"><a className="page-link" href="#">4</a></li>
                    <li className="page-item">
                      <a className="page-link" href="#"><i className="fa-solid fa-angle-right"></i></a>
                    </li>
                  </ul>
                </nav> */}
              </div>
            </div>
          </div>
          <PopupModal show={modalConfirmUpdateCardDetails} handleClose={modalConfirmUpdateCardDetailsPopupClose} customclass={' modal-dialog-centered   modal-sm body-sp-0'} closebtnclass={'close-x  bg-0055BA border-design'} closebtnicon={'icon'}>
            <div className="popup-body">
              <form className="pb-2 stripe_form" onSubmit={handleSubmit}>
                <div className="row">
                  <div className="col-12 mb-3">
                    <label>Card Number*</label>
                    <input type="text" className="form-control" value={cardNumber} onChange={handleCardNumberChange} placeholder='Enter Card Number' maxLength={16} />
                    {cardError && <span className="error text-danger">{cardError}</span>}
                  </div>
                  <div className="col-6 mb-3">
                    <label>Card Expire Month*</label>
                    <input type="text" className="form-control" id="expiryMonth" value={expiryMonth} onChange={handleExpiryMonthChange} placeholder='MM' maxLength={2} />
                    {expiryMonthError && <span className="error text-danger">{expiryMonthError}</span>}
                  </div>
                  <div className="col-6 mb-3">
                    <label>Card Expire Year*</label>
                    <input type="text" className="form-control" id="expiryYear" value={expiryYear} onChange={handleExpiryYearChange} placeholder='YYYY' maxLength={4} />
                    {expiryDateError && <span className="error text-danger">{expiryDateError}</span>}
                  </div>
                  <div className="col mb-4">
                    <label>Card CVV*</label>
                    <input type="text" className="form-control" value={cvc} onChange={handleCvcChange} placeholder='ex. 311' maxLength={3} />
                    {cvcError && <span className="error text-danger">{cvcError}</span>}
                  </div>
                  <div className='col-md-12 mb-3 text-right'>
                    <button className="btn signup mobile-w-100 btn-submit stripe_form_btn m-0" type="submit" style={{ "width": "100%" }}>
                      Update
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </PopupModal>
          <div className='col-lg-3'>
            <div className='card-box  p-4   b-r-8'>
              <p className='f-18 c-4D4D4D w-800  '>Payment method   method</p>
              <div className='row mt-3'>
                <div className='col-6'>
                  <p className='f-14 c-4D4D4D w-600  mb-1'>Amount</p>
                </div>
                <div className='col-6 text-right'>
                  <p className='f-14 c-4D4D4D w-600  mb-1'>$240.00</p>
                </div>
              </div>
              <div className='row mt-1'>
                <div className='col-6'>
                  <p className='f-14 c-4D4D4D w-600  mb-1'>Amount</p>
                </div>
                <div className='col-6 text-right'>
                  <p className='f-14 c-4D4D4D w-600  mb-1'>$240.00</p>
                </div>
              </div>
              <div className='row mt-1'>
                <div className='col-6'>
                  <p className='f-14 c-4D4D4D w-600  mb-1'>Amount</p>
                </div>
                <div className='col-6 text-right'>
                  <p className='f-14 c-4D4D4D w-500  mb-1'><a href="#" className='c-0055BA'><u>#01256545</u></a> </p>
                </div>
              </div>
            </div>
            <div className='card-box  p-4   b-r-8'>
              <div className='row mt-3'>
                <div className='col-8'>
                  <p className='f-18 c-4D4D4D w-800  '>Method will   &nbsp; <i className="fa-solid fa-circle-info c-0055BA"> </i> </p>
                </div>
                <div className='col-4 text-right'>
                  <p className=''><a href="#" className='new-btn'>New</a></p>
                </div>
              </div>
              <div className='row mt-1'>
                <div className='col-7'>
                  <p className='f-14 c-4D4D4D w-600  mb-1'>Amount</p>
                  <p className='f-14 c-4D4D4D w-400  mb-1'>Payment method</p>
                  <p className='f-14 c-4D4D4D w-600  mb-1'>$240.00</p>
                </div>
                <div className='col-5 text-right'>
                  <p className='f-14 c-4D4D4D w-600  mb-1'>$240.00</p>
                  <p className='f-14 c-4D4D4D w-400  mb-1'>04/01/2016	</p>
                </div>
              </div>
              <div className='row mt-2'>
                <div className='col-6'>
                  <p className='f-14 c-4D4D4D w-600  mb-1'>Amount</p>
                </div>
                <div className='col-6 text-right'>
                  <p className='f-14 c-4D4D4D w-600  mb-1'>$240.00</p>
                </div>
              </div>
              <div className='row mt-1'>
                <div className='col-6'>
                  <p className='f-14 c-4D4D4D w-600  mb-1'>Amount</p>
                </div>
                <div className='col-6 text-right'>
                  <p className='f-14 c-4D4D4D w-500  mb-1'><a href="#" className='c-0055BA'><u>#01256545</u></a> </p>
                </div>
              </div>
            </div>
            <div className='card-box  p-4   b-r-8'>
              <div className='row '>
                <div className='col-12'>
                  <p className='f-18 c-4D4D4D w-800 mb-3 '>Helpful Links</p>
                  <p className='mb-1'><a href="#" className='c-0055BA'><u>will be charged by the</u>  </a></p>
                  <p className='mb-1'><a href="#" className='c-0055BA'><u>will be charged by the</u>  </a></p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {showPopupunsave &&
        <SuccessToast message={showmessage} />
      }
      {showPopupunerror &&
        <ErrorToast message={showmessage} />
      }

    </>
  )
}
