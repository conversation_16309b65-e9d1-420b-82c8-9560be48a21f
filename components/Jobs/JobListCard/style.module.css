.job-list-card {
  display: flex;
  padding: 12px;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  background: #fff;
  cursor: pointer;
}

.job-list-card:hover {
  box-shadow:
    0px 1px 3px 0px rgba(21, 21, 21, 0.12),
    0px 2px 5px 0px rgba(21, 21, 21, 0.1),
    0px 4px 12px 0px rgba(21, 21, 21, 0.12);
}

.job-list-card.active {
  box-shadow:
    0px 1px 3px 0px rgba(21, 21, 21, 0.12),
    0px 2px 5px 0px rgba(21, 21, 21, 0.1),
    0px 4px 12px 0px rgba(21, 21, 21, 0.12);
}

.job-list-card .job-feature-bookmark {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
  padding-bottom: 4px;
}

.has-not-future.job-feature-bookmark {
  justify-content: flex-end;
}

.job-title {
  overflow: hidden;
  color: #2c2c2c;
  text-overflow: ellipsis;
  font-size: 18px;
  font-weight: 700;
  line-height: 160%;
}

.job-company-name {
  font-size: 16px;
  font-weight: 400;
  line-height: 140%;
  color: #999999;
}

.job-country-city {
  font-size: 16px;
  font-weight: 400;
  line-height: 140%;
  color: #999999;
}
.company-info {
  padding-top: 8px;
  padding-bottom: 16px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.job-location {
  display: flex;
  align-items: center;
  gap: 4px;
}
.posted-time {
  color: #999;
  font-size: 12px;
  font-weight: 300;
  line-height: 140%;
}
.bookmark-job {
  cursor: pointer;
}
.company-variant {
  display: flex;
  width: 306px;
  height: 212px;
  padding: 12px;
  align-items: flex-start;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
}
.view_jobs_container {
  padding-top: 16px;
  width: 100%;
}
.view_jobs_container button {
  display: flex;
  padding: 20px 16px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  border-radius: 8px;
  border: 2px solid #0055ba;
  background: rgba(0, 85, 186, 0.08);
  width: 100%;
}

@media screen and (max-width: 991px) {
  .company-variant {
    width: 100%;
  }
}
