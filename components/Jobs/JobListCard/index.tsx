import {Job} from '@/lib/types';
import Image from 'next/image';
import {useContext} from 'react';
import styles from './style.module.css';
import moment from 'moment';
import AuthContext from '@/Context/AuthContext';
import {Button} from 'antd';
import {useRouter} from 'next/router';
import {useHandleJobSaved} from '@/hooks/useHandleSavedJobs';
import {useGetCityNameByCityId} from '@/hooks/useGetCityNameByCityId';

interface jobListCardProps {
  job: Job;
  index: number;
  jobInCityName?: any;
  onClick?: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
  selectedJob?: Job | null;
  setSelectedJob?: React.Dispatch<React.SetStateAction<Job | null>>;
  variant?: 'job' | 'company';
  jobs?: Job[];
  favorites?: number[];
}

export const JobListCard = ({
  index,
  job,
  jobInCityName,
  onClick,
  selectedJob,
  setSelectedJob,
  variant = 'job',
  jobs,
  favorites,
}: jobListCardProps) => {
  const {user} = useContext(AuthContext);
  const router = useRouter();
  const {savedJob, unSaveJob, saveJob} = useHandleJobSaved(job, jobs ?? [], favorites ?? []);
  const {cityName} = useGetCityNameByCityId(job?.country?.country_name, job.job_city);

  const handleJobDetail = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    onClick?.(e);
    handleCardClick();
    setSelectedJob?.(job);
  };

  const handleBookMark = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    e.stopPropagation();
    savedJob ? unSaveJob(user?.id, job.id, job.company_id) : saveJob(job.id, job.company_id);
  };

  const handleCardClick = () => {
    const updateQuery = {
      ...router.query,
      job: job.job_slug,
    };

    router.push(
      {
        query: updateQuery,
      },
      undefined,
      {shallow: true},
    );
  };

  return (
    job && (
      <div
        className={`${styles['job-list-card']} ${job.id === selectedJob?.id ? styles['active'] : ''} ${
          variant === 'company' && styles['company-variant']
        }`}
        onClick={handleJobDetail}>
        <div className={`${styles['job-feature-bookmark']} ${job.is_featured === 0 && styles['has-not-future']}`}>
          {job.is_featured !== 0 ? (
            <div className="feat">
              <p className="feature">
                <img src="/icons/gem-outline.svg" alt="" width={14} height={14} />
                <span>Featured</span>
              </p>
            </div>
          ) : (
            ''
          )}
          <div className={styles['bookmark-job']} onClick={e => handleBookMark(e)}>
            <img
              src={savedJob ? '/icons/bookmark-blue-filled.svg' : '/icons/bookmark-black.svg'}
              width={13}
              height={17}
              alt="bookmark"
            />
          </div>
        </div>
        <div className={styles['job-title']}>{job.job_title}</div>
        <div className={styles['company-info']}>
          {variant === 'job' && <div className={styles['job-company-name']}>{job.company?.company_name}</div>}
          <div className={styles['job-location']}>
            <img src={'/icons/location-dot.svg'} alt="" width={20} height={20} />
            <span className={styles['job-country-city']}>
              {job.country?.country_name} - {jobInCityName || cityName}
            </span>
          </div>
        </div>

        <div className={styles['posted-time']}>
          Posted {''}
          {moment(job.created_at).fromNow()}
        </div>
        {variant === 'company' && (
          <div className={styles.view_jobs_container}>
            <Button>View Job</Button>
          </div>
        )}
      </div>
    )
  );
};
