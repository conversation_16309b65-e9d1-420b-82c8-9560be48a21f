import React, { useEffect, useState } from "react";
import { Button, Checkbox, Form, FormInstance, Select, Slider } from "antd";
import { Country, Sector, City, Skill } from "@/lib/types";
import { SearchOutlined } from "@ant-design/icons";
import axios from "axios";
import { getSectors } from "@/lib/ApiAdapter";
import { getAllCities, getAllSkills } from '@/lib/frontendapi';

interface JobSearchFiltersProps {
  form: FormInstance;
  onFinish?: (values: any) => void;
  countries?: Country[];
  skillName?: string;
  jobTypeData: string;
}

const MAX_SALARY = 70000;
const JobInCitySearchFilters = ({ form, onFinish, countries, skillName, jobTypeData }: JobSearchFiltersProps) => {
  const [sectors, setSectors] = useState<Sector[]>();
  const [citiesItems, setCitiesItems] = useState<City[]>();
  const [skills, setSkills] = useState<Skill[]>();
  const [selectedSalary, setSelectedSalary] = useState<number[]>();
  const [experienceRange, setExperienceRange] = useState<number[]>();
  const [selectedCurrency, setSelectedCurrency] = useState<Country>();

  useEffect(() => {
    const cancelTokenSource = axios.CancelToken.source();
    getSectors(undefined, undefined, cancelTokenSource)
      .then((res) => {
        setSectors(res);
      })
      .catch(() => { });
    getAllCities()
      .then(res => {
        setCitiesItems(res.data);
      })
      .catch(() => { });
    getAllSkills()
      .then(res => {
        setSkills(res.data);
      })
      .catch(() => { });
    return cancelTokenSource.cancel;
  }, []);

  let currencyCode: RegExpMatchArray | null = null;
  if (selectedCurrency) {
    currencyCode = selectedCurrency.currency.match(/(?<=\()(.*)(?=\))/g);
  }

  return (
    <div className="sidebar-filters">
      <Form form={form} onFinish={onFinish} className={"sidebar-filters-form"}>
        <div>
          <h4>Salary</h4>
          {selectedSalary && (
            <p>
              {currencyCode} {selectedSalary[0]} - {currencyCode} {selectedSalary[1]}
            </p>
          )}
          <Form.Item name={"salary"} noStyle initialValue={
            selectedSalary && selectedSalary.length > 0
              ? selectedSalary
              : [0, 70000]
          }>
            <Slider
              step={100}
              marks={{ 0: "Min", 70000: "Max" }}
              onChange={(values) => {
                setSelectedSalary(values);
              }}
              range
              defaultValue={[0, 70000]}
              max={MAX_SALARY}
            />
          </Form.Item>
          <Form.Item name={"currency"} noStyle>
            <Select
              onChange={(value) => {
                setSelectedCurrency(countries?.find((c) => c.id == value));
              }}
              placeholder={"Choose currency"}
              style={{ width: "100%" }}
              options={countries
                ?.filter((c) => c.currency)
                .map((c: Country) => {
                  return { label: c.currency, value: c.id.toString() };
                })}
            />
          </Form.Item>
        </div>
        <div>
          <h4>Experience</h4>
          <Form.Item name={"experience"} noStyle>
            {experienceRange && (
              <p>
                From {experienceRange[0]} to {experienceRange[1]} years
              </p>
            )}
            <Slider
              marks={{ 0: "", 20: "20+" }}
              onChange={(values) => {
                setExperienceRange(values);
              }}
              range
              defaultValue={[0, 20]}
              max={20}
            />
          </Form.Item>
        </div>
        <div>
          <h4>Job Type</h4>
          <Form.Item name={"job_type"} noStyle initialValue={jobTypeData}>
            <Checkbox.Group
              style={{ width: "100%" }}
              options={[
                { label: "Full time", value: "fulltime" },
                { label: "Part time", value: "parttime" },
                { label: "Contract", value: "contract" },
                { label: "Freelance", value: "freelance" },
              ]}
            />
          </Form.Item>
        </div>
        <div>
          <h5>Skills</h5>
          <Form.Item name={"skill"} noStyle>
            <Select
              allowClear
              showSearch
              mode={"multiple"}
              suffixIcon={<SearchOutlined />}
              placeholder={"Search skill"}
              style={{ width: "100%" }}
              options={skills?.map((s) => {
                return { label: s.skills, value: s.skills };
              })}
              defaultValue={skillName}
            />
          </Form.Item>
        </div>
        <div>
          <h5>Sector</h5>
          <Form.Item name={"sector"} noStyle>
            <Select
              allowClear
              showSearch
              mode={"multiple"}
              suffixIcon={<SearchOutlined />}
              placeholder={"Search sector"}
              style={{ width: "100%" }}
              options={sectors?.map((s) => {
                return { label: s.sector_name, value: s.id };
              })}
            />
          </Form.Item>
        </div>
        <Button type={"primary"} block htmlType={"submit"} id="dfgfdg">
          Apply filters
        </Button>
      </Form>
    </div>
  );
};

export default JobInCitySearchFilters;
