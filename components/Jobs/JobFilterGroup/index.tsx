import {type filterItems} from '@/components/Frontend/JobOfferPage';
import styles from './style.module.css';
import Image from 'next/image';
import useWindowDimensions from '@/helpers/useWindowDimensions';
import CustomDropdown from '@/components/Common/Dropdown';
import {Country, Sector} from '@/lib/types';
import {useHandleSelectSaveToQuery} from '@/hooks/useHandleSelectSaveToQuery';

export const JobFilterGroup = ({countries, sectors}: {countries?: Country[]; sectors?: Sector[]}) => {
  const windowDimensions: any = useWindowDimensions();

  const {handleSelectSaveToQuery} = useHandleSelectSaveToQuery();

  const filterItems: filterItems[] = [
    {
      label: 'Jobs By Location',
      menuProps:
        countries?.map((country: Country) => {
          return {label: country.country_name, value: [country.country_name]};
        }) || [],
      onSelect: value => handleSelectSaveToQuery(value, 'country'),
    },
    {
      label: 'Jobs By Sector',
      menuProps: sectors?.map((sector: Sector) => {
        return {label: sector.sector_name, value: [sector.sector_name]};
      }),
      onSelect: value => handleSelectSaveToQuery(value, 'keywords'),
      multiSelect: true,
      width: 280,
    },
    {
      label: 'jobs Type',
      menuProps: [
        {
          label: 'Full Time',
          value: ['fulltime'],
        },
        {
          label: 'Part Time',
          value: ['parttime'],
        },
        {
          label: 'Contract',
          value: ['contract'],
        },
        {
          label: 'Freelance',
          value: ['freelance'],
        },
      ],
      multiSelect: true,
      onSelect: value => handleSelectSaveToQuery(value, 'job_type'),
    },
  ];
  return (
    <div className={`${styles['job-filter-container']} `}>
      <div className={`${styles['circle-bg']} ${styles['circle-left']}`}>
        <img
          src={windowDimensions.width > 991 ? '/images/circle-bg.png' : '/images/circle-bg-mobile.png'}
          alt="circles-bg"
          width={windowDimensions.width > 991 ? 200 : 80}
          height={windowDimensions.width > 991 ? 150 : 80}
        />
      </div>
      {filterItems.map((item, index) => {
        return (
          <CustomDropdown
            key={index}
            item={item}
            onSelect={
              item.onSelect
                ? (value: string[]) => {
                    item.onSelect?.(value);
                  }
                : () => {}
            }
            variant={'dropdown-transparent'}
            countries={countries}
            className={styles['dropdown']}
          />
        );
      })}
      <div className={`${styles['circle-bg']} ${styles['circle-right']}`}>
        <img
          src={windowDimensions.width > 991 ? '/images/circle-bg.png' : '/images/circle-bg-mobile.png'}
          alt="circles-bg"
          width={windowDimensions.width > 991 ? 200 : 80}
          height={windowDimensions.width > 991 ? 150 : 80}
        />
      </div>
    </div>
  );
};
