.job-filter-container {
  padding: 40px 72px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  align-self: stretch;
  background: #001c3d;
  flex-wrap: wrap;
  position: relative;
}
.circle-bg {
  position: absolute;
}

.circle-bg.circle-left {
  left: 0;
  top: -30px;
}
.circle-bg.circle-right {
  right: 0;
  bottom: -30px;
  rotate: 180deg;
}

.dropdown {
  display: flex;
  padding: 27px 16px 27px 32px;
  align-items: center;
  justify-content: center;
  gap: 10px;
  border-radius: 16px;
  border: 1px solid #d9d9d9;
  background: transparent !important;
  color: white;
  font-family: Archivo;
  font-size: 18px;
  line-height: 120%;
}
.dropdown:hover {
  background: white !important;
  color: white !important;
  border: 1px solid white !important;
  color: #001c3d !important;
}

.dropdown button {
  display: flex;
  align-items: center;
}
.dropdown-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
}

@media screen and (max-width: 768px) {
  .dropdown {
    font-size: 16px;
    line-height: 120%;
  }
  .circle-bg.circle-left {
    left: 0;
    top: 0;
  }
  .circle-bg.circle-right {
    right: 0;
    bottom: 0;
  }
}
