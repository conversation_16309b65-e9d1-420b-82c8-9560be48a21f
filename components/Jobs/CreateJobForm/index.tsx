import React, {useContext, useEffect, useState} from 'react';
import {
  Button,
  Checkbox,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  message,
  notification,
  Row,
  Select,
  Switch,
} from 'antd';

import {HtmlEditor} from '@/components/Common/HtmlEditor';
import {
  getAllCountries,
  getAllSectors,
  getAllSkills,
  getExperiance,
  getSingleCountryAllCities,
  savePublishJob,
} from '@/lib/frontendapi';
import {City, Company, Country, Experience, Industry, Job, Sector, Skill} from '@/lib/types';
import {getAllCompany, getAllIndustries} from '@/lib/adminapi';
import AuthContext from '@/Context/AuthContext';
import dayjs from 'dayjs';
import moment from 'moment';
import {InputUi} from '@/components/Common/Input';
import {log} from 'console';
import {Dayjs} from 'dayjs';

interface CreateJobFormProps {
  onCompleted?: () => void;
  job?: any;
}

const FORM_GUTTER = 15;

const CreateJobForm = ({onCompleted, job}: CreateJobFormProps) => {
  const [skills, setSkills] = useState<Skill[]>([]);
  const [experiences, setExperiences] = useState<Experience[]>();
  const [sectors, setSectors] = useState<Sector[]>();
  const [countries, setCountries] = useState<Country[]>();
  const [industries, setIndustries] = useState<Industry[]>();
  const [cities, setCities] = useState<City[]>();
  const [isLoading, setIsLoading] = useState(false);
  const [description, setDescription] = useState<string>(job?.job_description ? job?.job_description : '');
  const [loadingCities, setLoadingCities] = useState(false);
  const [monthlyFixedSalaryCurrencyOptions, setMonthlyFixedSalaryCurrencyOptions] = useState<string[]>([]);
  const [companies, setCompanies] = useState<Company[]>();
  const [formErrorMessage, setFormErrorMessage] = useState<string>();
  const {user} = useContext(AuthContext);
  const [isFeatured, setIsFeatured] = useState(false);

  useEffect(() => {
    if (user && user.role === 'admin') {
      getAllCompany()
        .then(res => {
          setCompanies(res.data.data);
        })
        .catch(err => {
          console.log(err);
        });
    }
  }, [user]);

  useEffect(() => {
    getAllCountries()
      .then(res => {
        const activeCountries = res.filter((country: Country) => country.status === 'active');
        setCountries(activeCountries);
      })
      .catch(err => {
        console.log(err);
      });

    getAllSkills()
      .then(res => {
        setSkills(res.data);
      })
      .catch(err => {
        console.log(err);
      });

    getExperiance()
      .then(res => {
        setExperiences(res.data);
      })
      .catch(err => {
        console.log(err);
      });

    getAllSectors()
      .then(res => {
        setSectors(res.sectors);
      })
      .catch(err => {
        console.log(err);
      });

    getAllIndustries()
      .then(res => {
        setIndustries(res.data);
      })
      .catch(err => {
        console.log(err);
      });
    searchCompanies('');
  }, []);

  useEffect(() => {
    onCountrySelected();
  }, [countries]);

  const searchCompanies = (value: string) => {
    getAllCompany(value)
      .then(res => {
        setCompanies(res.data.data);
      })
      .catch(err => {
        console.log(err);
      });
  };

  const onCountrySelected = (value: number = parseInt(job?.job_country)) => {
    if (!countries) return;
    const selectedCountry: any = countries.find((c: Country) => c.id === value && c.status == 'active');

    if (value) {
      setMonthlyFixedSalaryCurrencyOptions([selectedCountry.currency]);
    }
    setLoadingCities(true);
    getSingleCountryAllCities(value)
      .then(res => {
        setLoadingCities(false);
        setCities(res.data);
      })
      .catch(err => {
        setLoadingCities(false);
        notification.warning({message: 'No cities registered on this country'});
        console.log(err);
      });
  };
  const submitForm = (data: any) => {
    console.log(data, 'data');
    if (!description) {
      setFormErrorMessage('Please, fill the description of the job');
      return;
    }

    const descriptionIsValid = description.replace(/<[^/>][^>]*><\/[^>]+>/gim, '').trim() === '';
    if (descriptionIsValid) {
      setFormErrorMessage('Please, fill the description of the job');
      return;
    }

    setFormErrorMessage(undefined);
    const jobData = {
      ...data,
      user_id: user?.id,
    };
    if (!data.company_id) {
      jobData.company_id = user?.company_id;
    }
    jobData.description = description;
    jobData.featured = isFeatured;
    jobData.job_id = job?.id;
    setIsLoading(true);
    savePublishJob(jobData)
      .then(() => {
        message.success('Saved');
        setIsLoading(false);
        if (onCompleted) {
          onCompleted();
        }
      })
      .catch(err => {
        notification.error({message: err?.response?.data?.error});
        setIsLoading(false);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  return (
    <Form layout={'vertical'} onFinish={submitForm} size={'large'}>
      <Row gutter={FORM_GUTTER}>
        {user && user.role === 'admin' && (
          <Col md={12}>
            <Form.Item
              label={'Company'}
              name={'company_id'}
              initialValue={job?.company_id ? parseInt(job?.company_id) : null}
              rules={[{required: true}]}>
              <Select
                showSearch={true}
                onSearch={searchCompanies}
                placeholder={'Company'}
                filterOption={false}
                optionFilterProp={'label'}
                options={companies?.map(c => {
                  return {label: c.company_name, value: c.id};
                })}
              />
            </Form.Item>
          </Col>
        )}
        <Col md={12}>
          <Form.Item name={'isFeatured'} valuePropName="checked" label={' '}>
            <Switch defaultChecked={job?.is_featured == 1 ? true : false} onChange={setIsFeatured} />
            &nbsp;&nbsp;Post as featured job
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={FORM_GUTTER}>
        <Col md={18}>
          <Form.Item label={'Job title'} rules={[{required: true}]} initialValue={job?.job_title} name={'title'}>
            <Input placeholder="Job Title" />
          </Form.Item>
        </Col>
        <Col md={6}>
          <Form.Item label={'Job type'} initialValue={job?.job_type || null} name={'type'}>
            <Select
              placeholder={'Select type'}
              options={[
                {value: '', label: 'Select type'},
                {value: 'fulltime', label: 'Full time'},
                {value: 'parttime', label: 'Part time'},
                {value: 'contract', label: 'Contract'},
                {value: 'freelance', label: 'Freelance'},
              ]}
            />
          </Form.Item>
        </Col>
      </Row>
      <Form.Item label={'Job description'} initialValue={description}>
        <HtmlEditor
          value={description}
          onChange={(name, value) => {
            if (value) {
              setDescription(value);
            }
          }}
        />
        {formErrorMessage && <div className="text-danger mt-2">{formErrorMessage}</div>}
      </Form.Item>

      <Row gutter={FORM_GUTTER}>
        <Col md={6}>
          <Form.Item
            label={'Country'}
            name={'country_id'}
            initialValue={parseInt(job?.job_country) || null}
            rules={[{required: true, message: 'Please select country'}]}>
            <Select
              showSearch={true}
              optionFilterProp={'label'}
              placeholder={'Select country'}
              options={countries?.map((c: Country) => {
                return {value: c.id, label: c.country_name};
              })}
              onChange={onCountrySelected}
            />
          </Form.Item>
        </Col>
        <Col md={6}>
          <Form.Item
            label={'Job city'}
            name={'job_city'}
            initialValue={parseInt(job?.job_city) || null}
            rules={[{required: true, message: 'Please select city'}]}>
            <Select
              showSearch={true}
              loading={loadingCities}
              optionFilterProp={'label'}
              placeholder={'Select city'}
              options={cities?.map((c: City) => {
                return {value: c.id, label: c.city_name};
              })}
            />
          </Form.Item>
        </Col>
        <Col md={6}>
          <Form.Item
            label={'Industry'}
            name={'industry'}
            initialValue={user?.role == 'admin' ? parseInt(job?.industry_id) || null : parseInt(job?.industry) || null}>
            <Select
              showSearch={true}
              optionFilterProp={'label'}
              placeholder={'Select industry'}
              options={industries?.map((c: Industry) => {
                return {value: c.id, label: c.name};
              })}
            />
          </Form.Item>
        </Col>
        <Col md={6}>
          <Form.Item label={'Sector'} name={'sector_id'} initialValue={parseInt(job?.sector_id) || null}>
            <Select
              showSearch={true}
              optionFilterProp={'label'}
              placeholder={'Select sector'}
              options={sectors?.map((c: Sector) => {
                return {value: c.id, label: c.sector_name};
              })}
            />
          </Form.Item>
        </Col>
        <Col md={6}>
          <Form.Item label={'Postal code'} name={'postal_code'} initialValue={parseInt(job?.postal_code, 10) || null}>
            <Input placeholder="Postal Code" />
          </Form.Item>
        </Col>
        <Col md={6}>
          <Form.Item label={'Street Address'} name={'street_address'} initialValue={job?.street_address}>
            <Input placeholder="Street Address" />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item
        label={'Required skills'}
        name={'skills'}
        initialValue={
          job?.skills_required ? job.skills_required.split(',').map((skill: any) => parseInt(skill.trim(), 10)) : []
        }>
        <Select
          showSearch
          optionFilterProp={'label'}
          allowClear
          mode={'multiple'}
          placeholder={'Select skill'}
          options={skills?.map((c: Skill) => {
            return {value: c.id, label: c.skills};
          })}
        />
      </Form.Item>
      <Row gutter={FORM_GUTTER}>
        <Col md={9} sm={24}>
          <Form.Item
            label={'Monthly Fixed Salary currency'}
            name={'monthly_fixed_salary_currency'}
            initialValue={job?.monthly_fixed_salary_currency}>
            <Select
              placeholder={'Currency'}
              options={monthlyFixedSalaryCurrencyOptions?.map((c: string) => {
                return {value: c, label: c};
              })}
            />
          </Form.Item>
        </Col>
        <Col md={5} sm={24}>
          <Form.Item
            name={'monthly_fixed_min_salary'}
            label={'Minimum'}
            initialValue={job?.monthly_fixed_salary_min}
            rules={[
              ({getFieldValue}) => ({
                validator(_, value) {
                  const maxSalary = getFieldValue('monthly_fixed_max_salary');
                  if (!value || !maxSalary) {
                    return Promise.resolve();
                  }
                  if (value >= maxSalary) {
                    return Promise.reject(new Error('Minimum salary must be less than maximum salary!'));
                  }
                  return Promise.resolve();
                },
              }),
            ]}>
            <InputNumber placeholder={'Min'} min={0} className="w-100" type="number" />
          </Form.Item>
        </Col>
        <Col md={5} sm={24}>
          <Form.Item
            name={'monthly_fixed_max_salary'}
            label={'Maximum'}
            initialValue={job?.monthly_fixed_salary_max}
            rules={[
              ({getFieldValue}) => ({
                validator(_, value) {
                  const minSalary = getFieldValue('monthly_fixed_min_salary');
                  if (!value || !minSalary) {
                    return Promise.resolve();
                  }
                  if (value <= minSalary) {
                    return Promise.reject(new Error('Maximum salary must be greater than minimum salary!'));
                  }
                  return Promise.resolve();
                },
              }),
            ]}>
            <InputNumber
              placeholder={'Max'}
              min={0}
              className="w-100"
              type="number"
              onInput={value => {
                const minSalary = job?.monthly_fixed_salary_min || 0;
                if (value && minSalary && value <= minSalary) {
                  return;
                }
              }}
            />
          </Form.Item>
        </Col>
        <Col md={5} sm={24}>
          <Form.Item label={'Available vacancies'} name={'available_vacancies'} initialValue={job?.available_vacancies}>
            <InputNumber placeholder={'Places'} className="w-100" min={0} />
          </Form.Item>
        </Col>
      </Row>

      <div className="row">
        <div className="col-lg-4">
          <Form.Item
            label={'Deadline'}
            name={'deadline'}
            help={'Estimated time to finish the recruitment process'}
            initialValue={job?.deadline ? dayjs(job?.deadline, 'YYYY-MM-DD') || null : ''}>
            <DatePicker
              defaultValue={dayjs(job?.deadline, 'YYYY-MM-DD')}
              type={'date'}
              disabledDate={current => {
                return current && current < dayjs().startOf('day');
              }}
            />
          </Form.Item>
        </div>
        <div className="col-lg-8">
          <Form.Item label={'Experience'} name={'experience'} initialValue={parseInt(job?.experience) || null}>
            <Select
              placeholder={'Select minimum experience'}
              options={experiences?.map((c: Experience) => {
                return {value: c.id, label: c.name};
              })}
            />
          </Form.Item>
        </div>
      </div>

      <Form.Item name={'hide_employer_details'}>
        <Checkbox>Hide Employer Details</Checkbox>
      </Form.Item>

      <div className="text-end">
        <Button htmlType={'submit'} loading={isLoading} type={'primary'}>
          Post job
        </Button>
      </div>
    </Form>
  );
};

export default CreateJobForm;
