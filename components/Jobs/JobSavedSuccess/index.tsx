import Image from 'next/image';
import styles from './style.module.css';

export const JobSavedSuccess = ({
  unsavedJobs,
  job_id,
  company_id,
  id,
}: {
  unsavedJobs: (id: number, job_id: number, company_id: number) => void;
  job_id: number;
  id?: number;
  company_id?: number;
}) => {
  return (
    <div className={styles['job-saved-success']}>
      <div className={styles['job-saved-success-content']}>
        <div className={styles['job-saved-success-icon']}>
          <img src={'/icons/success-green.svg'} alt="success" width={16} height={16} />
        </div>
        <div className={styles['job-saved-success-text']}>
          <p>
            <span className={styles['job-text-green']}>Job</span>{' '}
            <span className={styles['job-move-text']}>moved to saved</span>
          </p>
        </div>
      </div>
      <div
        className={styles['job-saved-success-undo']}
        onClick={() => unsavedJobs(id ? id : 0, job_id, company_id ? company_id : 0)}>
        Undo
      </div>
    </div>
  );
};
