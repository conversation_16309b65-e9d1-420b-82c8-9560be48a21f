import styles from '../JobDetailCard/style.module.css';

export const JobDetailSkeleton = () => {
  return (
    <div className={styles['job-detail']}>
      <div className={styles['job-detail__overview']}>
        <div className={styles['job-detail__header']}>
          <div className={styles['job-detail__logo']}>
            <div className={styles['skeleton-logo']} />
          </div>
          <div className={styles['job-detail__header-right']}>
            <div className={styles['job-detail__header-top']}>
              <div className={styles['skeleton-title']} />
              <div className={styles['skeleton-text']} />
            </div>
            <div className={styles['job-detail__header-bottom']}>
              <div className={styles['skeleton-button']} />
              <div className={styles['skeleton']} />
            </div>
          </div>
        </div>
        <div className={styles['job-overview']}>
          <div className={styles['job-overview__item']}>
            <div className={styles['skeleton']} />
            <div className={styles['skeleton']} />
          </div>
          <div className={styles['job-overview__item']}>
            <div className={styles['skeleton']} />
            <div className={styles['skeleton']} />
          </div>
          <div className={styles['job-overview__item']}>
            <div className={styles['skeleton']} />
            <div className={styles['skeleton']} />
          </div>
          <div className={styles['job-overview__item']}>
            <div className={styles['skeleton']} />
            <div className={styles['skeleton']} />
          </div>
        </div>
      </div>
      <div className={styles['job-description-container']}>
        <div className={styles['skeleton']} style={{ width: '80%', height: '20px' }} />
        <div className={styles['skeleton']} style={{ width: '100%', height: '100px' }} />
      </div>
      <div className={styles['skills-required-container']}>
        <div className={styles['skeleton']} style={{ width: '40%', height: '20px' }} />
        <div className={styles['skeleton']} style={{ width: '60%', height: '20px' }} />
      </div>
    </div>
  );
};
