import React, { useEffect, useState } from "react";
import { Button, Checkbox, Form, FormInstance, Select, Slider } from "antd";
import { Country, Sector } from "@/lib/types";
import { SearchOutlined } from "@ant-design/icons";
import axios from "axios";
import { useRouter } from 'next/router';
import { getSalaryInsights } from "@/lib/ApiAdapter";
import Image from "next/image";

interface JobSearchFiltersProps {
  form: FormInstance;
  onFinish?: (values: any) => void;
  countries?: Country[];
  sectors?: Sector[];
}

const MAX_SALARY = 70000;

const JobSearchFilters = ({ form, onFinish, countries, sectors }: JobSearchFiltersProps) => {
  const router = useRouter();
  const [jobOffers, setJobOffers] = useState<boolean>(false);
  const [jobFound, setJobFound] = useState<boolean>(false);
  const [selectedSalary, setSelectedSalary] = useState<number[]>();
  const [experienceRange, setExperienceRange] = useState<number[]>();
  const [selectedCurrency, setSelectedCurrency] = useState<Country>();

  useEffect(() => {
    const cancelTokenSource = axios.CancelToken.source();
    if (router.query.hasOwnProperty('country') && router.query.hasOwnProperty('keywords')) {
      let sectorsId = sectors?.filter((res: any) => {
        if (res.sector_name === router.query.keywords) {
          return res;
        }
      });
      let countryID = countries?.filter((res: any) => {
        if (res.country_name === router.query.country) {
          return res;
        }
      });
      if (countryID && countryID.length > 0 && sectorsId && sectorsId.length > 0) {
        getSalaryInsights(countryID[0].id, sectorsId[0].id).then((res: any) => {
          setJobFound(true);
        })
          .catch((error) => {
            setJobFound(false);
          })
      }
      setJobOffers(true);
    } else {
      setJobOffers(false);
    }
    return cancelTokenSource.cancel;
  }, [router.query, countries, sectors]);

  const exploreSalaries = () => {
    if (router.query.hasOwnProperty('country') && router.query.hasOwnProperty('keywords')) {
      const countryName = countries?.find((res: any) => {
        if (res.country_name === router.query.country) {
          return res;
        }
      })?.slug;
      if (countryName && typeof (router.query.keywords) === 'string') {
        router.push(`/salaries/${countryName}/${router.query.keywords?.replace(/\s+/g, '-').replace(/-+/g, '-').toLowerCase()}`);
      }
    }
  }



  let currencyCode: RegExpMatchArray | null = null;
  if (selectedCurrency) {
    currencyCode = selectedCurrency.currency.match(/(?<=\()(.*)(?=\))/g);
  }

  return (
    <div className="sidebar-filters">
      <Form form={form} onFinish={onFinish} className={"sidebar-filters-form"}>
        {jobOffers && !jobFound &&
          <div className="no-result-found">
            {/* <h3>No results found!</h3>
            <p>We couldn't find results for that. Try a different search term.</p> */}
            <h3>No salary insights found!</h3>
            <p>Salary data not found for this role. Try searching for a different position or location.</p>
          </div>
        }

        {jobOffers && jobFound &&
          <div className="salary-insights average-base">
            <p>Average Base Salary</p>
            <h3>AED 6,000</h3>
            <span onClick={() => {
              exploreSalaries();
            }}>Explore Salaries <img src="images/arrow.svg" alt="" /></span>
          </div>
        }
        <div>
          <h4>Salary</h4>
          {selectedSalary && (
            <p>
              {currencyCode} {selectedSalary[0]} - {currencyCode} {selectedSalary[1]}
            </p>
          )}
          <Form.Item name={"salary"} noStyle>
            <Slider
              step={100}
              marks={{ 0: "Min", 70000: "Max" }}
              onChange={(values) => {
                setSelectedSalary(values);
              }}
              range
              defaultValue={[0, 70000]}
              max={MAX_SALARY}
            />
          </Form.Item>
          <Form.Item name={"currency"} noStyle>
            <Select
              onChange={(value) => {
                setSelectedCurrency(countries?.find((c) => c.id == value));
              }}
              placeholder={"Choose currency"}
              style={{ width: "100%" }}
              options={countries
                ?.filter((c) => c.currency)
                .map((c: Country) => {
                  return { label: c.currency, value: c.id.toString() };
                })}
            />
          </Form.Item>
        </div>
        <div>
          <h4>Experience</h4>
          <Form.Item name={"experience"} noStyle>
            {experienceRange && (
              <p>
                From {experienceRange[0]} to {experienceRange[1]} years
              </p>
            )}
            <Slider
              marks={{ 0: "", 20: "20+" }}
              onChange={(values) => {
                setExperienceRange(values);
              }}
              range
              defaultValue={[0, 20]}
              max={20}
            />
          </Form.Item>
        </div>
        <div>
          <h4>Job Type</h4>
          <Form.Item name={"job_type"} noStyle>
            <Checkbox.Group
              style={{ width: "100%" }}
              options={[
                { label: "Full time", value: "fulltime" },
                { label: "Part time", value: "parttime" },
                { label: "Contract", value: "contract" },
                { label: "Freelance", value: "freelance" },
              ]}
            />
          </Form.Item>
        </div>
        {/* location start */}
        <div>
          <h5>Location</h5>
          <Form.Item name={"country"} noStyle>
            <Select
              allowClear
              showSearch
              mode={"multiple"}
              suffixIcon={<SearchOutlined />}
              placeholder={"Search country"}
              style={{ width: "100%" }}
              options={countries?.map((c) => {
                return { label: c.country_name, value: c.country_name };
              })}
            />
          </Form.Item>
        </div>
        {/* location end */}
        <div>
          <h5>Sector</h5>
          <Form.Item name={"keywords"} noStyle>
            <Select
              allowClear
              showSearch
              mode={"multiple"}
              suffixIcon={<SearchOutlined />}
              placeholder={"Search sector"}
              style={{ width: "100%" }}
              options={sectors?.map((s) => {
                return { label: s.sector_name, value: s.sector_name };
              })}
            />
          </Form.Item>
        </div>
        <Button type={"primary"} block htmlType={"submit"}>
          Apply filters
        </Button>
      </Form>
    </div>
  );
};

export default JobSearchFilters;
