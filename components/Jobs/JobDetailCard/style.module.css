.job-detail {
  display: flex;
  padding: 12px 12px 24px 12px !important;
  flex-direction: column;
  align-items: flex-start;
  flex: 2 1 auto;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  background: #fff;
  grid-column: span 2 / span 2;
  position: sticky;
  top: 10px;
  background-color: white;
  padding: 10px;
  z-index: 100;
}

.job-detail__logo img {
  display: flex;
  width: 80px;
  height: 80px;
  justify-content: center;
  align-items: center;
  border-radius: 6.531px;
  border: 0.667px solid #d9d9d9;
  object-fit: fill;
}

.job-detail__overview {
  width: 100%;
}

.job-detail__header {
  display: flex;
  align-items: start;
  gap: 16px;
  border-bottom: 1px solid #eee;
  padding-bottom: 16px;
}

.job-detail__header-top {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.job-detail__title {
  color: #191919;
  font-size: 24px;
  font-weight: 700;
  line-height: 120%;
}

.job-detail__company-name {
  color: #747474;
  font-family: Archivo;
  font-size: 16px;
  font-weight: 400;
  line-height: 120%;
  text-decoration: underline !important;
}

.job-detail__header-right {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.job-detail__header-bottom {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.bookmark {
  display: flex;
  padding: 8px;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  background: #eee;
  cursor: pointer;
}
.bookmark_saved {
  background: #2c2c2c !important;
}

.bookmark__icon {
  height: 27px;
  width: 27px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.job-detail__cta {
  display: flex;
  padding: 12px 16px !important;
  justify-content: center;
  width: 110px !important;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  background: #0070f5;
  height: 43px;
  font-size: 16px !important;
  font-weight: 400 !important;
  margin: 0 !important;
  border: none !important;
}
.job-detail__cta:hover {
  background: #0070f5 !important;
  border: none !important;
}

.job-overview {
  display: flex;
  align-items: flex-start;
  align-content: flex-start;
  gap: 24px 48px;
  align-self: stretch;
  flex-wrap: wrap;
  padding-top: 16px;
  padding-bottom: 40px;
}
.job-overview .job-overview_item {
  display: flex;
  align-items: flex-start;
  align-content: flex-start;
  gap: 24px 48px;
  align-self: stretch;
  flex-wrap: wrap;
}

.job-overview p {
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 140%;
  line-height: 140%;
}

.job-overview h4 {
  color: #2c2c2c;
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 160%;
}

.job-description-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  gap: 16px;
}
.description-label {
  font-size: 12px;
  font-weight: 400;
  line-height: 140%;
}

.job-description p,
.open-sans {
  font-family: var(--opensans-font) !important;
}
.job-description p {
  font-size: 16px;
  color: #4d4d4d;
  line-height: 22.4px;
}
.job-description p strong {
  font-weight: 600 !important;
  color: #4d4d4d !important;
}
.job-description p span {
  color: #4d4d4d !important;
}

.job-description img {
  max-width: 100%;
  height: auto;
}

.skills-required-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.skill-require-label {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 140%; /* 22.4px */
}

.job-skills {
  display: flex;
  column-gap: 4px;
  row-gap: 8px;
  flex-wrap: wrap;
}

.job-skills .job-skill {
  display: flex;
  padding: 6px 20px;
  align-items: center;
  gap: 8px;
  border-radius: 28px;
  background: rgba(0, 85, 186, 0.04);
}

.job-skills p {
  color: #0055ba;
  font-size: 18px;
  font-weight: 400;
  line-height: 160%;
}
.skeleton {
  background-color: #e0e0e0;
  border-radius: 4px;
  min-height: 20px;
  animation: pulse 1.5s ease-in-out infinite;
}

.skeleton-logo {
  background-color: #e0e0e0;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  animation: pulse 1.5s ease-in-out infinite;
}

.skeleton-text {
  width: 100%;
  height: 20px;
  background-color: #e0e0e0;
  border-radius: 4px;
  animation: pulse 1.5s ease-in-out infinite;
}

.skeleton-title {
  width: 60%;
  height: 30px;
  background-color: #e0e0e0;
  border-radius: 4px;
  animation: pulse 1.5s ease-in-out infinite;
}

.skeleton-button {
  width: 120px;
  height: 40px;
  background-color: #e0e0e0;
  border-radius: 4px;
  animation: pulse 1.5s ease-in-out infinite;
}
.job_section_body {
  max-height: 600px;
  overflow-y: auto;
  width: 100%;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

@media screen and (max-width: 991px) {
  .job-detail {
    border: none;
    width: 100%;
    border-radius: 0%;
    padding: 0%;
  }
  .job-detail__cta {
    width: 100% !important;
  }
  .job-detail__logo img {
    height: 40px;
    width: 40px;
  }
  .job-detail__title {
    font-size: 18px;
  }
  .job-detail__company-name {
    font-size: 14px;
  }
  .cta-container {
    border-bottom: 1px solid #eee !important;
    padding-bottom: 16px;
  }
  .job-overview__item p {
    font-size: 14px;
    line-height: 140%;
  }
  .job-overview__item h4 {
    font-size: 16px;
  }
  .job-description-container .description-label {
    font-size: 14px;
  }
  .job-detail-mobile-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
  }
  .job-detail-mobile-header img {
    cursor: pointer;
  }
}
