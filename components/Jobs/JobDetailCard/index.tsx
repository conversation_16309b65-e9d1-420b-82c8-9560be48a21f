import styles from './style.module.css';
import Image from 'next/image';
import {City, Job} from '@/lib/types';
import {useContext, useEffect, useState} from 'react';
import AuthContext from '@/Context/AuthContext';
import {getAllCitiesByCountryName, getJobSkills, toggleFavoriteJob} from '@/lib/frontendapi';
import {useRouter} from 'next/router';
import JobApplyButton from '@/components/Common/JobApplyButton';
import useWindowDimensions from '@/helpers/useWindowDimensions';
import {JobSavedSuccess} from '../JobSavedSuccess';
import {useHandleJobSaved} from '@/hooks/useHandleSavedJobs';
import {experienceConvert} from '@/utils/experienceConvert';
import {useGetCityNameByCityId} from '@/hooks/useGetCityNameByCityId';

export const JobDetailCard = ({
  job,
  setSelectedJob,
  jobs,
  favorites,
}: {
  job: Job;
  setSelectedJob: React.Dispatch<React.SetStateAction<Job | null>>;
  jobs?: Job[];
  favorites?: number[];
}) => {
  const router = useRouter();
  const windowDimensions: any = useWindowDimensions();
  const [jobSaved, setJobSaved] = useState(false);
  const [jobSkills, setJobSkills] = useState<
    {
      id: string;
      sector_id: string;
      skills: string;
      status: 'active' | 'inactive';
    }[]
  >([]);
  const [cities, setCities] = useState<City[]>();

  const {user} = useContext(AuthContext);
  const extractCurrencyCode = (value: string) => {
    if (!value) {
      return '';
    }
    const currencyCode = value.split('(')[1]?.trim()?.slice(0, -1);
    return currencyCode || '';
  };
  const {savedJob, unSaveJob, saveJob} = useHandleJobSaved(job, jobs ?? [], favorites ?? []);
  const {cityName} = useGetCityNameByCityId(job.country?.country_name ?? '', job.job_city);

  useEffect(() => {
    const jobId = job?.id;
    try {
      if (jobId) {
        const newData = {
          job_id: jobId,
          user_id: user?.id,
        };

        (async () => {
          const jobSkills = await getJobSkills(newData);
          setJobSkills(jobSkills?.data);
        })();
      }
      (async () => {
        const cities = await getAllCitiesByCountryName(job.country?.country_name);
        setCities(cities?.data);
      })();
    } catch (error) {
      console.log(error);
    }
  }, [router.query.job, job]);

  const handleSaveJob = () => {
    saveJob(job.id, job.company_id);
    if (user) {
      handleJobTimeOut();
    }
  };

  const handleBookMark = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    e.stopPropagation();

    savedJob ? unSaveJob(user?.id, job.id, job.company_id) : handleSaveJob();
  };

  const handleJobTimeOut = () => {
    setJobSaved(true);

    const savedJobTimeout = setTimeout(() => {
      setJobSaved(false);
    }, 4000);

    return () => clearTimeout(savedJobTimeout);
  };

  const handleNavigateJobList = () => {
    setSelectedJob?.(null);
  };

  return (
    <div className={styles['job-detail']}>
      <div className={styles['job-detail__overview']}>
        {windowDimensions.width < 911 && (
          <div className={styles['job-detail-mobile-header']}>
            <div>
              <img
                src={'/icons/prime_arrow-left.svg'}
                alt="back"
                width={20}
                height={20}
                onClick={handleNavigateJobList}
              />
            </div>
            <div>
              <img
                src={savedJob ? '/icons/bookmark-blue-filled.svg' : '/icons/bookmark-black.svg'}
                alt="bookmark"
                width={20}
                height={20}
                onClick={handleBookMark}
              />
            </div>
          </div>
        )}
        <div className={styles['job-detail__header']}>
          <div className={styles['job-detail__logo']}>
            <img
              src={job.company?.company_logo ?? '/images/logo-img.png'}
              alt={'logo-img'}
              width={windowDimensions.width > 911 ? 78 : 40}
              height={windowDimensions.width > 911 ? 78 : 40}
              priority={true}
              quality={100}
            />
          </div>
          <div className={styles['job-detail__header-right']}>
            <div className={styles['job-detail__header-top']}>
              <h2 className={styles['job-detail__title']}>{job.job_title}</h2>
              <a
                className='job'
                href={`/companies/${job.company?.company_slug}`}
                target="_blank">
                  <p className='p-16 mt-1 c_name'>{job.company?.company_name}</p>
              </a>
            </div>
            {windowDimensions.width > 911 && (
              <div className={styles['job-detail__header-bottom']}>
                <JobApplyButton job={job} className={styles['job-detail__cta']} />
                <div
                  className={`${styles['bookmark']}  ${savedJob && styles['bookmark_saved']}`}
                  onClick={handleBookMark}>
                  <div className={`${styles['bookmark__icon']}`}>
                    <img
                      src={savedJob ? '/icons/bookmark-white-filled.svg' : '/icons/bookmark-black.svg'}
                      alt="bookmark"
                      width={14}
                      height={20}
                    />
                  </div>
                </div>
              </div>
            )}
            {jobSaved && (
              <JobSavedSuccess unsavedJobs={unSaveJob} job_id={job.id} id={user?.id} company_id={job.company_id} />
            )}
          </div>
        </div>
        {windowDimensions.width < 911 && (
          <div className={styles['cta-container']}>
            <JobApplyButton job={job} className={styles['job-detail__cta']} />
          </div>
        )}
      </div>
      <div className={styles.job_section_body}>
        <div className={styles['job-overview']}>
          {job.job_type && (
            <div className={styles['job-overview__item']}>
              <p>Job Type</p>
              <h4>
                {job.job_type === 'parttime'
                  ? 'Part-Time'
                  : job.job_type === 'fulltime'
                    ? 'Full-Time'
                    : job.job_type === 'contract'
                      ? 'Contract'
                      : job.job_type === 'freelance'
                        ? 'Freelance'
                        : ' '}
              </h4>
            </div>
          )}
          <div className={styles['job-overview__item']}>
            <p>Location</p>
            <h4>
              {cityName && cityName} {job.country?.country_name && ', ' + job.country?.country_name}
            </h4>
          </div>
          {job.experience && (
            <div className={styles['job-overview__item']}>
              <p>Experience</p>
              <h4>{experienceConvert(job.experience === 'fresher' ? 0 : parseInt(job.experience))}</h4>
            </div>
          )}
          {(job.monthly_fixed_salary_max || job.monthly_fixed_salary_min) && (
            <div className={styles['job-overview__item']}>
              <p>Salary</p>
              <h4>
                {extractCurrencyCode(job?.monthly_fixed_salary_currency) &&
                  job?.monthly_fixed_salary_min &&
                  `${extractCurrencyCode(job.monthly_fixed_salary_currency)} ${job.monthly_fixed_salary_min}`}
                {job?.monthly_fixed_salary_max && job?.monthly_fixed_salary_min && ' - '}
                {job?.monthly_fixed_salary_max &&
                  `${extractCurrencyCode(job?.monthly_fixed_salary_currency)} ${job?.monthly_fixed_salary_max}`}
                {''} per month
              </h4>
            </div>
          )}
        </div>
        <div className={styles['job-description-container']}>
          <p className={styles['description-label']}>Job Description</p>
          <div className={styles['job-description']} dangerouslySetInnerHTML={{__html: job.job_description}} />
        </div>
        <div className={styles['skills-required-container']}>
          <p className={styles['skill-require-label']}>Skill Required</p>
          <div className={styles['job-skills']}>
            {jobSkills.length > 0 &&
              jobSkills?.map((skill, index) => {
                return (
                  <div className={styles['job-skill']} key={index}>
                    <p>{skill.skills}</p>
                  </div>
                );
              })}
          </div>
        </div>
      </div>
    </div>
  );
};
