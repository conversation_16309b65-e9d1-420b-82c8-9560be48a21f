import React, { Suspense, lazy, useMemo, useCallback } from "react";
import BlogCard from "./BlogCard";
import { Blogs } from "@/lib/types";
import { useRouter } from "next/router";
// import dynamic from 'next/dynamic';

const Pagination = lazy(() => import('../../components/Common/NewPagination'));

interface ExploreBlogsSectionProps {
   blogs: Blogs[];
   page: number;
   totalCount: number;
   selectedCategoryID: number | null;
}

const ExploreBlogsSection = ({ blogs, page, totalCount, selectedCategoryID }: ExploreBlogsSectionProps) => {
   const router = useRouter();

   const handlePageChange = useCallback((page: number): void => {
      router.push(`/blog?page=${page}`);
   }, [router]);

   const renderedBlogs = useMemo(() => blogs?.length > 0 && blogs?.map((blog) => (
      <div className="col-md-6 col-sm-12 col-lg-4 mt-3" key={blog.id}>
         <Suspense fallback={<div>Loading...</div>}>
            <BlogCard post={blog} showAuthor={false} />
         </Suspense>
      </div>
   )), [blogs]);

   const filteredBlogs = useMemo(() => {
      return selectedCategoryID === null
         ? blogs
         : blogs.filter(blog =>
            blog.blog_category_id !== undefined && parseInt(blog.blog_category_id) === selectedCategoryID
         );
   }, [blogs, selectedCategoryID]);

   return (
      <>
         <h3 aria-level={3} role="heading" className="c-151515 f-37 w-500 text-center">
            Explore Blogs
         </h3>
         <div className="container mt-3">
            {blogs?.length > 0 && (
               <div className="row">
                  {renderedBlogs}
               </div>
            )}
            <div className="pagination-wrapper mt-4">
               <Suspense fallback={<div>Loading...</div>}>
                  <Pagination
                     items={filteredBlogs}
                     currentPage={page}
                     totalCount={totalCount}
                     pageSize={10}
                     onPageChange={handlePageChange}
                     activePage={page}
                  />
               </Suspense>
            </div>
         </div>
      </>
   );
};

export default React.memo(ExploreBlogsSection);
