import React from 'react';
import Link from 'next/link';
import {Blogs} from '@/lib/types';
import Image from 'next/image';
import moment from 'moment';

interface BlogCardProps {
  post: Blogs;
  showAuthor?: boolean;
  authorName?: string;
  authorSlug?: string;
  authorImage?: string;
}

const formatDate = (dateStr: string) => {
  const date = new Date(dateStr);
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  };
  return date.toLocaleDateString(undefined, options);
};

const BlogCard = ({post, showAuthor = true}: BlogCardProps) => {
  let postImage = process.env.NEXT_PUBLIC_BASE_URL + 'images/placeholder.jpg';
  if (post.image) {
    postImage = process.env.NEXT_PUBLIC_IMAGE_URL + 'images/blogs/' + post.image;
  }

  return (
    <div className="tab-card-box related-resource-block">
      <Link prefetch={false} href={`/blog/${post.slug}`}>
        <img height={400} width={400} src={postImage} alt={post.name} className="blogImage" loading="lazy" />
        {post.tag !== undefined && post.tag !== null && (
          <ul className="list-tags">
            {post.tag.split(',').map((tag, index) => (
              <li key={index}>{tag}</li>
            ))}
          </ul>
        )}
        <h4 aria-level={4} role={'heading'} className={'title'}>
          {post?.name}
        </h4>
      </Link>
      {showAuthor && (
        <div className={'blog-author-card'}>
          <img
            height={48}
            width={48}
            src={
              post.author?.profile_image
                ? `${process.env.NEXT_PUBLIC_IMAGE_URL}images/blogs/author/${post.author?.profile_image}`
                : `${process.env.NEXT_PUBLIC_BASE_URL + 'images/users.jpg'}`
            }
            alt={post.author?.name || 'Author'}
            loading="lazy"
          />
          <div>
            <Link prefetch={false} href={`/author/${post.author?.slug}`}>
              <p className="name">{post.author?.name}</p>
            </Link>
            <small>
              {moment(formatDate(post?.created_at || '')).format('MMMM D, YYYY')} ·{' '}
              {post?.description ? Math.round(post.description?.split(' ').length / 200) : '0 '} min read
            </small>
          </div>
        </div>
      )}
    </div>
  );
};

export default BlogCard;
