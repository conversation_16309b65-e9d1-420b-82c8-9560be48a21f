// @ts-nocheck
import {Button, notification} from 'antd';
import {FacebookShare<PERSON>utton, LinkedinShareButton, TwitterShareButton} from 'next-share';
import Image from 'next/image';
import Link from 'next/link';
import React, {useState, useEffect, useRef} from 'react';
import CopyToClipboard from 'react-copy-to-clipboard';
import mobileImage from '../../public/images/mobile-apply-ready-img.png';

import styles from './listingsection.module.css';
import ts from 'typescript';
interface BlogSignupProps {
  blogPost: any;
  handleClickShowErrorMessage: (e: any, message: string) => void;
  user: any;
}

enum HtmlTags {
  H1 = 'H1',
  H2 = 'H2',
  H3 = 'H3',
}

const job_searches = [
  {
    id: 76,
    blog_id: 489,
    keyword: 'Accounting',
    country: 'United Arab Emirates',
    title: 'Accounting jobs in UAE',
    created_at: '2024-08-13T09:03:50.000000Z',
    updated_at: '2024-08-13T09:03:50.000000Z',
  },
  {
    id: 77,
    blog_id: 489,
    keyword: 'Graphic Designer',
    country: 'United Arab Emirates',
    title: 'Graphic Designer jobs in UAE',
    created_at: '2024-08-13T09:03:50.000000Z',
    updated_at: '2024-08-13T09:03:50.000000Z',
  },
  {
    id: 78,
    blog_id: 489,
    keyword: 'Product Manager',
    country: 'United Arab Emirates',
    title: 'Product Manager jobs in UAE',
    created_at: '2024-08-13T09:03:50.000000Z',
    updated_at: '2024-08-13T09:03:50.000000Z',
  },
  {
    id: 79,
    blog_id: 489,
    keyword: 'Business Development',
    country: 'United Arab Emirates',
    title: 'Business Development jobs in UAE',
    created_at: '2024-08-13T09:03:50.000000Z',
    updated_at: '2024-08-13T09:03:50.000000Z',
  },
  {
    id: 80,
    blog_id: 489,
    keyword: 'Junior Engineer',
    country: 'United Arab Emirates',
    title: 'Junior Engineer jobs in UAE',
    created_at: '2024-08-13T09:03:50.000000Z',
    updated_at: '2024-08-13T09:03:50.000000Z',
  },
];

const ListingSection = ({blogPost, handleClickShowErrorMessage, user}: BlogSignupProps) => {
  const handleCopyToClipboard = () => {
    notification.success({
      message: 'Blog URL copied to clipboard successfully!',
    });
  };

  const handleButtonClick = (url: string) => {
    if (url) {
      window.open(url, '_blank');
    } else {
      console.error('URL is not provided');
    }
  };

  const [toc, setToc] = useState<any[]>([]);
  // const [content, setContent] = useState("");
  const [sections, setSections] = useState<any[]>([]);
  const [sectionStart, setSectionStart] = useState<string>('');
  const [hasTocBot, setHasTocBot] = useState(false);

  const {heading = '', description = ''} = blogPost ?? {};

  const getInitialContent = () => {
    if (hasTocBot) {
      return sectionStart || description;
    }

    return heading || description;
  };

  const getTocBotContent = () => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(description ?? heading, 'text/html');
    const headings = doc.querySelectorAll('h2, h3');
    const splitSections: any[] = [];
    const tocItems: any[] = [];

    headings.forEach((heading, index) => {
      const id = `heading-${index}`;
      if (heading?.textContent) {
        tocItems.push({
          id,
          text: heading.textContent,
          tagName: heading.tagName,
        });
      }
      heading.id = id;
      const sectionContent = doc.body.innerHTML.substring(
        doc.body.innerHTML.indexOf(heading.outerHTML),
        doc.body.innerHTML.length,
      );
      // split the sections based on the headings
      splitSections.push(
        sectionContent.substring(0, sectionContent.indexOf(headings?.[index + 1]?.outerHTML) || sectionContent.length),
      );
    });

    // push the last section
    splitSections.push(
      doc.body.innerHTML.substring(
        doc.body.innerHTML.indexOf(headings?.[headings.length - 1]?.outerHTML),
        doc.body.innerHTML.length,
      ),
    );
    const headerHtml = headings?.[0]?.outerHTML;
    const startContent = doc.body.innerHTML.substring(0, doc.body.innerHTML.indexOf(headerHtml));

    return {
      startContent,
      splitSections,
      tocItems,
      hasHeadingTags: headings.length > 0,
    };
  };

  useEffect(() => {
    const {hasHeadingTags, startContent, splitSections, tocItems} = getTocBotContent();
    setSectionStart(startContent);

    if (hasHeadingTags) {
      setSections(splitSections);
      setToc(tocItems);
      setHasTocBot(true);
    }
  }, [description, heading]);

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({behavior: 'smooth'});
    }
  };

  return (
    <>
      <section className="blog-part">
        <div className="container">
          <div className="blog-outer-part">
            <div className="left-side-outer sticky-section">
              <div className="left-side">
                <p className="know-your">Know your worth</p>
                <p className="know-your-para">
                  Use our salary insights to discover what your skills and experience are worth in your local market.
                </p>
                <button className="know-your-btn" type="button">
                  Search for salaries
                </button>
              </div>
            </div>
            <div className="right-side middle-part">
              <div className="blog-main-content">
                <div
                  className="topMainPara"
                  dangerouslySetInnerHTML={{
                    __html: getInitialContent(),
                  }}></div>
                {sections.length ? (
                  <div className="article-box table-of-contents">
                    <p className="article-heading">In this article, we will discuss:</p>
                    <ul className="article-ul">
                      {toc.map((item: any, index: number) => {
                        const {tagName = HtmlTags.H2} = item ?? {};

                        if (tagName === HtmlTags.H3) {
                          return (
                            <ul key={index}>
                              <li onClick={() => scrollToSection(item.id)}>{item.text}</li>
                            </ul>
                          );
                        }

                        return (
                          <li key={index} onClick={() => scrollToSection(item.id)}>
                            {item.text}
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                ) : (
                  ''
                )}

                {sections.length ? (
                  <div>
                    {sections?.map((val: any, index: number) => (
                      <div key={index}>
                        <div
                          id={`${index}`}
                          dangerouslySetInnerHTML={{
                            __html: val,
                          }}
                        />
                        {index === 1 ? (
                          <div className="expand-job">
                            <p className="expand-job-heading mb-4" style={{fontSize: 22}}>
                              Expand your Job Search
                            </p>
                            <ul>
                              {job_searches?.map((job: any) => (
                                <li key={job?.keyword}>
                                  <a href={`/jobs-in-gulf?keywords=${job?.keyword}&country=${job?.country}`}>
                                    <i className="fa-solid fa-magnifying-glass me-2"></i>
                                    {job?.title}
                                  </a>
                                </li>
                              ))}
                            </ul>
                          </div>
                        ) : (
                          ''
                        )}
                        {(sections.length > 3 ? index === 2 : index === sections.length - 2) ? (
                          <div className="create-profile-sec">
                            <div className="cps-lft">
                              <p className="h3" style={{fontSize: 28, fontWeight: 'bold'}}>
                                Finding a job just got easier...
                              </p>
                              <p className="description-profile">Lorem Ipsum is simply dummy text of the printing</p>
                            </div>
                            <div className="cps-rgt">
                              <Link href="" className="cps-btn">
                                Create your profile for free
                              </Link>
                            </div>
                          </div>
                        ) : (
                          ''
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  ''
                )}
                {/* <h3>Our Takeaway</h3>
                        <p>In short, it is easy to find the perfect job in the UAE with these top job portals. Some of the unique features these websites have include new-age video calling interviews with TheTalentPoint and easy application processes with GulfTalent, ensuring a boosted job search for you. Whether you're an employer looking for top talent or a job seeker eyeing that perfect job, these job sites come in handy. The best job portal in the UAE, one shining star trusted by job seekers and employers alike for efficiency and reliability, is TheTalentpoint.</p>
                        <p>Start your job search today and take your next step toward your career!</p> */}
              </div>

              <div className="apply-ready-main">
                {/* <div className={`apply-ready-block`}>
                  <p role="heading" aria-level={3} className="c-0055BA w-700 f-31 Archivo text-left h3">
                    Ready to land your dream job?
                  </p>
                  <p className="h4">Explore 50,000+ jobs & connect with top employers!</p>
                  <div className="d-flex align-items-center">
                    <Link
                      prefetch={false}
                      href="/auth/signup/[[...slug]]"
                      className="btn-a primary-size-18 btn-bg-0055BA mr-1 tab-w-100"
                      style={{color: '#fff'}}>
                      Sign Up Now
                    </Link>
                  </div>
                  <div className="mobile-banner" style={{margin: 0}}>
                    <img src={mobileImage} alt="Promotion image" layout="responsive" />
                  </div>
                </div> */}
                <div className="all-post-tab d-flex align-items-center">
                  <Link
                    prefetch={false}
                    href="/blog"
                    className="btn-a border-primary-size-16 border-0055BA bg-ebf1f9 w-700">
                    <i className="fa-solid fa-arrow-left-long"></i> All Posts
                  </Link>
                </div>
                <div className="blog-share-sec">
                  <p role="heading" aria-level={4} className="c-1F1F1F w-500 f-22 Archivo mt-5 m-left h4">
                    Share
                  </p>
                  <p className="icon-soc">
                    <LinkedinShareButton url={`${process.env.NEXT_PUBLIC_BASE_URL}blog/${blogPost?.slug}`}>
                      <Link href="#" prefetch={false}>
                        <img
                          width={60}
                          height={40}
                          src={process.env.NEXT_PUBLIC_BASE_URL + 'images/Share-1.webp'}
                          alt="Default Author Avatar"
                          className="Share-1.png"
                        />
                      </Link>
                    </LinkedinShareButton>

                    <TwitterShareButton url={`${process.env.NEXT_PUBLIC_BASE_URL}blog/${blogPost?.slug}`}>
                      <Link href="#" prefetch={false}>
                        <img
                          width={60}
                          height={40}
                          src={process.env.NEXT_PUBLIC_BASE_URL + 'images/twitter.webp'}
                          alt="Default Author Avatar"
                          className="Share-2.png"
                        />
                      </Link>
                    </TwitterShareButton>

                    <FacebookShareButton url={`${process.env.NEXT_PUBLIC_BASE_URL}/blog/${blogPost?.slug}`}>
                      <Link href="#" prefetch={false}>
                        <img
                          width={60}
                          height={40}
                          src={process.env.NEXT_PUBLIC_BASE_URL + 'images/Share-3.webp'}
                          alt="Default Author Avatar"
                          className="Share-3.png"
                        />
                      </Link>
                    </FacebookShareButton>

                    <CopyToClipboard
                      text={`${process.env.NEXT_PUBLIC_BASE_URL}blog/${blogPost?.slug}`}
                      onCopy={handleCopyToClipboard}>
                      <span className="copy-clipbpard">
                        <img
                          width={60}
                          height={40}
                          src={process.env.NEXT_PUBLIC_BASE_URL + 'images/Share-4.webp'}
                          alt="Default Author Avatar"
                          className="Share-4.png"
                          style={{width: '60px'}}
                        />
                      </span>
                    </CopyToClipboard>
                  </p>
                </div>
              </div>
            </div>

            {/* =============================================================== */}
            <div className="third-box sticky-section">
              {/* {rightSection?.map((val: any, ind: any) => ( */}
              <div className="third-box-item">
                {/* {ind === 0 ? */}
                <div className="blog-profile-2 fixed-section blog_sidebar_dream_job_box_sec">
                  <img
                    src={process.env.NEXT_PUBLIC_BASE_URL + 'images/dream_job.webp'}
                    alt="blog sidebar dream job"
                    width={306}
                    height={224}
                    style={{width: '100%', borderRadius: '5%'}}
                  />
                  <div className="blog_sidebar_box_content_sec overlay_dream_job overlay_2 blog-overlay">
                    <p
                      role="heading"
                      aria-level={3}
                      className="c-0055BA w-600 f-18 blog_looking_to_hire_head sidebar-title">
                      Ready to land your dream job?
                    </p>
                    <p className="blog_dream_job_to_hire_sub_head">
                      Connect with top companies and find your perfect match.
                    </p>
                    <Link
                      prefetch={false}
                      href={
                        user?.id
                          ? user?.role == 'admin'
                            ? '/admin/employees'
                            : user?.role == 'employer'
                              ? '/employer/candidates'
                              : user?.role == 'employee'
                                ? '#'
                                : user?.role == 'staff'
                                  ? '/staff/candidates'
                                  : '/'
                          : '/auth/signup'
                      }
                      onClick={
                        user?.role == 'employee'
                          ? e => handleClickShowErrorMessage(e, 'You need to register as a employer to hire candidates')
                          : undefined
                      }>
                      <Button
                        className="apply-btn btn-a primary-size-18 w-100 tab-w-100"
                        style={{
                          height: 'auto',
                          color: '#151515',
                          background: '#FDCA40',
                          textWrap: 'wrap',
                        }}
                        // onClick={() => handleButtonClick('url')}
                        onClick={() => {}}>
                        Create your profile for free
                      </Button>
                    </Link>
                  </div>
                </div>
                <div className="blog-profile-2 fixed-section blog_sidebar_looking_box_sec">
                  <img
                    src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blog_side_bar_looking.webp'}
                    alt="blog sidebar looking hire"
                    className=""
                    width={306}
                    height={224}
                    style={{width: '100%', borderRadius: '5%'}}
                  />
                  <div className="blog_sidebar_box_content_sec overlay overlay_2 blog-overlay">
                    <p role="heading" aria-level={3} className="c-0055BA w-600 f-18 blog_looking_to_hire_head">
                      Looking to hire?
                    </p>
                    <p className="blog_looking_to_hire_sub_head">
                      Join us & get access to our pool of talented candidates.
                    </p>
                    <Link
                      prefetch={false}
                      href={
                        user?.id
                          ? user?.role == 'admin'
                            ? '#'
                            : user?.role == 'employer'
                              ? '/employer/candidates'
                              : user?.role == 'employee'
                                ? '#'
                                : user?.role == 'staff'
                                  ? '/staff/candidates'
                                  : '/'
                          : '/auth/signup'
                      }
                      onClick={
                        user?.role == 'employee'
                          ? e => handleClickShowErrorMessage(e, 'You need to register as a employer to hire candidates')
                          : undefined
                      }>
                      <Button
                        className="apply-btn btn-a primary-size-18 btn-bg-fff w-100 tab-w-100"
                        style={{height: 'auto', color: '#151515', textWrap: 'wrap'}}
                        // onClick={() => handleButtonClick('url')}
                        onClick={() => {}}>
                        Find your next hire
                      </Button>
                    </Link>
                  </div>
                </div>
                {/* } */}
              </div>
              {/* ))} */}
            </div>
          </div>
        </div>
      </section>
    </>
  );
};
export default ListingSection;
