import React, { useCallback } from "react";
import { Button } from "antd";
import Link from "next/link";

interface BlogPageHiringSection {
   user: any;
}

const BlogPageHiringSection = ({ user }: BlogPageHiringSection) => {
   const handleClickShowErrorMessage = useCallback((e: React.MouseEvent, message: string) => {
      e.preventDefault();
      // notification.error({ message: message });
   }, []);

   return (
      <section className="are-blog mb-5">
         <div className="container">
            <div className="row">
               <div className="col-lg-6 col-md-12">
                  <div className="make-it mb-4">
                     <h5 aria-level={5} role="heading" className="mt-3">Are you hiring?</h5>
                     <h6 aria-level={6} role="heading">Take the first step and hire professionals now.</h6>
                     <Link
                        prefetch={false}
                        href={
                           user?.id
                              ? user?.role === 'employer'
                                 ? '/employer/candidates'
                                 : user?.role === 'employee'
                                    ? '#'
                                    : '/'
                              : '/auth/signup'
                        }
                        onClick={
                           user?.role === 'employee'
                              ? e => handleClickShowErrorMessage(e, 'You need to register as an employer to hire candidates')
                              : undefined
                        }
                     >
                        <Button className="btn-a primary-size-22 btn-bg-fff mt-3 tab-w-100 mb-3" style={{ height: 'auto' }}>
                           Get Started
                        </Button>
                     </Link>
                  </div>
               </div>
               <div className="col-lg-6 col-md-12">
                  <div className="tips">
                     <h5 className="mt-3" aria-level={5} role="heading">Looking for a job?</h5>
                     <h6 aria-level={6} role="heading">Search for job & start applying today!</h6>

                     <Link
                        prefetch={false}
                        href={
                           user?.id
                              ? user?.role === 'employee'
                                 ? '/jobs-in-gulf'
                                 : user?.role === 'employer'
                                    ? '#'
                                    : '/'
                              : '/auth/signup'
                        }
                        onClick={
                           user?.role === 'employer'
                              ? e => handleClickShowErrorMessage(e, 'You need to login as a candidate to apply for a job')
                              : undefined
                        }
                     >
                        <Button className="btn-a primary-size-22 btn-bg-fff mt-3 tab-w-100 mb-3" style={{ height: 'auto' }}>
                           Get Started
                        </Button>
                     </Link>
                  </div>
               </div>
            </div>
         </div>
      </section>
   );
}

export default React.memo(BlogPageHiringSection);
