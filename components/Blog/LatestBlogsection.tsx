import {Blogs} from '@/lib/types';
import Image from 'next/image';
import Link from 'next/link';
import React, {useMemo} from 'react';

interface LatestBlogSectionProps {
  latestBlog: Blogs;
}

const LatestBlogsection = ({latestBlog}: LatestBlogSectionProps) => {
  const tags = useMemo(() => latestBlog.tag?.split(','), [latestBlog.tag]);

  return (
    <div className="container">
      <div className="row">
        <div className="col-sm-12 col-md-4">
          <Link prefetch={false} href={`/blog/${latestBlog.slug}`} passHref>
            <img
              src={
                latestBlog.image
                  ? `${process.env.NEXT_PUBLIC_IMAGE_URL}images/blogs/${latestBlog.image}`
                  : `${process.env.NEXT_PUBLIC_BASE_URL}images/placeholder.jpg`
              }
              alt="Default Blog"
              className="tab-1 w-100"
              width={680}
              height={680}
              priority
            />
          </Link>
        </div>
        <div className="col-sm-12 col-md-8">
          <Link prefetch={false} href={`/blog/${latestBlog.slug}`} passHref>
            {tags && tags?.length > 0 && (
              <ul className="list-tags">
                {tags.map((tag, index) => (
                  <li key={index}>{tag}</li>
                ))}
              </ul>
            )}
            <h2 aria-level={2} role="heading" className="c-0055BA f-45 w-500 mt-2">
              {latestBlog.name?.slice(0, 80)}
            </h2>
            <div className="user-w-max">
              <div className="row mt-4">
                <div className="col-sm-3 col-3 pr-0">
                  <img
                    src={
                      latestBlog.author?.profile_image
                        ? `${process.env.NEXT_PUBLIC_IMAGE_URL}images/blogs/author/${latestBlog.author?.profile_image}`
                        : `${process.env.NEXT_PUBLIC_BASE_URL}images/placeholder.jpg`
                    }
                    alt="Author Avatar"
                    className="w-48"
                    width={48}
                    height={48}
                    priority
                  />
                </div>
                <div className="col-sm-9 col-9 pl-0">
                  <h6 aria-level={6} role="heading" className="tab-name-user">
                    {latestBlog.author?.name}
                  </h6>
                  <p className="tab-interview">{`${latestBlog.created_at || ''} - 5 min read`}</p>
                </div>
              </div>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default React.memo(LatestBlogsection);
