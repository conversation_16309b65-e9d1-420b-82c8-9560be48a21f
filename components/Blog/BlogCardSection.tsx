import Link from "next/link";
import React from "react";
import BlogCard from "./BlogCard";
import { Blogs } from "@/lib/types";
interface BlogCardSectionProps {
   blog: Blogs[]
}
const BlogCardSection = ({ blog }: BlogCardSectionProps) => {
   return (
      <>
         <section className="blog-cards">
            <div className="container">
               <div className="row">
                  <div className="col-lg-8 col-md-8 col-sm-12">
                     <h4 role='heading' aria-level={4} className="c-151515 w-700 f-45 ">Related Resources</h4>
                  </div>
                  <div className="col-lg-4 col-md-4 col-sm-12 d-flex align-items-center justify-content-end">
                     <Link prefetch={false} href="/blog" className="btn-a border-primary-size-16 border-0055BA bg-ebf1f9 w-700 ">
                        {' '}
                        View All{' '}
                     </Link>
                  </div>
               </div>

               <div className="related-resources row">
                  {blog.slice(0, 3).map((blog, index) => (
                     <div className="col-lg-4 col-md-4 col-sm-12" key={index}>
                        <BlogCard post={blog} />
                     </div>
                  ))}
               </div>
            </div>
         </section>
      </>
   )
}
export default BlogCardSection
