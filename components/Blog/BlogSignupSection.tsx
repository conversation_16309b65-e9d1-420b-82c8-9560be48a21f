// @ts-nocheck
import {Blogs} from '@/lib/types';
import {Button, notification} from 'antd';
import {FacebookShareButton, LinkedinShareButton, TwitterShareButton} from 'next-share';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';
import CopyToClipboard from 'react-copy-to-clipboard';
interface BlogSignupProps {
  blogPost: Blogs;
  isFixed: boolean;
  handleClickShowErrorMessage: (e: any, message: string) => void;
  user: any;
}
const BlogSignupSection = ({blogPost, isFixed, handleClickShowErrorMessage, user}: BlogSignupProps) => {
  const handleCopyToClipboard = () => {
    notification.success({message: 'Blog URL copied to clipboard successfully!'});
  };
  return (
    <>
      <section className="blog-part">
        <div className="container">
          <div className="row">
            <div className="col-sm-9">
              {/* <img src={blogImage} alt={`${blogPost?.name} - TalentPoint`} className="w-100 mb-5" /> */}
              <div
                className="blog-description-content"
                dangerouslySetInnerHTML={{
                  __html: blogPost?.description ?? '',
                }}
              />
              <div className="pb-5">
                <h3 role="heading" aria-level={3} className="c-0055BA w-700 f-31 Archivo text-left mt-5">
                  Ready to take the next step in your career?
                </h3>
                <p className="f-18 w-400 c-1F1F1F mt-2 mb-2">Sign up to explore our latest job</p>
                <br />
                <Link
                  prefetch={false}
                  href="/auth/signup/[[...slug]]"
                  className="btn-a primary-size-18 btn-bg-0055BA mr-1 tab-w-100"
                  style={{color: '#fff'}}>
                  Sign Up Now
                </Link>
              </div>
              <div>
                <Link
                  prefetch={false}
                  href="/blog"
                  className="btn-a border-primary-size-16 border-0055BA bg-ebf1f9 w-700 mt-5">
                  <i className="fa-solid fa-arrow-left-long"></i> All Posts
                </Link>
              </div>
              <h4 role="heading" aria-level={4} className="c-1F1F1F w-500 f-22 Archivo mt-5 m-left">
                Share
              </h4>
              <p className="mt-2 icon-soc">
                <LinkedinShareButton url={`${process.env.NEXT_PUBLIC_BASE_URL}blog/${blogPost?.slug}`}>
                  <Link href="#" prefetch={false}>
                    <img
                      width={60}
                      height={40}
                      src={process.env.NEXT_PUBLIC_BASE_URL + 'images/Share-1.webp'}
                      alt="Default Author Avatar"
                      className="Share-1.png"
                    />
                  </Link>
                </LinkedinShareButton>

                <TwitterShareButton url={`${process.env.NEXT_PUBLIC_BASE_URL}blog/${blogPost?.slug}`}>
                  <Link href="#" prefetch={false}>
                    <img
                      width={60}
                      height={40}
                      src={process.env.NEXT_PUBLIC_BASE_URL + 'images/twitter.webp'}
                      alt="Default Author Avatar"
                      className="Share-2.png"
                    />
                  </Link>
                </TwitterShareButton>

                <FacebookShareButton url={`${process.env.NEXT_PUBLIC_BASE_URL}/blog/${blogPost?.slug}`}>
                  <Link href="#" prefetch={false}>
                    <img
                      width={60}
                      height={40}
                      src={process.env.NEXT_PUBLIC_BASE_URL + 'images/Share-3.webp'}
                      alt="Default Author Avatar"
                      className="Share-3.png"
                    />
                  </Link>
                </FacebookShareButton>

                <CopyToClipboard
                  text={`${process.env.NEXT_PUBLIC_BASE_URL}blog/${blogPost?.slug}`}
                  onCopy={handleCopyToClipboard}>
                  <span className="copy-clipbpard">
                    {/* <img
                        src={process.env.NEXT_PUBLIC_BASE_URL + 'images/Share-4.png'}
                        alt="Default Author Avatar"
                        className="Share-4.png"
                        style={{ width: '60px' }}
                      /> */}
                    <img
                      width={60}
                      height={40}
                      src={process.env.NEXT_PUBLIC_BASE_URL + 'images/Share-4.webp'}
                      alt="Default Author Avatar"
                      className="Share-4.png"
                      style={{width: '60px'}}
                    />
                  </span>
                </CopyToClipboard>
              </p>
            </div>
            <div className="col-sm-3">
              <div className={`${isFixed ? 'blog_sidebar_section' : ''}`}>
                <div className="blog-profile-2 fixed-section blog_sidebar_dream_job_box_sec">
                  <img
                    src={process.env.NEXT_PUBLIC_BASE_URL + 'images/dream_job.webp'}
                    alt="blog sidebar dream job"
                    width={306}
                    height={224}
                    style={{width: '100%', borderRadius: '5%'}}
                  />
                  <div className="blog_sidebar_box_content_sec overlay_dream_job overlay_2">
                    <h3 role="heading" aria-level={3} className="c-0055BA w-600 f-18 blog_looking_to_hire_head">
                      Ready to l111111111and your dream job?
                    </h3>
                    <p className="blog_dream_job_to_hire_sub_head">Explore 50,000+ Jobs & connect with top employers</p>
                    <Link
                      prefetch={false}
                      href={
                        user?.id
                          ? user?.role == 'admin'
                            ? '/admin/employees'
                            : user?.role == 'employer'
                              ? '/employer/candidates'
                              : user?.role == 'employee'
                                ? '#'
                                : user?.role == 'staff'
                                  ? '/staff/candidates'
                                  : '/'
                          : '/auth/signup'
                      }
                      onClick={
                        user?.role == 'employee'
                          ? e => handleClickShowErrorMessage(e, 'You need to register as a employer to hire candidates')
                          : undefined
                      }>
                      <Button
                        className="apply-btn btn-a primary-size-18 w-100 tab-w-100"
                        style={{height: 'auto', color: '#2C2C2C', background: '#FDCA40'}}>
                        Apply Now
                      </Button>
                    </Link>
                  </div>
                </div>
                <div className="blog-profile-2 fixed-section blog_sidebar_looking_box_sec">
                  <img
                    src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blog_side_bar_looking.webp'}
                    alt="blog sidebar looking hire"
                    className=""
                    width={306}
                    height={224}
                    style={{width: '100%', borderRadius: '5%'}}
                  />
                  <div className="blog_sidebar_box_content_sec overlay overlay_2">
                    <h3 role="heading" aria-level={3} className="c-0055BA w-600 f-18 blog_looking_to_hire_head">
                      Looking to hire?
                    </h3>
                    <p className="blog_looking_to_hire_sub_head">
                      Join us & get access to our pool of talented candidates
                    </p>
                    <Link
                      prefetch={false}
                      href={
                        user?.id
                          ? user?.role == 'admin'
                            ? '#'
                            : user?.role == 'employer'
                              ? '/employer/candidates'
                              : user?.role == 'employee'
                                ? '#'
                                : user?.role == 'staff'
                                  ? '/staff/candidates'
                                  : '/'
                          : '/auth/signup'
                      }
                      onClick={
                        user?.role == 'employee'
                          ? e => handleClickShowErrorMessage(e, 'You need to register as a employer to hire candidates')
                          : undefined
                      }>
                      <Button
                        className="apply-btn btn-a primary-size-18 btn-bg-fff w-100 tab-w-100"
                        style={{height: 'auto', color: '#0055BA'}}>
                        Hire Now
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};
export default React.memo(BlogSignupSection);
