.company_card {
  display: flex;
  gap: 12px;
}
.company_image {
  border: 1px solid #cfe5ff;
  background: #fff;
  border-radius: 8px;
  display: flex;
  width: 80px;
  height: 80px;
  justify-content: center;
  align-items: center;
}
.company_image img {
  border-radius: 8px;
  height: 80px;
  width: 80px;
}
.company_info {
  display: flex;
  flex-direction: column;
  gap: 13px;
}
.company_name_container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
}
.rating_container {
  display: flex;
  align-items: center;
  gap: 8px;
}
.rating_container a {
  color: #0070f5;
  font-size: 16px;
  line-height: 140%;
}
.company_jobs_container a {
  color: #2c2c2c !important;
}
.company_jobs_container a:hover {
  text-decoration: underline !important;
}

@media screen and (max-width: 991px) {
  .company_image {
    height: 48px;
    width: 48px;
  }
  .company_image img {
    height: 48px;
    width: 48px;
  }
}
