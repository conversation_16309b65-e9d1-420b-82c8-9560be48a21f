import {useState} from 'react';
import {PopularCompanies} from '..';
import {StarSelection} from '../StartSelection';
import styles from './style.module.css';

interface PopularCompanyProps {
  company: PopularCompanies;
}

export const PopularCompany = ({company}: PopularCompanyProps) => {
  const {name, average_rating, id, location, logo, sector, total_reviews} = company?.company ?? {};
  const [companyLogo, setCompanyLogo] = useState(logo || '/images/placeholder.jpg');
  return (
    <div className={styles.company_card}>
      <div className={styles.company_image}>
        {companyLogo && <img src={companyLogo} alt={name} onError={() => setCompanyLogo('/images/placeholder.jpg')} />}
      </div>
      <div className={styles.company_info}>
        <div className={styles.company_name_container}>
          <h5>{name}</h5>
          <div className={styles.rating_container}>
            <StarSelection value={average_rating} variant="notSelectable" />
            <a href={`/companies/${company?.company_id}/reviews`}>{total_reviews} reviews</a>
          </div>
        </div>
        <div className={styles.company_jobs_container}>
          <a href={`/companies/${company.company_id}/jobs`}>view jobs</a>
        </div>
      </div>
    </div>
  );
};
