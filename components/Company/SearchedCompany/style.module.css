.search_company_card {
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.card_left {
  display: flex;
  align-items: start;
  gap: 12px;
}
.company_name_container a {
  color: #0055ba;
  font-size: 18px;
  font-weight: 600;
  line-height: 120%;
}
.company_name_container a:hover {
  text-decoration: underline;
}
.rating_and_review_container {
  display: flex;
  align-items: center;
  gap: 8px;
}
.rating_container {
  display: flex;
  align-items: center;
  gap: 4px;
}
.rating_container h5 {
  color: #2c2c2c;
  font-family: 'Open Sans';
  font-size: 16px;
  font-weight: 600;
  line-height: 140%;
  text-decoration-line: underline;
  text-decoration-style: solid;
}
.rating_and_review_container p {
  color: #999;
  font-size: 12px;
  font-weight: 400;
}
.company_department {
  color: #999;
  font-size: 12px;
  font-weight: 400;
  line-height: 140%;
}
.search_company_image {
  display: flex;
  width: 48px;
  height: 48px;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  border: 0.6px solid #cfe5ff;
  background: #fff;
}
.search_company_image img {
  border-radius: 8px;
  height: 48px;
  width: 48px;
  font-size: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.company_jobs_container_desktop a {
  color: #0070f5;
  font-size: 16px;
  font-weight: 600;
  line-height: 140%;
  text-decoration-line: underline !important;
  text-decoration-style: solid;
  text-decoration-skip-ink: none;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
}
.company_name_container .view_jobs {
  display: none;
}
.searched_company_text_container h5 {
  color: #191919;
  font-size: 16px;
  font-weight: 600;
  line-height: 160%;
}
.searched_company_text_container p {
  color: #4d4d4d;
  font-size: 16px;
  font-weight: 400;
  line-height: 160%;
}

@media screen and (max-width: 991px) {
  .company_jobs_container_desktop {
    display: none;
  }
  .company_name_container .view_jobs {
    color: #0070f5;
    font-size: 14px;
    font-weight: 600;
    line-height: 140%;
    text-decoration-line: underline !important;
    text-decoration-style: solid;
    display: block;
    text-decoration-skip-ink: none;
    text-decoration-thickness: auto;
    text-underline-offset: auto;
    text-underline-position: from-font;
  }
}
