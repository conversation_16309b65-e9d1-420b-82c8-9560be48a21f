import {SearchedCompanies} from '@/modules/companies/mutation/useGetSearchedCompanies';
import {StarSelection} from '../StartSelection';
import styles from './style.module.css';
import {useRouter} from 'next/router';
import {getSectorsList} from '@/lib/ApiAdapter';
import {useEffect, useState} from 'react';
import {getAllSector} from '@/lib/adminapi';
import {Sector} from '@/lib/types';

interface SearchedCompanyProps {
  company: SearchedCompanies;
}

export const SearchedCompany = ({company}: SearchedCompanyProps) => {
  const router = useRouter();
  const [sector, setSector] = useState<string>();
  const [companyLogo, setCompanyLogo] = useState(company?.company_logo || '/images/placeholder.jpg');
  useEffect(() => {
    (async () => {
      if (company?.company_sector) {
        const sections: {sectors: Sector[]} = await getAllSector();
        const currentSector = sections?.sectors?.find(el => el?.id === Number(company?.company_sector));
        setSector(currentSector?.sector_name);
      }
    })();
  }, [company?.company_sector]);
  return (
    <div className={styles.search_company_card}>
      <div className={styles.card_left}>
        <div className={styles.search_company_image}>
          {companyLogo && (
            <img
              src={companyLogo}
              alt={company.company_name}
              height={48}
              width={48}
              title={company?.company_name}
              onError={() => setCompanyLogo('/images/placeholder.jpg')}
            />
          )}
        </div>
        <div className={styles.company_info}>
          <div className={styles.company_name_container}>
            <a href={`/companies/${company?.id}`}>{company.company_name}</a>
            <div className={styles.rating_and_review_container}>
              <div className={styles.rating_container}>
                <h5
                  style={{
                    marginTop: '-4px',
                  }}>
                  {company.average_rating ?? 0}
                </h5>
                <StarSelection value={company.average_rating ?? 0} variant="notSelectable" type="single" size="small" />
              </div>
              <p>{company.total_reviews} reviews</p>
            </div>
            {sector && <p className={styles.company_department}>{sector}</p>}
            <a href={`/companies/${company?.id}/jobs`} className={styles.view_jobs}>
              View jobs
            </a>
          </div>
        </div>
      </div>
      <div className={styles.company_jobs_container_desktop}>
        <a href={`/companies/${company?.id}/jobs`}>View jobs</a>
      </div>
    </div>
  );
};
