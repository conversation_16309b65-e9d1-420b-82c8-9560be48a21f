import Image from 'next/image';
import {StarSelection} from '../StartSelection';
import {useState} from 'react';
import styles from './style.module.css';

interface EmployeeStarCollectionProps {
  hasImage?: boolean;
}

export const EmployeeStartCollection = ({hasImage = true}: EmployeeStarCollectionProps) => {
  const [starValue, setStarValue] = useState(0);

  return (
    <div className={`${styles.rating_employer_container} ${!hasImage && styles.no_image_rating_container}`}>
      <div className={`${styles.rating_employer_card} ${!hasImage && styles.no_image_card}`}>
        {hasImage && <img src={'/images/company/employee_star.png'} alt="employee star" width={160} height={200} />}
        <p>How would you rate your most recent employer?</p>
        <div className={styles.star_selection_container}>
          <div className={styles.star_container}>
            <StarSelection
              value={starValue}
              variant="selectable"
              onChange={value => setStarValue(value)}
              size="medium"
            />
          </div>
        </div>
      </div>
    </div>
  );
};
