.filterSection {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.filterSection h5 {
  color: #2c2c2c;
  font-size: 16px;
  font-weight: 600;
  line-height: 140%;
}

.selectFilterDropdown {
  display: flex;
  flex-direction: column;
}

.dropdown {
}

.rangePicker {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkboxDropdown {
  display: flex;
  flex-direction: column;
  gap: 4px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 8px;
}

.checkboxWithSearch {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  background-color: #f5f5f5;
}

.checkboxWithSearch input {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 6px 8px;
}

.checkboxWithSearch label {
  display: flex;
  align-items: center;
  gap: 8px;
}
