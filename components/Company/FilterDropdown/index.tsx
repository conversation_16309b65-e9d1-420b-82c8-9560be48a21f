import React from 'react';
import {Select} from 'antd';
import styles from './style.module.css';

interface FilterDropdownProps {
  label: string;
  options: {label: string; value: string | null}[];
  placeholder?: string;
  onChange: (value: string | null) => void;
  type: 'dropdown' | 'rangePicker' | 'checkboxDropdown' | 'checkboxWithSearch';
}

const FilterDropdown: React.FC<FilterDropdownProps> = ({label, options, placeholder = 'Select', onChange, type}) => {
  const getTypeClassName = () => {
    switch (type) {
      case 'rangePicker':
        return styles.rangePicker;
      case 'checkboxDropdown':
        return styles.checkboxDropdown;
      case 'checkboxWithSearch':
        return styles.checkboxWithSearch;
      default:
        return styles.dropdown;
    }
  };

  return (
    <div className={`${styles.filterSection} ${getTypeClassName()}`}>
      <h5>{label}</h5>
      {type === 'dropdown' && (
        <Select
          options={options}
          style={{width: '100%'}}
          placeholder={placeholder}
          className={styles.selectFilterDropdown}
          onChange={onChange}
        />
      )}
      {type === 'rangePicker' && (
        <div>
          <span>Range Picker Placeholder</span>
        </div>
      )}
      {type === 'checkboxDropdown' && (
        <div>
          {options.map(option => (
            <label key={option.value}>
              <input type="checkbox" value={option.value || ''} onChange={e => onChange(e.target.value || null)} />
              {option.label}
            </label>
          ))}
        </div>
      )}
      {type === 'checkboxWithSearch' && (
        <div>
          <input type="text" placeholder="Search..." />
          {options.map(option => (
            <label key={option.value}>
              <input type="checkbox" value={option.value || ''} onChange={e => onChange(e.target.value || null)} />
              {option.label}
            </label>
          ))}
        </div>
      )}
    </div>
  );
};

export default FilterDropdown;
