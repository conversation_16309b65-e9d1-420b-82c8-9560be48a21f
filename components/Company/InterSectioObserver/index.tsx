import React, {useEffect} from 'react';

interface IntersectionObserverProps {
  onIntersect: (isIntersecting: boolean) => void;
  children: React.ReactNode;
}

export const IntersectionObserverContainer: React.FC<IntersectionObserverProps> = ({
  children,
  onIntersect,
  ...rest
}) => {
  const ref = React.useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      e => {
        onIntersect(e?.[0]?.isIntersecting ?? false);
      },
      {
        root: null,
        threshold: 0.5,
      },
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [onIntersect]);

  return (
    <div {...rest} ref={ref}>
      {children}
    </div>
  );
};
