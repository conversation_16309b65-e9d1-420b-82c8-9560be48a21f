import React, {useCallback, useContext, useEffect, useMemo} from 'react';
import styles from './style.module.css';
import {useFormik} from 'formik';
import {StarInput} from '@/components/Common/StarInput';
import Image from 'next/image';
import {InputUi} from '@/components/Common/Input';
import {DatePickerUi} from '@/components/Common/DatePicker';
import Dayjs from 'dayjs';
import {Checkbox, Dropdown, Select} from 'antd';
import {reviewValidationSchema} from '@/schema/reviewValidationSchema';
import {useWriteReviewMutation} from '@/modules/companies/mutation/useCreateCompanyReview';
import moment from 'moment';
import AuthContext from '@/Context/AuthContext';
import {useRouter} from 'next/router';
import {useGetCompanyDetails} from '@/modules/companies/query/useGetCompanyDetail';
import {useGetSectors} from '@/modules/common/query/useGetSectors';
import {useGetCountries} from '@/modules/common/query/useGetCountries';
import {useGetCountryByCity} from '@/modules/common/query/useGetCityByCountry';

function WriteReview() {
  const {mutate: writeReview} = useWriteReviewMutation();
  const {user} = useContext(AuthContext);
  const router = useRouter();
  const {data: sectors} = useGetSectors();
  const {values, handleSubmit, setFieldValue, errors, isValid} = useFormik({
    initialValues: {
      feedback: {
        work_environment: 0,
        work_life_balance: 0,
        career_growth: 0,
        compensation_benefits: 0,
        job_security: 0,
      },
      review: {
        review_summary: '',
        review_details: '',
      },
      work_experience: {
        job_title: '',
        job_location: {
          country: '',
          city: '',
        },
        job_start_date: '',
        job_end_date: '',
        present: false,
      },
    },
    enableReinitialize: true,
    validationSchema: reviewValidationSchema,
    onSubmit: values => {
      user?.id &&
        writeReview(
          {
            career_growth: values?.feedback?.career_growth.toString(),
            compensation_benefit: values?.feedback?.compensation_benefits.toString(),
            work_environment: values?.feedback?.work_environment.toString(),
            work_life_balance: values?.feedback?.work_life_balance.toString(),
            job_security: values?.feedback?.job_security.toString(),
            review_description: values?.review?.review_details,
            review_title: values?.review?.review_summary,
            job_title: values?.work_experience?.job_title,
            country: values?.work_experience?.job_location?.country,
            city: values?.work_experience?.job_location?.city,
            start_date:
              values?.work_experience?.job_start_date &&
              moment(values?.work_experience?.job_start_date).format('DD.MM.YYYY'),
            end_date:
              values?.work_experience?.job_end_date &&
              moment(values?.work_experience?.job_end_date).format('DD.MM.YYYY'),
            user_id: user?.id,
            company_id: parseInt(router?.query?.slug as string),
          },
          {
            onSuccess: () => {
              router?.push(`/companies/${router?.query?.slug}/review-thanks`);
            },
          },
        );
    },
  });
  const {data: companyDetail} = useGetCompanyDetails({
    slug: router?.query?.slug as string,
    user_id: user?.id as number,
  });
  const {data: cities} = useGetCountryByCity({
    countryName: values?.work_experience?.job_location?.country as string,
  });

  const keyToLabel = (key: string) => {
    switch (key) {
      case 'work_environment':
        return 'Work Environment*';
      case 'work_life_balance':
        return 'Work-life Balance';
      case 'career_growth':
        return 'Career Growth';
      case 'compensation_benefits':
        return 'Compensation & Benefits';
      case 'job_security':
        return 'Job Security';
      default:
        return '';
    }
  };

  const allSelected = useCallback((values: Record<string, any>, errors: any) => {
    let allSelected = true;
    Object?.entries(errors ?? {}).forEach(([key, value]) => {
      if (value === null) {
        allSelected = false;
      }
      if (typeof value === 'number' && value === 0) {
        allSelected = false;
      } else if (typeof value === 'string' && value === '') {
        allSelected = false;
      }
    });

    if (values?.present || values?.job_end_date) {
      return allSelected;
    }

    Object?.entries(values ?? {}).forEach(([key, value]) => {
      if (value === null) {
        allSelected = false;
      }
      if (typeof value === 'number' && value === 0) {
        allSelected = false;
      } else if (typeof value === 'string' && value === '') {
        allSelected = false;
      }
    });

    return allSelected;
  }, []);

  const dynamicLabel = (label: string, object: Record<string, any>, errors: any) => {
    return (
      <div className={styles.section_label_container}>
        {allSelected(object, errors) ? (
          <img src={'/images/company/check_circle.svg'} alt="check_circle" width={24} height={24} />
        ) : (
          <img src={'/images/company/cancel.svg'} alt="cancel" width={24} height={24} />
        )}
        <h5>{label}</h5>
      </div>
    );
  };

  useEffect(() => {
    if (values.work_experience.present) {
      setFieldValue('work_experience.job_end_date', null);
    }
  }, [values?.work_experience?.present]);

  const {data: countries} = useGetCountries();

  return (
    <div className={`container ${styles.write_review_container}`}>
      <div className={styles.write_review_top}>
        <h4>Write a review for {companyDetail?.company?.company_name}</h4>
        <div className={styles.write_review_list_content}>
          <h6>What you need to know?</h6>
          <ul className={styles.write_review_unOrder_list}>
            <li>
              Your review is completely <span>anonymous</span>.
            </li>
            <li>
              It will <span>never</span> be visible to recruiters or appear in job applications.
            </li>
            <li>
              Your review includes <span>only</span> star ratings, written feedback, job details, and the review date.
            </li>
          </ul>
        </div>
      </div>
      <div className={styles.divider}></div>
      <form className={styles.form_container} onSubmit={handleSubmit}>
        <div className={styles.share_feedback_container}>
          {dynamicLabel('Share your feedback', values.feedback, errors.feedback)}
          <div className={styles.feedback_start_container}>
            {Object.entries(values.feedback).map(([key, value]) => (
              <div key={key} className={styles.star_container}>
                <StarInput
                  key={key}
                  label={keyToLabel(key)}
                  required
                  value={value}
                  onChange={value => setFieldValue(`feedback.${key}`, value)}
                  size="medium"
                />
              </div>
            ))}
          </div>
        </div>
        <div className={styles.review_container}>
          {dynamicLabel('Write your review', values.review, errors.review)}
          <div className={styles.review_content}>
            <InputUi
              label="Review Summary"
              isRequired
              value={values.review.review_summary}
              onChange={e => setFieldValue('review.review_summary', e.target.value)}
              characterLimit={80}
              placeholder="Eg: Great team and growth opportunities!"
            />
            <label className={styles.review_label}>
              Your Review
              <span>*</span>
            </label>
            <p>Share details about your experience, including: </p>
            <ul>
              <li>Work environment, company culture, management support, and opportunities for growth.</li>
              <li>Be honest and specific—mention what you liked and any challenges you faced.</li>
              <li>Advice for future employees. </li>
              <li>Your insights help others make informed decisions!</li>
            </ul>
          </div>
          <div className={styles.review_details}>
            <InputUi
              value={values.review.review_details}
              onChange={e => setFieldValue('review.review_details', e.target.value)}
              isMultiline
            />
          </div>
        </div>
        <div className={styles.work_experience_container}>
          {dynamicLabel('Share your work experience', values?.work_experience, errors?.work_experience)}
          <div>
            <label className={styles.location_label}>
              Job title at {companyDetail?.company?.company_name}
              <span>*</span>
            </label>
            <Select
              options={sectors?.map(el => {
                return {
                  label: el.sector_name,
                  value: el.sector_name,
                };
              })}
              style={{
                width: '100%',
              }}
              className={styles.sector_dropdown}
              onChange={value => setFieldValue('work_experience.job_title', value)}
              value={values?.work_experience?.job_title}
              showSearch
            />
          </div>
          <div>
            <label className={styles.location_label}>
              Job location at {companyDetail?.company?.company_name}
              <span>*</span>
            </label>
            <div className={styles.work_experience_input_container}>
              <Select
                options={countries?.map(el => {
                  return {
                    label: el.country_name,
                    value: el.country_name,
                  };
                })}
                style={{
                  width: '100%',
                }}
                className={styles.sector_dropdown}
                onChange={value => setFieldValue('work_experience.job_location.country', value)}
                filterOption={(input, option) => (option?.value ?? '')?.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                showSearch
                placeholder="Country"
              />
              <Select
                options={cities?.map(el => {
                  return {
                    label: el.city_name,
                    value: el.city_name,
                  };
                })}
                style={{
                  width: '100%',
                }}
                className={styles.sector_dropdown}
                onChange={value => setFieldValue('work_experience.job_location.city', value)}
                filterOption={(input, option) => (option?.value ?? '')?.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                showSearch
                placeholder="City"
              />
            </div>
            <div>
              <div className={styles.work_experience_dropdown_container}>
                <DatePickerUi
                  label="Start Date"
                  isRequired
                  value={values.work_experience.job_start_date ? Dayjs(values.work_experience.job_start_date) : null}
                  onChange={date => setFieldValue('work_experience.job_start_date', date)}
                  placeholder="MM/YYYY"
                  format={'MM/YYYY'}
                />
                <DatePickerUi
                  label="End Date"
                  value={values.work_experience.job_end_date ? Dayjs(values.work_experience.job_end_date) : null}
                  onChange={date => setFieldValue('work_experience.job_end_date', date)}
                  isRequired
                  placeholder="MM/YYYY"
                  format={'MM/YYYY'}
                  disabled={values?.work_experience?.present}
                />
              </div>
              <div className={styles.checkbox_container}>
                <Checkbox
                  onChange={e => {
                    setFieldValue('work_experience.present', e.target.checked);
                  }}
                  name="work_experience.present"
                  checked={values?.work_experience?.present}
                />
                <p> I currently work here</p>
              </div>
            </div>
          </div>
        </div>
        <div className={styles.button_container}>
          <button type="submit" disabled={!isValid}>
            Submit your review
          </button>
        </div>
      </form>
    </div>
  );
}

export default WriteReview;
