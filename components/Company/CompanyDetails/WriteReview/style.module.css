.write_review_container {
  display: flex;
  max-width: 624px;
  flex-direction: column;
  gap: 40px;
  padding-top: 40px;
  padding-bottom: 80px;
}
.write_review_top {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  align-self: stretch;
}
.write_review_top h4 {
  color: #151515;
  font-size: 26px;
  font-weight: 500;
  line-height: 120%;
}

.write_review_list_content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.write_review_unOrder_list {
  padding-left: 20px;
}

.write_review_unOrder_list li {
  list-style-type: disc;
  color: #4d4d4d;
  font-size: 16px;
  font-weight: 400;
  line-height: 160%;
}

.write_review_unOrder_list span {
  font-weight: 700;
}

.divider {
  width: 100%;
  height: 1px;
  background-color: #eee;
}
.feedback_start_container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}
.form_container {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.star_container {
  display: flex;
  padding: 10px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 8px;
  flex: 1 0 0;
}
.section_label_container {
  display: flex;
  gap: 8px;
  align-items: center;
  padding-bottom: 24px;
}

.section_label_container h5 {
  color: #151515;
  font-size: 22px;
  font-weight: 500;
  line-height: 120%;
}
.review_label {
  color: #2c2c2c;
  font-size: 12px;
  line-height: 140%;
}

.review_label span {
  color: #d04e4f;
}
.review_content p {
  padding-top: 4px;
}
.review_content ul {
  padding-left: 20px;
}
.review_content ul li {
  list-style-type: disc;
}
.review_content p,
.review_content ul li {
  color: #747474;
  font-size: 14px;
  line-height: 160%;
}
.review_details {
  padding-top: 4px;
}
.character_limit {
  height: 16px;
}
.work_experience_input_container {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 24px;
}
.location_label {
  color: #2c2c2c;
  font-size: 14px;
  font-weight: 600;
  line-height: 140%;
  padding-bottom: 4px;
}
.location_label span {
  color: #d04e4f;
}
.work_experience_dropdown_container {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 24px;
}
.checkbox_container {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: flex-end;
  padding-top: 4px;
}
.button_container {
  display: flex;
  justify-content: center;
}
.button_container button {
  display: flex;
  padding: 12px 16px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  background: #0055ba;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  line-height: 120%;
  box-shadow: none;
  width: 320px;
}
.button_container button:disabled {
  opacity: 0.5;
}
.sector_dropdown {
  height: 56px;
  margin-bottom: 16px;
}
.sector_dropdown span {
  color: #2c2c2c;
  font-feature-settings:
    'liga' off,
    'clig' off;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: -0.5px;
}
@media screen and (max-width: 991px) {
  .work_experience_input_container {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .feedback_start_container {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .work_experience_input_container {
    gap: 4px;
  }
  .write_review_top h4 {
    font-size: 23px;
  }
  .write_review_list_content {
    color: #2c2c2c;
    font-size: 16px;
    font-weight: 600;
    line-height: 160%;
  }
  .write_review_unOrder_list li {
    font-size: 14px;
  }
  .section_label_container h5 {
    font-size: 19px;
  }
  .review_content p,
  .review_content ul li {
    font-size: 14px;
  }
}
