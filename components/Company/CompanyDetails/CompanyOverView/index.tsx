import {CompanyDetail} from '@/modules/companies/query/useGetCompanyDetail';
import styles from './style.module.css';
import Image from 'next/image';
import moment from 'moment';
import {JobListCard} from '@/components/Jobs/JobListCard';
import Link from 'next/link';
import {StarSelection} from '../../StartSelection';
import {ReviewCard} from '../ReviewCard';
import {Job} from '@/lib/types';
import {useRouter} from 'next/router';

interface CompanyOverViewProps {
  companyDetails: CompanyDetail;
}
export const CompanyOverviews = ({companyDetails}: CompanyOverViewProps) => {
  const {company, recent_reviews} = companyDetails;
  const {jobs} = companyDetails?.company ?? {};
  const router = useRouter();

  return (
    <div className={styles.tab_container}>
      <div className={styles.card_container}>
        {company?.sector?.sector_name && (
          <div className={styles.card}>
            <h4 className={styles.card_title}>INDUSTRY</h4>
            <span className={styles.card_content}>{company?.sector?.sector_name}</span>
          </div>
        )}
        {company?.no_of_employees && (
          <div className={styles.card}>
            <h4 className={styles.card_title}>Company size</h4>
            <span className={styles.card_content}>{company?.no_of_employees}</span>
          </div>
        )}
      </div>
      {company?.company_description && (
        <div className={styles.about_company}>
          <h4>About Company</h4>
          <span
            dangerouslySetInnerHTML={{
              __html: company?.company_description,
            }}
          />
        </div>
      )}
      {jobs?.data?.length > 0 && (
        <div className={styles.job_opening_container}>
          <h4>Job openings at {company?.company_name}</h4>
          <div className={styles.jobList_card}>
            {jobs?.data?.map((job: Job, index: number) => (
              <JobListCard
                job={job}
                key={index}
                index={index}
                jobInCityName={''}
                variant="company"
                onClick={e => {
                  router.push(`/companies/${router.query.slug}/jobs?job=${job.job_slug}`);
                }}
              />
            ))}
          </div>
          <Link href={`/companies/${router.query.slug}/jobs`} className={styles.view_jobs}>
            <span> View all jobs</span>
            <img src={'/images/company/arrow-right.svg'} alt="arrow right" height={16} width={16} />
          </Link>
        </div>
      )}
      {recent_reviews?.length > 0 && (
        <div className={styles.review_section}>
          <h4>Review</h4>
          <div className={styles.review_container}>
            {recent_reviews
              ?.slice(0, 3)
              .map((review, index) => <ReviewCard starType="single" review={review} key={index} variant="read-only" />)}
          </div>
          <Link href={`/companies/${router?.query?.slug}/reviews`} className={styles.view_reviews}>
            <span> View all Reviews</span>
            <img src={'/images/company/arrow-right.svg'} alt="arrow right" height={16} width={16} />
          </Link>
        </div>
      )}
    </div>
  );
};
