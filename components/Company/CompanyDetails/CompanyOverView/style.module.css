.tab_container {
  display: flex;
  flex-direction: column;
  gap: 40px;
}
.card_container {
  display: flex;
  gap: 16px;
}
.card {
  display: flex;
  width: 240px;
  height: 120px;
  padding: 15px;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  border-radius: 8px;
  border: 1px solid var(--Grayscale-03, #d9d9d9);
  background: #fff;
}

.card .card_title {
  color: #747474;
  font-size: 12px;
  font-weight: 700;
  line-height: 140%;
  text-transform: uppercase;
}
.card .card_content {
  overflow: hidden;
  color: #2c2c2c;
  text-overflow: ellipsis;
  font-size: 22px;
  font-style: normal;
  font-weight: 500;
  line-height: 120%;
  max-width: 220px;
  white-space: normal;
  overflow-wrap: break-word;
}
.about_company {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  align-self: stretch;
}
.about_company h4,
.review_container h4,
.job_opening_container h4 {
  color: #2c2c2c;
  font-size: 26px;
  font-weight: 500;
  line-height: 120%;
}
.about_company span {
  color: #2c2c2c;
  font-size: 16px;
  font-weight: 400;
  line-height: 160%;
}
.jobList_card {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
  padding-top: 32px;
  padding-top: 24px;
}
.view_jobs,
.view_reviews {
  display: flex;
  align-items: center;
  gap: 8px;
  padding-top: 24px;
}
.view_jobs span,
.view_reviews span {
  color: #0070f5 !important;
  font-size: 16px;
  font-weight: 500;
  line-height: 120%;
  border-bottom: 2px solid #0070f5 !important;
}
.review_section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 32px;
  align-self: stretch;
}
.location {
  padding-bottom: 4px;
}

.tab_container {
  padding-bottom: 40px;
}
.review_container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.view_reviews {
  padding-top: 0 !important;
}
@media screen and (max-width: 991px) {
  .jobList_card {
    width: 100%;
  }
}
