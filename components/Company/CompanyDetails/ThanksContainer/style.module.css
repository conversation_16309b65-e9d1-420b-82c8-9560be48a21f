.review_thanks_container {
  display: flex;
  max-width: 624px;
  flex-direction: column;
  align-items: flex-start;
  padding-top: 40px;
  gap: 40px;
}
.divider {
  width: 100%;
  height: 1px;
  background: #eee;
}
.thanks_container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.thanks_container h2 {
  color: #128269;
  font-size: 26px;
  font-weight: 500;
  line-height: 120%;
}
.thanks_container h4 {
  color: #2c2c2c;
  font-size: 18px;
  font-weight: 600;
  line-height: 160%;
}

.thanks_container h6 {
  color: #2c2c2c;
  font-size: 16px;
  font-weight: 600;
  line-height: 140%;
}
.thanks_container ul {
  padding-left: 12px;
}
.thanks_container ul li {
  color: #4d4d4d;
  font-size: 16px;
  line-height: 160%;
  list-style-type: disc;
}
.thanks_container ul li a {
  color: #0070f5;
  text-decoration-line: underline !important;
  text-decoration-style: solid;
  text-decoration-skip-ink: none;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
}
.what_next_container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.what_next_container h3 {
  color: #151515;
  font-size: 22px;
  font-weight: 500;
  line-height: 120%;
}
.next_link_container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.next_link_container a {
  border-bottom: 2px solid #0070f5;
  color: #0070f5;
  font-size: 16px;
  font-weight: 500;
  line-height: 120%;
}

@media screen and (max-width: 991px) {
  .thanks_container h2 {
    font-size: 23px;
  }
  .thanks_container h4 {
    font-size: 16px;
  }
  .thanks_container h6 {
    font-size: 13px;
  }
  .thanks_container ul li {
    font-size: 14px;
  }
  .what_next_container h3 {
    font-size: 19px;
  }
}
