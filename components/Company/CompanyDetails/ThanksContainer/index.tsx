import styles from './style.module.css';

interface ThanksPageProps {
  variant: 'review' | 'claim';
}

export const ThanksPage = ({variant}: ThanksPageProps) => {
  return (
    <div className={`container ${styles.review_thanks_container}`}>
      <div className={styles.thanks_container}>
        <h2>{variant === 'review' && 'Thank you for submitting your review.'}</h2>
        <h2>{variant === 'claim' && 'Thank you for claiming your Company Profile'}</h2>
        {variant === 'review' && (
          <h4>Your review helps us others make informed decisions and make our platform a better place!</h4>
        )}
        {variant === 'claim' && <h4>With an company account, you can:</h4>}
        {variant === 'review' && <h6>What happens to your review?</h6>}
        {variant === 'review' && (
          <ul>
            <li>
              Our team will review your submission to ensure that it meets ours <a href="">Community Guidelines</a>.
              This typically takes 2-4 days.
            </li>
            <li>Once approved it will appear on the company page.</li>
          </ul>
        )}

        {variant === 'claim' && (
          <ul>
            <li>Attract top talent by showcasing your company's culture, mission, and values.</li>
            <li>Easily advertise your available positions to a targeted audience of job seekers.</li>
            <li>Update company information, including your logo, banner, and website link.</li>
            <li>Directly interact with potential candidates through messaging and interviews.</li>
            <li>Increase your company's online presence and credibility.</li>
          </ul>
        )}
      </div>
      <div className={styles.divider}></div>
      <div className={styles.what_next_container}>
        <h3>What’s next?</h3>
        {variant === 'review' && (
          <div className={styles.next_link_container}>
            <a href="/jobs-in-gulf">Explore Jobs</a>
            <a href="/salaries">Search for Salaries</a>
          </div>
        )}
        {variant === 'claim' && (
          <div className={styles.next_link_container}>
            <a href="">Post a job</a>
            <a href="">Search for CVs</a>
          </div>
        )}
      </div>
    </div>
  );
};
