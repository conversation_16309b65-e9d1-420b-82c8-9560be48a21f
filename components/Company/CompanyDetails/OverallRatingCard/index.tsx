import {Reviews} from '@/modules/companies/query/useGetCompanyDetail';
import {StarSelection} from '../../StartSelection';
import styles from './style.module.css';

interface OverAllRatingCardProps {
  reviews: Reviews[];
}

export const OverAllRatingCard = ({reviews}: OverAllRatingCardProps) => {
  return (
    <div className={styles.overall_rating_card}>
      <h6>Overall rating</h6>
      <div className={styles.rating_analysis}>
        <div className={styles.avg_star_container}>
          <div className={styles.star_icon}>
            <StarSelection value={1} type="single" variant="notSelectable" size="large" />
          </div>
          <span>
            {reviews?.length > 0
              ? (reviews?.reduce((acc, review) => acc + Number(review?.rating), 0) / reviews.length).toFixed(1)
              : 0}
          </span>
        </div>
        <span className={styles.no_of_reviews_text}>
          from {reviews?.length} <br /> reviews
        </span>
      </div>
      <div className={styles.progress_container}>
        {[5, 4, 3, 2, 1].map((rating, index) => (
          <div key={index} className={styles.progress_bar_container}>
            <span>{rating}</span>
            <div>
              <StarSelection value={1} type="single" variant="notSelectable" />
            </div>
            <div className={styles.progressBar}>
              <div
                className={styles.progressFill}
                style={{
                  width:
                    reviews?.length > 0
                      ? `${
                          (reviews.filter(review => Math.round(Number(review.rating)) === rating).length /
                            reviews.length) *
                          100
                        }%`
                      : 0,
                }}></div>
            </div>
            <div>{reviews?.filter(review => Math.round(Number(review.rating)) === rating).length}</div>
          </div>
        ))}
      </div>
    </div>
  );
};
