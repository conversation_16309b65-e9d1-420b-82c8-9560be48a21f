.overall_rating_card {
  display: flex;
  width: 306px;
  padding: 12px;
  flex-direction: column;
  align-items: flex-start;
  gap: 24px;
  border-radius: 16px;
  border: 1px solid #d9d9d9;
  margin-top: 35px;
}
.rating_analysis {
  display: flex;
  gap: 16px;
  align-items: center;
}
.avg_star_container {
  display: flex;
  align-items: center;
  gap: 4px;
}
.avg_star_container span {
  color: #2c2c2c;
  font-size: 56px;
  font-weight: 700;
  line-height: 120%;
}
.no_of_reviews_text {
  color: #4d4d4d;
  font-size: 16px;
  font-weight: 400;
  line-height: 140%;
  max-width: 128px;
}
.progress_container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.progress_bar_container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}
.progressBar {
  position: relative;
  flex: 1;
  height: 10px;
  background-color: #f9f9f9;
  border-radius: 5px;
  overflow: hidden;
  margin-right: 8px;
  width: 100%;
  min-width: 186px;
  border: 1px solid #d9d9d9;
}

.progressFill {
  height: 100%;
  background-color: #0eb1d2;
  transition: width 0.3s ease;
  border-radius: 12px;
}
.star_icon {
  margin-top: 6px;
}

@media screen and (max-width: 991px) {
  .overall_rating_card {
    width: 100%;
  }
  .progressBar {
    width: 231px;
  }
}
