.modal_divider {
  width: 100%;
  height: 1px;
  background: #eee;
  margin: 12px 0;
}
.radio_group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.radio_content {
  display: flex;
  padding: 16px 0px;
  flex-direction: column;
  gap: 16px;
}
.radio_content p {
  color: #4d4d4d;
  font-size: 16px;
  line-height: 140%;
}
.radio_content label {
  color: #2c2c2c;
  font-size: 16px;
  line-height: 140%;
}
.body {
  width: 100%;
}
.header_content {
  padding: 16px 12px 0 12px;
  color: #191919;
  font-size: 18px;
  font-weight: 600;
  max-width: 291px;
}
.modal_content {
  padding: 16px 12px 0 12px;
}
.button_container {
  display: flex;
  align-items: center;
  gap: 16px;
  justify-content: end;
  padding: 16px 12px;
}
.button_container .primary_button {
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px 18px;
  background-color: #0055ba;
  height: 40px;
}
.cancel_button {
  color: #747474;
}
