import NiceModal, {antdModal, useModal} from '@ebay/nice-modal-react';
import {Button, Modal, Radio} from 'antd';
import styles from './style.module.css';
import {RadioUi} from '@/components/Common/Radio';
import {consoleLogger} from '@/utils/logger';
import {useContext, useState} from 'react';
import {useUpdateReportReview} from '@/modules/companies/mutation/useUpdateReportReview';
import AuthContext from '@/Context/AuthContext';
import {ModalWrapper} from '@/components/modal';
import {ReportThanks} from '@/components/modal/ReportThanks';

const reportContent = [
  {
    label: 'Offensive language',
    name: 'report',
  },
  {
    label: 'Spam or irrelevant content',
    name: 'report',
  },
  {
    label: 'Promotional content',
    name: 'report',
  },
  {
    label: 'Defamation',
    name: 'report',
  },
  {
    label: 'False or misleading information',
    name: 'report',
  },
];

interface ReportModalProps {
  review_id: string;
}
export const ReportModal = NiceModal.create(({review_id}: ReportModalProps) => {
  const [reportValue, setReportValue] = useState<string>();
  const {mutate: mutateReport} = useUpdateReportReview();
  const modal = useModal();
  const {user} = useContext(AuthContext);

  const handleSubmit = () => {
    reportValue &&
      mutateReport(
        {
          report_reason: reportValue,
          review_id: review_id,
          user_id: user?.id.toString() ?? '',
        },
        {
          onSuccess: () => {
            NiceModal.show(ReportThanks);
            modal.hide();
          },
        },
      );
  };

  return (
    <ModalWrapper {...antdModal(modal)}>
      <div className={styles.header_content}>Why should this review be removed?</div>
      <div className={styles.modal_divider} />
      <div className={styles.modal_content}>
        <div className={styles?.radio_content}>
          <p>it contains:</p>
          <Radio.Group className={styles.radio_group}>
            {reportContent.map((item, index) => {
              return (
                <RadioUi
                  key={index}
                  value={item.label}
                  label={item.label}
                  name={item.name}
                  onChange={e => {
                    setReportValue(e.target.value);
                  }}
                  checked={reportValue === item?.label}
                />
              );
            })}
          </Radio.Group>
        </div>
      </div>
      <div className={styles.modal_divider} />
      <div className={styles.button_container}>
        <Button type="text" className={styles.cancel_button} onClick={modal.hide}>
          Cancel
        </Button>
        <Button type="primary" className={styles.primary_button} onClick={handleSubmit}>
          Submit
        </Button>
      </div>
    </ModalWrapper>
  );
});
