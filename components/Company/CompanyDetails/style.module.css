.banner_container {
  height: 234px;
  position: relative;
}
.company_header {
  display: flex;
  align-items: start;
  justify-content: space-between;
  padding-top: 24px;
}
.header_left_container {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}
.image_container {
  display: flex;
  width: 120px;
  height: 120px;
  justify-content: center;
  align-items: center;
  border-radius: 9.796px;
  border: 1px solid #d9d9d9;
  background: #d9d9d9;
}
.company_content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.company_content h4 {
  color: #191919;
  font-size: 32px;
  font-weight: 700;
  line-height: 120%;
  padding-bottom: 4px;
}
.company_location,
.company_portfolio {
  display: flex;
  align-items: center;
  gap: 4px;
  padding-bottom: 4px;
}
.company_portfolio {
  padding-bottom: 8px !important;
}
.company_location img,
.company_portfolio img {
  width: 16px;
  height: 16px;
}
.company_location span,
.company_portfolio span {
  color: #bababa;
  font-size: 16px;
  font-weight: 400;
  line-height: 120%;
}
.action_container {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}
.follow_button {
  display: flex;
  padding: 1.22rem 18px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  border: 2px solid #0055ba;
  background: rgba(0, 85, 186, 0.08);
}

.review_button {
  display: flex;
  padding: 1.285rem 23px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  background: #0055ba;
}
.tab_container {
  display: flex;
  padding-top: 24px;
  align-items: flex-start;
}

@media screen and (max-width: 991px) {
  .banner_container {
    height: 67.708px;
  }
  .review_section {
    padding: 0 !important;
  }
  .company_header {
    display: flex;
    padding: 0px 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding-top: 12px;
  }
  .header_left_container {
    display: flex;
    flex-direction: column;
  }
  .image_container {
    width: 80px;
    height: 80px;
  }
  .image_container img {
    width: 80px;
    height: 80px;
  }
  .company_content {
    display: flex;
    flex-direction: column;
  }
  .action_container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 8px;
    width: 100%;
  }
  .action_container button {
    width: 100%;
    height: 43px;
  }
  .tab_container {
    padding: 24px 12px 0 12px;
  }
}
