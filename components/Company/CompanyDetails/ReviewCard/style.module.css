.rating_container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding-bottom: 16px;
  width: 100%;
}
.rating_container h4 {
  color: #2c2c2c;
  font-size: 24px;
  font-weight: 600;
  line-height: 140%;
  text-decoration-line: underline;
  text-decoration-style: solid;
  text-decoration-skip-ink: none;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
}
.rating_container p {
  color: #747474;
  font-size: 16px;
  line-height: 140%;
}
.review_content h4 {
  color: #191919;
  font-size: 22px;
  font-weight: 500;
  line-height: 120%;
}
.review_content p {
  color: #2c2c2c;
  font-size: 16px;
  line-height: 160%;
}
.review_action {
  padding-top: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
}
.helpful_container p {
  color: #747474;
  font-size: 16px;
  line-height: 140%;
}
.helpful_button_container {
  padding-top: 4px;
  display: flex;
  align-items: center;
  gap: 7px;
}

.helpful_button_container button {
  display: flex;
  padding: 8px 12px;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  background: #eee;
  border: none;
  color: #747474;
  font-size: 14px;
  line-height: 120%;
  gap: 4px;
}
.helpful_button_container button:hover {
  background: #d9d9d9 !important;
  color: #747474 !important;
}
.helpful_yes_review_count {
  font-size: 12px;
  line-height: 120%;
  color: #0c5a14;
}
.helpful_no_review_count {
  color: #d04e4f;
  font-size: 12px;
  line-height: 120%;
}
.review_count {
  color: #999;
  font-size: 12px;
  line-height: 120%;
}
.helpful_yes_flag {
  border-radius: 8px;
  background: #dcf2ea !important;
}
.helpful_no_flag {
  background-color: #ffebeb !important;
  border-radius: 8px;
}
.helpful_yes_text {
  color: #0c5a14 !important;
}
.helpful_no_text {
  color: #d04e4f !important;
}
.report_container {
  display: flex;
  align-items: center;
  gap: 4px;
}
.report_container p {
  color: #bababa;
  font-size: 16px;
  line-height: 140%;
}
.location_date_container {
  color: #747474;
  font-size: 16px;
  line-height: 160%;
  padding-top: 4px;
  padding-bottom: 16px;
}

.location_date_container span {
  text-decoration-line: underline;
  text-decoration-style: solid;
  text-decoration-skip-ink: none;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
}
.review_card_container {
  display: flex;
  gap: 16px;
}
.star_rating_container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  position: relative;
}
.star_rating_container h4 {
  color: #2c2c2c;
  font-size: 32px;
  font-weight: 600;
  line-height: 140%;
  text-decoration-line: underline;
  text-decoration-style: solid;
  text-decoration-skip-ink: none;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
}
.review_card {
  width: 100%;
}
.rating_detail_container {
  display: flex;
  opacity: 0;
  transform: translateY(-10px);
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;
  width: 248px;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 8px;
  border: 1px solid #eee;
  box-shadow:
    0px 2px 5px 0px rgba(21, 21, 21, 0.12),
    0px 4px 8px -3px rgba(21, 21, 21, 0.15),
    0px 14px 20px -2px rgba(21, 21, 21, 0.16);
  position: absolute;
  top: 40px;
  z-index: 30;
  left: 0px;
  visibility: hidden;
}
.rating_card {
  display: flex;
  padding: 10px;
  align-items: center;
  gap: 6px;
  align-self: stretch;
  background: #fff;
}

.star_rating_container {
  position: relative;
  cursor: pointer;
}
.star_rating_container:hover .rating_detail_container {
  opacity: 1;
  transform: translateY(0);
  visibility: visible;
}

@media screen and (max-width: 991px) {
  .review_card_container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  .star_rating_container {
    flex-direction: row !important;
    align-items: center;
    gap: 4px;
  }
  .star_rating_container h4 {
    font-size: 24px;
  }
  .star_selection_container {
    margin-top: -10px;
  }
}
