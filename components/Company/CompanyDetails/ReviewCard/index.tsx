import moment from 'moment';
import {StarSelection} from '../../StartSelection';
import styles from './style.module.css';
import Image from 'next/image';
import {Button} from 'antd';
import {Reviews} from '@/modules/companies/query/useGetCompanyDetail';
import NiceModal from '@ebay/nice-modal-react';
import {ReportModal} from '../ReportModal';
import {useUpdateCompanyHelpful} from '@/modules/companies/mutation/useUpdateComapnyHelpful';
import {useContext} from 'react';
import AuthContext from '@/Context/AuthContext';
import {useRouter} from 'next/router';

interface ReviewCardProps {
  review: Reviews;
  starType: 'single' | 'multiple';
  variant: 'read-only' | 'interactive';
}

type Keys = 'career_growth' | 'compensation_benefit' | 'job_security' | 'work_environment' | 'work_life_balance';

export const ReviewCard = ({review, starType = 'single', variant}: ReviewCardProps) => {
  const {mutate: updateCompanyHelpful} = useUpdateCompanyHelpful();
  const {user} = useContext(AuthContext);
  const router = useRouter();
  const handleReport = () => {
    if (!user) {
      router.push('/auth/signup');
      return;
    }
    if (review?.report_flag) {
      return;
    }
    NiceModal.show(ReportModal, {
      review_id: review?.id.toString(),
    });
  };

  const keyToWords = (key: Keys) => {
    switch (key) {
      case 'work_environment':
        return 'Work Environment';
      case 'work_life_balance':
        return 'Work-life Balance';
      case 'career_growth':
        return 'Career Growth';
      case 'compensation_benefit':
        return 'Compensation & Benefits';
      case 'job_security':
        return 'Job Security';
      default:
        break;
    }
  };

  const handleCompanyHelpFull = (value: 'yes' | 'no') => {
    if (!user) {
      router.push('/auth/signup');
      return;
    }
    updateCompanyHelpful({
      reviewType: value,
      review_id: review?.id?.toString(),
      user_id: user?.id?.toString(),
    });
  };

  return (
    <div className={styles.review_card_container}>
      {starType === 'multiple' && (
        <div className={styles.star_rating_container}>
          <h4 className={styles.star_rating_container_text}>{review?.rating}</h4>
          <div className={styles.star_selection_container}>
            <StarSelection variant="notSelectable" type={starType} value={parseInt(review?.rating)} />
          </div>
          <div className={styles.rating_detail_container}>
            {Object.entries(review?.detail_review ?? {})?.map(([key, value], index) => {
              return (
                <div key={index} className={styles.rating_card}>
                  <div className={styles.star_container}>
                    <StarSelection value={Number(value)} variant="notSelectable" size="small" type="multiple" />
                  </div>
                  <span>{keyToWords(key as Keys)}</span>
                </div>
              );
            })}
          </div>
        </div>
      )}
      <div className={styles.review_card}>
        {starType === 'single' && (
          <div>
            {review?.user?.current_position
              ? `${review?.user?.current_position}${!!review?.user?.country ? ' in' + review?.user?.country : ''}`
              : review?.user?.country}
          </div>
        )}
        {starType === 'single' && (
          <div className={styles.rating_container}>
            <h4>{review?.rating}</h4>
            <div>
              <StarSelection variant="notSelectable" type={starType} value={Number(review?.rating)} />
            </div>
            <p>{moment(review?.created_at).format('DD MMMM YYYY')}</p>
          </div>
        )}
        <div className={styles.review_content}>
          <h4>{review?.review_title}</h4>
          {starType === 'multiple' && (
            <div className={styles.location_date_container}>
              <span>
                {review?.user?.current_position
                  ? `${review?.user?.current_position}${!!review?.user?.country ? ' in' + review?.user?.country : ''}`
                  : review?.user?.country}
              </span>
              {(review?.user?.current_position || review?.user?.country) && ' - '}

              {moment(review?.created_at).format('DD MMMM YYYY')}
            </div>
          )}
          <p>{review?.review_description}</p>
        </div>
        {variant === 'interactive' && (
          <div className={styles.review_action}>
            <div className={styles.helpful_container}>
              <p>Found this review helpful? Let others know!</p>
              <div className={styles.helpful_button_container}>
                <Button
                  onClick={() => handleCompanyHelpFull('yes')}
                  className={styles[review?.helpful_yes_flag ? 'helpful_yes_flag' : 'helpful_yes_count']}>
                  <span className={styles[review?.helpful_yes_flag ? 'helpful_yes_text' : '']}>Yes</span>
                  {review?.helpful_yes_flag ? (
                    <div>
                      <img src="/icons/company/thumbs_up.svg" alt="tick" height={16} width={16} />
                      <span className={styles[review?.helpful_yes_flag ? 'helpful_yes_review_count' : '']}>
                        {review?.helpful_yes_count}
                      </span>
                    </div>
                  ) : (
                    !!review?.helpful_yes_count && (
                      <span className={styles.review_count}>{review?.helpful_yes_count}</span>
                    )
                  )}
                </Button>

                <Button
                  onClick={() => handleCompanyHelpFull('no')}
                  className={styles[review?.helpful_no_flag ? 'helpful_no_flag' : 'helpful_no_count']}>
                  <span className={styles[review?.helpful_no_flag ? 'helpful_no_text' : '']}>No</span>
                  {review?.helpful_no_flag ? (
                    <div>
                      <img src="/icons/company/thumbs_down.svg" alt="tick" height={16} width={16} />
                      <span className={styles[review?.helpful_no_flag ? 'helpful_no_review_count' : '']}>
                        {review?.helpful_no_count}
                      </span>
                    </div>
                  ) : (
                    !!review?.helpful_no_count && (
                      <span className={styles.review_count}>{review?.helpful_no_count}</span>
                    )
                  )}
                </Button>
              </div>
            </div>
            <div
              className={styles.report_container}
              onClick={handleReport}
              style={{
                cursor: 'pointer',
              }}>
              <img
                src="/images/company/prime_flag.svg"
                alt="flag"
                height={24}
                width={24}
                className={styles.flag_icon}
              />
              <p>Report</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
