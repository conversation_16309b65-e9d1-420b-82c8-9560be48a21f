import styles from './style.module.css';
import {Select} from 'antd';
import {ReviewCard} from '../ReviewCard';
import Image from 'next/image';
import {EmployeeStartCollection} from '../../RatingEmployees';
import {Job} from '@/lib/types';
import {JobListCard} from '@/components/Jobs/JobListCard';
import {OverAllRatingCard} from '../OverallRatingCard';
import {useGetSingleCompanyReviews} from '@/modules/companies/query/useGetSingleCompanyReview';
import {useRouter} from 'next/router';
import {useContext, useState} from 'react';
import {useGetCountries} from '@/modules/common/query/useGetCountries';
import {useGetSectors} from '@/modules/common/query/useGetSectors';
import NiceModal from '@ebay/nice-modal-react';
import AuthContext from '@/Context/AuthContext';
import {CompanyClaimModal} from '@/components/modal/CompanyClaimed';
import {CompanyDetail} from '@/modules/companies/query/useGetCompanyDetail';

interface CompanyReviewsProps {
  variant?: 'interactive' | 'static';
  companyDetail: CompanyDetail;
}

export const CompanyReviews = ({companyDetail, variant = 'static'}: CompanyReviewsProps) => {
  const router = useRouter();
  const {jobs} = companyDetail;
  const [query, setQuery] = useState<Record<string, string>>({});
  const {user} = useContext(AuthContext);
  const {data: reviewDetails} = useGetSingleCompanyReviews({
    id: Number(router?.query?.slug),
    params: query,
    user_id: user?.id,
  });
  const {data} = useGetCountries();
  const {reviews} = reviewDetails ?? {};
  const {data: sectors} = useGetSectors();

  const profile_claimed = companyDetail?.claim_flag;

  const handleCompanyClaim = (company_claimed: boolean) => {
    if (company_claimed) {
      NiceModal.show(CompanyClaimModal);
    } else {
      if (!user?.id) {
        router.push('/auth/signup');
        return;
      }
      router.push(`/companies/${router.query?.slug}/claim`);
    }
  };

  return (
    <div className={styles.review_section}>
      <div className={styles.header_top_content}>
        <h4>Reviews for {companyDetail?.company?.company_name}</h4>
        <p>Displaying {reviews?.length} reviews</p>
      </div>
      <div className={styles.reviews_container_left}>
        <div className={styles.review_content}>
          <div className={styles.filter_container}>
            <div className={styles.job_title_filter}>
              <h5>job Title</h5>
              <Select
                options={[
                  {
                    label: 'All',
                    value: null,
                  },
                  ...(sectors?.map(el => ({
                    label: el.sector_name,
                    value: el.sector_name,
                  })) ?? []),
                ]}
                style={{
                  width: '100%',
                }}
                placeholder="All"
                className={styles.select_filter_dropdown}
                onChange={value =>
                  setQuery({
                    ...query,
                    job_title: value,
                  })
                }
              />
            </div>
            <div className={styles.location_filter}>
              <h5>Location</h5>
              <Select
                options={[
                  {
                    label: 'All',
                    value: null,
                  },
                  ...(data?.map(el => ({
                    label: el.country_name,
                    value: el.country_name,
                  })) ?? []),
                ]}
                style={{
                  width: '100%',
                }}
                placeholder="All"
                className={styles.select_filter_dropdown}
                onChange={value =>
                  setQuery({
                    ...query,
                    location: value,
                  })
                }
              />
            </div>
            <div className={styles.sort_filter}>
              <h5>Sort by</h5>
              <Select
                options={[
                  {
                    label: 'Oldest',
                    value: 'oldest',
                  },
                  {
                    label: 'Most Recent',
                    value: 'most_recent',
                  },
                ]}
                style={{
                  width: '100%',
                }}
                placeholder="Most Recent"
                className={styles.select_filter_dropdown}
                onChange={value =>
                  setQuery({
                    ...query,
                    sort_by: value,
                  })
                }
              />
            </div>
          </div>
          {reviews && (
            <div className={styles.mobile_overall_review}>
              <OverAllRatingCard reviews={reviews} />
            </div>
          )}
          {reviews && reviews?.length > 0 && (
            <div className={styles.reviews_container}>
              {reviews.map((review, index) => {
                if (index === 3) {
                  return <EmployeeStartCollection key={index} hasImage={false} />;
                }
                if (index === 9) {
                  return (
                    <div key={index} className={styles.job_opening_container}>
                      <h4>Recent job openings</h4>
                      <div className={styles.job_card_container}>
                        {jobs?.slice(0, 2).map((el, index) => {
                          return <JobListCard index={index} job={el} variant="company" key={index} />;
                        })}
                        <div className={styles.viewAll_jobs_container}>
                          <a href="">View all jobs</a>
                          <img src={'/images/company/arrow-right.svg'} alt="arrow" height={16} width={16} />
                        </div>
                      </div>
                    </div>
                  );
                }
                return <ReviewCard review={review} starType="multiple" variant="interactive" key={index} />;
              })}
            </div>
          )}
        </div>
        <div>
          <div onClick={() => handleCompanyClaim(profile_claimed ?? false)}>
            {profile_claimed ? (
              <div className={styles.company_claimed}>
                <img src="/images/company/verified_icon.svg" alt="flag" height={24} width={24} />
                <span>Company profile claimed</span>
              </div>
            ) : (
              <div className={styles.company_not_claimed}>
                <span>Claim this company profile</span>
              </div>
            )}
          </div>

          {reviews && (
            <div className={styles.desktop_overall_review}>
              <OverAllRatingCard reviews={reviews} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
