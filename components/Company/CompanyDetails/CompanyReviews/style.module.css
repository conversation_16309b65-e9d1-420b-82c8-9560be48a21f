.review_section {
  display: flex;
  flex-direction: column;
  padding-bottom: 34px;
}
.header_top_content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-bottom: 32px;
}
.filter_container {
  display: flex;
  padding: 12px;
  align-items: flex-start;
  gap: 24px;
  align-self: stretch;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
}
.reviews_container {
  padding-top: 32px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.job_title_filter,
.location_filter,
.sort_filter {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.job_title_filter h5,
.location_filter h5,
.sort_filter h5 {
  color: #2c2c2c;
  font-size: 16px;
  font-weight: 600;
  line-height: 140%;
}
.select_filter_dropdown {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.reviews_container_left {
  display: flex;
  gap: 24px;
}

.company_claimed {
  display: flex;
  width: 306px;
  padding: 12px 8px;
  gap: 8px;
  border-radius: 24px;
  background: rgba(0, 112, 245, 0.08);
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.company_claimed span {
  color: #0055ba;
  font-size: 16px;
  line-height: 140%;
}

.starWrapper {
  margin-top: -10px;
  margin-right: 8px;
}
.review_content {
  width: 100%;
}
.job_opening_container {
  display: flex;
  padding: 12px;
  flex-direction: column;
  align-items: flex-start;
  gap: 32px;
  align-self: stretch;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  width: 100%;
}
.job_card_container {
  display: flex;
  align-items: center;
  gap: 24px;
}
.viewAll_jobs_container {
  display: flex;
  align-items: center;
  gap: 8px;
}
.viewAll_jobs_container a {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-bottom: 2px solid #0070f5;
}
.mobile_overall_review {
  display: none;
}
.company_not_claimed {
  text-align: center;
  padding-top: 16px;
  cursor: pointer;
}
.company_not_claimed span {
  color: #0070f5;
  font-size: 16px;
  line-height: 140%;
  cursor: pointer;
}
.company_not_claimed span:hover {
  text-decoration: underline !important;
}
.desktop_overall_review {
  padding-top: 6px;
}

@media screen and (max-width: 991px) {
  .filter_container {
    display: flex;
    padding: 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    align-self: stretch;
    width: 100%;
  }
  .reviews_container_left {
    display: flex;
    flex-direction: column;
    gap: 32px;
  }
  .desktop_overall_review {
    display: none;
  }
  .mobile_overall_review {
    display: block;
  }
  .job_opening_container {
    padding: 24px 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 32px;
    align-self: stretch;
  }
  .job_card_container {
    flex-direction: column;
    gap: 24px;
    align-self: stretch;
  }
  .company_claimed {
    display: none;
  }
  .viewAll_jobs_container {
    width: 100%;
    display: flex;
    justify-content: start;
  }
}
