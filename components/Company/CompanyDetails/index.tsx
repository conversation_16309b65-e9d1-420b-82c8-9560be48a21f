import {useGetCompanyDetails} from '@/modules/companies/query/useGetCompanyDetail';
import {useRouter} from 'next/router';
import React, {useContext} from 'react';
import styles from './style.module.css';
import Image from 'next/image';
import {Button} from 'antd';
import {CompanyDetailTabs} from './CompanyDetailsTabs';
import AuthContext from '@/Context/AuthContext';
import {useFollowCompany} from '@/modules/companies/mutation/useFollowCompany';
import {useDeleteFollowCompany} from '@/modules/companies/mutation/useDeleteFollowCompany';

function CompanyDetailContainer() {
  const router = useRouter();
  const {user} = useContext(AuthContext);
  const companySlug = router.query?.slug as string;
  const {data, isLoading} = useGetCompanyDetails({
    slug: companySlug,
    user_id: user?.id as number,
  });
  const {mutate: follow} = useFollowCompany();
  const {mutate: unFollow} = useDeleteFollowCompany();
  const {company, jobs, recent_reviews} = data ?? {};

  const handleWriteReviews = () => {
    if (!user) {
      router.push('/auth/signup');
      return;
    }
    router.push(`/companies/${router.query?.slug}/write`);
  };

  const handleFollowCompany = () => {
    if (!user) {
      router.push('/auth/signup');
      return;
    }

    company?.id &&
      (data?.follow_flag
        ? unFollow({company_id: company?.id, user_id: user?.id})
        : follow({
            user_id: user?.id,
            company_id: company?.id,
          }));
  };

  return (
    <div className={`container ${styles.review_section}`}>
      {company?.background_banner_image && (
        <div className={styles.banner_container}>
          <img src={company?.background_banner_image} fill alt="company banner" unoptimized />
        </div>
      )}
      <div className={styles.company_header}>
        <div className={styles.header_left_container}>
          <div className={styles.image_container}>
            {company?.company_logo && (
              <img src={company?.company_logo} alt={company?.company_name} height={120} width={120} unoptimized />
            )}
          </div>
          <div className={styles.company_content}>
            <h4>{company?.company_name}</h4>
            {company?.company_location && (
              <div className={styles.company_location}>
                <img src={'/icons/location-dot.svg'} alt="location" height={16} width={16} />
                <span>
                  {company?.company_location}
                  {company?.country?.country_name ? ',' + company?.country?.country_name : ''}
                </span>
              </div>
            )}
            {company?.company_website && (
              <div className={styles.company_portfolio}>
                <img src={'/icons/portfolio.svg'} alt="website" height={16} width={16} />
                <a href={company?.company_website} target="_blank" rel="noreferrer nofollow">
                  {company?.company_website}
                </a>
              </div>
            )}
            <div className={styles.social_container}>
              {company?.twitter_link && (
                <a href={company?.twitter_link} target="_blank" rel="noreferrer">
                  <img src={'/icons/prime_twitter.svg'} alt="twitter" height={24} width={24} />
                </a>
              )}
              {company?.facebook_link && (
                <a href={company?.facebook_link} target="_blank" rel="noreferrer">
                  <img src={'/icons/prime_facebook.svg'} alt="facebook" height={24} width={24} />
                </a>
              )}
              {company?.linkedin_link && (
                <a href={company?.linkedin_link} target="_blank" rel="noreferrer">
                  <img src={'/icons/prime_linkedin.svg'} alt="linkedin" height={24} width={24} />
                </a>
              )}
            </div>
          </div>
        </div>
        <div className={styles.action_container}>
          <Button className={styles.follow_button} onClick={handleFollowCompany}>
            {data?.follow_flag ? 'Unfollow' : 'Follow'}
          </Button>
          <Button className={styles.review_button} type="primary" onClick={handleWriteReviews}>
            Write A Review
          </Button>
        </div>
      </div>
      {data ? (
        <div className={styles.tab_container}>
          <CompanyDetailTabs companyDetails={data} />
        </div>
      ) : null}
    </div>
  );
}

export default CompanyDetailContainer;
