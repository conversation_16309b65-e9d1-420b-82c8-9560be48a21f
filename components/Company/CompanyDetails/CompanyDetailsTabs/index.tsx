import {Tabs} from 'antd';
import {usePathname} from 'next/navigation';
import {useRouter} from 'next/router';
import {useContext, useEffect, useState} from 'react';
import styles from './style.module.css';
import {CompanyDetail} from '@/modules/companies/query/useGetCompanyDetail';
import {CompanyOverviews} from '../CompanyOverView';
import {CompanyReviews} from '../CompanyReviews';
import {CompanyJobs} from '../CompanyJobs';
import {useGetSingleCompanyReviews} from '@/modules/companies/query/useGetSingleCompanyReview';
import AuthContext from '@/Context/AuthContext';

interface CompanyDetailTabsProps {
  companyDetails: CompanyDetail;
}

export const CompanyDetailTabs = ({companyDetails}: CompanyDetailTabsProps) => {
  const router = useRouter();
  const pathname = usePathname();
  const {user} = useContext(AuthContext);
  const companySlug = router.query?.slug as string;
  const {data: reviewDetails} = useGetSingleCompanyReviews({
    id: Number(router?.query?.slug),
    user_id: user?.id,
  });

  const currentPage =
    pathname === `/companies/${decodeURIComponent(companySlug as string)}` ? 'overview' : pathname?.split('/').pop();
  const [selectedTabValue, setSelectedTabValue] = useState(currentPage);

  const handleNavigateTab = (key?: string) => {
    if (key) {
      setSelectedTabValue(key);
      router.push(`/companies/${companySlug}/${key}`);
      return;
    }
    setSelectedTabValue('overview');
    router.push(`/companies/${companySlug}`);
  };

  useEffect(() => {
    if (currentPage) {
      setSelectedTabValue(currentPage);
    }
  }, [currentPage]);

  const items = [
    {
      key: 'overview',
      label: 'Overview',
      onClick: () => handleNavigateTab(),
      children: <CompanyOverviews companyDetails={companyDetails} />,
    },
    {
      key: 'reviews',
      label: 'Reviews',
      onClick: () => handleNavigateTab('reviews'),
      children: reviewDetails && <CompanyReviews companyDetail={companyDetails} />,
      number: reviewDetails?.reviews?.length,
    },
    {
      key: 'jobs',
      label: 'Jobs',
      onClick: () => handleNavigateTab('jobs'),
      children: <CompanyJobs companyDetails={companyDetails} />,
      number: companyDetails?.jobs?.length,
    },
  ];

  return (
    <>
      <Tabs
        defaultActiveKey={selectedTabValue}
        items={items.map(({key, label, onClick, children, number}) => ({
          key,
          label: (
            <div className={styles.tab_label} onClick={onClick}>
              <span style={{cursor: 'pointer'}}>{label}</span>
              {number !== null && number !== undefined && (
                <span className={`${styles.number} ${selectedTabValue === key ? styles.activeNumber : ''}`}>
                  {number}
                </span>
              )}
            </div>
          ),
          children: children,
        }))}
        key={selectedTabValue}
        style={{
          width: '100%',
        }}
        className={'company-detail-tab'}
      />
    </>
  );
};
