.jobs-container {
  display: grid;
  align-items: start;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 12px;
}
.job-listing {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.joblist-cards {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.no_job_found {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}
.job-search-top {
  border-radius: 20px;
  margin: 0 auto;
  padding: 0 12px 20px 12px;

  .fa-solid {
    font-size: 23px;
    color: var(--primary-color);
  }
  .form-container {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  .form-input-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    width: 100%;
  }

  .form-input-container-city {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
  }

  .field-container {
    display: flex;
    align-items: center;
    padding: 0 5px 0 15px;
    overflow: hidden;
    border: 1px solid #b6c8e2;
    border-radius: 8px;
    background: #ffffff;
    height: 48px;
  }
  .job-search-cta {
    background-color: #0055ba;
    color: white;
    border-radius: 8px;
    height: 47px;
    display: flex;
    width: 140px;
    padding: 14px 24px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 500;
    line-height: 120%;
  }
}
.job_label {
  color: var(--Grayscale-08, #2c2c2c);
  font-size: 23px;
  font-weight: 500;
  line-height: 120%;
  padding: 12px 0 32px 0;
}

@media screen and (max-width: 991px) {
  .jobs-container {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .jobs-container {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .form-container {
    flex-direction: column;
  }
  .form-input-container {
    grid-template-columns: repeat(1, 1fr) !important;
  }
  .field-container {
    width: 100%;
    grid-column: span 1 / span 1 !important;
  }
  .job-search-cta {
    width: 100% !important;
  }
  .form-submit-container {
    width: 100%;
  }
  .job-search-top {
    padding: 0;
    padding-bottom: 32px;
  }
  .jobs-container {
    padding: 0;
  }
}
