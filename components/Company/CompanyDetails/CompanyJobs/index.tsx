import useWindowDimensions from '@/helpers/useWindowDimensions';
import styles from './style.module.css';
import {Country, Job, Sector} from '@/lib/types';
import {JobListCard} from '@/components/Jobs/JobListCard';
import {Button, Form, Pagination} from 'antd';
import {JobDetailCard} from '@/components/Jobs/JobDetailCard';
import {useContext, useEffect, useMemo, useState} from 'react';
import {JobDetailSkeleton} from '@/components/Jobs/JobDetailSkeleton';
import Image from 'next/image';
import {Select} from 'antd/lib';
import {useForm} from 'antd/lib/form/Form';
import {useRouter} from 'next/router';
import axios from 'axios';
import {getCountries, getSectorsList} from '@/lib/ApiAdapter';
import ErrorHandler from '@/lib/ErrorHandler';
import {CompanyDetail} from '@/modules/companies/query/useGetCompanyDetail';
import {useGetCompanyJobs} from '@/modules/companies/query/useGetCompanyJobs';
import AuthContext from '@/Context/AuthContext';

interface CompanyJobsProps {
  companyDetails: CompanyDetail;
}

export const CompanyJobs = ({companyDetails}: CompanyJobsProps) => {
  const windowDimensions: any = useWindowDimensions();
  const {user} = useContext(AuthContext);

  const [form] = useForm();
  const [countries, setCountries] = useState<Country[]>();
  const [sectors, setSectors] = useState<[]>();

  const router = useRouter();

  const routerKeyWord = useMemo(() => {
    if (router.query.hasOwnProperty('keywords')) {
      return router.query.keywords;
    } else {
      return null;
    }
  }, [router]);

  const {data: jobs} = useGetCompanyJobs({
    params: {
      company_id: companyDetails?.company?.id,
      location: router.query?.country ? Number(router.query?.country as string) : undefined,
      sector: router.query?.keywords ? Number(router.query?.keywords as string) : undefined,
    },
  });

  const [selectedJob, setSelectedJob] = useState<Job | null>(jobs?.[0] ?? null);

  const submitSearchForm = (values: any) => {
    const filteredOptions = Object.keys(values)
      .filter(function (k) {
        return values[k] != null;
      })
      .reduce(function (acc: any, k) {
        acc[k] = values[k];
        return acc;
      }, {});
    router
      .push(
        {
          pathname: `/companies/${router.query?.slug}/jobs`,
          query: filteredOptions,
        },
        undefined,
        {shallow: false},
      )
      .then();
  };

  useEffect(() => {
    const cancelTokenSource = axios.CancelToken.source();
    getSectorsList()
      .then((res: any) => {
        setSectors(res);
      })
      .catch(error => {});
    getCountries(undefined, undefined, cancelTokenSource)
      .then(res => {
        setCountries(res);
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });

    return cancelTokenSource.cancel;
  }, []);

  useEffect(() => {
    if (router.query.job) {
      const selectedJob = jobs?.find(job => job.job_slug === router.query.job);
      if (selectedJob) {
        setSelectedJob(selectedJob);
      }
    }
  }, [router.query.job]);
  return (
    <>
      <h4 className={styles.job_label}>Job openings at {companyDetails?.company?.company_name} </h4>
      <div className={styles['job-search-top']}>
        <Form
          form={form}
          onFinish={submitSearchForm}
          size={'large'}
          initialValues={router.query}
          className={styles['form-container']}>
          <div className={`${styles['form-input-container']}`}>
            <div
              className={styles['field-container']}
              style={{
                gridColumn: 'span 2 / span 2',
              }}>
              <img src="/icons/search.svg" alt="search" width={21} height={21} />
              <Form.Item name={'keywords'} noStyle>
                <Select
                  allowClear
                  bordered={false}
                  style={{width: '100%'}}
                  showSearch
                  optionFilterProp={'label'}
                  placeholder={'Job Title, Keyword, Company or Phrase'}
                  options={sectors?.map((sector: Sector) => {
                    return {value: sector.id, label: sector.sector_name};
                  })}
                  value={routerKeyWord}
                />
              </Form.Item>
            </div>
            <div className={styles['field-container']}>
              <img src="/icons/location.svg" alt="location" width={21} height={26} />
              <Form.Item name={'country'} noStyle>
                <Select
                  allowClear
                  bordered={false}
                  style={{width: '100%'}}
                  showSearch
                  optionFilterProp={'label'}
                  placeholder={'Select country'}
                  options={countries?.map((country: Country) => {
                    return {value: `${country.id}`, label: country.country_name};
                  })}
                />
              </Form.Item>
            </div>
          </div>
          <div className={styles['form-submit-container']}>
            <Button htmlType={'submit'} type={'primary'} block className={styles['job-search-cta']}>
              Find Jobs
            </Button>
          </div>
        </Form>
      </div>
      <section className={`${styles['jobs-container']} container`}>
        {(windowDimensions.width >= 991 || (windowDimensions.width < 991 && !selectedJob)) && (
          <>
            <div className={styles['job-listing']}>
              <div className={styles['joblist-cards']}>
                {jobs &&
                  jobs?.length > 0 &&
                  jobs.map((job: Job, index: number) => {
                    return (
                      <JobListCard
                        index={index}
                        job={job}
                        key={index}
                        selectedJob={selectedJob}
                        setSelectedJob={setSelectedJob}
                      />
                    );
                  })}
              </div>
            </div>
          </>
        )}
        {jobs?.length === 0 && <div className={styles.no_job_found}>No Jobs Found</div>}

        {selectedJob && <JobDetailCard job={selectedJob} setSelectedJob={setSelectedJob} />}
      </section>
    </>
  );
};
