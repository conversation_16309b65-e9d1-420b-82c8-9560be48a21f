import React, {useContext} from 'react';
import styles from './style.module.css';
import {Button} from 'antd';
import {useRouter} from 'next/router';
import AuthContext from '@/Context/AuthContext';
import {ClaimStatus, useClaimCompany} from '@/modules/companies/mutation/useClaimCompany';
import {Company} from '@/lib/types';

interface CompanyClaimProps {
  company: Company;
}

const CompanyClaim = ({company}: CompanyClaimProps) => {
  const {mutate: claimCompany} = useClaimCompany();
  const router = useRouter();


  const {user} = useContext(AuthContext);

  const handleClaimCompany = () => {
    user?.id &&
      claimCompany(
        {
          company_id: Number(router.query?.slug as string),
          status: ClaimStatus.requested,
          user_id: user?.id,
        },
        {
          onSuccess: data => {
            if (data?.status === ClaimStatus.requested) {
              router.push(`/companies/${router.query?.slug}/claim-thanks`);
            }
          },
        },
      );
  };

  return (
    <div className={styles.company_claim_container}>
      <div className={styles.company_claim_top_container}>
        <h2>Are you a business owner?</h2>
        <h6>With an company account, you can:</h6>
        <ul>
          <li>Attract top talent by showcasing your company's culture, mission, and values.</li>
          <li>Easily advertise your available positions to a targeted audience of job seekers.</li>
          <li>Update company information, including your logo, banner, and website link.</li>
          <li>Directly interact with potential candidates through messaging and interviews.</li>
          <li>Increase your company's online presence and credibility.</li>
        </ul>
      </div>
      <div className={styles.divider_line}></div>
      <div className={styles.company_claim_bottom_container}>
        <h3>Claim {company?.company_name} Company Page</h3>
        <div className={styles.button_container}>
          <Button type="primary" onClick={handleClaimCompany}>
            Claim
          </Button>
          <span>Cancel</span>
        </div>
        <p>
          By claiming this company profile, you confirm your authority to represent this company and agree to abide by
          Talent Point's <a href="">Terms of Service</a>, and <a href="">Privacy Policy</a>.
        </p>
      </div>
    </div>
  );
};

export default CompanyClaim;
