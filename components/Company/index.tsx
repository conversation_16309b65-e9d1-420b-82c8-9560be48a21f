import Image from 'next/image';
import {But<PERSON>, Select} from 'antd';
import styles from './style.module.css';
import Link from 'next/link';
import {useEffect, useRef, useState} from 'react';
import {useRouter} from 'next/router';
import {PopularCompany} from './PopularCompany';
import {SearchedCompany} from './SearchedCompany';
import {useGetPopularCompanies} from '@/modules/companies/query/useGetPopularCompanies';
import {SearchedCompanies, useGetSearchedCompanies} from '@/modules/companies/mutation/useGetSearchedCompanies';
import {useDebounce} from '@/hooks/useDebounceCallback';
import {EmployeeStartCollection} from './RatingEmployees';

export interface PopularCompany {
  id: number;
  name: string;
  sector: string;
  location: string;
  logo: string;
  total_reviews: number;
  average_rating: number;
}

export interface PopularCompanies {
  company_id: number;
  view_count: number;
  company: PopularCompany;
}

interface CompanyContainerProps {
  variant: 'popular' | 'searched';
}

export const CompaniesContainer = ({variant}: CompanyContainerProps) => {
  const {data: popularCompaniesData} = useGetPopularCompanies();
  const router = useRouter();
  const [searchInput, setSearchInput] = useState(router?.query?.search);
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const [selectedOption, setSelectedOption] = useState<string | undefined>(undefined);

  const [searchTerm, setSearchTerm] = useState<string>();

  const {isLoading: searchedOptionLoading, data: searchedCompaniesOption} = useGetSearchedCompanies({
    keyword: searchTerm,
    type: 'suggestion',
  });

  const {isLoading: searchedListLoading, data: searchedCompaniesList} = useGetSearchedCompanies({
    keyword: router?.query?.search as string,
    type: 'view',
  });
  const handleSearch = useDebounce((value: string) => {
    setSearchTerm(value);
  }, 500);

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    handleSearch(value);
    setSearchInput(value);
    setDropdownVisible(true);
  };

  const handleSelectCompany = (company: SearchedCompanies | string) => {
    const companyName = typeof company === 'string' ? company : company?.company_name;
    setSearchInput(companyName);
    setSelectedOption(companyName);
    setDropdownVisible(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (inputRef.current && !inputRef.current.contains(event.target as Node)) {
        setDropdownVisible(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleFindCompanies = (e: any) => {
    e.preventDefault();
    router.push(`/companies/list?search=${searchInput}`);
    setDropdownVisible(false);
  };

  return (
    <div className={`container ${styles.company_container}`}>
      {variant === 'popular' && (
        <>
          {' '}
          <div className={styles.ellipse_bg_container}>
            <img
              src="/images/company/ellipse-bg.svg"
              alt="ellipse bg"
              width={500}
              height={280}
              className={styles.ellipse_bg}
            />
          </div>
          <div className={styles.ellipse_bg_mobile_container}>
            <img
              src="/images/company/elipse_bg_mobile.png"
              alt="employer"
              width={500}
              height={280}
              className={styles.ellipse_bg}
            />
          </div>
        </>
      )}
      <div
        className={`${styles.companies_top_container} ${
          searchedCompaniesList && searchedCompaniesList?.length > 0 && styles.search_top_container
        }`}>
        <h3>Explore top workplaces</h3>
        <p>Search & review thousands of companies listed in UAE & Gulf region.</p>
        <div className={styles.search_container} ref={inputRef}>
          <form onSubmit={handleFindCompanies} className={styles['field-container']} style={{position: 'relative'}}>
            <img src="/icons/search.svg" alt="search" width={21} height={21} />
            <input
              type="text"
              value={searchInput}
              placeholder="Search for companies"
              onChange={handleSearchInputChange}
              onFocus={() => {
                setDropdownVisible(true);
              }}
              className={styles.custom_input}
            />
            {dropdownVisible && searchedCompaniesOption !== undefined && (
              <div className={styles.dropdown}>
                {searchedOptionLoading ? (
                  <div className={styles.loading_container}>Loading...</div>
                ) : (searchedCompaniesOption?.length ?? 0) > 0 ? (
                  (searchedCompaniesOption as string[])?.map((company, index) => (
                    <div
                      key={index}
                      className={styles.dropdown_item}
                      onClick={() => handleSelectCompany(company)}
                      style={{cursor: 'pointer', padding: '8px 12px'}}>
                      {company}
                    </div>
                  ))
                ) : (
                  <div className={styles.loading_container}>No Data Found</div>
                )}
              </div>
            )}
          </form>
          <Button htmlType="button" type="primary" className={styles['job-search-cta']} onClick={handleFindCompanies}>
            Find Companies
          </Button>
        </div>
        {popularCompaniesData && popularCompaniesData?.length > 0 && (
          <Link href="/salaries" className={styles.salary_text}>
            Want to make informed career decisions? Search for salaries here.
          </Link>
        )}
        {variant === 'searched' && (
          <div className={styles.searched_company_text_container}>
            <h5>Search results for {router?.query?.search}</h5>
            <p>Displaying companies with reviews and active job listings</p>
          </div>
        )}
      </div>
      <div
        className={`${
          !!(popularCompaniesData && popularCompaniesData?.length > 0) && styles.popularCompanies_container
        } ${searchedCompaniesList && searchedCompaniesList?.length > 0 ? styles.searched_company_container : ''}`}>
        {popularCompaniesData && popularCompaniesData?.length > 0 && variant === 'popular' && (
          <>
            <h4
              style={{
                paddingBottom: '24px',
              }}>
              Popular Companies
            </h4>
            <div className={styles.companies_container}>
              {popularCompaniesData.slice(0, 9).map((company, index) => (
                <>
                  <PopularCompany company={company} key={index} />
                  {index !== 8 && <div className={styles.divider}></div>}
                </>
              ))}
            </div>
          </>
        )}
        {!!searchedCompaniesList && searchedCompaniesList?.length > 0 && (
          <>
            <div className={styles.search_company_container}>
              {searchedCompaniesList.map((company, index) => (
                <>
                  {' '}
                  <SearchedCompany company={company as SearchedCompanies} key={index} />
                  {index !== searchedCompaniesList?.length - 1 && (
                    <div
                      className={`${styles.divider} ${
                        searchedCompaniesList?.length > 0 && styles.search_company_divider
                      }`}></div>
                  )}
                </>
              ))}
            </div>
          </>
        )}
        {((searchedCompaniesList && searchedCompaniesList?.length === 0) || !searchedCompaniesList) &&
          variant === 'searched' && <div className={styles.no_data_container}>No Data Found</div>}
      </div>

      <EmployeeStartCollection />
    </div>
  );
};
