import Image from 'next/image';
import styles from './style.module.css';

interface StatusChipProps {
  variant: 'free' | 'pro' | 'unclaimed' | 'requested';
}

export const StatusChip = ({variant}: StatusChipProps) => {
  return (
    <div className={styles.status_chip_container}>
      {variant === 'pro' ? (
        <img src={'/images/employer/pro-card.png'} width={59} height={25} alt="Pro Card" />
      ) : variant === 'free' ? (
        <img src={'/images/employer/free-card.png'} width={59} height={25} alt="Free Card" />
      ) : variant === 'unclaimed' ? (
        <div className={styles.unclaimed}>Unclaimed</div>
      ) : variant === 'requested' ? (
        <div className={styles.requested}>Requested</div>
      ) : null}
    </div>
  );
};
