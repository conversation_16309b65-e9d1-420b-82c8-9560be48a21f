.field-container {
  display: flex;
  align-items: center;
  padding: 0 5px 0 15px;
  overflow: visible;
  border: 1px solid #cfe5ff;
  border-radius: 8px;
  background: #ffffff;
  height: 44px;
  max-width: 640px;
  width: 100%;
  position: relative;
}
.job-search-cta {
  border-radius: 8px;
  background: #0055ba;
  display: flex;
  padding: 13px 24px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  line-height: 110%;
  height: 44px;
}
.search_container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding-bottom: 24px;
  width: 100%;
}
.companies_top_container {
  display: flex;
  padding: 40px 72px;
  flex-direction: column;
  justify-content: flex-end;
}
.search_top_container {
  padding: 24px 72px !important;
}
.companies_top_container h3 {
  color: #191919;
  font-size: 37px;
  font-weight: 700;
  line-height: 120%;
  padding-bottom: 8px;
}

.companies_top_container p {
  color: #2c2c2c;
  font-size: 18px;
  font-weight: 400;
  line-height: 160%;
  padding-bottom: 16px;
}

.companies_top_container a {
  text-decoration: underline !important;
}
.popularCompanies_container {
  display: flex;
  padding: 40px 72px;
  flex-direction: column;
}

.popularCompanies_container h4 {
  color: #2c2c2c;
  font-size: 26px;
  font-weight: 500;
  line-height: 120%;
}
.companies_container {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 48px 24px;
}

.company_image img {
  border-radius: 8px;
}

.company_info h5 {
  color: #2c2c2c;
  font-size: 22px;
  font-weight: 500;
  line-height: 120%;
}

.dropdown {
  position: absolute;
  top: 48px;
  left: 0;
  right: 0;
  max-height: 180px;
  overflow-y: auto;
  background: #ffffff;
  border: 1px solid #ddd;
  border-radius: 8px;
  z-index: 1000;
  width: 100%;
  box-shadow:
    0 2px 5px rgba(21, 21, 21, 0.1),
    0 4px 8px rgba(21, 21, 21, 0.15);
}

.dropdown_item {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.dropdown_item:hover {
  background-color: #f0f0f0;
}

.custom_input {
  width: 100%;
  border: none;
  outline: none;
  padding-left: 15px;
  height: 100%;
  background-color: transparent;
  color: #191919;
}

.custom_input::placeholder {
  color: #5c5c5c;
  font-size: 16px;
  font-weight: 400;
}
.searched_company_container {
  display: flex;
  padding: 24px 72px 40px 72px !important ;
  flex-direction: column;
  gap: 24px;
}
.search_company_container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 24px;
  align-self: stretch;
}
.ellipse_bg_container {
  width: 522.717px;
  height: 283px;
  position: absolute;
  right: 0;
  top: 0;
}
.company_container {
  position: relative;
}
.dropdown_item {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  transition: background-color 0.3s;
}

.dropdown_item:hover {
  background-color: #ebf4ff;
}

.dropdown_item.active {
  background-color: #ebf4ff;
  font-weight: 500;
}
.loading_container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 160px;
}
.divider {
  display: none;
}
.searched_company_text_container h5 {
  color: #191919;
  font-size: 18px;
  font-weight: 600;
  line-height: 160%;
}
.searched_company_text_container p {
  color: #4d4d4d;
  font-size: 18px;
  font-weight: 400;
  line-height: 160%;
}
.search_company_divider {
  display: flex !important ;
  height: 1px;
  background: #eee;
  width: 100%;
}
.ellipse_bg_mobile_container {
  display: none;
}
.no_data_container {
  display: flex;
  justify-content: center;
  min-height: 200px;
}
.salary_text {
  color: #0070f5;
  font-size: 18px;
  font-weight: 600;
  line-height: 160%;
  text-decoration-line: underline;
  text-decoration-style: solid;
  text-decoration-skip-ink: none;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
}

@media screen and (max-width: 1400px) {
  .companies_container {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media screen and (max-width: 991px) {
  .companies_container {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .companies_top_container {
    padding: 24px 0;
  }
  .companies_top_container a {
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    line-height: 160%;
    text-decoration-line: underline;
    text-decoration-style: solid;
    text-decoration-skip-ink: none;
    text-decoration-thickness: auto;
    text-underline-offset: auto;
    text-underline-position: from-font;
  }
  .popularCompanies_container {
    padding: 40px 0;
  }

  .search_container {
    flex-direction: column;
    width: 100%;
    gap: 16px;
  }
  .field-container {
    padding: 8px;
    height: 56px;
  }
  .form-submit-container {
    width: 100%;
  }

  .job-search-cta {
    width: 100%;
  }
  .ellipse_bg_container {
    display: none;
  }
  .ellipse_bg_mobile_container {
    display: block;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
  }

  .searched_company_container {
    padding: 24px 0 40px 0 !important;
  }
  .divider {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 12px;
    align-self: stretch;
    height: 1px;
    background: #eee;
    margin: 12px 0;
  }
  .companies_container {
    gap: 0;
  }
  .search_top_container {
    padding: 24px 0 !important;
  }
}
