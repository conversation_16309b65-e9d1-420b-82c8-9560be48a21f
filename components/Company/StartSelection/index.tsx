import React, {useMemo, useState} from 'react';
import CustomStar from './EmptyStar';

interface StarSelectionProps {
  value: number;
  variant?: 'selectable' | 'notSelectable';
  onChange?: (value: number) => void;
  type?: 'single' | 'multiple';
  size?: 'small' | 'medium' | 'large';
  startType?: 'blue' | 'green';
}

export const StarSelection: React.FC<StarSelectionProps> = ({
  value,
  variant = 'notSelectable',
  onChange,
  type = 'multiple',
  size = 'small',
  startType,
}) => {
  const [hoveredValue, setHoveredValue] = useState<number | null>(null);

  const handleMouseMove = (e: React.MouseEvent, index: number) => {
    if (variant === 'selectable') {
      setHoveredValue(index + 1);
    }
  };

  const handleTouchMove = (e: React.TouchEvent, index: number) => {
    if (variant === 'selectable') {
      setHoveredValue(index + 1);
    }
  };

  const handleMouseLeave = () => {
    setHoveredValue(null);
  };

  const handleClick = (index: number) => {
    if (variant === 'selectable' && onChange) {
      onChange(index + 1);
    }
  };

  const handleTouchEnd = (index: number) => {
    handleClick(index);
  };

  const displayValue = hoveredValue !== null ? hoveredValue : value;

  const starSize = size === 'small' ? 16 : size === 'medium' ? 24 : 38;

  const color = useMemo(() => {
    if (startType) {
      if (startType === 'blue') {
        return '#0055BA';
      }
      return '0EB1D2';
    } else {
      if (variant === 'selectable') {
        return '#0055BA';
      }
      return '#0EB1D2';
    }
  }, []);

  return (
    <div
      style={{
        display: 'flex',
        cursor: variant === 'selectable' ? 'pointer' : 'default',
      }}
      onMouseLeave={handleMouseLeave}>
      {(type === 'multiple' ? [0, 1, 2, 3, 4] : [0]).map(index => {
        const fillPercentage = Math.min(Math.max(displayValue - index, 0), 1) * 100;
        return (
          <div
            key={index}
            onMouseMove={e => handleMouseMove(e, index)}
            onClick={() => handleClick(index)}
            onTouchMove={e => handleTouchMove(e, index)}
            onTouchEnd={() => handleTouchEnd(index)}
            style={{position: 'relative', width: starSize, height: starSize, marginTop: '-8px'}}>
            <CustomStar isFilled={false} color={color} variant={variant} size={starSize} startType={startType} />
            {fillPercentage > 0 && (
              <div
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  overflow: 'hidden',
                  width: `${fillPercentage}%`,
                }}>
                <CustomStar isFilled color={color} variant={variant} size={starSize} startType={startType} />
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};
