import React, {useState, useEffect, useContext} from 'react';
import 'react-toastify/dist/ReactToastify.css';
import swal from 'sweetalert';
import {
  updatePortfolio,
  getportfolio,
  deletePortfolio,
  addPortfolio,
  getSinglePortfilio,
} from '../../../lib/frontendapi';
import AuthContext from '@/Context/AuthContext';
import {notification} from 'antd';
import ErrorHandler from '@/lib/ErrorHandler';
import moment from 'moment';

export default function Portfolio() {
  const {user} = useContext(AuthContext);
  const [portfolioList, SetportfolioList] = useState([]);
  const [editportfolio, setEditportfolio] = useState(0);
  const [portfolio, Setportfolio] = useState({
    title: '',
    portfolio_link: '',
    start_date: '',
    end_date: '',
    present: '',
    description: '',
  });
  const [editPortfolioList, SeteditPortfolioList]: any = useState([]);
  const [showFormWork, setshowFormWork] = useState(false);

  const [errors, setErrors] = useState<any>({});
  const [validationErrors, setValidationErrors] = useState<any>({
    title: '',
    portfolio_link: '',
    start_date: '',
    end_date: '',
    description: '',
  });

  useEffect(() => {
    getportfolio(user?.id)
      .then(res => {
        SetportfolioList(res.data);
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  }, [user]);

  const handleClickWork = () => {
    setshowFormWork(prevState => !prevState);
  };

  const handleportfolioCancel = () => {
    setshowFormWork(false);
  };

  const handlePortfiolioClick = (index: number) => {
    if (editportfolio === index) {
      setEditportfolio(0);
    } else {
      setEditportfolio(index);
    }

    getSinglePortfilio(index)
      .then(res => {
        SeteditPortfolioList(res.data);
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  };

  const updateportfolioForm = (event: any, id: any) => {
    event.preventDefault();

    const validateFields = () => {
      const requiredFields = ['title', 'portfolio_link', 'start_date', 'description'];
      let isValid = true;
      const newValidationErrors: any = {};

      for (const field of requiredFields) {
        if (!editPortfolioList[field]) {
          newValidationErrors[field] = 'This field is required';
          isValid = false;
        } else {
          newValidationErrors[field] = '';
          newValidationErrors['id'] = id;
        }
      }
      setValidationErrors(newValidationErrors);
      return isValid;
    };

    if (validateFields()) {
      const userId = user?.id;
      updatePortfolio(editPortfolioList)
        .then(res => {
          if (res.status) {
            notification.success({
              message: res.message,
            });
            getportfolio(user?.id).then(res => {
              SetportfolioList(res.data);
              ErrorHandler.showNotification(res.data);
            });
            setEditportfolio(0);
          } else {
            notification.error({
              message: res.message,
            });
          }
        })
        .catch(err => {
          ErrorHandler.showNotification(err);
        });
    }
  };

  const Portfolioupdate = (event: any) => {
    const {name, type, checked, value} = event.target;

    const newValue = type === 'checkbox' ? (checked ? '1' : '0') : value;

    if (name === 'present') {
      SeteditPortfolioList((prevState: any) => ({
        ...prevState,
        end_date: checked ? '' : prevState.end_date,
        [name]: newValue,
      }));
    } else {
      SeteditPortfolioList((prevState: any) => ({
        ...prevState,
        [name]: newValue,
      }));
    }
  };

  const handleportfolioDelete = (e: any, id: any) => {
    e.preventDefault();
    swal({
      title: 'Are you sure?',
      text: 'You want to delete your Portfolio',
      icon: 'warning',
      dangerMode: true,
      buttons: ['Cancel', 'Yes, I am sure!'],
    }).then(willDelete => {
      if (willDelete) {
        deletePortfolio(id)
          .then(res => {
            if (res.status === true) {
              swal('Your Portfolio has been deleted!', {
                icon: 'success',
              });
              getportfolio(user?.id).then(res => {
                SetportfolioList(res.data);
              });
            } else {
              notification.info({
                message: res.message,
              });
            }
          })
          .catch(err => {
            notification.error({
              message: err.message,
            });
          });
      }
    });
  };

  const onportfolioForm = (event: any) => {
    event.preventDefault();
    if (validateportfolioForm()) {
      const userId = user?.id;
      const data = {
        user_id: userId,
        title: portfolio.title,
        portfolio_link: portfolio.portfolio_link,
        start_date: portfolio.start_date,
        end_date: portfolio.end_date,
        present: portfolio.present,
        description: portfolio.description,
      };
      addPortfolio(data)
        .then(res => {
          if (res.status) {
            notification.success({
              message: res.message,
            });
            Setportfolio({
              title: '',
              portfolio_link: '',
              start_date: '',
              end_date: '',
              present: '',
              description: '',
            });
            getportfolio(user?.id).then(res => {
              SetportfolioList(res.data);
              ErrorHandler.showNotification(res.data);
            });
            setshowFormWork(false);
          } else {
            notification.error({
              message: res.message,
            });
          }
        })
        .catch(err => {
          ErrorHandler.showNotification(err);
        });
    }
  };

  const validateportfolioForm = () => {
    const newErrors: any = {};
    let isValid = true;

    if (portfolio.title.trim() === '') {
      newErrors.title = 'Title is required';
      isValid = false;
    }
    if (portfolio.portfolio_link.trim() === '') {
      newErrors.portfolio_link = 'Degree is required';
      isValid = false;
    }
    if (portfolio.start_date.trim() === '') {
      newErrors.start_date = 'Start Date is required';
      isValid = false;
    }
    if (!portfolio.present && portfolio.end_date.trim() === '') {
      newErrors.end_date = 'End Date is required';
      isValid = false;
    } else if (!portfolio.present && portfolio.start_date && portfolio.end_date < portfolio.start_date) {
      newErrors.end_date = 'End date cannot be before start date';
      isValid = false;
    }
    if (portfolio.description.trim() === '') {
      newErrors.description = 'description is required';
      isValid = false;
    } else if (portfolio.description.split(' ').length > 250) {
      newErrors.description = 'Description should not exceed 250 words';
      isValid = false;
    }
    setErrors(newErrors);
    return isValid;
  };

  const PortfolioChange = (event: any) => {
    const {name, value, type, checked} = event.target;
    const newValue = type === 'checkbox' ? checked : value;

    if (name === 'present' && checked) {
      Setportfolio(prevState => ({
        ...prevState,
        end_date: '',
        [name]: newValue,
      }));
    } else {
      Setportfolio(prevState => ({
        ...prevState,
        [name]: newValue,
      }));
    }
  };
  return (
    <>
      <div className="cdata-management m-p-10 mt-2">
        <div className="work-experience-fieild m-p-10">
          <div className="row">
            <div className="col-lg-3 col-md-3">
              <div className="left-text-fieild">
                <h3>Portfolio/Projects</h3>
                <p className="c-747474">Showcase your work</p>
              </div>
            </div>
            <div className="col-lg-9 col-md-9">
              {portfolioList.map((portfolio: any, index: any) => (
                <div className="right-text-edit mt-1" key={index}>
                  <div className="row mobile-column-reverse">
                    <div className="col-sm-9">
                      <h6>{portfolio.title}</h6>
                      <p>
                        <strong>{portfolio.portfolio_link}</strong>
                      </p>
                      <p className="date-time">
                        {new Date(portfolio.start_date).toLocaleString('en-US', {
                          month: '2-digit',
                          year: 'numeric',
                        })}{' '}
                        -{' '}
                        {portfolio.present === '1'
                          ? 'Till Now'
                          : new Date(portfolio.end_date).toLocaleString('en-US', {
                              month: '2-digit',
                              year: 'numeric',
                            })}
                      </p>
                    </div>
                    <div className="col-sm-3 text-right">
                      <div className="edit-pi">
                        <i className="fa-solid fa-square-pen" onClick={() => handlePortfiolioClick(portfolio.id)}></i>
                      </div>
                    </div>
                  </div>
                  {editportfolio === portfolio.id && (
                    <form
                      className="form-experience-fieild"
                      onSubmit={e => updateportfolioForm(e, editPortfolioList.id)}>
                      <input type="hidden" name="id" value={editPortfolioList.id} />
                      <div className="form_field_sec">
                        <input
                          type="text"
                          placeholder="Portfolio/Project"
                          name="title"
                          onChange={Portfolioupdate}
                          className="fild-des"
                          value={editPortfolioList.title}
                        />
                        <label>Title*</label>
                      </div>
                      {validationErrors.id == editPortfolioList.id
                        ? validationErrors.title && <p className="error">{validationErrors.title}</p>
                        : null}
                      <div className="form_field_sec">
                        <input
                          type="text"
                          name="portfolio_link"
                          onChange={Portfolioupdate}
                          placeholder="Add your project or portfolio link here"
                          value={editPortfolioList.portfolio_link}
                          className="fild-des"
                        />
                        <label>Link</label>
                      </div>
                      {validationErrors.id == editPortfolioList.id
                        ? validationErrors.portfolio_link && <p className="error">{validationErrors.portfolio_link}</p>
                        : null}

                      <div className="row">
                        <div className="col-sm-6">
                          <div className="form_field_sec">
                            <input
                              type="date"
                              name="start_date"
                              value={editPortfolioList.start_date}
                              onChange={Portfolioupdate}
                              className="fild-des"
                            />
                            <label>Start Date*</label>
                          </div>
                        </div>
                        {validationErrors.id == editPortfolioList.id
                          ? validationErrors.start_date && <p className="error">{validationErrors.start_date}</p>
                          : ''}
                        <div className="col-sm-6">
                          <div className="form_field_sec">
                            <input
                              type="date"
                              name="end_date"
                              onChange={Portfolioupdate}
                              className="fild-des"
                              value={editPortfolioList.end_date || ''}
                              disabled={editPortfolioList.present === '1'}
                            />
                            <label>End Date*</label>
                          </div>
                          <label className="d-flex-form">
                            <input
                              type="checkbox"
                              name="present"
                              onChange={Portfolioupdate}
                              checked={editPortfolioList.present === '1'}
                            />{' '}
                            Present
                          </label>
                        </div>
                        {validationErrors.id == editPortfolioList.id
                          ? validationErrors.end_date && <p className="error">{validationErrors.end_date}</p>
                          : ''}
                      </div>
                      <div className="form_field_sec">
                        <textarea
                          placeholder="Your description goes here..."
                          className="fild-des"
                          name="description"
                          onChange={Portfolioupdate}
                          value={editPortfolioList.description}></textarea>
                        <label>Description*</label>
                      </div>
                      {validationErrors.id == editPortfolioList.id
                        ? validationErrors.description && <p className="error">{validationErrors.description}</p>
                        : ''}
                      <p className="font-12 text-right words">250 words</p>
                      <div className="text-right mt-3">
                        <div className="m-d-flex">
                          <a
                            className="rmewp  m-d-b  m-w-100 mb-2"
                            onClick={e => handleportfolioDelete(e, editPortfolioList.id)}>
                            Remove Portfolio
                          </a>

                          <a className="cancel m-d-b m-w-47" onClick={() => setEditportfolio(0)}>
                            Cancel
                          </a>
                          <button className="save  m-w-47">Save</button>
                        </div>
                      </div>
                    </form>
                  )}
                </div>
              ))}
              <p className="add" onClick={handleClickWork} style={{cursor: 'pointer'}}>
                <i className="fa-solid fa-plus"></i> Add Portfolio/Projects
              </p>
              {showFormWork && (
                <form className="form-experience-fieild" onSubmit={onportfolioForm}>
                  <div className="form_field_sec">
                    <input
                      type="text"
                      placeholder="Portfolio/Project"
                      name="title"
                      value={portfolio.title}
                      onChange={PortfolioChange}
                      className="fild-des"
                    />
                    <label>Title*</label>
                  </div>
                  {errors.title && <p className="error">{errors.title}</p>}
                  <div className="form_field_sec">
                    <input
                      type="text"
                      name="portfolio_link"
                      value={portfolio.portfolio_link}
                      onChange={PortfolioChange}
                      placeholder="Add your project or portfolio link here"
                      className="fild-des"
                    />
                    <label>Link</label>
                  </div>
                  {errors.portfolio_link && <p className="error">{errors.portfolio_link}</p>}

                  <div className="row">
                    <div className="col-sm-6">
                      <div className="form_field_sec">
                        <input
                          type="date"
                          name="start_date"
                          value={portfolio.start_date}
                          onChange={PortfolioChange}
                          className="fild-des"
                          max={moment().format('YYYY-MM-DD')}
                        />
                        <label>Start Date*</label>
                      </div>
                      {errors.start_date && <p className="error">{errors.start_date}</p>}
                    </div>
                    <div className="col-sm-6">
                      <div className="form_field_sec">
                        <input
                          type="date"
                          name="end_date"
                          value={portfolio.end_date}
                          onChange={PortfolioChange}
                          className="fild-des"
                          max={moment().format('YYYY-MM-DD')}
                        />
                        <label>End Date*</label>
                      </div>
                      {errors.end_date && <p className="error">{errors.end_date}</p>}
                      <label className="d-flex-form">
                        <input
                          type="checkbox"
                          name="present"
                          onChange={PortfolioChange}
                          style={{
                            width: 'fit-content',
                          }}
                        />{' '}
                        Present
                      </label>
                    </div>
                  </div>
                  <div className="form_field_sec">
                    <textarea
                      placeholder="Your description goes here..."
                      className="fild-des"
                      name="description"
                      value={portfolio.description}
                      onChange={PortfolioChange}></textarea>
                    <label>Description*</label>
                  </div>
                  {errors.description && <p className="error">{errors.description}</p>}
                  <p className="font-12 text-right words">250 words</p>

                  <div className="text-right mt-3">
                    <a className="cancel" onClick={handleportfolioCancel}>
                      Cancel
                    </a>
                    <button className="save">Save</button>
                  </div>
                </form>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
