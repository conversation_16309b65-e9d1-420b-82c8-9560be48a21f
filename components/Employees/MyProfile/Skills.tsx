import React, {useState, useEffect, useContext, useRef} from 'react';
import {deleteEmployeeSkills, getSingleUserSkill, searchSkills, addEmployeeSkills} from '../../../lib/frontendapi';
import 'react-toastify/dist/ReactToastify.css';
import AuthContext from '@/Context/AuthContext';
import {Dropdown, notification, Select} from 'antd';
import <PERSON>rror<PERSON>and<PERSON> from '@/lib/ErrorHandler';
import {useClickOutside} from '@/hooks/useOutSideClick';
import styles from './Profile.module.css';

export default function Skills() {
  const {user} = useContext(AuthContext);
  const [skill, setSkill] = useState([]);
  const [searchResults, setSearchResults] = useState([]);
  const [search, setSearch] = useState('');
  const [showLine, setshowLine] = useState(false);
  const [selectedSkills, setSelectedSkills] = useState<string[]>([]);
  const [error, setError] = useState('');
  const [showDropdown, setShowDropdown] = useState(searchResults.length > 0);
  const dropdownRef = useRef<any>(null);

  useClickOutside(dropdownRef, [], () => {
    setShowDropdown(false);
  });

  useEffect(() => {
    getSingleUserSkill(user?.id)
      .then(res => {
        if (res.status == true) {
          setSkill(res.data);
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  }, [user]);

  const handleDeleteSkill = async (id: any, skill_id: any) => {
    try {
      const response = await deleteEmployeeSkills(id);
      if (response.status === 'success') {
        setSelectedSkills(selectedSkills.filter(skill => skill !== skill_id));
        getSingleUserSkill(user?.id)
          .then(res => {
            if (res.status == true) {
              setSkill(res.data);
            }
          })
          .catch(err => {
            ErrorHandler.showNotification(err);
          });
      } else {
        notification.info({
          message: response.message,
        });
      }
    } catch (error) {
      ErrorHandler.showNotification(error);
    }
  };

  const handleSearch = async (e: any) => {
    const value = e?.target?.value;
    setSearch(value);
    if (value) {
      searchSkills(value)
        .then(res => {
          const matchingResults = res.data.filter((result: any) =>
            result.skills.toLowerCase().startsWith(value.toLowerCase()),
          );
          setSearchResults(matchingResults);
        })
        .catch(error => {
          setSearchResults([]);
        });
      setshowLine(true);
    } else {
      setSearchResults([]);
      setshowLine(false);
    }
  };

  const handleSkillClick = (e: any, skill: string) => {
    if (!selectedSkills.includes(skill)) {
      setSelectedSkills([...selectedSkills, skill]);
      updateSkillInDatabase(skill);
      setError('');
      setShowDropdown(false);
      setSearch('');
    } else {
      setError('Skill already added');
    }
  };

  const updateSkillInDatabase = (skill: any) => {
    const userId = user?.id;
    const data = {
      user_id: userId,
      skill_ids: skill,
    };

    addEmployeeSkills(data)
      .then(res => {
        if (res.status === 'success') {
          getSingleUserSkill(user?.id)
            .then(res => {
              if (res.status == true) {
                setSkill(res.data);
              }
            })
            .catch(err => {
              ErrorHandler.showNotification(err);
            });
        } else {
          notification.info({
            message: res.message,
          });
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  };

  useEffect(() => {
    if (searchResults.length > 0) {
      setShowDropdown(true);
    } else {
      setShowDropdown(false);
    }
  }, [searchResults]);

  return (
    <>
      <div className="mt-2">
        <div className="work-experience-fieild">
          <div className="row">
            <div className="col-lg-3 col-md-3">
              <div className="left-text-fieild">
                <h3>Your Skills</h3>
                <p className="c-747474">
                  Flaunt your skills to
                  <br /> employers
                </p>
              </div>
            </div>
            <div className="col-lg-9 col-md-9">
              <ul className="skills">
                {skill.map((ski: any, index) => (
                  <li key={index}>
                    <p className="cat">
                      {ski.skills}
                      <span
                        onClick={() => handleDeleteSkill(ski.id, ski.skill_id)}
                        style={{marginLeft: '5px', cursor: 'pointer'}}>
                        x
                      </span>
                    </p>
                  </li>
                ))}
              </ul>
              {error && <p style={{color: 'red'}}>{error}</p>}
              <label>Add Skills</label>

              <div className="search-in w-100 relative">
                <input
                  className="form-control me-2"
                  type="search"
                  placeholder="e.g. React, Node.js, Sass"
                  aria-label="Search"
                  onChange={handleSearch}
                  value={search}
                  onFocus={() => {
                    setShowDropdown(true);
                  }}
                />
                <i className="fa-solid fa-magnifying-glass glass-ser"></i>
              </div>

              {showDropdown && (
                <div className="relative">
                  <ul className={styles.dropdown_menu} ref={dropdownRef}>
                    {searchResults.map((searchResult: any, index) => (
                      <li key={index} onClick={e => handleSkillClick(e, searchResult.id)} style={{cursor: 'pointer'}}>
                        {searchResult.skills}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
