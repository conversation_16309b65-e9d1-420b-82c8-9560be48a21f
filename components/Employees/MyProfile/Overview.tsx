import React, {useState, useEffect, useContext} from 'react';
import {
  getSingleUserDetails,
  getWorkExperienceEmployee,
  getEducationsEmployee,
  getSingleUserSkill,
} from '../../../lib/frontendapi';
import Link from 'next/link';
import AuthContext from '@/Context/AuthContext';
import <PERSON>rrorHandler from '@/lib/ErrorHandler';
import {WorkExperiences, Education, Skill} from '@/lib/types';
import UserProfileImage from '@/components/Common/UserProfileImage';

export default function Overview() {
  const {user} = useContext(AuthContext);
  const [users, setUsers]: any = useState([]);
  const [workExperiance, setWorkExperiance] = useState<WorkExperiences[]>([]);
  const [education, setEducation] = useState<Education[]>([]);
  const [skill, setSkill] = useState<Skill[]>([]);

  useEffect(() => {
    getSingleUserDetails(user?.id)
      .then(res => {
        if (res.status == true) {
          setUsers(res.user);
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });

    getWorkExperienceEmployee(user?.id)
      .then(res => {
        if (res.status == true) {
          setWorkExperiance(res.data);
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });

    getEducationsEmployee(user?.id)
      .then(res => {
        if (res.status == true) {
          setEducation(res.education);
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });

    getSingleUserSkill(user?.id)
      .then(res => {
        if (res.status == true) {
          setSkill(res.data);
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  }, [user]);

  return (
    <>
      <div className="dash-right">
        <h1>
          My <span className="span-color">Profile</span>
        </h1>
        <div className="row m-column-reverse">
          <div className="col-sm-7">
            <ul className="list-loc m-m-0 mt-4">
              <li className="active">
                <Link href="/employees/myprofile">Overview</Link>
              </li>
              <li>
                <Link href="/employees/myprofile/profile">Profile</Link>
              </li>
              <li>
                <Link href="/employees/myprofile/jobpreferences">Preferences</Link>
              </li>
            </ul>
          </div>
          <div className="col-sm-5">
            <ul className="blue-text-line mt-4 text-right">
              <li>
                <Link target="_blank" href={'/candidate-profile/' + users.slug}>
                  View Public Profile
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="data-management p-0">
          <div className="work-experience-fieild">
            <div className="row">
              <div className="col-lg-3 col-md-3">
                <div className="left-text-fieild">
                  <h3>What employers will see</h3>
                  <p className="c-747474">A glimpse into what recruiters look at on your profile</p>
                </div>
              </div>
              <div className="col-lg-9 col-md-9">
                <div className="text-right link-right-icons">
                  <p className="m-text-right mb-2">
                    {users.website_url ? (
                      <Link href={users.website_url}>
                        <i className="fa-solid fa-globe"></i>
                      </Link>
                    ) : null}
                    {users.linkedin_link ? (
                      <Link href={users.linkedin_link}>
                        <i className="fa-brands fa-linkedin"></i>
                      </Link>
                    ) : null}
                    {users.facebook_link ? (
                      <Link href={users.facebook_link}>
                        <i className="fa-brands fa-facebook"></i>
                      </Link>
                    ) : null}
                    {users.twitter_link ? (
                      <Link href={users.twitter_link}>
                        <i className="fa-brands fa-x-twitter"></i>
                      </Link>
                    ) : null}
                    {users.instagram_link ? (
                      <Link href={users.instagram_link}>
                        <i className="fa-brands fa-instagram"></i>
                      </Link>
                    ) : null}
                  </p>
                </div>
                <div className="row">
                  <div className="col-sm-12">
                    <UserProfileImage user={user} showUploadButton={true} className="mxw-unset" />
                  </div>
                  <div className="col-sm-10 col-8">
                    <h4 className="em-name name-user">{users.name}</h4>
                    <h5 className="em-work name-work">
                      {users.current_position} {users.company ? `@${users.company?.company_name}` : ''}
                    </h5>
                    <p className="f-12">
                      {users.country_name ? (
                        <>
                          <i className="fa-solid fa-location-dot"></i> {users.country_name}
                        </>
                      ) : null}
                    </p>
                  </div>
                  <div className="col-12">
                    <p className="f-16 c-4D4D4D">{users.bio}</p>
                  </div>
                </div>
                <br />
                {workExperiance.length > 0 ? <p className="f-12 c-000 mb-0">Work Experience</p> : ''}
                {workExperiance.map((work, index) => (
                  <div key={index}>
                    <p className="f-18 mt-3">{work.title}</p>
                    <p className="f-16 c-0055BA w-600">{work.company}</p>
                    <p className="f-16 c-999999">
                      {new Date(work.start_date).toLocaleString('en-US', {
                        month: '2-digit',
                        year: 'numeric',
                      })}{' '}
                      -{' '}
                      {new Date(work.end_date).toLocaleString('en-US', {
                        month: '2-digit',
                        year: 'numeric',
                      })}
                    </p>
                    <p className="f-16 c-4D4D4D">{work.description}</p>
                  </div>
                ))}

                <br />
                {education.length > 0 ? <p className="f-12 c-000 mb-0">Education</p> : ''}
                {education.map((edu, index) => (
                  <div key={index}>
                    <p className="f-18 mt-4">{edu.education_title}</p>
                    <p className="f-16 c-0055BA w-600 ">{edu.degree}</p>
                    <p className="f-16 c-999999">
                      {new Date(edu.start_date).toLocaleString('en-US', {
                        month: '2-digit',
                        year: 'numeric',
                      })}{' '}
                      -{' '}
                      {new Date(edu.end_date).toLocaleString('en-US', {
                        month: '2-digit',
                        year: 'numeric',
                      })}
                    </p>
                  </div>
                ))}
                <br />
                {skill.length > 0 ? <p className="f-12 c-000 mb-0">Skills</p> : ''}
                <ul className="skills">
                  {skill.map((ski, index) => (
                    <li key={index}>
                      <p className="cat">{ski.skills}</p>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
