import React, {useState, useEffect, useContext} from 'react';
import {
  getEducationsSingleEmployee,
  updateEducation,
  getEducationsEmployee,
  deleteEducation,
  addEducation,
} from '../../../lib/frontendapi';
import 'react-toastify/dist/ReactToastify.css';
import swal from 'sweetalert';
import AuthContext from '@/Context/AuthContext';
import {notification} from 'antd';
import <PERSON>rror<PERSON>andler from '@/lib/ErrorHandler';
import {FaAlignJustify} from 'react-icons/fa6';
import moment from 'moment';

export default function Education() {
  const {user} = useContext(AuthContext);
  const [educationList, SetEducationList] = useState([]);
  const [editIndices, setEditIndices] = useState(0);
  const [editeducationList, SetEditEducationList]: any = useState([]);
  const [education, SetEducation] = useState({
    education_title: '',
    degree: '',
    start_date: '',
    end_date: '',
    currently_study_here: false,
    your_score: '',
    max_score: '',
  });
  const [errors, setErrors] = useState<any>({});
  const [showSection, setShowSection] = useState(false);
  const [validationErrors, setValidationErrors] = useState<any>({
    education_title: '',
    degree: '',
    start_date: '',
    end_date: '',
    currently_study_here: '',
    your_score: '',
    max_score: '',
  });

  useEffect(() => {
    getEducationsSingleEmployee(user?.id)
      .then(res => {
        SetEditEducationList(res.education);
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });

    getEducationsEmployee(user?.id)
      .then(res => {
        SetEducationList(res.education);
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  }, [user]);

  const handleClick = () => {
    setShowSection(!showSection);
  };

  const handleeducationCancel = () => {
    setShowSection(false);
  };

  const handleEditClick = (index: number) => {
    if (editIndices === index) {
      setEditIndices(0);
    } else {
      setEditIndices(index);
    }
    getEducationsSingleEmployee(index)
      .then(res => {
        SetEditEducationList(res.education);
        ErrorHandler.showNotification(editeducationList);
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  };

  const updateUserEducation = async (event: any, id: any) => {
    event.preventDefault();

    const validateEducationFields = () => {
      const requiredFields = ['education_title', 'degree', 'start_date', 'your_score', 'max_score'];
      let isValid = true;
      const newValidationErrors: any = {};

      for (const field of requiredFields) {
        if (!editeducationList[field]) {
          newValidationErrors[field] = 'This field is required';
          isValid = false;
        } else {
          newValidationErrors[field] = '';
        }
      }
      if (!editeducationList.currently_study_here && !editeducationList.end_date) {
        newValidationErrors['end_date'] = 'End Date is required';
        isValid = false;
      } else {
        newValidationErrors['end_date'] = '';
      }
      newValidationErrors['id'] = id;
      setValidationErrors(newValidationErrors);
      return isValid;
    };

    if (validateEducationFields()) {
      const userId = user?.id;
      try {
        const res = await updateEducation(editeducationList);

        if (res.status) {
          notification.success({
            message: res.message,
          });
          const updatedEducations = await getEducationsEmployee(user?.id);
          SetEducationList(updatedEducations.education);
          setEditIndices(0);
        } else {
          notification.error({
            message: res.message,
          });
        }
      } catch (err) {
        ErrorHandler.showNotification(err);
      }
    }
  };

  const educationChange = (event: any) => {
    const {name, type, checked, value} = event.target;

    const newValue = type === 'checkbox' ? (checked ? '1' : '0') : value;

    if (name === 'currently_study_here') {
      SetEditEducationList((prevState: any) => ({
        ...prevState,
        end_date: checked ? '' : prevState.end_date,
        [name]: newValue,
      }));
    } else {
      SetEditEducationList((prevState: any) => ({
        ...prevState,
        [name]: newValue,
      }));
    }
  };

  const handleeducationDelete = (e: any, id: any) => {
    e.preventDefault();
    swal({
      title: 'Are you sure?',
      text: 'You want to delete your Education',
      icon: 'warning',
      dangerMode: true,
      buttons: ['Cancel', 'Yes, I am sure!'],
    }).then(willDelete => {
      if (willDelete) {
        deleteEducation(id)
          .then(res => {
            if (res.status === true) {
              swal('Your Education has been deleted!', {
                icon: 'success',
              });
              getEducationsEmployee(user?.id).then(res => {
                SetEducationList(res.education);
                ErrorHandler.showNotification(res.education);
              });
            } else {
              notification.error({
                message: res.message,
              });
            }
          })
          .catch(err => {
            notification.error({
              message: err.message,
            });
          });
      }
    });
  };

  const validateEducationForm = () => {
    const newErrors: any = {};
    let isValid = true;

    if (education.education_title.trim() === '') {
      newErrors.education_title = 'Education Title is required';
      isValid = false;
    }
    if (education.degree.trim() === '') {
      newErrors.degree = 'Degree is required';
      isValid = false;
    }
    if (education.start_date.trim() === '') {
      newErrors.start_date = 'Start Date is required';
      isValid = false;
    }
    if (!education.currently_study_here && education.end_date.trim() === '') {
      newErrors.end_date = 'End Date is required';
      isValid = false;
    } else if (!education.currently_study_here && education.start_date && education.end_date < education.start_date) {
      newErrors.end_date = 'End date cannot be before start date';
      isValid = false;
    }
    if (education.your_score.trim() === '') {
      newErrors.your_score = 'your score is required';
      isValid = false;
    }
    if (education.max_score.trim() === '') {
      newErrors.max_score = 'Max Score is required';
      isValid = false;
    }
    setErrors(newErrors);
    return isValid;
  };

  const onSubmitForm = (event: any) => {
    event.preventDefault();
    if (validateEducationForm()) {
      const userId = user?.id;
      const data = {
        user_id: userId,
        education_title: education.education_title,
        degree: education.degree,
        start_date: education.start_date,
        end_date: education.end_date,
        currently_study_here: education.currently_study_here,
        your_score: education.your_score,
        max_score: education.max_score,
      };
      addEducation(data)
        .then(res => {
          if (res.status) {
            notification.success({
              message: res.message,
            });
            SetEducation({
              education_title: '',
              degree: '',
              start_date: '',
              end_date: '',
              currently_study_here: false,
              your_score: '',
              max_score: '',
            });
            getEducationsEmployee(user?.id).then(res => {
              SetEducationList(res.education);
              ErrorHandler.showNotification(res.education);
            });
            setShowSection(false);
          } else {
            notification.error({
              message: res.message,
            });
          }
        })
        .catch(err => {
          ErrorHandler.showNotification(err);
        });
    }
  };

  const handleChange = (event: any) => {
    const {name, value, type, checked} = event.target;
    const newValue = type === 'checkbox' ? checked : value;

    if (name === 'currently_study_here' && checked) {
      SetEducation(prevState => ({
        ...prevState,
        end_date: '',
        [name]: newValue,
      }));
    } else {
      SetEducation(prevState => ({
        ...prevState,
        [name]: newValue,
      }));
    }
  };

  useEffect(() => {
    if (education.start_date && education.end_date) {
      moment(education.start_date).isAfter(education.end_date)
        ? setErrors((prev: any) => ({...prev, end_date: 'End date cannot be before start date'}))
        : setErrors((prev: any) => ({...prev, end_date: ''}));
    }
  }, [education.start_date, education.end_date]);

  return (
    <>
      <div className="cdata-management m-p-10 mt-2">
        <div className="work-experience-fieild m-p-10">
          <div className="row">
            <div className="col-lg-3 col-md-3">
              <div className="left-text-fieild">
                <h3>Education</h3>
                <p className="c-747474">
                  Share the schools you've <br /> studied at
                </p>
              </div>
            </div>
            <div className="col-lg-9 col-md-9">
              {educationList.map((educations: any, index: any) => (
                <div className="right-text-edit mt-1" key={index}>
                  <div className="row mobile-column-reverse">
                    <div className="col-sm-9">
                      <h6>{educations.education_title}</h6>
                      <p>
                        <strong>{educations.degree}</strong>
                      </p>
                      <p className="date-time">
                        {new Date(educations.start_date).toLocaleString('en-US', {
                          month: '2-digit',
                          year: 'numeric',
                        })}{' '}
                        -{' '}
                        {educations.currently_study_here === '1'
                          ? 'Till Now'
                          : new Date(educations.end_date).toLocaleString('en-US', {
                              month: '2-digit',
                              year: 'numeric',
                            })}
                      </p>
                    </div>
                    <div className="col-sm-3 text-right">
                      <div className="edit-pi">
                        <i
                          className="fa-solid fa-square-pen"
                          onClick={() => handleEditClick(Number(educations.id))}></i>
                      </div>
                    </div>
                  </div>
                  {editIndices === Number(educations.id) && (
                    <form
                      className="form-experience-fieild"
                      onSubmit={e => updateUserEducation(e, editeducationList.id)}>
                      <input type="hidden" name="id" value={editeducationList.id} id="" />
                      <div className="form_field_sec">
                        <input
                          type="text"
                          placeholder="College/University"
                          onChange={educationChange}
                          value={editeducationList.education_title}
                          name="education_title"
                          className="fild-des"
                        />
                        <label>Education*</label>
                      </div>
                      {validationErrors.id == editeducationList.id
                        ? validationErrors.education_title && (
                            <p className="error">{validationErrors.education_title}</p>
                          )
                        : null}
                      <div className="form_field_sec">
                        <input
                          type="text"
                          placeholder="Master of technology"
                          value={editeducationList.degree}
                          onChange={educationChange}
                          name="degree"
                          className="fild-des"
                        />
                        <label>Degree/Major*</label>
                      </div>
                      {validationErrors.id == editeducationList.id
                        ? validationErrors.degree && <p className="error">{validationErrors.degree}</p>
                        : null}

                      <div className="row">
                        <div className="col-sm-6">
                          <div className="form_field_sec">
                            <input
                              type="date"
                              className="fild-des"
                              name="start_date"
                              value={editeducationList.start_date}
                              onChange={educationChange}
                            />
                            <label>Start Date*</label>
                          </div>
                          {validationErrors.id == editeducationList.id
                            ? validationErrors.start_date && <p className="error">{validationErrors.start_date}</p>
                            : null}
                        </div>

                        <div className="col-sm-6">
                          <div className="form_field_sec">
                            <input
                              type="date"
                              className="fild-des"
                              name="end_date"
                              value={editeducationList.end_date || ''}
                              onChange={educationChange}
                              disabled={editeducationList.currently_study_here === '1'}
                            />
                            <label>End Date*</label>
                          </div>
                          {validationErrors.id == editeducationList.id
                            ? validationErrors.end_date && <p className="error">{validationErrors.end_date}</p>
                            : null}

                          <label className="chek-set">I currently study here*</label>
                          <input
                            type="checkbox"
                            name="currently_study_here"
                            checked={editeducationList.currently_study_here === '1'}
                            onChange={educationChange}
                          />
                        </div>
                      </div>

                      <div className="row">
                        <div className="col-sm-6">
                          <div className="form_field_sec">
                            <input
                              type="number"
                              placeholder="75"
                              className="fild-des"
                              name="your_score"
                              value={editeducationList.your_score}
                              onChange={educationChange}
                              onInput={e => {
                                const input = e.target as HTMLInputElement;
                                input.value = input.value.slice(0, 5);
                              }}
                            />
                            <label>Youe Score*</label>
                          </div>
                          {validationErrors.id == editeducationList.id
                            ? validationErrors.your_score && <p className="error">{validationErrors.your_score}</p>
                            : null}
                        </div>
                        <div className="col-sm-6">
                          <div className="form_field_sec">
                            <input
                              type="number"
                              placeholder="80"
                              className="fild-des"
                              name="max_score"
                              value={editeducationList.max_score}
                              onChange={educationChange}
                              onInput={e => {
                                const input = e.target as HTMLInputElement;
                                input.value = input.value.slice(0, 5);
                              }}
                            />
                            <label>Major Score</label>
                          </div>
                          {validationErrors.id == editeducationList.id
                            ? validationErrors.max_score && <p className="error">{validationErrors.max_score}</p>
                            : null}
                        </div>
                      </div>
                      <div className="text-right mt-3">
                        <div className="m-d-flex">
                          <a
                            className="rmewp m-d-b  m-w-100 mb-2"
                            onClick={e => handleeducationDelete(e, editeducationList.id)}>
                            Remove Education
                          </a>
                          <a className="cancel m-d-b m-w-47" onClick={() => setEditIndices(0)}>
                            Cancel
                          </a>
                          <button className="save m-w-47">Save</button>
                        </div>
                      </div>
                    </form>
                  )}
                </div>
              ))}

              <p className="add" onClick={handleClick} style={{cursor: 'pointer'}}>
                <i className="fa-solid fa-plus"></i> Add Education
              </p>
              {showSection && (
                <form className="form-experience-fieild" onSubmit={onSubmitForm}>
                  <div className="form_field_sec">
                    <input
                      type="text"
                      placeholder="Collage/University"
                      name="education_title"
                      className="fild-des"
                      value={education.education_title}
                      onChange={handleChange}
                    />
                    <label>Education*</label>
                  </div>
                  {errors.education_title && <p className="error">{errors.education_title}</p>}
                  <div className="form_field_sec">
                    <input
                      type="text"
                      placeholder="Master of technology"
                      name="degree"
                      className="fild-des"
                      value={education.degree}
                      onChange={handleChange}
                    />
                    <label>Degree/Major*</label>
                  </div>
                  {errors.degree && <p className="error">{errors.education_title}</p>}
                  <div className="row">
                    <div className="col-sm-6">
                      <div className="form_field_sec">
                        <input
                          type="date"
                          name="start_date"
                          value={education.start_date}
                          onChange={handleChange}
                          className="fild-des"
                          max={moment().format('YYYY-MM-DD')}
                        />
                        <label>Start Date*</label>
                      </div>
                      {errors.start_date && <p className="error">{errors.start_date}</p>}
                    </div>
                    <div className="col-sm-6">
                      <div className="form_field_sec">
                        <input
                          type="date"
                          name="end_date"
                          value={education.end_date}
                          onChange={handleChange}
                          className="fild-des"
                          disabled={education.currently_study_here}
                          style={{
                            marginBottom: 6,
                          }}
                          max={moment().format('YYYY-MM-DD')}
                        />
                        <label>End Date*</label>
                      </div>
                      {errors.end_date && (
                        <p
                          className="error"
                          style={{
                            marginTop: '0px',
                          }}>
                          {errors.end_date}
                        </p>
                      )}
                      <div style={{display: 'flex', gap: 6, marginBottom: 20}}>
                        <input
                          type="checkbox"
                          name="currently_study_here"
                          checked={education.currently_study_here}
                          onChange={handleChange}
                          style={{
                            width: 18,
                          }}
                        />
                        <label style={{marginRight: '8px'}}>I currently Study here</label>
                      </div>
                    </div>
                  </div>

                  <div className="row">
                    <div className="col-sm-6">
                      <div className="form_field_sec">
                        <input
                          type="number"
                          name="your_score"
                          value={education.your_score}
                          onChange={handleChange}
                          className="fild-des"
                          placeholder="75"
                        />
                        <label>Your Score*</label>
                      </div>
                      {errors.your_score && <p className="error">{errors.your_score}</p>}
                    </div>
                    <div className="col-sm-6">
                      <div className="form_field_sec">
                        <input
                          type="number"
                          name="max_score"
                          value={education.max_score}
                          onChange={handleChange}
                          className="fild-des"
                          placeholder="100"
                        />
                        <label>Max Score*</label>
                      </div>
                      {errors.max_score && <p className="error">{errors.max_score}</p>}
                    </div>
                  </div>

                  <div className="text-right mt-3">
                    <a className="cancel" onClick={handleeducationCancel}>
                      Cancel
                    </a>
                    <button className="save">Save</button>
                  </div>
                </form>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
