import React, {useState, useEffect, useContext} from 'react';
import {updateJobPref} from '../../../lib/employeeapi';
import {getCurrentUserDetails} from '../../../lib/frontendapi';
import Link from 'next/link';
import AuthContext from '@/Context/AuthContext';
import {notification} from 'antd';
import <PERSON><PERSON>r<PERSON>andler from '@/lib/ErrorHandler';

export default function JobPreferences() {
  const {user} = useContext(AuthContext);
  const [currentJobStatus, setCurrentJobStatus] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [selectedJobType, setSelectedJobType] = useState<any[]>([]);
  const [prevJobStatus, setPrevJobStatus] = useState('');
  const [prevSelectedJobType, setPrevSelectedJobType] = useState<any>([]);

  const handleItemClick = (status: any) => {
    setCurrentJobStatus(status);
    setPrevJobStatus(currentJobStatus);
    setIsDropdownOpen(false);
  };
  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const handleChangeJobType = (e: any) => {
    const jobType = e.target.value;
    setSelectedJobType((prevJobTypes: any) => {
      if (e.target.checked) {
        return [...prevJobTypes, jobType];
      } else {
        return prevJobTypes.filter((type: any) => type !== jobType);
      }
    });
    setPrevSelectedJobType(selectedJobType);
  };

  const handleCancelClick = () => {
    setCurrentJobStatus(prevJobStatus);
    setSelectedJobType(prevSelectedJobType);
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    const user_id = user?.id;
    updateJobPref(user_id, {
      job_type: selectedJobType.join(','),
      job_status: currentJobStatus,
    })
      .then(response => {
        notification.success({
          message: response.message,
        });
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });
  };

  useEffect(() => {
    if (user?.id) {
      getCurrentUserDetails(user?.id)
        .then(res => {
          if (res.status == true) {
            setCurrentJobStatus(res.user.job_status);
            if (res.user.job_type) {
              setSelectedJobType(res.user.job_type.split(','));
            }
          }
        })
        .catch(err => {
          ErrorHandler.showNotification(err);
        });
    }
  }, [user]);

  const getJobStatusClass = (jobStatus: any) => {
    if (jobStatus === currentJobStatus) {
      return 'active f-0055BA ';
    }
    return '';
  };

  return (
    <>
      <div className="dash-right">
        <h1>
          My <span className="span-color">Profile</span>
        </h1>
        <div className="row m-column-reverse">
          <div className="col-sm-7">
            <ul className="list-loc m-m-0 mt-4">
              <li>
                <Link href="/employees/myprofile">Overview</Link>
              </li>
              <li>
                <Link href="/employees/myprofile/profile">Profile</Link>
              </li>
              <li className="active">
                <Link href="/employees/myprofile/jobpreferences">Preferences</Link>
              </li>
            </ul>
          </div>
          <div className="col-sm-5">
            <ul className="blue-text-line mt-4 text-right">
              <li>
                <Link target="_blank" href={'/candidate-profile/' + user?.slug}>
                  View Public Profile
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="data-management m-p-10 p-0">
          <div className="work-experience-fieild m-p-10">
            <div className="row">
              <div className="col-lg-3 col-md-3">
                <div className="left-text-fieild">
                  <h3 className=" m-center">Job Preferences</h3>
                  <p className="c-747474  m-center">How would you like to find jobs?</p>
                </div>
              </div>
              <div className="col-lg-9 col-md-9">
                <form className="form-experience-fieild" onSubmit={handleSubmit}>
                  <div className="accordion" id="accordionExample">
                    <label className="mt-2">Where are you in job search?*</label>
                    <div className="accordion" id="accordionExample">
                      <div className="ready-to-in">
                        <div className="accordion-item">
                          <h2 className="accordion-header" id="headingOne">
                            <button
                              className={`accordion-button btn-acc${getJobStatusClass('currentJobStatus')}`}
                              type="button"
                              data-bs-toggle="collapse"
                              data-bs-target="#collapseOnee"
                              onClick={toggleDropdown}
                              aria-expanded={isDropdownOpen}
                              aria-controls="collapseOnee">
                              {currentJobStatus === 'ready_to_interview'
                                ? 'Ready To Interview'
                                : currentJobStatus === 'not_looking'
                                ? 'Not Looking'
                                : currentJobStatus === 'open_to_offer'
                                ? 'Open to Offers'
                                : currentJobStatus}
                            </button>
                          </h2>
                          <div
                            id="collapseOnee"
                            className={`accordion-collapse collapse ${isDropdownOpen ? 'show' : ''}`}
                            aria-labelledby="headingOne"
                            data-bs-parent="#accordionExample">
                            <div className="accordion-body">
                              <p
                                className={`${getJobStatusClass('ready_to_interview')}`}
                                onClick={() => handleItemClick('ready_to_interview')}
                                style={{cursor: 'pointer'}}>
                                <b>Ready to Interview:</b>
                                <br /> Actively looking for openings & ready to interview.{' '}
                                {currentJobStatus === 'ready_to_interview' && <i className="fa-solid fa-check"></i>}
                              </p>
                              <p
                                className={`${getJobStatusClass('open_to_offer')}`}
                                onClick={() => handleItemClick('open_to_offer')}
                                style={{cursor: 'pointer'}}>
                                <b>Open to Offers:</b>
                                <br /> Not actively looking but open to hear about new opportunities.{' '}
                                {currentJobStatus === 'open_to_offer' && <i className="fa-solid fa-check"></i>}
                              </p>
                              <p
                                className={`${getJobStatusClass('not_looking')}`}
                                onClick={() => handleItemClick('not_looking')}
                                style={{cursor: 'pointer'}}>
                                <b>Not looking:</b>
                                <br /> Not actively looking for opportunities right now.{' '}
                                {currentJobStatus === 'not_looking' && <i className="fa-solid fa-check"></i>}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <label className="mt-4">What kind of openings are you looking for?</label>
                  <div className="col-sm-12">
                    <div className="salary-box mt-2">
                      <div className="form-master-field dflex mb-2 ">
                        <input
                          type="checkbox"
                          placeholder="Placeholder"
                          className="master-fields checkbox border-0 mb-0 mt-0"
                          value="fulltime"
                          onChange={handleChangeJobType}
                          checked={selectedJobType.includes('fulltime')}
                        />
                        <label className="check-label c-4D4D4D mb-0">Full-Time</label>
                      </div>

                      <div className="form-master-field dflex mb-2 ">
                        <input
                          type="checkbox"
                          placeholder="Placeholder"
                          className="master-fields checkbox border-0 mb-0 mt-0"
                          value="parttime"
                          onChange={handleChangeJobType}
                          checked={selectedJobType.includes('parttime')}
                        />
                        <label className="check-label c-4D4D4D mb-0">Part-time</label>
                      </div>

                      <div className="form-master-field dflex mb-2 ">
                        <input
                          type="checkbox"
                          placeholder="Placeholder"
                          className="master-fields checkbox border-0 mb-0 mt-0"
                          value="contract"
                          onChange={handleChangeJobType}
                          checked={selectedJobType.includes('contract')}
                        />
                        <label className="check-label c-4D4D4D mb-0">Contract</label>
                      </div>

                      <div className="form-master-field dflex mb-2 ">
                        <input
                          type="checkbox"
                          placeholder="Placeholder"
                          className="master-fields checkbox border-0 mb-0 mt-0"
                          value="freelance"
                          onChange={handleChangeJobType}
                          checked={selectedJobType.includes('freelance')}
                        />
                        <label className="check-label c-4D4D4D mb-0">FreeLancer</label>
                      </div>
                    </div>
                  </div>
                  <div className="text-right mt-3">
                    <a className="cancel" onClick={handleCancelClick} style={{cursor: 'pointer'}}>
                      Cancel
                    </a>
                    <button className="save">Save</button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
