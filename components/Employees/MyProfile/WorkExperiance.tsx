import React, {useState, useEffect, useContext} from 'react';
import {
  getSingleWorkExperience,
  updateWorkExperience,
  getWorkExperienceEmployee,
  destroyWorkExperience,
  addWorkExperience,
} from '../../../lib/frontendapi';
import 'react-toastify/dist/ReactToastify.css';
import swal from 'sweetalert';
import AuthContext from '@/Context/AuthContext';
import {notification} from 'antd';
import ErrorHandler from '@/lib/ErrorHandler';
import moment from 'moment';

export default function WorkExperiance() {
  const {user} = useContext(AuthContext);
  const [workexperienceList, SetWorkexperienceList] = useState([]);
  const [editwork, setEditWork] = useState(0);
  const [editWorkList, SeteditWorkList]: any = useState([]);
  const [workexperience, SetWorkExprience] = useState({
    title: '',
    workexperience: '',
    company: '',
    start_date: '',
    end_date: '',
    currently_work_here: false,
    description: '',
  });
  const [showForm, setShowForm] = useState(false);
  const [errors, setErrors] = useState<any>({});
  const [validationErrors, setValidationErrors] = useState<any>({
    title: '',
    company: '',
    start_date: '',
    end_date: '',
    currently_work_here: false,
    description: '',
  });

  useEffect(() => {
    getWorkExperienceEmployee(user?.id)
      .then(res => {
        SetWorkexperienceList(res.data);
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });

    getSingleWorkExperience(user?.id)
      .then(res => {
        SeteditWorkList(res.workexperience);
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  }, [user]);

  const handleWorkClick = (index: number) => {
    if (editwork === index) {
      setEditWork(0);
    } else {
      setEditWork(index);
    }
    getSingleWorkExperience(index)
      .then(res => {
        SeteditWorkList(res.workexperience);
        ErrorHandler.showNotification(res.workexperience);
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  };

  const handleupdateWorkExperience = (event: any, id: any) => {
    event.preventDefault();
    const validateexperienceFields = () => {
      const requiredFields = ['title', 'company', 'start_date', 'currently_work_here', 'description'];
      let isValid = true;
      const newValidationErrors: any = {};
      for (const field of requiredFields) {
        if (!editWorkList[field]) {
          newValidationErrors[field] = 'This field is required';
          isValid = false;
        } else {
          newValidationErrors[field] = '';
          newValidationErrors['id'] = id;
        }
      }
      setValidationErrors(newValidationErrors);
      return isValid;
    };

    if (validateexperienceFields()) {
      updateWorkExperience(editWorkList)
        .then(res => {
          if (res.status) {
            notification.success({
              message: res.message,
            });
            getWorkExperienceEmployee(user?.id).then(res => {
              SetWorkexperienceList(res.data);
            });
            setEditWork(0);
          } else {
            notification.info({
              message: res.message,
            });
          }
        })
        .catch(err => {
          ErrorHandler.showNotification(err);
        });
    }
  };

  const experienceChange = (event: any) => {
    const {name, type, checked, value} = event.target;
    const newValue = type === 'checkbox' ? (checked ? '1' : '0') : value;
    if (name === 'currently_work_here') {
      SeteditWorkList((prevState: any) => ({
        ...prevState,
        end_date: checked ? '' : prevState.end_date,
        [name]: newValue,
      }));
    } else {
      SeteditWorkList((prevState: any) => ({
        ...prevState,
        [name]: newValue,
      }));
    }
  };

  const handleworkDelete = (e: any, id: any) => {
    e.preventDefault();
    swal({
      title: 'Are you sure?',
      text: 'You want to delete your Position',
      icon: 'warning',
      dangerMode: true,
      buttons: ['Cancel', 'Yes, I am sure!'],
    }).then(willDelete => {
      if (willDelete) {
        destroyWorkExperience(id)
          .then(res => {
            if (res.status === true) {
              swal('Your Postion has been deleted!', {
                icon: 'success',
              });
              getWorkExperienceEmployee(user?.id).then(res => {
                SetWorkexperienceList(res.data);
              });
            } else {
              notification.info({
                message: res.message,
              });
            }
          })
          .catch(err => {
            notification.error({
              message: err.message,
            });
          });
      }
    });
  };

  const handleClickExp = () => {
    setShowForm(prevState => !prevState);
  };

  const onhandleForm = (event: any) => {
    event.preventDefault();
    if (validateWorkExpForm()) {
      const userId = user?.id;
      const data = {
        user_id: userId,
        title: workexperience.title,
        company: workexperience.company,
        start_date: workexperience.start_date,
        end_date: workexperience.end_date,
        currently_work_here: workexperience.currently_work_here,
        description: workexperience.description,
      };
      addWorkExperience(data)
        .then(res => {
          if (res.status) {
            notification.success({
              message: res.message,
            });
            SetWorkExprience({
              title: '',
              workexperience: '',
              company: '',
              start_date: '',
              end_date: '',
              currently_work_here: false,
              description: '',
            });
            getWorkExperienceEmployee(user?.id).then(res => {
              SetWorkexperienceList(res.data);
            });
            setShowForm(false);
          } else {
            notification.info({
              message: res.message,
            });
          }
        })
        .catch(err => {
          ErrorHandler.showNotification(err);
        });
    }
  };

  const validateWorkExpForm = () => {
    const newErrors: any = {};
    let isValid = true;

    if (workexperience.title.trim() === '') {
      newErrors.title = 'Title is required';
      isValid = false;
    }
    if (workexperience.company.trim() === '') {
      newErrors.company = 'Company is required';
      isValid = false;
    }
    if (workexperience.start_date.trim() === '') {
      newErrors.start_date = 'Start Date is required';
      isValid = false;
    }
    if (!workexperience.currently_work_here && workexperience.end_date.trim() === '') {
      newErrors.end_date = 'End Date is required';
      isValid = false;
    } else if (
      !workexperience.currently_work_here &&
      workexperience.start_date &&
      workexperience.end_date < workexperience.start_date
    ) {
      newErrors.end_date = 'End date cannot be before start date';
      isValid = false;
    }
    if (workexperience.description.trim() === '') {
      newErrors.description = 'Description is required';
      isValid = false;
    } else if (workexperience.description.split(' ').length > 250) {
      newErrors.description = 'Description should not exceed 250 words';
      isValid = false;
    }
    setErrors(newErrors);
    return isValid;
  };

  const workhandleChange = (event: any) => {
    const {name, value, type, checked} = event.target;
    const newValue = type === 'checkbox' ? checked : value;
    if (name === 'currently_work_here' && checked) {
      SetWorkExprience(prevState => ({
        ...prevState,
        end_date: '',
        [name]: newValue,
      }));
    } else {
      SetWorkExprience(prevState => ({
        ...prevState,
        [name]: newValue,
      }));
    }
  };

  const handleworkCancel = () => {
    setShowForm(false);
  };

  return (
    <>
      <div className="cdata-management m-p-10 mt-2">
        <div className="work-experience-fieild m-p-10">
          <div className="row">
            <div className="col-lg-3 col-md-3">
              <div className="left-text-fieild">
                <h3>
                  Your Work <br /> Experience
                </h3>
                <p className="c-747474">
                  Share your employment <br /> history
                </p>
              </div>
            </div>
            <div className="col-lg-9 col-md-9">
              {workexperienceList.map((work: any, index: any) => (
                <div className="right-text-edit mt-1" key={index}>
                  <div className="row mobile-column-reverse">
                    <div className="col-sm-9">
                      <h6>{work.title}</h6>
                      <p>
                        <strong>{work.company}</strong>
                      </p>
                      <p className="date-time">
                        {new Date(work.start_date).toLocaleString('en-US', {
                          month: '2-digit',
                          year: 'numeric',
                        })}{' '}
                        -{' '}
                        {work.currently_work_here === '1'
                          ? 'Till Now'
                          : new Date(work.end_date).toLocaleString('en-US', {
                              month: '2-digit',
                              year: 'numeric',
                            })}
                      </p>
                    </div>
                    <div className="col-sm-3 text-right">
                      <div className="edit-pi">
                        <i className="fa-solid fa-square-pen" onClick={() => handleWorkClick(work.id)}></i>
                      </div>
                    </div>
                  </div>
                  {editwork === work.id && (
                    <form
                      className="form-experience-fieild"
                      onSubmit={e => handleupdateWorkExperience(e, editWorkList.id)}>
                      <input type="hidden" name="id" value={editWorkList.id} id="" />
                      <div className="form_field_sec">
                        <input
                          type="text"
                          placeholder="Software Engineer"
                          name="title"
                          className="fild-des"
                          value={editWorkList.title}
                          onChange={experienceChange}
                        />
                        <label>Title*</label>
                      </div>
                      {validationErrors.id == editWorkList.id
                        ? validationErrors.title && <p className="error">{validationErrors.title}</p>
                        : null}
                      <div className="form_field_sec">
                        <input
                          type="text"
                          placeholder="Meta"
                          name="company"
                          className="fild-des"
                          value={editWorkList.company}
                          onChange={experienceChange}
                        />
                        <label>Company*</label>
                      </div>
                      {validationErrors.id == editWorkList.id
                        ? validationErrors.company && <p className="error">{validationErrors.company}</p>
                        : null}
                      <div className="row">
                        <div className="col-sm-6">
                          <div className="form_field_sec">
                            <input
                              type="date"
                              name="start_date"
                              value={editWorkList.start_date}
                              onChange={experienceChange}
                              className="fild-des"
                            />
                            <label>Start Date*</label>
                          </div>
                        </div>
                        {validationErrors.id == editWorkList.id
                          ? validationErrors.start_date && <p className="error">{validationErrors.start_date}</p>
                          : null}
                        <div className="col-sm-6">
                          <div className="form_field_sec">
                            <input
                              type="date"
                              name="end_date"
                              value={editWorkList.end_date}
                              onChange={experienceChange}
                              className="fild-des"
                              disabled={editWorkList.currently_work_here === '1'}
                            />
                            <label>End Date*</label>
                          </div>
                          {validationErrors.id == editWorkList.id
                            ? validationErrors.end_date && <p className="error">{validationErrors.end_date}</p>
                            : null}
                            <div style={{display: 'flex', gap: 6, marginBottom: 20}}>
                              <input
                                type="checkbox"
                                name="currently_work_here"
                                checked={editWorkList.currently_work_here === '1'}
                                onChange={experienceChange}
                                style={{
                                width: 18,
                              }}
                              />
                              <label className="d-flex-form">I currently work here</label>
                            </div>
                        </div>
                      </div>
                      <div className="form_field_sec">
                        <textarea
                          placeholder="Your description goes here..."
                          className="fild-des"
                          name="description"
                          onChange={experienceChange}
                          value={editWorkList.description}></textarea>
                        <label>Description*</label>
                      </div>
                      {validationErrors.id == editWorkList.id
                        ? validationErrors.description && <p className="error">{validationErrors.description}</p>
                        : null}
                      <p className="font-12 text-right words">250 words</p>

                      <div className="text-right mt-3">
                        <div className="m-d-flex">
                          <a className="rmewp m-d-b  m-w-100 mb-2" onClick={e => handleworkDelete(e, editWorkList.id)}>
                            Remove Postion
                          </a>
                          <a className="cancel m-d-b m-w-47" onClick={() => setEditWork(0)}>
                            Cancel
                          </a>
                          <button className="save m-w-47">Save</button>
                        </div>
                      </div>
                    </form>
                  )}
                </div>
              ))}
              <p className="add" onClick={handleClickExp} style={{cursor: 'pointer'}}>
                <i className="fa-solid fa-plus"></i> Add Experience
              </p>
              {showForm && (
                <form className="form-experience-fieild" onSubmit={onhandleForm}>
                  <div className="form_field_sec">
                    <input
                      type="text"
                      placeholder="Software Engineer"
                      name="title"
                      className="fild-des"
                      value={workexperience.title}
                      onChange={workhandleChange}
                    />
                    <label>Title*</label>
                  </div>
                  {errors.title && <p className="error">{errors.title}</p>}
                  <div className="form_field_sec">
                    <input
                      type="text"
                      placeholder="Meta"
                      name="company"
                      className="fild-des"
                      value={workexperience.company}
                      onChange={workhandleChange}
                    />
                    <label>Company*</label>
                  </div>
                  {errors.company && <p className="error">{errors.company}</p>}

                  <div className="row">
                    <div className="col-sm-6">
                      <div className="form_field_sec">
                        <input
                          type="date"
                          name="start_date"
                          value={workexperience.start_date}
                          onChange={workhandleChange}
                          className="fild-des"
                          max={moment().format('YYYY-MM-DD')}
                        />
                        <label>Start Date*</label>
                      </div>
                      {errors.start_date && <p className="error">{errors.start_date}</p>}
                    </div>
                    <div className="col-sm-6">
                      <div className="form_field_sec">
                        <input
                          type="date"
                          name="end_date"
                          value={workexperience.end_date}
                          onChange={workhandleChange}
                          className="fild-des"
                          disabled={workexperience.currently_work_here}
                          style={{
                            marginBottom: 6,
                          }}
                          max={moment().format('YYYY-MM-DD')}
                        />
                        <label>End Date*</label>
                      </div>
                      {errors.end_date && (
                        <p
                          className="error"
                          style={{
                            marginTop: 0,
                          }}>
                          {errors.end_date}
                        </p>
                      )}
                      <div style={{display: 'flex', gap: 6, marginBottom: 20}}>
                        <input
                          type="checkbox"
                          name="currently_work_here"
                          checked={workexperience.currently_work_here}
                          onChange={workhandleChange}
                          style={{
                            width: 18,
                          }}
                        />
                        <label className="d-flex-form">I currently work here</label>
                      </div>
                    </div>
                  </div>
                  <div className="form_field_sec">
                    <textarea
                      placeholder="Your description goes here..."
                      className="fild-des"
                      name="description"
                      onChange={workhandleChange}></textarea>
                    <label>Description*</label>
                  </div>
                  {errors.description && <p className="error">{errors.description}</p>}
                  <p className="font-12 text-right words">250 words</p>

                  <div className="text-right mt-3">
                    <a className="cancel" onClick={handleworkCancel}>
                      Cancel
                    </a>
                    <button className="save">Save</button>
                  </div>
                </form>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
