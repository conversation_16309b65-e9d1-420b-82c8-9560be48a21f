import React, {useState, useEffect, useContext} from 'react';
import 'react-toastify/dist/ReactToastify.css';
import {updateUserSocialLinks, getSocialLinks} from '../../../lib/frontendapi';
import AuthContext from '@/Context/AuthContext';
import {Form, Input, notification} from 'antd';
import <PERSON><PERSON>r<PERSON>and<PERSON> from '@/lib/ErrorHandler';
import {FaLinkedinIn} from 'react-icons/fa6';
import {FaXTwitter} from 'react-icons/fa6';
import {FaInstagram} from 'react-icons/fa';
import {FaFacebookF} from 'react-icons/fa';
import {FaGlobe} from 'react-icons/fa';
import {useFormik} from 'formik';
import {ButtonUi} from '@/ui/Button';
import {useGetSocialLinks} from '@/modules/employees/query/useGetSocialLinks';

export default function SocailLinks() {
  const {user} = useContext(AuthContext);

  const {data: getSocials} = useGetSocialLinks(user?.id);

  const {values, handleChange, setFieldValue, dirty, handleSubmit, initialValues} = useFormik({
    initialValues: {
      linkedin_link: getSocials?.linkedin_link || '',
      twitter_link: getSocials?.twitter_link || '',
      instagram_link: getSocials?.instagram_link || '',
      facebook_link: getSocials?.facebook_link || '',
      website_url: getSocials?.website_url || '',
    },
    onSubmit: values => {
      if (validateSocialForm()) {
        const userId = user?.id;
        updateUserSocialLinks(userId, values)
          .then(response => {
            notification.success({
              message: response.message,
            });
          })
          .catch(error => {
            ErrorHandler.showNotification(error);
          });
      }
    },
  });
  const [errors, setErrors] = useState<any>({});

  const handleChangeLinks = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleChange(e);
  };

  const validateSocialForm = () => {
    const newErrors: any = {};
    let isValid = true;

    if (values.linkedin_link && !isValidUrl(values.linkedin_link)) {
      newErrors.linkedin_link = 'Invalid LinkedIn URL';
      isValid = false;
    }
    if (values.twitter_link && !isValidUrl(values.twitter_link)) {
      newErrors.twitter_link = 'Invalid Twitter URL';
      isValid = false;
    }
    if (values.instagram_link && !isValidUrl(values.instagram_link)) {
      newErrors.instagram_link = 'Invalid Instagram URL';
      isValid = false;
    }
    if (values.facebook_link && !isValidUrl(values.facebook_link)) {
      newErrors.facebook_link = 'Invalid Facebook URL';
      isValid = false;
    }
    if (values.website_url && !isValidUrl(values.website_url)) {
      newErrors.website_url = 'Invalid Website URL';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const isValidUrl = (url: string) => {
    const urlRegex = /^(ftp|http|https):\/\/[^ "]+$/;
    return urlRegex.test(url);
  };

  return (
    <>
      <div className="work-experience-fieild m-p-10 mb-2 mt-2 rounded">
        <div className="row">
          <div className="col-lg-3 col-md-3">
            <div className="left-text-fieild">
              <h3>Social Links</h3>
              <p className="c-747474">
                Where can talent find you <br /> online?
              </p>
            </div>
          </div>
          <div className="col-lg-9 col-md-9">
            <form className="form-experience-fieild" onSubmit={handleSubmit}>
              <Form layout="vertical">
                <Form.Item label={'LinkedIn*'}>
                  <Input
                    placeholder="LinkedIn"
                    prefix={<FaLinkedinIn />}
                    name="linkedin_link"
                    value={values.linkedin_link}
                    onChange={handleChangeLinks}
                  />
                </Form.Item>

                {errors.linkedin_link && <p className="error">{errors.linkedin_link}</p>}

                <Form.Item label={'Twitter*'}>
                  <Input
                    placeholder="Twitter"
                    prefix={<FaXTwitter />}
                    name="twitter_link"
                    value={values.twitter_link}
                    onChange={handleChangeLinks}
                  />
                </Form.Item>
                {errors.twitter_link && <p className="error">{errors.twitter_link}</p>}
                <Form.Item label={'Instagram*'}>
                  <Input
                    placeholder="Instagram"
                    prefix={<FaInstagram />}
                    name="instagram_link"
                    value={values.instagram_link}
                    onChange={handleChangeLinks}
                  />
                </Form.Item>
                {errors.instagram_link && <p className="error">{errors.instagram_link}</p>}
                <Form.Item label={'Facebook*'}>
                  <Input
                    placeholder="Facebook"
                    prefix={<FaFacebookF />}
                    name="facebook_link"
                    value={values.facebook_link}
                    onChange={handleChangeLinks}
                  />
                </Form.Item>
                {errors.facebook_link && <p className="error">{errors.facebook_link}</p>}
                <Form.Item label={'Website*'}>
                  <Input
                    placeholder="Website"
                    prefix={<FaGlobe />}
                    name="website_url"
                    value={values.website_url}
                    onChange={handleChangeLinks}
                  />
                </Form.Item>
                {errors.website_url && <p className="error">{errors.website_url}</p>}
              </Form>

              <div
                className="text-right mt-3"
                style={{
                  display: 'flex',
                  justifySelf: 'flex-end',
                  alignItems: 'center',
                }}>
                <a className="cancel">Cancel</a>
                <ButtonUi
                  color="primary"
                  variant="contained"
                  className="save"
                  disabled={!dirty}
                  onClick={e => handleSubmit()}
                  style={{
                    height: '45px',
                    width: '100px',
                  }}>
                  Save
                </ButtonUi>
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
}
