import React, { useState, useEffect, useContext } from 'react'
import 'react-toastify/dist/ReactToastify.css';
import { getSingleLanguage, updateLanguage, getLanguage, addlanguage, deleteLanguage } from '../../../lib/frontendapi'
import swal from "sweetalert";
import AuthContext from "@/Context/AuthContext";
import { notification } from 'antd';
import <PERSON>rrorHandler from '@/lib/ErrorHandler';


export default function Language() {
  const {user} = useContext(AuthContext);
  const [langageList, SetLanguageList]: any = useState([]);
  const [editlanguage, setEditlanguage] = useState(0);
  const [editLanguageList, SeteditLanguageList]: any = useState({ proficiency: '' });
  const [language, SetLanguage] = useState({
    language: "",
    proficiency: "",
  });
  const [showlanguage, setshowLanguage] = useState(false);
  const [errors, setErrors] = useState<any>({});

  useEffect(() => {
    getLanguage(user?.id)
    .then(res => {
      SetLanguageList(res.data);
    })
  }, [user])

  const handleLanguageClick = (index: number) => {
    if (editlanguage === index) {
      setEditlanguage(0)
    } else {
      setEditlanguage(index)
    }
    getSingleLanguage(index)
      .then(res => {
        SeteditLanguageList(res.data);
        ErrorHandler.showNotification(res.data);
      })
  };

  const updatelanguageForm = (event: any) => {
    event.preventDefault();
    const userId = user?.id;
    const data = {
      user_id: userId,
      language: language.language,
      proficiency: language.proficiency,
    };
    updateLanguage(editLanguageList)
      .then(res => {
        if (res.status) {
          notification.success({
            message: res.message
          });
          getLanguage(user?.id)
            .then(res => {
              SetLanguageList(res.data);
            })
          setEditlanguage(0);
        } else {
          notification.error({
            message: res.message
          });
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  };

  const Languageupdate = (event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = event.target;
    let newValue: string | boolean;
    if (type === "checkbox") {
      newValue = (event.target as HTMLInputElement).checked;
    } else {
      newValue = value;
    }
    SeteditLanguageList((prevState: any) => ({
      ...prevState,
      [name]: newValue,
    }));
  };

  const handlelanguage = () => {
    setshowLanguage(prevState => !prevState);
  }

  const onlanguageForm = (event: any) => {
    event.preventDefault();
    if (validatelanguageForm()) {
      const userId = user?.id;
      const data = {
        user_id: userId,
        language: language.language,
        proficiency: language.proficiency,
      };
      addlanguage(data)
        .then(res => {
          if (res.status) {
            notification.success({
              message: res.message
            });
            SetLanguage({
              language: "",
              proficiency: "",
            });
            getLanguage(user?.id)
              .then(res => {
                SetLanguageList(res.data);
              })
            setshowLanguage(false);
          } else {
            notification.error({
            message: res.message
          });
          }
        })
        .catch(err => {
          ErrorHandler.showNotification(err);
        });
    }
  };

  const validatelanguageForm = () => {
    if (langageList.some((item: any) => item.language === language.language)) {
      notification.info({
        message: 'Language already exists.'
      });
      return false;
    }
    const newErrors: any = {};
    let isValid = true;
    if (language.language.trim() === '') {
      newErrors.language = 'Language is required';
      isValid = false;
    }
    if (language.proficiency.trim() === '') {
      newErrors.proficiency = 'Proficiency is required';
      isValid = false;
    }
    setErrors(newErrors);
    return isValid;
  };

  const handleLanguageChange = (event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = event.target;
    let newValue: string | boolean;
    if (type === "checkbox") {
      newValue = (event.target as HTMLInputElement).checked;
    } else {
      newValue = value;
    }
    SetLanguage((prevState) => ({
      ...prevState,
      [name]: newValue,
    }));
  };

  const handlelanguageCancel = () => {
    setshowLanguage(false);
  };

  const handleLanguageDelete = (e: any, id: any) => {
    e.preventDefault();
    swal({
      title: "Are you sure?",
      text: "You want to delete your Language",
      icon: "warning",
      dangerMode: true,
      buttons: ["Cancel", "Yes, I am sure!"],
    }).then((willDelete) => {
      if (willDelete) {
        deleteLanguage(id)
          .then((res) => {
            if (res.status === true) {
              swal("Your Language has been deleted!", {
                icon: "success",
              });
              getLanguage(user?.id)
                .then(res => {
                  SetLanguageList(res.data);
                })
            } else {
              notification.success({
                message: res.message
              });
            }
          })
          .catch((err) => {
            notification.success({
              message: err.message
            });
          });
      }
    });
  };
  return (
    <>
      <div className="mt-2">
        <div className="work-experience-fieild">
          <div className="row">
            <div className="col-lg-3 col-md-3">
              <div className="left-text-fieild">
                <h3>Languages</h3>
                <p className="c-747474">Enter the languages you know</p>
              </div>
            </div>
            <div className="col-lg-9 col-md-9">
              {langageList.map((language: any, index: any) => (
                <div className="right-text-edit mt-1" key={index}>
                  <div className="row mobile-column-reverse">
                    <div className="col-sm-9">
                      <h6>{language.language}</h6>
                      <p>
                        <strong>{language.proficiency}</strong>
                      </p>
                    </div>
                    <div className="col-sm-3 text-right">
                      <div className="edit-pi">
                        <i
                          className="fa-solid fa-square-pen"
                          onClick={() => handleLanguageClick(language.id)}
                        ></i>
                      </div>
                    </div>
                  </div>
                  {editlanguage === language.id && (
                    <form
                      className="form-experience-fieild"
                      onSubmit={updatelanguageForm}
                    >
                      <label>Language</label>
                      <div className="search-in w-100 mb-3">
                        <input
                          className="form-control me-2"
                          type="search"
                          placeholder="e.g. German, English, French"
                          aria-label="Search"
                          onChange={Languageupdate}
                          name="language"
                          value={editLanguageList.language}
                        />
                        <i className="fa-solid fa-magnifying-glass glass-ser"></i>
                      </div>
                      <label>Proficiency</label>
                      <select
                        className="fild-des"
                        onChange={Languageupdate}
                        name="proficiency"
                        value={editLanguageList.proficiency}
                      >
                        <option disabled selected>
                          Select
                        </option>
                        <option>Beginner</option>
                        <option>Intermediate</option>
                        <option>Proficient</option>
                        <option>Fluent</option>
                        <option>Native Speaker</option>
                      </select>

                      <div className="text-right mt-3">
                      <div className='m-d-flex'>
                        <a
                          className="rmewp m-d-b  m-w-100 mb-2"
                          onClick={(e) =>
                            handleLanguageDelete(e, editLanguageList.id)
                          }
                        >
                          Remove language
                        </a>
                        <a className="cancel m-d-b m-w-47">Cancel</a>
                        <button className="save m-w-47">Save</button>
                      </div>
                      </div>
                    </form>
                  )}
                </div>
              ))}
              <p
                className="add"
                onClick={handlelanguage}
                style={{ cursor: "pointer" }}
              >
                <i className="fa-solid fa-plus"></i> Add Languages
              </p>
              {showlanguage && (
                <form
                  className="form-experience-fieild"
                  onSubmit={onlanguageForm}
                >
                  <label>Language</label>
                  <div className="search-in w-100 mb-3">
                    <input
                      className="form-control me-2"
                      type="search"
                      placeholder="e.g. German, English, French"
                      aria-label="Search"
                      onChange={handleLanguageChange}
                      value={language.language}
                      name="language"
                    />
                    {errors.language && (
                      <p className="error mt-2">{errors.language}</p>
                    )}
                    <i className="fa-solid fa-magnifying-glass glass-ser"></i>
                  </div>
                  <label>Proficiency</label>
                  <select
                    className="fild-des"
                    onChange={handleLanguageChange}
                    name="proficiency"
                    value={language.proficiency}
                  >
                    <option disabled selected value="">
                      Select Proficiency
                    </option>
                    <option>Beginner</option>
                    <option>Intermediate</option>
                    <option>Proficient</option>
                    <option>Fluent</option>
                    <option>Native Speaker</option>
                  </select>

                  {errors.proficiency && (
                    <p className="error">{errors.proficiency}</p>
                  )}
                  <div className="text-right mt-3">
                    <a className="cancel" onClick={handlelanguageCancel}>
                      Cancel
                    </a>
                    <button className="save">Save</button>
                  </div>
                </form>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
