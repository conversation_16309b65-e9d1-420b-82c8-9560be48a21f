import React, {useState, useEffect, useContext} from 'react';
import {
  getSingleUserDetails,
  updateEmployee,
  getAllCountries,
  getExperiance,
  getIndustries,
  getAllSectors,
  updateUserSectorAndIndustry,
  getAllNationality,
  getSingleCountryAllCities,
} from '../../../lib/frontendapi';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import Link from 'next/link';
import Education from './Education';
import WorkExperiance from './WorkExperiance';
import Portfolio from './Portfolio';
import SocailLinks from './SocialLinks';
import Language from './Language';
import Skills from './Skills';
import {Country, userData} from '@/lib/types';
import AuthContext from '@/Context/AuthContext';
import {message, notification} from 'antd';
import ErrorHandler from '@/lib/ErrorHandler';
import UserProfileImage from '@/components/Common/UserProfileImage';
import Image from 'next/image';
import {updateJobStatus} from '@/lib/employeeapi';
import styles from './Profile.module.css';
import {useGetSingleUserDetails} from '@/modules/employees/query/useGetSingleUserDetails';
import {QUERY_GET_SINGLE_USER_DETAILS} from '@/modules/employees/constants';
import {useQueryClient} from 'react-query';

export default function Profile() {
  const {user} = useContext(AuthContext);
  const [userProfileImage, setUserProfileImage] = useState('');
  const [previewImage, setPreviewImage] = useState('');
  const [uploadError, setUploadError] = useState('');
  const [errors, setErrors] = useState<any>({});
  const [Country, setCountry] = useState<Country[]>([]);
  const [nationality, setNationality] = useState([]);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [experiance, setExperience] = useState([]);
  const [jobStatus, setJobStatus] = useState(user?.jobStatus);
  const [userData, SetUserData] = useState<userData>({
    name: '',
    email: '',
    contact_no: '',
    date_of_birth: '',
    bio: '',
    gender: '',
    years_of_experience: '',
    currency: '',
    current_salary: '',
    desired_salary: '',
    nationality: '',
    countries: '',
    cities: '',
    current_position: '',
    id: 0,
    slug: '',
    sector: '',
    industry: '',
  });
  const [industry, setIndustry] = useState([]);
  const [sector, setSector] = useState([]);
  const [cities, setCities] = useState([]);
  const queryClient = useQueryClient();
  const {data: userDetails} = useGetSingleUserDetails(user?.id);

  useEffect(() => {
    if (user) {
      SetUserData(userDetails);
      setUserProfileImage(userDetails?.profile_image);
      setJobStatus(userDetails?.job_status);
    }
    getAllCountries()
      .then(res => {
        if (res) {
          setCountry(res);
        } else {
          setCountry([]);
        }
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });

    getAllNationality()
      .then(res => {
        if (res) {
          setNationality(res);
        } else {
          setNationality([]);
        }
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });

    getExperiance()
      .then(res => {
        if (res.status == true) {
          setExperience(res.data);
        } else {
          setExperience([]);
        }
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });

    getIndustries()
      .then(res => {
        if (res.success == true) {
          setIndustry(res.data);
        } else {
          setIndustry([]);
        }
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });

    getAllSectors()
      .then(res => {
        if (res.success == true) {
          setSector(res.sectors);
        } else {
          setSector([]);
        }
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });
  }, [user, userDetails]);

  useEffect(() => {
    if (userData?.countries) {
      (async () => {
        const response = await getSingleCountryAllCities(userData?.countries);

        if (response) {
          setCities(response?.data);
        } else {
          setCities([]);
        }
      })();
    }
  }, [userData?.countries]);

  const submitForm = (event: any) => {
    event.preventDefault();
    if (validateForm()) {
      updateEmployee(user?.id, userData, userProfileImage)
        .then(res => {
          if (res.status) {
            notification.success({
              message: res.message,
            });
            window.location.reload();
          } else {
            notification.error({
              message: res.message,
            });
          }
        })
        .catch(error => {
          ErrorHandler.showNotification(error);
        });
    }
  };

  const validateForm = () => {
    const newErrors: any = {};
    let isValid = true;
    if (!userData?.name || userData?.name.trim() === '') {
      newErrors.name = 'Name is required';
      isValid = false;
    }
    if (!userData?.contact_no || userData?.contact_no.trim() === '') {
      newErrors.contact_no = 'Contact No is required';
      isValid = false;
    }
    if (!userData?.nationality) {
      newErrors.nationality = 'Nationality is required';
      isValid = false;
    }
    if (!userData?.countries || userData?.countries.trim() === '') {
      newErrors.where_currently_based = 'Where Currently Based is required';
      isValid = false;
    }
    if (!userData?.date_of_birth || userData?.date_of_birth.trim() === '') {
      newErrors.date_of_birth = 'Date of Birth is required';
      isValid = false;
    }
    if (!userData?.gender || userData?.gender.trim() === '') {
      newErrors.gender = 'gender is required';
      isValid = false;
    }
    if (!userData?.current_position || userData?.current_position.trim() === '') {
      newErrors.current_position = 'Current Position is required';
      isValid = false;
    }
    if (Number(userData?.current_salary) < 0) {
      newErrors.current_salary = 'Salary cannot be negative. Please enter a valid amount.';
    }
    if (Number(userData?.desired_salary) < Number(userData?.current_salary)) {
      newErrors.desired_salary = 'Desired Salary cannot be Less than Current Salary';
      isValid = false;
    }
    if (!userData?.bio || userData?.bio.trim() === '') {
      newErrors.bio = 'Bio is required';
      isValid = false;
    }
    setErrors(newErrors);
    return isValid;
  };

  const EditChange = (name: string, value: string) => {
    if (name === 'countries') {
      const selectedCountryData = Country.find(cnt => cnt.id === Number(value) && cnt.status === 'active');
      const currency = selectedCountryData ? selectedCountryData.currency : userData?.currency;
      SetUserData(prevState => ({
        ...prevState,
        [name]: value,
        currency: currency,
      }));
    } else {
      SetUserData(prevState => ({
        ...prevState,
        [name]: value,
      }));
    }
  };

  const extractCurrencyCode = (value: string) => {
    if (!value) {
      return '';
    }
    const currencyCode = value.split('(')[1]?.trim()?.slice(0, -1);
    return currencyCode || '';
  };

  const handleImageChange = (e: any) => {
    const file = e.target.files[0];
    setUserProfileImage(file);
    if (file) {
      if (file.type.includes('image')) {
        const reader = new FileReader();
        reader.onloadend = () => {
          if (reader.result !== null) {
            setPreviewImage(reader.result.toString());
            setUploadError('');
          }
        };
        reader.readAsDataURL(file);
      } else {
        setUploadError('Please upload an image file (JPEG,JPG,PNG).');
      }
    } else {
      setPreviewImage('');
    }
  };

  const handleJobStatusUpdate = (e: any, status: any) => {
    e.preventDefault();
    if (user?.id) {
      const data = {
        id: user?.id,
        value: status,
      };
      updateJobStatus(data)
        .then(() => {
          queryClient.invalidateQueries([QUERY_GET_SINGLE_USER_DETAILS]);
          setIsDropdownOpen(false);
          setJobStatus(status);
          message.success('Saved');
        })
        .catch(error => {
          console.error(error);
        });
    }
  };

  const submitSectorAndIndustry = (event: any) => {
    event.preventDefault();
    const data = {
      sector: userData?.sector,
      industry: userData?.industry,
    };
    updateUserSectorAndIndustry(user?.id, data)
      .then(res => {
        if (res.status) {
          notification.success({
            message: res.message,
          });
        } else {
          notification.error({
            message: res.message,
          });
        }
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });
  };
  function getCurrentDate() {
    const now = new Date();
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  return (
    <>
      <div className={'dash-right'}>
        <h1>
          My <span className="span-color">Profile</span>
        </h1>
        <div className="row m-column-reverse">
          <div className="col-sm-7">
            <ul className="list-loc m-m-0 mt-4">
              <li>
                <Link href="/employees/myprofile">Overview</Link>
              </li>
              <li className="active">
                <Link href="/employees/myprofile/profile">Profile</Link>
              </li>
              <li>
                <Link href="/employees/myprofile/jobpreferences">Preferences</Link>
              </li>
            </ul>
          </div>
          <div className="col-sm-5">
            <ul className="blue-text-line mt-4 text-right">
              <li>
                <Link target="_blank" href={'/candidate-profile/' + userData?.slug}>
                  View Public Profile
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className={`data-management m-p-10 p-0 ${styles.profile_container}`}>
          <div className="work-experience-fieild m-p-10">
            <div className="accordion text-left" id="accordionExample">
              <div className="up-down-item m-auto">
                <div className={`accordion-item left-accor ${styles.job_status}`}>
                  <h2 className="accordion-header" id="headingOne">
                    <button
                      className={`accordion-button border-green collapsed  ${
                        jobStatus === 'ready_to_interview' ? 'ready-to-interview-class ' : ''
                      } ${jobStatus === 'not_looking' ? 'not-looking-class' : ''} ${
                        jobStatus === 'open_to_offer' ? 'Open-to-Offers-class' : ''
                      }`}
                      type="button"
                      data-bs-toggle="collapse"
                      data-bs-target="#collapseOne"
                      aria-expanded="true"
                      aria-controls="collapseOne"
                      onClick={() => setIsDropdownOpen(!isDropdownOpen)}>
                      <img
                        src={
                          !jobStatus || jobStatus === 'ready_to_interview'
                            ? '/images/icon-1.png'
                            : jobStatus === 'not_looking'
                              ? '/images/icon-3.png'
                              : jobStatus === 'open_to_offer'
                                ? '/images/icon-2.png'
                                : ''
                        }
                        alt="icon-1"
                        className="w-16"
                        width={16}
                        height={16}
                      />
                      {!jobStatus || jobStatus === 'ready_to_interview'
                        ? 'Ready To Interview'
                        : jobStatus === 'not_looking'
                          ? 'Not Looking'
                          : jobStatus === 'open_to_offer'
                            ? 'Open to Offers'
                            : jobStatus}
                    </button>
                  </h2>
                  <div
                    id="collapseOne"
                    className={`accordion-collapse collapse ${isDropdownOpen ? 'show' : ''}`}
                    aria-labelledby="headingOne"
                    data-bs-parent="#accordionExample">
                    <div
                      className="accordion-body"
                      onClick={e => handleJobStatusUpdate(e, 'ready_to_interview')}
                      style={{cursor: 'pointer'}}>
                      <h5>
                        <img src="/images/icon-1.png" alt="icon-1" className="w-24" width={24} height={24} /> Ready to
                        Interview
                      </h5>
                      <p>Actively looking for openings & ready to interview.</p>
                    </div>
                    <div
                      className="accordion-body f-D57B11"
                      onClick={e => handleJobStatusUpdate(e, 'open_to_offer')}
                      style={{cursor: 'pointer'}}>
                      <h5>
                        <img src="/images/icon-2.png" alt="icon-2" className="w-24" width={24} height={24} /> Open to
                        Offers
                      </h5>
                      <p>Not actively looking but open to hear about new opportunities. </p>
                    </div>
                    <div
                      className="accordion-body f-D04E4F"
                      onClick={e => handleJobStatusUpdate(e, 'not_looking')}
                      style={{cursor: 'pointer'}}>
                      <h5>
                        <img src="/images/icon-3.png" alt="icon-3" className="w-24" width={24} height={24} /> Not
                        Looking
                      </h5>
                      <p>Not actively looking for opportunities right now.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="row">
              <div className="col-lg-3 col-md-3">
                <div className="left-text-fieild">
                  <h3 className=" m-center">About</h3>
                  <p className="c-747474  m-center">Tell us about yourself</p>
                </div>
              </div>
              <div className="col-lg-9 col-md-9">
                <form className="form-experience-fieild" onSubmit={submitForm}>
                  <div className="row">
                    <div className="col-lg-12">
                      <p className="f-12 c-2C2C2C m-center">Profile Picture</p>
                      <UserProfileImage user={user} showUploadButton={true} className="mxw-unset" />
                    </div>
                  </div>
                  <div className="row">
                    <div className="col-sm-6">
                      <div className="form_field_sec">
                        <input
                          type="text"
                          placeholder="Name"
                          onChange={event => EditChange('name', event.target.value)}
                          name="name"
                          className="fild-des"
                          maxLength={30}
                          defaultValue={userData?.name}
                        />
                        <label>Your Name*</label>
                      </div>
                      {errors.name && <p className="error">{errors.name}</p>}
                    </div>
                    <div className="col-sm-6">
                      <label>Email ID*</label>
                      <input
                        type="email"
                        placeholder="Email"
                        className="fild-des filed_disabled addClass"
                        defaultValue={userData?.email}
                        readOnly
                      />
                    </div>
                  </div>
                  <div className="row">
                    <div className="col-sm-6">
                      <label>Nationality*</label>
                      <select
                        className="fild-des"
                        name="nationality"
                        onChange={event => EditChange('nationality', event.target.value)}
                        value={userData?.nationality}>
                        <option selected>Select Nationality</option>
                        {nationality.map((nationalities: any, index) => (
                          <option value={nationalities.id} key={index}>
                            {nationalities.country_name}
                          </option>
                        ))}
                      </select>
                      {errors.nationality && <p className="error">{errors.nationality}</p>}
                    </div>
                    <div className="col-sm-6 form-design-num">
                      <label>Contact Number*</label>
                      <PhoneInput
                        country={'ae'}
                        value={'' + userData?.contact_no}
                        inputClass="fild-des-contact"
                        onChange={(value: any) => EditChange('contact_no', value)}
                        inputProps={{name: 'contact_no'}}
                      />
                      {errors.contact_no && <p className="error error-cs">{errors.contact_no}</p>}
                    </div>
                  </div>

                  <div className="row">
                    <div className="col-sm-6">
                      <label>Date of Birth*</label>
                      <input
                        type="date"
                        placeholder="(+971) 123 – 456 – 7890"
                        className="fild-des"
                        name="date_of_birth"
                        max={getCurrentDate()}
                        defaultValue={userData?.date_of_birth}
                        onChange={event => EditChange('date_of_birth', event.target.value)}
                      />
                      {errors.date_of_birth && <p className="error">{errors.date_of_birth}</p>}
                    </div>
                    <div className="col-sm-6">
                      <label>Gender</label>
                      <select
                        className="fild-des"
                        name="gender"
                        onChange={event => EditChange('gender', event.target.value)}
                        value={userData?.gender}>
                        <option value="" selected>
                          Select Gender
                        </option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                        <option value="other">Other</option>
                      </select>
                      {errors.gender && <p className="error">{errors.gender}</p>}
                    </div>
                  </div>

                  <div className="row">
                    <div className="col-sm-6">
                      <label>Where are you currently based?*</label>
                      <select
                        className="fild-des"
                        name="countries"
                        onChange={event => EditChange('countries', event.target.value)}
                        value={userData?.countries}>
                        <option selected>Select Country</option>
                        {Country.map((cnt: any, index) => {
                          if (cnt.status === 'active') {
                            return (
                              <option key={index} value={cnt.id}>
                                {cnt.country_name}
                              </option>
                            );
                          }
                        })}
                      </select>
                      {errors.where_currently_based && <p className="error">{errors.where_currently_based}</p>}
                    </div>

                    <div className="col-sm-6">
                      <label>Where are you in job search?*</label>
                      <select
                        className="fild-des"
                        name="cities"
                        onChange={event => EditChange('cities', event.target.value)}
                        value={userData?.cities}>
                        <option selected>Select City</option>
                        {cities.length > 0 &&
                          cities?.map((city: any, index) => (
                            <option key={index} value={city.id}>
                              {city.city_name}
                            </option>
                          ))}
                      </select>
                      {errors.where_job_search && <p className="error">{errors.where_job_search}</p>}
                    </div>
                  </div>

                  <div className="row">
                    <div className="col-sm-6">
                      <label>Your Current Role*</label>
                      <input
                        type="text"
                        className="fild-des m-0"
                        name="current_position"
                        maxLength={50}
                        onChange={event => EditChange('current_position', event.target.value)}
                        value={userData?.current_position}
                      />
                      {errors.current_position && <p className="error">{errors.current_position}</p>}
                    </div>
                    <div className="col-sm-6">
                      <label>Years of Experience</label>
                      <select
                        className="fild-des"
                        name="years_of_experience"
                        onChange={event => EditChange('years_of_experience', event.target.value)}
                        value={userData?.years_of_experience}>
                        <option value="">Select Experience</option>
                        {experiance.length > 0 ? (
                          experiance.map((experianceData: any, index: number) => {
                            const experienceYears = experianceData.name.split('-');
                            let optionValue = '';
                            if (experienceYears[0] === 'fresher') {
                              optionValue = 'Fresher';
                            } else if (experienceYears[0] === '0' && experienceYears[1] === '1') {
                              optionValue = `${experienceYears[0]}-${experienceYears[1]} year`;
                            } else {
                              optionValue = `${experienceYears[0]} years`;
                            }
                            return (
                              <option value={experianceData.name} key={index}>
                                {optionValue}
                              </option>
                            );
                          })
                        ) : (
                          <option value="">No Data Found</option>
                        )}
                      </select>
                    </div>
                  </div>

                  <div className="row">
                    <div className="col-sm-6">
                      <label>Current Salary</label>
                      <div className="salary m-0">
                        <div className="currency_dropdown">{extractCurrencyCode(userData?.currency)}</div>
                        <input
                          type="number"
                          className="fild-des m-0"
                          name="current_salary"
                          onChange={event => {
                            const inputValue = event.target.value;
                            if (inputValue.length <= 8) {
                              EditChange('current_salary', inputValue);
                            }
                          }}
                          value={userData?.current_salary}
                          maxLength={8}
                          min={0}
                        />
                      </div>
                    </div>
                    <div className="col-sm-6">
                      <label>Desired Salary</label>
                      <div className="salary m-0">
                        <div className="currency_dropdown">{extractCurrencyCode(userData?.currency)}</div>
                        <input
                          type="number"
                          className="fild-des m-0"
                          name="desired_salary"
                          onChange={event => {
                            const inputValue = event.target.value;
                            if (inputValue.length <= 10) {
                              EditChange('desired_salary', inputValue);
                            }
                          }}
                          value={userData?.desired_salary}
                          maxLength={10}
                          min={userData?.current_salary}
                        />
                      </div>
                      {errors.desired_salary && <p className="error error-cs">{errors.desired_salary}</p>}
                    </div>
                  </div>
                  <div className="form_field_sec mt-2">
                    <textarea
                      placeholder="Enter Your Bio"
                      name="bio"
                      onChange={event => EditChange('bio', event.target.value)}
                      className="fild-des"
                      value={userData?.bio}></textarea>
                    <label>Your Bio**</label>
                  </div>
                  {errors.bio && <p className="error">{errors.bio}</p>}
                  <div className="text-right mt-3">
                    <a className="cancel">Cancel</a>
                    <button className="save">Save</button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        <Education />

        <WorkExperiance />

        <Portfolio />

        <SocailLinks />

        <Skills />

        <Language />

        <div className="mt-2">
          <div className="work-experience-fieild">
            <div className="row">
              <div className="col-lg-3 col-md-3">
                <div className="left-text-fieild">
                  <h3>Your Industry & Sector</h3>
                  <p className="c-747474">
                    Enter The sector and industry
                    <br /> employers
                  </p>
                </div>
              </div>
              <div className="col-lg-9 col-md-9">
                <form className="form-experience-fieild" onSubmit={submitSectorAndIndustry}>
                  <div className="row">
                    <div className="col-sm-6">
                      <label>Industry</label>
                      <select
                        className="fild-des"
                        name="industry"
                        onChange={event => EditChange('industry', event.target.value)}
                        value={userData?.industry}>
                        <option selected value="">
                          Select Industry
                        </option>
                        {industry.map((indus: any, index) => (
                          <option value={indus.id} key={index}>
                            {indus.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="col-sm-6">
                      <label>Sector</label>
                      <select
                        className="fild-des"
                        name="sector_name"
                        onChange={event => EditChange('sector', event.target.value)}
                        value={userData?.sector}>
                        <option value="" selected>
                          Select Sector
                        </option>
                        {sector.map((sect: any, index) => (
                          <option value={sect.id} key={index}>
                            {sect.sector_name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                  <div className="text-right mt-3">
                    <a className="cancel">Cancel</a>
                    <button className="save">Save</button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
