import React, { useState, useEffect, useContext } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation'
import { notification } from 'antd';
import AuthContext from '@/Context/AuthContext';
import JobListItem from '@/components/Common/JobListItem';
import { getAllJobsWithId, getTotalApplicationsForjob, getTotalSavedjob } from '@/lib/employeeapi';
import {
  updateFirstTimePopupStatus,
  updateUnlockInstantApply,
  getNotifications,
  getTotalMessageUnReadCount,
} from '@/lib/frontendapi';
import WelcomeModal from '@/components/Common/Modals/WelcomeModal';
import ErrorHandler from '@/lib/ErrorHandler';
import PopupModal from '../../../components/Common/PopupModal';
import ProfileChecklist from './ProfileChecklist';

export default function Dashboard() {
  const { user } = useContext(AuthContext);
  const [showWelcomeModal, setShowWelcomeModal] = useState(false);
  const [allJobs, setAllJobs] = useState([]);
  const [totalAppliedJobs, setTotalAppliedJobs] = useState('');
  const [getSavedJobs, setGetSavedJobs] = useState('');
  const [completionPercentage, setCompletionPercentage] = useState(0);
  const [totalNotifications, setTotalNotifications] = useState([0]);
  const [totalUnReadMessageCount, setTotalUnReadMessageCount] = useState(0);
  const pathname = usePathname()
  const [checkboxes, setCheckboxes] = useState({
    resume: false,
    updateInfo: false,
    professionalInfo: false,
    applyOpening: false,
  });

  const updateFirstTimePopup = async (userId: any) => {
    try {
      if (user?.first_login == 0) {
        setShowWelcomeModal(true);
        const res = await updateFirstTimePopupStatus(userId);
      }
    } catch (err) {
      ErrorHandler.showNotification(err);
    }
  };

  const updateProfileCompletion = () => setCompletionPercentage(completionPercentage);

  const getImageSrc = () => {
    const base_url = process.env.NEXT_PUBLIC_BASE_URL;
    return base_url + `images/profile-${Math.min(Math.ceil(completionPercentage / 20), 5)}.png`;
  };


  useEffect(() => {
    if (user?.id) {
      getAllJobsWithId(user?.id)
        .then(({ data }) => {
          setAllJobs(data);
        })
        .catch(error => ErrorHandler.showNotification(error));

      getTotalSavedjob(user?.id)
        .then(({ data }) => setGetSavedJobs(data))
        .catch(error => ErrorHandler.showNotification(error));

      handleTotalSavedJobs();

      fetch(`${process.env.NEXT_PUBLIC_API_URL}/users/showCompletionPercentage/${user?.id}`)
        .then(response => response.json())
        .then(({ data, checkboxes }) => {
          setCompletionPercentage(data);
          setCheckboxes(checkboxes);
        })
        .catch(error => ErrorHandler.showNotification('Error:', error));

      updateProfileCompletion();

      const notification_data = { notify_to: user?.id };
      getNotifications(notification_data)
        .then(({ status, data }) => setTotalNotifications(status === true ? data.length : []))
        .catch(error => {
          ErrorHandler.showNotification(error)
        });
    }
  }, [user]);

  useEffect(() => {
    const message_data = { sender_id: user?.id };
    getTotalMessageUnReadCount(message_data)
      .then(({ status, total_unread_message_count }) =>
        setTotalUnReadMessageCount(status === true ? total_unread_message_count : 0),
      )
      .catch(error => ErrorHandler.showNotification(error));
    updateFirstTimePopup(user?.id);
  }, [user]);

  const handleTotalSavedJobs = () => {
    getTotalApplicationsForjob(user?.id)
      .then(({ data }) => setTotalAppliedJobs(data))
      .catch(error => ErrorHandler.showNotification(error));
  };

  const handleProfileNotComplete = () => {
    notification.error({ message: 'Please complete your profile!' });
  };

  const handleUnlockInstantApply = () => {
    updateUnlockInstantApply(user?.id)
      .then(() => { })
      .catch(error => ErrorHandler.showNotification(error));
  };

  return (
    <>
      <div className="dash-right">
        <h1>
          Welcome <span className="span-color">{user?.name && user?.name}</span>
        </h1>
        <div className="row mt-4">
          <div className="col-lg-3 col-md-6 col-6">
            <Link href="/employees/applications" prefetch={false}>
              <div className="dash-card d-c-1">
                <div className="row">
                  <div className="col-sm-4 col-12">
                    <h5 className="dash-card-h5">{totalAppliedJobs ? totalAppliedJobs : 0}</h5>
                  </div>
                  <div className="col-sm-8 col-12">
                    <div className="text-right">
                      <h6>Applied Jobs</h6>
                      <p className="f-12">View All</p>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          </div>
          <div className="col-lg-3 col-md-6 col-6">
            <Link href="/employees/jobs/savedjobs" prefetch={false}>
              <div className="dash-card d-c-2">
                <div className="row">
                  <div className="col-sm-4 col-12">
                    <h5 className="dash-card-h5">{getSavedJobs ? getSavedJobs : 0}</h5>
                  </div>
                  <div className="col-sm-8 col-12">
                    <div className="text-right">
                      <h6>Saved Jobs</h6>
                      <p className="f-12">View All</p>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          </div>
          <div className="col-lg-3 col-md-6 col-6">
            <Link href="/employees/messages" prefetch={false}>
              <div className="dash-card d-c-4">
                <div className="row">
                  <div className="col-sm-4 col-12">
                    <h5 className="dash-card-h5">{totalUnReadMessageCount ? totalUnReadMessageCount : 0}</h5>
                  </div>
                  <div className="col-sm-8 col-12">
                    <div className="text-right">
                      <h6>Messages</h6>
                      <p className="f-12">View All</p>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          </div>
          <div className="col-lg-3 col-md-6 col-6">
            <Link href="/employees/notifications" prefetch={false}>
              <div className="dash-card d-c-3">
                <div className="row">
                  <div className="col-sm-4 col-12">
                    <h5 className="dash-card-h5">{totalNotifications ? totalNotifications : 0}</h5>
                  </div>
                  <div className="col-sm-8 col-12">
                    <div className="text-right">
                      <h6>Notifications</h6>
                      <p className="f-12">View All</p>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          </div>
        </div>
        <p className="over mt-4 mb-4">New Openings</p>
        <div className="row">
          <div className="col-lg-8 col-md-12">
            {allJobs?.length > 0 &&
              allJobs.map((jobs: any, index: any) => <JobListItem job={jobs} index={index} key={index} jobInCityName={pathname.substring(1)} jobPosting={false} />)}
            <center>
              <p className="explore-jobs mt-4">
                {' '}
                <Link href="/jobs-in-gulf">Explore All Jobs</Link>{' '}
              </p>
            </center>
          </div>

          <div className="col-lg-4 col-md-12">
            <ProfileChecklist
              checkboxes={checkboxes}
              handleUnlockInstantApply={handleUnlockInstantApply}
              handleProfileNotComplete={handleProfileNotComplete}
              completionPercentage={completionPercentage}
              user={user}
              getImageSrc={getImageSrc}
            />
          </div>
        </div>

        <PopupModal
          show={showWelcomeModal}
          handleClose={() => setShowWelcomeModal(false)}
          customclass={'add_company_signup_popup modal-lg big-size body-sp-0 b-r-30 up-down-ani'}
          closebtnclass={'close-x mt-2'}
          closebtnicon={'icon'}>
          <WelcomeModal onClose={() => setShowWelcomeModal(false)} />
        </PopupModal>
      </div>
    </>
  );
}
