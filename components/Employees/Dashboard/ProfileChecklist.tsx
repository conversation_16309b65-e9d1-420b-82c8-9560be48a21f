import React, {useState, useEffect} from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {Tooltip} from 'antd';
import moment from 'moment';
import {getFutureInterviewSchedule} from '@/lib/frontendapi';

interface ChecklistProps {
  user: any;
  completionPercentage: number;
  getImageSrc: any;
  checkboxes: any;
  handleUnlockInstantApply?: any;
  handleProfileNotComplete?: any;
}

export default function ProfileChecklist({
  user,
  completionPercentage = 0,
  getImageSrc,
  checkboxes,
  handleUnlockInstantApply,
  handleProfileNotComplete,
}: ChecklistProps) {
  const [futureInterviewsSchedule, setFutureInterviewSchedule] = useState([]);

  useEffect(() => {
    getFutureInterviewSchedule(user?.id)
      .then(res => {
        if (res.status == true) {
          setFutureInterviewSchedule(res.interviews);
        } else {
          setFutureInterviewSchedule([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  }, [user?.id]);

  return Number(user?.unlock_instant_apply) === 1 && user?.profile_complete_percentage == 100 ? (
    <div className="col-lg-12 col-md-6 mb-4">
      <div className="blue-box">
        <div className="row">
          <div className="col-10">
            <p className="f-26 c-fff w-700">Interviews</p>
            <p className="f-22  c-fff">Coming up...</p>
            {futureInterviewsSchedule.length > 0 ? (
              ''
            ) : (
              <p className="f-18  c-fff">You don't have any interviews scheduled right now....</p>
            )}
          </div>
          <div className="col-2 text-center">
            <Tooltip
              color={'#EBF4FF'}
              placement="bottom"
              title={<span style={{color: '#4D4D4D'}}>Recent Interviews Scheduled.</span>}>
              <span className="custom-tooltip-container" style={{position: 'relative', display: 'inline-block'}}>
                <i className="fa-solid fa-circle-info c-D9D9D9"></i>
              </span>
            </Tooltip>
          </div>
        </div>
        {futureInterviewsSchedule.length > 0
          ? futureInterviewsSchedule.slice(0, 2).map((interviews_data: any, index: any) => {
              return (
                <div key={index}>
                  <div className="bg-D9D9D9 work-senior p-2 text-center mt-2 mb-3">
                    <p className="f-12 c-2C2C2C w-700 mb-sp c-0070F5">
                      {moment(interviews_data.interview_schedule_date).format('MMMM')}
                    </p>
                    <h3 className="f-54 c-191919 mt-3 mb-3 c-0070F5">
                      {moment(interviews_data.interview_schedule_date).format('D')}
                    </h3>
                    <p className="f-12 c-2C2C2C w-700  mb-sp c-0070F5">
                      {moment(interviews_data.interview_schedule_date).format('dddd')}
                    </p>
                  </div>
                  <p className="f-18 c-fff w-600 ">{interviews_data.job_title}</p>
                  <p className="f-16 w-600 c-fff">{interviews_data.applicants_name}</p>
                  <ul className="full-time f-12-list">
                    <li>
                      <i className="fa-regular fa-clock"></i>{' '}
                      {moment(interviews_data.interview_schedule_from_time, 'hh:mm A').format('hh:mm A')} -{' '}
                      {moment(interviews_data.interview_schedule_to_time, 'hh:mm A').format('hh:mm A')}
                    </li>
                  </ul>
                </div>
              );
            })
          : ''}
        <p className="mb-0 mt-4">
          <Link href="/employees/messages/interviews" className="f-16 w-700 c-fff">
            {' '}
            VIEW All <i className="fa-solid fa-arrow-right"></i>
          </Link>
        </p>
      </div>
    </div>
  ) : (
    <div className="profile-checklist">
      <div className="row">
        <div className="col-4 pr-0">
          <img src={getImageSrc()} alt="profile-4" width={90} height={79} loading="lazy" />
        </div>
        <div className="col-8">
          <h5>Profile Checklist</h5>
          <h6>You're almost there!</h6>
        </div>
      </div>
      <ul className="Verify">
        <li>
          <i className={`fa-regular ${completionPercentage >= 20 ? 'fa-circle-check' : 'fa-circle'}`}></i> Verify your
          account
        </li>
        <li>
          <i
            className={`fa-regular ${
              checkboxes && checkboxes?.updateInfo === true ? 'fa-circle-check' : 'fa-circle'
            }`}></i>{' '}
          <Link
            href="/employees/myprofile/profile"
            id="verify1"
            prefetch={false}
            style={{color: `${checkboxes?.updateInfo === true ? '#ffffff' : '#ffffff7a'}`}}>
            Update your information
          </Link>
        </li>
        <li>
          <i
            className={`fa-regular ${
              checkboxes && checkboxes?.professionalInfo === true ? 'fa-circle-check' : 'fa-circle'
            }`}></i>{' '}
          <Link
            href="/employees/myprofile/profile"
            id="verify1"
            prefetch={false}
            style={{color: `${checkboxes?.professionalInfo === true ? '#ffffff' : '#ffffff7a'}`}}>
            Professional information
          </Link>
        </li>
        <li>
          <i
            className={`fa-regular ${
              checkboxes && checkboxes?.resume === true ? 'fa-circle-check' : 'fa-circle'
            }`}></i>{' '}
          <Link
            href="/employees/resume"
            id="verify1"
            style={{color: `${checkboxes?.resume === true ? '#ffffff' : '#ffffff7a'}`}}>
            Upload your resume
          </Link>
        </li>
        <li style={{color: `${checkboxes?.applyOpening === true ? '#ffffff' : '#ffffff7a'}`}}>
          <i
            className={`fa-regular ${
              checkboxes && checkboxes?.applyOpening === true ? 'fa-circle-check' : 'fa-circle'
            }`}></i>{' '}
          Apply for an opening
        </li>
      </ul>
      {checkboxes.applyOpening === true &&
      checkboxes.resume === true &&
      checkboxes?.professionalInfo === true &&
      checkboxes?.updateInfo === true ? (
        <button className="signup-cards blue-btn f-22" onClick={handleUnlockInstantApply}>
          <img src="/images/lock.png" alt="lock" className="lock-img" width={16} height={21} loading="lazy" />
          Unlock Instant Apply
        </button>
      ) : (
        <button className="signup-cards white-btn f-22" onClick={handleProfileNotComplete}>
          <img src="/images/lock.png" alt="lock" className="lock-img" width={16} height={21} loading="lazy" />
          Unlock Instant Apply
        </button>
      )}
    </div>
  );
}
