import React, { useState, useEffect, use } from 'react';
import PopupModal from '../../../components/Common/PopupModal';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getCurrentUserData } from "../../../lib/session";
import { getAllJobsWithId, getTotalApplicationsForjob, getTotalSavedjob } from '../../../lib/employeeapi';
import { toggleFavoriteJob, removeFavoriteJob, applyJob, getSingleUserDetails, updateFirstTimePopupStatus, getCurrentUserDetails, updateUnlockInstantApply, getNotifications, getTotalMessageUnReadCount } from '../../../lib/frontendapi';
import { HtmlEditor } from "../../Common/HtmlEditor";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import Link from 'next/link'
import SuccessToast from "../../Common/showSuccessTostrMessage";
import ErrorToast from "../../Common/showErrorTostrMessage";
import Image from 'next/image';


interface User {
  name: string;
  email: string;
  contact_no: string;
  date_of_birth: string;
  bio: string;
  gender: string;
  years_of_experience: string;
  current_salary: string;
  desired_salary: string;
  where_currently_based: string;
  current_position: string;
  description: string;
  resume_pdf_path: string;
  default_resume: string;
  unlock_instant_apply: string;
  country_name: string;
  resumedate: string;
}

export default function Dashboard() {
  const [showPopup, setShowPopup] = useState(false);
  const [modalConfirm1, setModalConfirm1] = useState(false);
  const [modalConfirm2, setModalConfirm2] = useState(false);
  const [current_user_id, setCurrentUserId] = useState(false);
  const [current_user_name, setCurrentUserName] = useState('');
  const [allJobs, setAllJobs] = useState([]);
  // const [showAllJobs, setShowAllJobs] = useState(false);
  // const jobsToRender = showAllJobs ? allJobs.slice(0, 3) : allJobs;
  const [popupShown, setPopupShown] = useState(false);
  const [selectedJobId, setSelectedJobId] = useState('');
  const [selecteduserId, setSelectedUserId] = useState('');
  const [selectedcompany, setSelectedcompany] = useState('');
  const [selectCompanyId, setcompanyId] = useState('');
  const [appliedJobs, setAppliedJobs] = useState<string[]>([]);
  const [totalAppliedJobs, setTotalAppliedJobs] = useState('');
  const [getsavedjobs, setGetsavedjobs] = useState('');
  const [modalConfirm6, setModalConfirm6] = useState(false);
  const [completionPercentage, setCompletionPercentage] = useState(0);
  const [isInstantApplyUnlocked, setIsInstantApplyUnlocked] = useState(false);
  const [selectedResume, setSelectedResume] = useState(null);
  const [selectedCover, setSelectedCover] = useState(null);
  const [errorMessage, setErrorMessage] = useState('');
  const [covererrorMessage, setcoverErrorMessage] = useState('');
  const [totalNotifications, setTotalNotifications] = useState([]);
  const [descriptionError, setDescriptionError] = useState<string>("");
  const MAX_DESCRIPTION_WORDS = 250;
  const [checkboxes, setCheckboxes] = useState({
    resume: false,
    updateInfo: false,
    professionalInfo: false,
    applyOpening: false
  });
  const [showNewButton, setShowNewButton] = useState(false);
  const [modalConfirm3, setModalConfirm3] = useState(false);
  const currentDate = new Date();
  const [selectedFileName, setSelectedFileName] = useState('');
  const [allCountryJobs, setAllCountryJobs] = useState([]);
  const [totalUnReadMessageCount, setTotalUnReadMessageCount] = useState(0);
  const [showPopupunsave, setShowPopupunsave] = useState(false);
  const [showPopupunerror, setShowPopupunerror] = useState(false);
  const [showmessage, setShowmessage] = useState('');

  const handleFileChange = (event: any) => {
    const file = event.target.files[0];
    setSelectedFileName(file.name);
    setSelectedResume(file);
    if (file) {
      const allowedFormats = ['pdf', 'doc', 'docx'];
      const fileExtension = file.name.split('.').pop().toLowerCase();
      if (!allowedFormats.includes(fileExtension)) {
        setErrorMessage('Invalid file. Only PDF or DOC files are allowed.');
        setSelectedResume(null);
      } else {
        setErrorMessage('');

      }
    }
  };

  const handleCoverChange = (event: any) => {
    const file = event.target.files[0];
    setSelectedCover(file);
    if (file) {
      const allowedFormats = ['pdf', 'doc', 'docx'];
      const fileExtension = file.name.split('.').pop().toLowerCase();
      if (!allowedFormats.includes(fileExtension)) {
        setcoverErrorMessage('Invalid file. Only PDF or DOC files are allowed.');
        setSelectedCover(null);
      } else {
        setcoverErrorMessage('');

      }
    }
  };


  const modalConfirmOpen1 = () => {
    setModalConfirm1(true);
  }

  const modalConfirmOpen6 = () => {
    setModalConfirm6(true);
  }

  const modalConfirmClose6 = () => {
    setModalConfirm6(false);
  }

  const modalConfirmOpen3 = () => {
    setModalConfirm3(true);
  }

  const modalConfirmClose3 = () => {
    setModalConfirm3(false);
  }

  const updateFirstTimePopup = () => {
    const current_user_data: any = getCurrentUserData();
    updateFirstTimePopupStatus(current_user_data.id)
      .then(res => {
        if (res.status == true) {
          setModalConfirm6(false);
        } else {
          setModalConfirm6(true);
        }
      })
      .catch(err => {
        console.log(err);
      });
  }

  useEffect(() => {
    setTimeout(() => {
      modalConfirmOpen1();
    }, 2000);
    const current_user_data: any = getCurrentUserData();
    current_user_data.username ? setCurrentUserName(current_user_data.username) : setCurrentUserName('');
    current_user_data.id ? setCurrentUserId(true) : setCurrentUserId(false);
    getAllJobsWithId(current_user_data.id)
      .then((response) => {
        setAllJobs(response.data);
        setAllCountryJobs(response.countrybasedjobs);

        // if (response.data.length > 3) {
        //   setShowAllJobs(true);
        // }
      })
      .catch((error) => {
        console.error(error);
      });

    getTotalApplicationsForjob(current_user_data.id)
      .then((response) => {
        setTotalAppliedJobs(response.data);
      })
      .catch((error) => {
        console.error(error);
      });

    getTotalSavedjob(current_user_data.id)
      .then((response) => {
        setGetsavedjobs(response.data);
      })
      .catch((error) => {
        console.error(error);
      });

    getCurrentUserDetails(current_user_data.id)
      .then(res => {
        if (res.status == true) {
          if (res.user.first_login == '0') {
            setTimeout(() => {
              modalConfirmOpen6();
            }, 2000);
          }
        } else {

        }
      })
      .catch(err => {
        console.log(err);
      });
    fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/users/showCompletionPercentage/${current_user_data.id}`
    )
      .then((response) => response.json())
      .then((data) => {
        setCompletionPercentage(data.data);
        setCheckboxes({
          resume: data.checkboxes.resume,
          updateInfo: data.checkboxes.updateInfo,
          professionalInfo: data.checkboxes.professionalInfo,
          applyOpening: data.checkboxes.applyOpening,
        });
      })
      .catch((error) => {
        console.error("Error:", error);
      });
    updateProfileCompletion();

    const noti_data = {
      notify_to: current_user_data.id
    }
    getNotifications(noti_data)
      .then(res => {
        if (res.status == true) {
          setTotalNotifications(res.data.length);
        } else {
          setTotalNotifications([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  }, []);


  const updateProfileCompletion = () => {
    const newCompletionPercentage = completionPercentage;
    setCompletionPercentage(newCompletionPercentage);

    if (newCompletionPercentage === 100) {
      setShowNewButton(true);
    }
  };

  const getImageSrc = () => {
    if (completionPercentage <= 20) {
      return process.env.NEXT_PUBLIC_BASE_URL + 'images/profile-1.png';
    } else if (completionPercentage <= 40) {
      return process.env.NEXT_PUBLIC_BASE_URL + 'images/profile-2.png';
    }
    else if (completionPercentage <= 60) {
      return process.env.NEXT_PUBLIC_BASE_URL + 'images/profile-3.png';
    }
    else if (completionPercentage <= 80) {
      return process.env.NEXT_PUBLIC_BASE_URL + 'images/profile-4.png';
    }
    else {
      return process.env.NEXT_PUBLIC_BASE_URL + 'images/profile-5.png';
    }
  };

  const modalConfirmClose1 = () => {
    setModalConfirm1(false);
  }

  const modalConfirmOpen2 = () => {
    setModalConfirm2(true);
  }

  const modalConfirmClose2 = () => {
    setModalConfirm2(false);
  }
  const modalConfirmClose5 = () => {
    setShowPopup(false);
  }
  const modalConfirmClose8 = () => {
    setShowPopupunsave(false);
  }


  useEffect(() => {
    const current_user_data: any = getCurrentUserData();
    getSingleUserDetails(current_user_data.id)
      .then(res => {
        if (res.status === true) {
          SetUserData(res.user);
        } else {
          SetUserData({
            name: "",
            email: "",
            contact_no: "",
            date_of_birth: "",
            bio: "",
            gender: "",
            years_of_experience: "",
            current_salary: "",
            desired_salary: "",
            where_currently_based: "",
            current_position: "",
            description: "",
            resume_pdf_path: '',
            default_resume: '',
            unlock_instant_apply: '',
            country_name: '',
            resumedate: ''
          });
        }
      })
      .catch(err => {
        console.log(err);
      });
    const message_data = {
      sender_id: current_user_data.id
    }
    getTotalMessageUnReadCount(message_data)
      .then(res => {
        if (res.status == true) {
          setTotalUnReadMessageCount(res.total_unread_message_count);
        } else {
          setTotalUnReadMessageCount(0);
        }
      })
      .catch(err => {
        console.log(err);
      });
  }, []);

  const handleCancel = () => {
    modalConfirmClose2();
  };

  const [user, SetUserData] = useState<User>({
    name: "",
    email: "",
    contact_no: "",
    date_of_birth: "",
    bio: "",
    gender: "",
    years_of_experience: "",
    current_salary: "",
    desired_salary: "",
    where_currently_based: "",
    current_position: "",
    description: "",
    resume_pdf_path: '',
    default_resume: '',
    unlock_instant_apply: '',
    country_name: '',
    resumedate: ''
  });

  const EditChange = (name: any, value: any) => {
    SetUserData((prevState) => {
      return {
        ...prevState,
        [name]: value,
      };
    });
  };

  const isJobApplied = (jobId: any) => {
    return appliedJobs.includes(jobId);
  };

  const submitForm = (event: any) => {
    event.preventDefault();
    const current_user_data: any = getCurrentUserData();

    const description = user.description;
    if (!description || description.trim() === '') {
      setShowmessage('Please enter a description.');
      setShowPopupunerror(true);
      setTimeout(() => {
        setShowPopupunerror(false)
      }, 10000)
      return;
    }

    const descriptionWords = user.description.split(/\s+/).filter(Boolean).length;
    if (descriptionWords > MAX_DESCRIPTION_WORDS) {
      setDescriptionError(`Description should not exceed ${MAX_DESCRIPTION_WORDS} words.`);
      return;
    } else {
      setDescriptionError("");
    }


    const resume_files = event.target.resume.files;
    const resume_path = resume_files && resume_files[0] ? resume_files[0] : user.resume_pdf_path || '';


    let selectedPath = "";
    if (selectedChoice === "above") {
      selectedPath = resume_path;

    } else if (selectedChoice === "below") {
      selectedPath = user.resume_pdf_path;

    }
    else {
      selectedPath = resume_path
    }

    const data = {
      company_id: selectCompanyId,
      jobpost_by_userid: selecteduserId,
      user_id: current_user_data.id,
      job_id: selectedJobId,
      description: user.description,
      resume_path: selectedPath,//resume_path || user.resume_pdf_path ,
      default_resume: 0,
      cover_letter: selectedCover,
      instant_apply: 0
    }
    //console.log(data)
    applyJob(data)
      .then((res) => {
        if (res.status) {
          setTimeout(() => {
            modalConfirmOpen3();
          }, 100);
          setTimeout(() => {
            modalConfirmClose2();
            //window.location.reload();

            SetUserData({ ...user, description: '' });
            getTotalApplicationsForjob(current_user_data.id)
              .then((response) => {
                setTotalAppliedJobs(response.data);
              })
              .catch((error) => {
                console.error(error);
              });

            getAllJobsWithId(current_user_data.id)
              .then((response) => {
                setAllJobs(response.data);
                setAllCountryJobs(response.countrybasedjobs);

              })
              .catch((error) => {
                console.error(error);
              });

            fetch(
              `${process.env.NEXT_PUBLIC_API_URL}/users/showCompletionPercentage/${current_user_data.id}`
            )
              .then((response) => response.json())
              .then((data) => {
                setCompletionPercentage(data.data);
                setCheckboxes({
                  resume: data.checkboxes.resume,
                  updateInfo: data.checkboxes.updateInfo,
                  professionalInfo: data.checkboxes.professionalInfo,
                  applyOpening: data.checkboxes.applyOpening,
                });
              })
              .catch((error) => {
                console.error("Error:", error);
              });
          }, 100);

        } else {
          if (res.error === 'job_already_applied') {
            setShowmessage('You have already applied for this job');
            setShowPopupunerror(true);
            setTimeout(() => {
              setShowPopupunerror(false)
            }, 10000)
          } else {
            setShowmessage('An error occurred while applying for the job.');
            setShowPopupunerror(true);
            setTimeout(() => {
              setShowPopupunerror(false)
            }, 10000)
          }
        }
      })
      .catch((err) => {
        setShowmessage('An error occurred while applying for the job.');
        setShowPopupunerror(true);
        setTimeout(() => {
          setShowPopupunerror(false)
        }, 10000)
      });
  };

  const autosubmitForm = (jobId: any) => {
    const current_user_data: any = getCurrentUserData();
    let resume_path = user.resume_pdf_path
    const data = {
      company_id: selectCompanyId,
      user_id: current_user_data.id,
      job_id: jobId,
      resume_path: resume_path,
      instant_apply: 1
    };

    applyJob(data)
      .then((res) => {
        if (res.status) {
          setTimeout(() => {
            modalConfirmOpen3();
            //window.location.reload();
            getTotalApplicationsForjob(current_user_data.id)
              .then((response) => {
                setTotalAppliedJobs(response.data);
              })
              .catch((error) => {
                console.error(error);
              });
            getAllJobsWithId(current_user_data.id)
              .then((response) => {
                setAllJobs(response.data);
                setAllCountryJobs(response.countrybasedjobs);
              })
              .catch((error) => {
                console.error(error);
              });
          }, 100);
        }
      })
      .catch((err) => {
        setShowmessage('An error occurred while applying for the job');
        setShowPopupunerror(true);
        setTimeout(() => {
          setShowPopupunerror(false)
        }, 10000)
      });
  };

  const handleApplyJob = (jobId: any, company_name: any, userId: any, companyId: any) => {
    if (isJobApplied(jobId)) {
    }
    else {
      setSelectedJobId(jobId);
      setSelectedcompany(company_name)
      setSelectedUserId(userId)
      setcompanyId(companyId)
      modalConfirmOpen2();
    }
  };

  const handleautoApplyJob = (jobId: any, companyId: any) => {
    if (Number(user.unlock_instant_apply) === 1) {
      setSelectedJobId(jobId);
      setcompanyId(companyId)
      modalConfirmOpen3();
      autosubmitForm(jobId);
      //console.log(`Auto apply job with jobId: ${jobId}`);
    } else {
      console.log(`Cannot auto apply job with jobId: ${jobId}`);
    }
  };

  const savedjobs = (job_id: any, company_id: any) => {
    const current_user_data: any = getCurrentUserData();
    const data = {
      user_id: current_user_data.id,
      company_id: company_id,
      job_id: job_id
    }
    toggleFavoriteJob(data)
      .then(res => {
        if (res.status == true) {
          setShowPopup(true);
          setTimeout(() => {
            setShowPopup(false)
          }, 3000)
          getAllJobsWithId(current_user_data.id)
            .then((response) => {
              setAllJobs(response.data);
              setAllCountryJobs(response.countrybasedjobs);
            })
            .catch((error) => {
              console.error(error);
            });

          getTotalSavedjob(current_user_data.id).then((response) => {
            setGetsavedjobs(response.data);
          });

        } else {
          setShowmessage(res.message);
          setShowPopupunerror(true);
          setTimeout(() => {
            setShowPopupunerror(false)
          }, 10000)
        }
      })
      .catch(err => {
        setShowmessage(err);
        setShowPopupunerror(true);
        setTimeout(() => {
          setShowPopupunerror(false)
        }, 10000)
      });
  }

  const unSavedjobs = (id: any, job_id: any, company_id: any) => {
    const current_user_data: any = getCurrentUserData();
    const data = {
      user_id: current_user_data.id,
      company_id: company_id,
      job_id: job_id
    }
    removeFavoriteJob(id)
      .then(res => {
        if (res.status == true) {
          setShowPopupunsave(true);
          setTimeout(() => {
            setShowPopupunsave(false)
          }, 3000)
          getAllJobsWithId(current_user_data.id)
            .then((response) => {
              setAllJobs(response.data);
              setAllCountryJobs(response.countrybasedjobs);
            })
          getTotalSavedjob(current_user_data.id)
            .then((response) => {
              setGetsavedjobs(response.data);
            })
            .catch((error) => {
              console.error(error);
            });
        }
        else {
          setShowmessage(res.message);
          setShowPopupunerror(true);
          setTimeout(() => {
            setShowPopupunerror(false)
          }, 10000)
        }
      })
      .catch(err => {
        setShowmessage(err);
        setShowPopupunerror(true);
        setTimeout(() => {
          setShowPopupunerror(false)
        }, 10000)
      });
  }

  const handleUnlockInstantApply = () => {

    const current_user_data: any = getCurrentUserData();
    const user_id = current_user_data && current_user_data.id ? current_user_data.id : null;

    updateUnlockInstantApply(user_id)
      .then(response => {
        setIsInstantApplyUnlocked(true);
        window.location.reload();
      })
      .catch(error => {
        console.log(error);
      });
  };

  const [selectedChoice, setSelectedChoice] = useState('');
  const [selectedPath, setSelectedPath] = useState('');

  return (
    <>
      <div className="dash-right">
        <h1>
          Welcome <span className="span-color">{current_user_name}</span>
        </h1>
        <div className="row mt-4">
          <div className="col-lg-3 col-md-6 col-6">
            <Link href="/employees/applications">
              <div className="dash-card d-c-1">
                <div className="row">
                  <div className="col-sm-4 col-12">
                    <h5 className="dash-card-h5">{totalAppliedJobs}</h5>
                  </div>
                  <div className="col-sm-8 col-12">
                    <div className="text-right">
                      <h6>Applied Jobs</h6>
                      <p className="f-12">View All</p>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          </div>
          <div className="col-lg-3 col-md-6 col-6">
            <Link href="/employees/jobs/savedjobs">
              <div className="dash-card d-c-2">
                <div className="row">
                  <div className="col-sm-4 col-12">
                    <h5 className="dash-card-h5">{getsavedjobs}</h5>
                  </div>
                  <div className="col-sm-8 col-12">
                    <div className="text-right">
                      <h6>Saved Jobs</h6>
                      <p className="f-12">View All</p>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          </div>
          <div className="col-lg-3 col-md-6 col-6">
            <Link href="/employees/messages">
              <div className="dash-card d-c-4">
                <div className="row">
                  <div className="col-sm-4 col-12">
                    <h5 className="dash-card-h5">{totalUnReadMessageCount}</h5>
                  </div>
                  <div className="col-sm-8 col-12">
                    <div className="text-right">
                      <h6>Messages</h6>
                      <p className="f-12">View All</p>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          </div>
          <div className="col-lg-3 col-md-6 col-6">
            <Link href="/employees/notifications">
              <div className="dash-card d-c-3">
                <div className="row">
                  <div className="col-sm-4 col-12">
                    <h5 className="dash-card-h5">{totalNotifications}</h5>
                  </div>
                  <div className="col-sm-8 col-12">
                    <div className="text-right">
                      <h6>Notifications</h6>
                      <p className="f-12">View All</p>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          </div>
        </div>
        <p className="over mt-4 mb-4">New Openings</p>
        <div className="row">
          <div className="col-lg-8 col-md-12">
            {allCountryJobs.length > 0 ? (
              allCountryJobs.slice(0, 3).map((jobs: any, index: any) => (
                <div className="filter filter-sp m-center " key={index}>
                  <div className="row">
                    <div className="col-sm-2 pr-0">
                      {jobs.company_logo ? (
                        <img
                          src={
                            process.env.NEXT_PUBLIC_IMAGE_URL +
                            "images/companylogo/" +
                            jobs.company_logo
                          }
                          alt=" logo-img"
                          className="logo-filter"
                        />
                      ) : (
                        <img
                          src={
                            process.env.NEXT_PUBLIC_BASE_URL +
                            "images/logo-img.png"
                          }
                          alt=" logo-img"
                          className="logo-filter"
                        />
                      )}
                    </div>
                    <div className="col-sm-6">
                      <a target="_blank" href={"/job/" + jobs.job_slug}>
                        <p className="p-18">{jobs.job_title}</p>
                      </a>
                      <a target="_blank" href={"/companies/" + jobs.company_slug}>
                        {" "}
                        <p className="p-16 mt-1">{jobs.company_name}</p>
                      </a>
                      {jobs.is_featured == "1" ? (
                        <button className="pro">
                          Featured{" "}
                          <img
                            src={
                              process.env.NEXT_PUBLIC_BASE_URL +
                              "images/pro.png"
                            }
                            alt=" pro"
                            className="w-25 "
                            width={25} height={25}
                          />
                        </button>
                      ) : (

                        'hello'
                      )}
                      <ul className="full-time">
                        <li>
                          <i className="fa-solid fa-business-time"></i>{" "}
                          {jobs.job_type}{" "}
                        </li>
                        <li>
                          <i className="fa-solid fa-location-dot"></i>{" "}
                          {jobs.job_country_name}
                        </li>
                      </ul>
                    </div>
                    <div className="col-sm-4 text-right m-center">
                      {jobs.is_appied ? (
                        <button
                          className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp"
                          disabled
                        >
                          Applied
                        </button>
                      ) : Number(user.unlock_instant_apply) === 1 &&
                        completionPercentage === 100 ? (
                        <button
                          className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp"
                          onClick={() => handleautoApplyJob(jobs.id, jobs.company_id)}
                        >
                          {Number(user.unlock_instant_apply) === 1 &&
                            completionPercentage === 100 && (
                              <i className="fa-solid fa-bolt"></i>
                            )}{" "}
                          Apply Now
                        </button>
                      ) : (
                        <button
                          className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp"
                          onClick={() =>
                            handleApplyJob(jobs.id, jobs.company_name, jobs.user_id, jobs.company_id)
                          }
                        >
                          {Number(user.unlock_instant_apply) === 1 &&
                            completionPercentage === 100 && (
                              <i className="fa-solid fa-bolt"></i>
                            )}{" "}
                          Apply Now
                        </button>
                      )}

                      {jobs.saved_id ? (
                        <button
                          className="download mt-3 w-100"
                          onClick={() =>
                            unSavedjobs(jobs.saved_id, jobs.id, jobs.company_id)
                          }
                        >
                          <i className="fa-solid fa-bookmark"></i> &nbsp; Saved
                          Job
                        </button>
                      ) : (
                        <button
                          className="download mt-3 w-100"
                          onClick={() => savedjobs(jobs.id, jobs.company_id)}
                        >
                          <i className="fa-regular fa-bookmark"></i> &nbsp; Save
                          Job
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              allJobs.slice(0, 3).map((jobs: any, index: any) => (
                <div className="filter filter-sp m-center " key={index}>
                  <div className="row">
                    <div className="col-sm-2 pr-0">
                      {jobs.company_logo ? (
                        <img
                          src={
                            process.env.NEXT_PUBLIC_IMAGE_URL +
                            "images/companylogo/" +
                            jobs.company_logo
                          }
                          alt="logo-img"
                          className="logo-filter"
                        />
                      ) : (
                        <img
                          src={
                            process.env.NEXT_PUBLIC_BASE_URL +
                            "images/logo-img.png"
                          }
                          alt="logo-img"
                          className="logo-filter"
                        />
                      )}
                    </div>
                    <div className="col-sm-6">
                      <a target="_blank" href={"/job/" + jobs.job_slug}>
                        <p className="p-18">{jobs.job_title}</p>
                      </a>
                      <a target="_blank" href={"/companies/" + jobs.company_slug}>
                        {" "}
                        <p className="p-16 mt-1">{jobs.company_name}</p>
                      </a>
                      {jobs.is_featured == "1" ? (
                        <button className="pro">
                          Featured{" "}
                          <img
                            src={
                              process.env.NEXT_PUBLIC_BASE_URL +
                              "images/pro.png"
                            }
                            alt=" pro"
                            className="w-25 "
                          />
                        </button>
                      ) : (
                        <button className='pro actively '>
                          Actively Hiring{" "}
                          <i className='fa-solid fa-circle-check'></i>
                        </button>
                      )}
                      <ul className="full-time">
                        <li>
                          <i className="fa-solid fa-business-time"></i>{" "}
                          {jobs.job_type}{" "}
                        </li>
                        <li>
                          <i className="fa-solid fa-location-dot"></i>{" "}
                          {jobs.job_country_name}
                        </li>
                      </ul>
                    </div>
                    <div className="col-sm-4 text-right m-center">
                      {jobs.is_appied ? (
                        <button
                          className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp"
                          disabled
                        >
                          Applied
                        </button>
                      ) : Number(user.unlock_instant_apply) === 1 &&
                        completionPercentage === 100 ? (
                        <button
                          className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp"
                          onClick={() => handleautoApplyJob(jobs.id, jobs.company_id)}
                        >
                          {Number(user.unlock_instant_apply) === 1 &&
                            completionPercentage === 100 && (
                              <i className="fa-solid fa-bolt"></i>
                            )}{" "}
                          Apply Now
                        </button>
                      ) : (
                        <button
                          className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp"
                          onClick={() =>
                            handleApplyJob(jobs.id, jobs.company_name, jobs.user_id, jobs.company_id)
                          }
                        >
                          {Number(user.unlock_instant_apply) === 1 &&
                            completionPercentage === 100 && (
                              <i className="fa-solid fa-bolt"></i>
                            )}{" "}
                          Apply Now
                        </button>
                      )}

                      {jobs.saved_id ? (
                        <button
                          className="download mt-3 w-100"
                          onClick={() =>
                            unSavedjobs(jobs.saved_id, jobs.id, jobs.company_id)
                          }
                        >
                          <i className="fa-solid fa-bookmark"></i> &nbsp; Saved
                          Job
                        </button>
                      ) : (
                        <button
                          className="download mt-3 w-100"
                          onClick={() => savedjobs(jobs.id, jobs.company_id)}
                        >
                          <i className="fa-regular fa-bookmark"></i> &nbsp; Save
                          Job
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}

            <center>
              <p className="explore-jobs mt-4">
                {" "}
                <Link href="/jobs-in-gulf">Explore All Jobs</Link>{" "}
              </p>
            </center>
          </div>

          <div className="col-lg-4 col-md-12">
            {Number(user.unlock_instant_apply) === 1 &&
              completionPercentage == 100 ? (
              <div className="col-lg-4 col-md-6 mb-4">
                <div className="profile-checklist back-white">
                  <div className="row">
                    <div className="col-4 pr-0">
                      <img
                        src={
                          process.env.NEXT_PUBLIC_BASE_URL +
                          "images/profile-6.png"
                        }
                        alt="profile-6"
                      />
                    </div>
                    <div className="col-8">
                      <h5>Profile Checklist</h5>
                      <h6>Way to go, Alan!</h6>
                    </div>
                  </div>
                  <div className="row">
                    <div className="col-3 pr-0"></div>
                    <div className="col-9">
                      <p className="unlocked-text">You just unlocked:</p>
                    </div>
                  </div>

                  <button className="signup-cards blue-btn f-22">
                    <i className="fa-solid fa-unlock"></i>Instant Apply{" "}
                  </button>
                </div>
              </div>
            ) : (
              <div className="profile-checklist">
                <div className="row">
                  <div className="col-4 pr-0">
                    <img src={getImageSrc()} alt="profile-4" />
                  </div>
                  <div className="col-8">
                    <h5>Profile Checklist</h5>
                    <h6>You're almost there!</h6>
                  </div>
                </div>
                <ul className="Verify">
                  <li>
                    <i
                      className={`fa-regular ${completionPercentage >= 20
                        ? "fa-circle-check"
                        : "fa-circle"
                        }`}
                    ></i>{" "}
                    Verify your account
                  </li>
                  <li>
                    <i
                      className={`fa-regular ${checkboxes.updateInfo === true
                        ? "fa-circle-check"
                        : "fa-circle"
                        }`}
                    ></i>{" "}
                    <Link href="/employees/myprofile/profile">Update your information</Link>
                  </li>
                  <li>
                    <i
                      className={`fa-regular ${checkboxes.professionalInfo === true
                        ? "fa-circle-check"
                        : "fa-circle"
                        }`}
                    ></i>{" "}
                    <Link href="/employees/myprofile/profile">Professional information</Link>
                  </li>
                  <li>
                    <i
                      className={`fa-regular ${checkboxes.resume === true
                        ? "fa-circle-check"
                        : "fa-circle"
                        }`}
                    ></i>{" "}
                    <Link href="/employees/resume">Upload your resume</Link>
                  </li>
                  <li className="dis">
                    <i
                      className={`fa-regular ${checkboxes.applyOpening === true
                        ? "fa-circle-check"
                        : "fa-circle"
                        }`}
                    ></i>{" "}
                    Apply for an opening
                  </li>
                </ul>
                {completionPercentage !== 100 ? (
                  <button
                    className="signup-cards white-btn f-22"
                    onClick={() => alert("Profile Not Completed")}
                  >
                    <i className="fa-solid fa-lock"></i>Unlock Instant Apply
                  </button>
                ) : (
                  <button
                    className="signup-cards blue-btn f-22"
                    onClick={handleUnlockInstantApply}
                  >
                    <i className="fa-solid fa-lock"></i>Unlock Instant Apply
                  </button>
                )}
              </div>
            )}
          </div>
        </div>

        {modalConfirm1 && (
          <PopupModal
            show={modalConfirm6}
            handleClose={modalConfirmClose6}
            customclass={
              "add_company_signup_popup modal-lg big-size body-sp-0 b-r-30 up-down-ani"
            }
            closebtnclass={"close-x mt-2"}
            closebtnicon={"icon"}
          >
            <div className="popup-modal">
              <div className="row">
                <div className="col-sm-4 popup-right border-left"></div>
                <div className="col-sm-8">
                  <div className="popup-left-text p-25 text-center">
                    <h3>
                      Welcome{" "}
                      <span className="span-color">
                        {" "}
                        to The Talent Point!{" "}
                      </span>
                    </h3>
                    <p className="f-22 c-2C2C2C">
                      The gateway to finding your dream job
                    </p>
                    <p className="f-18 c-4D4D4D">
                      Complete your profile to level up your job search and
                      unlock the true potential of our platform.
                    </p>
                    <button
                      className="btn login  mt-4"
                      type="submit"
                      onClick={updateFirstTimePopup}
                    >
                      Get Started
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </PopupModal>
        )}
        <br />
        <br />

        <PopupModal
          show={modalConfirm2}
          handleClose={modalConfirmClose2}
          customclass={"add_company_signup_popup modal-lg body-sp-0 "}
          closebtnclass={"close-x  bg-0055BA border-design close-b-des"}
          closebtnicon={"icon"}
        >
          <div className="head-box">
            <div className="row">
              <div className="col-sm-10">
                <p className="f-26 mb-2 mt-2"> Apply to {selectedcompany}</p>
                <p className="f-16">
                  {" "}
                  Enter your basic information & get apply.
                </p>
              </div>
              <div className="col-sm-2 text-right">
                <button
                  type="button"
                  className="close-x  bg-0055BA border-design close-b-des"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                >
                  <i
                    className="fa-solid fa-xmark"
                    onClick={modalConfirmClose2}
                  ></i>
                </button>
              </div>
            </div>
          </div>
          <div className="popup-body scroll-pop-h">


            <form className="form-experience-fieild" onSubmit={submitForm}>
              <div className="row ">
                <div className="col-sm-7 text-right">
                  <p className="f-22 mt-2">Upload your recent Resume/CV:</p>
                </div>
                <div className="col-sm-5">
                  <div className="uploade-btn">
                    <input
                      type="file"
                      name="resume"
                      accept='.pdf, .docx'
                      onChange={handleFileChange}
                    />
                    <button className="download ">
                      <i className="fa-solid fa-upload"></i> Upload Resume
                    </button>
                    {errorMessage && (
                      <div className="text-danger mt-2">{errorMessage}</div>
                    )}
                    {selectedFileName && <p className='text-dark'>Selected File: {selectedFileName}</p>}
                  </div>
                </div>
              </div>
              {selectedResume && (
                <div className="w-box bg-fff mt-3 mb-3 p-3">
                  <div className="row">
                    <div className="col-sm-8">
                      <p className="f-18 w-600 mb-2">{user.name}-Resume</p>
                      <p className="f-16 c-999999">
                        Uploaded on{" "}
                        {currentDate.toLocaleString("en-US", {
                          month: "short",
                          day: "numeric",
                        })}
                      </p>
                    </div>
                    <div className="col-sm-4 text-right">
                      <span
                        className={
                          selectedChoice === "above" ? "selected" : "default"
                        } style={{ cursor: 'pointer' }}
                        onClick={() => {
                          setSelectedChoice("above");
                          // setSelectedPath("path for the above choice");
                        }}
                      >
                        Choose
                      </span>
                      &nbsp;&nbsp;
                    </div>
                  </div>
                </div>
              )}

              {user.resume_pdf_path && Number(user.default_resume) === 1 ? (
                <div className="w-box bg-fff mt-3 mb-3 p-3">
                  <div className="row">
                    <div className="col-sm-8">
                      <p className="f-18 w-600 mb-2">{user.name}-Resume</p>
                      <p className="f-16 c-999999">
                        Uploaded on{" "}
                        {new Date(user.resumedate).toLocaleString("en-US", {
                          month: "short",
                          day: "numeric",
                        })}
                      </p>
                    </div>
                    <div className="col-sm-4 text-right">
                      <span
                        className={
                          selectedChoice === "below" ? "selected" : "default"
                        } style={{ cursor: 'pointer' }}
                        onClick={() => {
                          setSelectedChoice("below");
                          setSelectedPath("path for the below choice");
                        }}
                      >
                        Choose
                      </span>
                      &nbsp;&nbsp;
                    </div>
                  </div>
                </div>
              ) : null}

              <label>Your Name*</label>
              <input
                type="text"
                placeholder="Alan Moore"
                className="fild-des"
                name="name"
                defaultValue={user.name}
              />

              <div className="row">
                <div className="col-sm-6">
                  <label>Email ID*</label>
                  <input
                    type="email"
                    placeholder="<EMAIL>"
                    className="fild-des"
                    //value={user.email}
                    defaultValue={user.email}
                    readOnly
                  />
                </div>
                <div className="col-sm-6">
                  <label>Contact Number*</label>
                  {/* <input
                      type="text"
                      placeholder="(+971) 123 – 456 – 7890"
                      className="fild-des"
                      name="contact_no"
                      value={user.contact_no}
                    /> */}
                  <PhoneInput country={"ae"}
                    value={'' + user.contact_no}
                    inputClass="fild-des-contact"
                  />
                </div>
              </div>

              <div className="row">
                <div className="col-sm-6">
                  <label>Date of Birth*</label>
                  <input
                    type="date"
                    placeholder="<EMAIL>"
                    className="fild-des"
                    name="date_of_birth"
                    defaultValue={user.date_of_birth}
                  />
                </div>
                <div className="col-sm-6">
                  <label>Gender</label>
                  <select
                    className="fild-des"
                    name="gender"
                    defaultValue={user.gender}
                  >
                    <option>Male</option>
                    <option>Female</option>
                  </select>
                </div>
              </div>

              <label>Where are you currently based?*</label>
              <input
                type="text"
                placeholder="Dubai"
                className="fild-des"
                defaultValue={user.country_name}
                name="where_currently_based"
              />

              <label>Your Cover Letter*</label>
              <HtmlEditor
                name="edit_job_description"
                value={user.description}
                onChange={(name: any, value: any) => {
                  EditChange("description", value);
                }}
              />
              {descriptionError && <div className="text-danger mt-2">{descriptionError}</div>}
              {/* <p className="font-12 text-right words">250 words</p> */}

              {/* <div className='d-flex justify-content-center'>
              <button className="download d-flex justify-content-center ">
                    <i className="fa-solid fa-upload"></i> Upload Cover Letter
              </button>
              </div> */}
              <div className="d-flex justify-content-center mt-4">
                <div className="uploade-btn ">
                  <input
                    type="file"
                    name="resume"
                    accept='.pdf, .docx'
                    onChange={handleCoverChange}
                  />
                  <button className="download ">
                    <i className="fa-solid fa-upload"></i> Upload Cover Letter
                  </button>
                  {covererrorMessage && (
                    <div className="text-danger mt-2">{covererrorMessage}</div>
                  )}
                </div>
              </div>
              {selectedCover && (
                <div className="w-box bg-fff mt-3 mb-3 p-3">
                  <div className="row">
                    <div className="col-sm-8">
                      <p className="f-18 w-600 mb-2">{user.name}-Cover-Letter</p>
                      <p className="f-16 c-999999">
                        Uploaded on{" "}
                        {currentDate.toLocaleString("en-US", {
                          month: "short",
                          day: "numeric",
                        })}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <div className="text-right mt-3">
                <a className="cancel" onClick={handleCancel}>
                  Cancel
                </a>
                <button className="save">Save</button>
              </div>


            </form>
            <ToastContainer />
          </div>
        </PopupModal>

        <PopupModal
          show={modalConfirm3}
          handleClose={modalConfirmClose3}
          customclass={"modal-lg  header-remove body-sp-0 "}
        >
          <div className="popup-body">
            <div className="row">
              <div className="col-sm-10"> </div>
              <div className="col-sm-2 text-right">
                <button
                  type="button"
                  className="close-x  bg-0055BA border-design close-b-des"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                >
                  <i
                    className="fa-solid fa-xmark"
                    onClick={modalConfirmClose3}
                  ></i>
                </button>
              </div>
            </div>
            <div className="text-center pb-3 pt-2">
              <div className='pog-r w-120 m-0auto mb-4 mt-4'>
                <img src={process.env.NEXT_PUBLIC_BASE_URL + "images/blue-round.png"} alt="blue-round" className="w-120 fa-spin" />
                <img src={process.env.NEXT_PUBLIC_BASE_URL + "images/check-i.png"} alt="check-i" className="check-i" />
              </div>
              <h2 className="f-31 mb-3">
                Application <span className="span-color">Submitted</span>
              </h2>
              <p className="f-18 w-400">
                Go to{" "}
                <Link href="/employees/applications" className="c-0070F5 w-700  ">
                  {" "}
                  My Applications
                </Link>{" "}
                to view your application status.
              </p>
            </div>
          </div>
        </PopupModal>

        {showPopup && (
          <div className="toast-box color-0055BA code-job">
            <div className="toast-footer">
              <div className="row">
                <div className="col-7">
                  <h5 >
                    <i className="fa-solid fa-circle-info"></i> Your job has
                    been saved.
                  </h5>
                </div>
                <div className="col-5 text-right">
                  <p>
                    <Link href="/employees/jobs/savedjobs" className="link-12">
                      View Saved Jobs
                    </Link>{" "}
                    <i
                      className="fa-solid fa-xmark xmark-icon c-0070F5"
                      onClick={modalConfirmClose5}
                    ></i>
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {showPopupunsave &&
          (<div className="toast-box color-0055BA code-job">
            <div className="toast-footer">
              <div className="row">
                <div className="col-7">
                  <h5 className='unsave'>
                    <i className="fa-solid fa-circle-info"></i> Your job has
                    been Unsaved.
                  </h5>
                </div>
                <div className="col-5 text-right">
                  <p>
                    <i
                      className="fa-solid fa-xmark xmark-icon c-0070F5"
                      onClick={modalConfirmClose8}
                    ></i>
                  </p>
                </div>
              </div>
            </div>
          </div>)}

      </div>
      {showPopupunsave &&
        <SuccessToast message={showmessage} />
      }
      {showPopupunerror &&
        <ErrorToast message={showmessage} />
      }
    </>
  );
}
