import React, {useState, useEffect, useContext} from 'react';
import {notification} from 'antd';
import {useRouter} from 'next/router';
import <PERSON>rror<PERSON>andler from '@/lib/ErrorHandler';
import Link from 'next/link';
import 'react-phone-input-2/lib/style.css';
import AuthContext from '@/Context/AuthContext';
import {toggleFavoriteJob, applyJob, getSingleUserDetails, getAllCountries} from '@/lib/frontendapi';
import {Button, Space} from 'antd';
import ModalForm from '@/components/Common/ModalForm';
import PhoneInput from 'react-phone-input-2';
import {User, JobItemProps} from '@/lib/types';
import {getTotalApplicationsForjob} from '@/lib/employeeapi';
import {HtmlEditor} from '@/components/Common/HtmlEditor';
import Image from 'next/image';

export default function CompanyJobs({jobs_data, index, isSaved = false}: JobItemProps) {
  const {user} = useContext(AuthContext);
  const [userdata, SetUserData] = useState<User>();
  const [selectedJobId, setSelectedJobId] = useState('');
  const [selectedcompany, setSelectedcompany] = useState('');
  const [selecteduserId, setSelectedUserId] = useState('');
  const [selectCompanyId, setCompanyId] = useState('');
  const [applyJobSubmitModal, setApplyJobSubmitModal] = useState(false);
  const [appliedJobs, setAppliedJobs] = useState<string[]>([]);
  const router = useRouter();
  const [errorMessage, setErrorMessage] = useState('');
  const [covererrorMessage, setCoverErrorMessage] = useState('');
  const [selectedCover, setSelectedCover] = useState(null);
  const [selectedResume, setSelectedResume] = useState(null);
  const [descriptionError, setDescriptionError] = useState<string>('');
  const MAX_DESCRIPTION_WORDS = 250;
  const [isLoading, setIsLoading] = useState(false);
  const [selectedChoice, setSelectedChoice] = useState('');
  const [name, SetName] = useState('');
  const [contactNo, SetContactNo] = useState('');
  const [gender, SetGender] = useState('');
  const [dateOfBirth, SetDateOFBirth] = useState('');
  const [currentlyLocation, SetCurrentlyLocation] = useState('');
  const [selectedFileName, setSelectedFileName] = useState('');
  const currentDate = new Date();
  const [totalAppliedJobs, setTotalAppliedJobs] = useState('');
  const [country, setCountry] = useState([]);
  const [currentSavedStatus, setCurrentSavedStatus] = useState(isSaved);

  useEffect(() => {
    getAllCountries()
      .then(res => {
        if (res) {
          setCountry(res);
        } else {
          setCountry([]);
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
    getSingleUserDetails(user?.id)
      .then(res => {
        if (res.status === true) {
          SetUserData(res.user);
          SetName(res.user.name);
          SetContactNo(res.user.contact_no);
          SetGender(res.user.gender);
          SetDateOFBirth(res.user.date_of_birth);
          SetCurrentlyLocation(res.user.where_currently_based);
          if (res.user.resume_path != 'null') {
            setSelectedChoice('below');
          }
        } else {
          SetUserData(undefined);
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
    getTotalApplicationsForjob(user?.id)
      .then(response => {
        setTotalAppliedJobs(response.data);
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });
  }, [user]);

  const handleCancel = () => {
    applyjobModalClose();
  };

  const applyJobModal = () => {
    setApplyJobSubmitModal(true);
  };
  const applyjobModalClose = () => {
    setApplyJobSubmitModal(false);
  };

  const autoSubmitForm = (jobId: any, userId: any, companyId: any) => {
    const data = {
      company_id: companyId,
      jobpost_by_userid: userId,
      user_id: user?.id,
      job_id: jobId,
      description: '',
      resume_path: selectedResume,
      cover_letter: selectedCover,
      choice: selectedChoice,
      instant_apply: 1,
      name: name ? name : user?.name,
      contact_no: contactNo ? contactNo : user?.contact_no,
      gender: gender ? gender : user?.gender,
      date_of_birth: dateOfBirth ? dateOfBirth : user?.date_of_birth,
      where_currently_based: currentlyLocation ? currentlyLocation : user?.where_currently_based,
    };
    applyJob(data)
      .then(res => {
        if (res.status) {
          setTimeout(() => {
            window.location.reload();
          }, 3000);
        }
      })
      .catch(err => {
        notification.error({
          message: err.message,
        });
      });
  };

  const handleAutoApplyJob = (jobId: any, userId: any, companyId: any) => {
    if (Number(user?.unlock_instant_apply) === 1) {
      autoSubmitForm(jobId, userId, companyId);
      console.log(`Auto apply job with jobId: ${jobId}`);
    } else {
      console.log(`Cannot auto apply job with jobId: ${jobId}`);
    }
  };

  const isJobApplied = (jobId: any) => {
    return appliedJobs.includes(jobId);
  };

  const handleApplyJob = (jobId: any, company_name: any, userId: any, companyId: any) => {
    if (isJobApplied(jobId)) {
      console.log('Job already applied');
    } else {
      setSelectedJobId(jobId);
      setSelectedcompany(company_name);
      setSelectedUserId(userId);
      setCompanyId(companyId);
      applyJobModal();
    }
  };

  const handleCoverChange = (event: any) => {
    const file = event.target.files[0];
    setSelectedCover(file);
    if (file) {
      const allowedFormats = ['pdf', 'doc', 'docx'];
      const fileExtension = file.name.split('.').pop().toLowerCase();
      if (!allowedFormats.includes(fileExtension)) {
        setCoverErrorMessage('Invalid file. Only PDF or DOC files are allowed.');
        setSelectedCover(null);
      } else {
        setCoverErrorMessage('');
      }
    }
  };

  const resetForm = () => {
    // @ts-ignore
    SetUserData({...user, description: ''});
    setSelectedResume(null);
    setSelectedCover(null);
  };

  useEffect(() => {
    setCurrentSavedStatus(isSaved);
  }, [isSaved]);

  const onToggleFavoriteJob = (job_id: any) => {
    if (!user) {
      notification.info({message: 'Please, login or create and account'});
      router.push('auth/login').then();
      return;
    }
    const data = {job_id: job_id};
    toggleFavoriteJob(data)
      .then((res: any) => {
        notification.success({
          message: currentSavedStatus ? 'Removed from favorites jobs' : 'Saved to favorite jobs',
        });
        setCurrentSavedStatus(!currentSavedStatus);
      })
      .catch((err: any) => {
        ErrorHandler.showNotification(err);
      });
  };

  const submitForm = (event: any) => {
    event.preventDefault();
    const description = userdata?.description;
    if (!description || description.trim() === '') {
      notification.info({
        message: 'Please enter a description.',
      });
      return;
    }
    const descriptionWords = userdata?.description?.split(/\s+/).filter(Boolean).length;
    if (descriptionWords && descriptionWords > MAX_DESCRIPTION_WORDS) {
      setDescriptionError(`Description should not exceed ${MAX_DESCRIPTION_WORDS} words.`);
      return;
    } else {
      setDescriptionError('');
    }
    setIsLoading(true);
    const data = {
      company_id: selectCompanyId,
      jobpost_by_userid: selecteduserId,
      user_id: userdata?.id,
      job_id: selectedJobId,
      description: userdata?.description,
      resume_path: selectedResume,
      cover_letter: selectedCover,
      choice: selectedChoice,
      instant_apply: 0,
      name: name ? name : userdata?.name,
      contact_no: contactNo ? contactNo : userdata?.contact_no,
      gender: gender ? gender : userdata?.gender,
      date_of_birth: dateOfBirth ? dateOfBirth : userdata?.date_of_birth,
      where_currently_based: currentlyLocation ? currentlyLocation : userdata?.where_currently_based,
    };
    applyJob(data)
      .then(res => {
        if (res.status) {
          resetForm();
          applyjobModalClose();
          setIsLoading(false);
          notification.success({
            message: res.message,
          });
          setTimeout(() => {
            window.location.reload();
          }, 3000);
        } else {
          if (res.error === 'job_already_applied') {
            notification.info({
              message: 'You have already applied for this job',
            });
          } else {
            notification.error({
              message: 'An error occurred while applying for the job.',
            });
          }
        }
      })
      .catch(err => {
        notification.error({
          message: 'An error occurred while applying for the job.',
        });
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleFileChange = (event: any) => {
    const file = event.target.files[0];
    setSelectedFileName(file.name);
    setSelectedResume(file);
    if (file) {
      const allowedFormats = ['pdf', 'doc', 'docx'];
      const fileExtension = file.name.split('.').pop().toLowerCase();
      if (!allowedFormats.includes(fileExtension)) {
        setErrorMessage('Invalid file. Only PDF or DOC files are allowed.');
        setSelectedResume(null);
      } else {
        setErrorMessage('');
        setSelectedChoice('above');
      }
    }
  };

  const EditChange = (name: any, value: any) => {
    SetUserData(value);
  };

  return (
    <>
      <div className="col-sm-2"></div>
      <div className="col-sm-8 col-12"></div>
      <div className="filter filter-sp m-center mt-4" key={index}>
        <div className="row">
          <div className="col pr-0 max-w-99">
            <img
              src={
                jobs_data.company_logo
                  ? `${process.env.NEXT_PUBLIC_IMAGE_URL}images/companylogo/${jobs_data.company_logo}`
                  : `/images/logo-img.png`
              }
              alt="logo-img"
              className="logo-filter m-none"
              width={78}
              height={78}
            />
          </div>
          <div className="col-sm-7 m-text-left">
            <a target="_blank" href={'/job/' + jobs_data.job_slug}>
              <p className="p-18">{jobs_data.job_title}</p>
            </a>
            <p className="p-16 mt-1">{jobs_data.company?.company_name}</p>
            {jobs_data.job_status == 'active' ? (
              <span className="pro actively ">
                Actively Hiring <i className="icon fa-solid fa-circle-check"></i>
              </span>
            ) : (
              ''
            )}
            <ul className="full-time">
              <li className="f-12">
                <i className="fa-solid fa-business-time"></i>{' '}
                {jobs_data.job_type === 'parttime'
                  ? 'Part-Time'
                  : jobs_data.job_type === 'fulltime'
                    ? 'Full-Time'
                    : jobs_data.job_type === 'contract'
                      ? 'Contract'
                      : jobs_data.job_type === 'freelance'
                        ? 'Freelance'
                        : ' '}
              </li>
              <li className="f-12">
                <i className="fa-solid fa-location-dot"></i> {jobs_data.country?.country_name}
              </li>
            </ul>
          </div>

          {user?.id != jobs_data.user_id ? (
            <>
              {jobs_data.is_appied}
              <div className="col-sm-3">
                {user?.id && user?.role == 'employee' ? (
                  <>
                    {jobs_data.is_appied ? (
                      <Button
                        type={'primary'}
                        size={'large'}
                        className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp"
                        disabled>
                        Applied
                      </Button>
                    ) : Number(user?.unlock_instant_apply) == 1 && Number(user?.profile_complete_percentage) == 100 ? (
                      <Button
                        type={'primary'}
                        size={'large'}
                        className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp"
                        onClick={() => handleAutoApplyJob(jobs_data.id, jobs_data.user_id, jobs_data.company_id)}>
                        <i className="fa-solid fa-bolt"></i> Apply Now
                      </Button>
                    ) : (
                      <Button
                        type={'primary'}
                        size={'large'}
                        className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp"
                        onClick={() =>
                          handleApplyJob(
                            jobs_data.id,
                            jobs_data.company?.company_name,
                            jobs_data.user_id,
                            jobs_data.company_id,
                          )
                        }>
                        <i className="fa-solid fa-bolt"></i> Apply Now
                      </Button>
                    )}
                    <Button
                      type={'primary'}
                      size={'large'}
                      className="download mt-3 w-100"
                      onClick={() => onToggleFavoriteJob(jobs_data.id)}>
                      <i className={`fa-${currentSavedStatus ? 'solid' : 'regular'} fa-bookmark`}></i>&nbsp;
                      {currentSavedStatus ? 'Remove save' : 'Save Job'}
                    </Button>
                  </>
                ) : (
                  <>
                    <Link href="/auth/login">
                      <Button
                        type={'primary'}
                        size={'large'}
                        className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp">
                        {' '}
                        Apply Now
                      </Button>
                    </Link>
                    <Link href="/auth/login">
                      <Button type={'primary'} size={'large'} className="download mt-3 w-100">
                        <i className="fa-regular fa-bookmark"></i> &nbsp; Save Job
                      </Button>
                    </Link>
                  </>
                )}
              </div>
            </>
          ) : (
            ''
          )}
        </div>
      </div>
      <ModalForm title={''} open={applyJobSubmitModal} onCancel={() => setApplyJobSubmitModal(false)}>
        <div className="head-box">
          <div className="row">
            <div className="col-sm-10">
              <p className="f-26 mb-2 mt-2"> Apply to {selectedcompany}</p>
              <p className="f-16"> Enter your basic information & get apply.</p>
            </div>
            <div className="col-sm-2 text-right">
              <Button
                className="close-x  bg-0055BA border-design close-b-des"
                data-bs-dismiss="modal"
                aria-label="Close">
                <i className="fa-solid fa-xmark" onClick={applyjobModalClose}></i>
              </Button>
            </div>
          </div>
        </div>
        <div className="popup-body scroll-pop-h">
          <form className="form-experience-fieild" onSubmit={submitForm}>
            <div className="row ">
              <div className="col-sm-7 text-right">
                <p className="f-22 mt-2">Upload your recent Resume/CV:</p>
              </div>
              <div className="col-sm-5">
                <div className="uploade-btn">
                  <input type="file" accept=".pdf, .docx" name="resume" onChange={handleFileChange} />
                  <Button type={'primary'} size={'large'} className="download ">
                    <i className="fa-solid fa-upload"></i> Upload Resume
                  </Button>
                  {errorMessage && <div className="text-danger mt-2">{errorMessage}</div>}
                  {selectedFileName && <p className="text-dark">Selected File: {selectedFileName}</p>}
                </div>
              </div>
            </div>
            {selectedResume && (
              <div className="w-box bg-fff mt-3 mb-3 p-3">
                <div className="row">
                  <div className="col-sm-8">
                    <p className="f-18 w-600 mb-2">{user?.name}-Resume</p>
                    <p className="f-16 c-999999">
                      Uploaded on{' '}
                      {currentDate.toLocaleString('en-US', {
                        month: 'short',
                        day: 'numeric',
                      })}
                    </p>
                  </div>

                  <div className="col-sm-4 text-right">
                    <span
                      className={selectedChoice == 'above' ? 'default' : 'selected'}
                      style={{cursor: 'pointer'}}
                      onClick={() => {
                        setSelectedChoice('above');
                      }}>
                      Choose
                      {}
                    </span>
                    &nbsp;&nbsp;
                  </div>
                </div>
              </div>
            )}
            {user?.resume_pdf_path && Number(user.default_resume) === 1 ? (
              <div className="w-box bg-fff mt-3 mb-3 p-3">
                <div className="row">
                  <div className="col-sm-8">
                    <p className="f-18 w-600 mb-2">{user.name}-Default Resume</p>
                    <p className="f-16 c-999999">
                      Uploaded on{' '}
                      {user?.resumedate &&
                        new Date(user?.resumedate).toLocaleString('en-US', {
                          month: 'short',
                          day: 'numeric',
                        })}
                    </p>
                  </div>

                  <div className="col-sm-4 text-right">
                    <span
                      className={selectedChoice == 'below' ? 'default' : 'selected'}
                      style={{cursor: 'pointer'}}
                      onClick={() => {
                        setSelectedChoice('below');
                      }}>
                      Choose
                    </span>
                    &nbsp;&nbsp;
                  </div>
                </div>
              </div>
            ) : null}
            <div className="row">
              <div className="col-sm-6">
                <label>Your Name*</label>
                <input
                  type="text"
                  placeholder="Alan Moore"
                  className="fild-des"
                  name="name"
                  value={name || ''}
                  onChange={e => SetName(e.target.value)}
                />
              </div>
              <div className="col-sm-6">
                <label>Email ID*</label>
                <input
                  type="email"
                  placeholder="<EMAIL>"
                  className="fild-des filed_disabled"
                  defaultValue={user?.email}
                  disabled
                />
              </div>
            </div>
            <div className="row">
              <div className="col-sm-6">
                <label>Contact Number*</label>
                <PhoneInput
                  country={'us'}
                  value={contactNo || ''}
                  inputClass="fild-des-contact"
                  onChange={contactNumber => SetContactNo(contactNumber)}
                />
              </div>
              <div className="col-sm-6">
                <label>Gender</label>
                <select
                  className="fild-des"
                  name="gender"
                  value={gender || ''}
                  onChange={e => SetGender(e.target.value)}>
                  <option value="">Select gender</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>

            <div className="row">
              <div className="col-sm-6">
                <label>Date of Birth*</label>
                <input
                  type="date"
                  placeholder="<EMAIL>"
                  className="fild-des"
                  name="date_of_birth"
                  value={dateOfBirth || ''}
                  onChange={e => SetDateOFBirth(e.target.value)}
                />
              </div>
              <div className="col-sm-6">
                <label>Where are you currently based?*</label>

                <select
                  className="fild-des"
                  value={currentlyLocation || ''}
                  onChange={e => SetCurrentlyLocation(e.target.value)}>
                  {country.map((countries: any) => {
                    if (countries.status === 'active') {
                      return (
                        <option key={countries.id} value={countries.id}>
                          {countries.country_name}
                        </option>
                      );
                    }
                  })}
                </select>
              </div>
            </div>

            <label>Your Cover Letter*</label>
            <HtmlEditor
              name="edit_job_description"
              value={user?.description}
              onChange={(name: any, value: any) => {
                EditChange('description', value);
              }}
            />
            {descriptionError && <div className="text-danger mt-2">{descriptionError}</div>}
            <div className="d-flex justify-content-center mt-4">
              <div className="uploade-btn ">
                <input type="file" name="resume" accept=".pdf, .docx" onChange={handleCoverChange} />
                <Button type={'primary'} size={'large'} className="download ">
                  <i className="fa-solid fa-upload"></i> Upload Cover Letter
                </Button>
                {covererrorMessage && <div className="text-danger mt-2">{covererrorMessage}</div>}
              </div>
            </div>
            {selectedCover && (
              <div className="w-box bg-fff mt-3 mb-3 p-3">
                <div className="row">
                  <div className="col-sm-8">
                    <p className="f-18 w-600 mb-2">{user?.name}-Cover-Letter</p>
                    <p className="f-16 c-999999">
                      Uploaded on{' '}
                      {currentDate.toLocaleString('en-US', {
                        month: 'short',
                        day: 'numeric',
                      })}
                    </p>
                  </div>
                </div>
              </div>
            )}
            {Number(totalAppliedJobs) == 0 && (
              <p className="pt-4 text-center" style={{color: '#D04E4F'}}>
                Note: An incomplete profile may affect your chances of landing your dream job.
              </p>
            )}
            <div className="text-right mt-3">
              <Space>
                <Button onClick={handleCancel} size={'large'}>
                  Cancel
                </Button>
                <Button type={'primary'} size={'large'} className="save" htmlType="submit" disabled={isLoading}>
                  {isLoading ? 'Please wait...' : 'Save'}
                </Button>
              </Space>
            </div>
          </form>
        </div>
      </ModalForm>
    </>
  );
}
