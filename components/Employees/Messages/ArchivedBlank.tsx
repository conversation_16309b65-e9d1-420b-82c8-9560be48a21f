import React from 'react';
import Link from 'next/link';
import Image from 'next/image';


export default function ArchivedBlank() {
    return (
        <>
            <div className="dash-right">
                <h1>My <span className='span-color'> Messages</span></h1>
                <div className='row '>
                    <div className='col-sm-7'>
                        <ul className='list-loc m-m-0 mt-4'>
                            <li>
                                <Link href="/employees/messages">Inbox </Link>
                            </li>
                            <li>
                                <Link href="/employees/messages/interviews">Interviews </Link>
                            </li>
                            <li className='active'>
                                <Link href="/employees/messages/archived">Archived </Link>
                            </li>
                        </ul>
                    </div>
                    <div className='col-sm-5'>
                        <ul className='blue-text-line mt-4 text-right'>
                            <li>
                                <Link href="/common/emptyarchived">Empty Archived</Link>
                            </li>
                        </ul>
                    </div>
                </div>

                <div className='data-management m-p-10  p-0'>
                    <div className='work-experience-fieild m-p-10'>
                        <div className='work-experience-fieild m-p-10 text-center'>
                            <img
                                src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blank-3.png'}
                                alt="blank-3" className=''
                            />
                            <p className='f-22 c-BABABA mb-1'>You don't seem to have any archived messages</p>
                            <p className='f-18 w-600 c-BABABA'>Go to  <a href="#" className='c-0070F5' > Job</a></p>
                        </div>
                    </div>

                </div>
            </div>
        </>
    )
}
