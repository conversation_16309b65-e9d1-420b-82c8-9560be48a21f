import React, {useState, useEffect, useContext} from 'react';
import {
  getAllEmployeesReceiverMessages,
  getUserInterviewss,
  getAllEmployeesReceiverArchivedMessages,
  updateMessagesReadUnReadStatus,
  getTotalMessageUnReadCount,
  getAllEmployerReceiverMessages,
} from '../../../lib/frontendapi';
import {useRouter} from 'next/router';
import moment from 'moment';
import Link from 'next/link';
import Image from 'next/image';
import AuthContext from '@/Context/AuthContext';
import ErrorHandler from '@/lib/ErrorHandler';

export default function Inbox() {
  const {user} = useContext(AuthContext);
  const [messagesData, setMessagesData]: any = useState([]);
  const [archivedMessagesData, setArchivedMessagesData]: any = useState([]);
  const [interview, setInterview] = useState([]);
  const router = useRouter();
  const [totalUnReadMessageCount, setTotalUnReadMessageCount] = useState(0);
  useEffect(() => {
    // getAllEmployerReceiverMessages(user?.id)
    getAllEmployeesReceiverMessages(user?.id)
      .then(res => {
        if (res.status == true) {
          setMessagesData(res.data);
        } else {
          setMessagesData([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
    getUserInterviewss(user?.id)
      .then(res => {
        if (res.status == true) {
          setInterview(res.data);
        } else {
          setInterview([]);
        }
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });
    getAllEmployeesReceiverArchivedMessages(user?.id)
      .then(res => {
        if (res.status == true) {
          setArchivedMessagesData(res.data);
        } else {
          setArchivedMessagesData([]);
        }
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });
    const message_data = {
      sender_id: user?.id,
    };
    getTotalMessageUnReadCount(message_data)
      .then(res => {
        if (res.status == true) {
          setTotalUnReadMessageCount(res.total_unread_message_count);
        } else {
          setTotalUnReadMessageCount(0);
        }
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });
  }, [user]);
  const handleClickMessageStatusUpdate = (e: any, receiver_id: any) => {
    let senderId;
    let receiverId;
    if (user?.id == receiver_id) {
      senderId = user?.id;
      receiverId = receiver_id;
    } else {
      senderId = receiver_id;
      receiverId = user?.id;
    }
    const data = {
      sender_id: senderId,
      receiver_id: receiverId,
    };
    updateMessagesReadUnReadStatus(data)
      .then(res => {
        if (res.status == true) {
          router.push('/employees/messages/inbox/' + receiver_id);
        } else {
          router.push('/employees/messages/inbox/' + receiver_id);
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  };
  return (
    <>
      <div className="dash-right">
        <h1>
          My <span className="span-color"> Messages</span>
        </h1>
        <div className="row ">
          <div className="col-sm-7">
            <ul className="list-loc m-m-0 mt-4">
              <li className="active">
                <Link href="/employees/messages">
                  Inbox <span className="tab-span-sa c-0070F5">{messagesData.length}</span>
                </Link>
              </li>
              <li>
                <Link href="/employees/messages/interviews">
                  Interviews <span className="tab-span-sa">{interview.length}</span>
                </Link>
              </li>
              <li>
                <Link href="/employees/messages/archived">
                  Archived <span className="tab-span-sa">{archivedMessagesData.length}</span>
                </Link>
              </li>
            </ul>
          </div>
        </div>
        <div className="data-management m-p-10  p-0">
          <div className="work-experience-fieild m-p-10">
            <div className="row">
              <div className="col-sm-6">
                <p className="f-22 m-center">Inbox</p>
              </div>
              <div className="col-sm-6 text-right">
                <p className="f-16 c-BABABA m-center">
                  You have {messagesData.length} {messagesData.length !== 1 ? 'chats' : 'chat'} and{' '}
                  {totalUnReadMessageCount} unread {totalUnReadMessageCount !== 1 ? 'messages' : 'message'}
                </p>
              </div>
            </div>
            {messagesData.length > 0 ? (
              messagesData.map((messages_data: any, index: any) => {
                let file_url = '';
                if (messages_data.attachment_path) {
                  file_url =
                    process.env.NEXT_PUBLIC_IMAGE_URL + 'images/messageAttachmentFile/' + messages_data.attachment_path;
                }
                const filename = file_url.substring(file_url.lastIndexOf('/') + 1);
                const extension = filename.split('.').pop();
                return (
                  <div className="box-text-img bg-CFE5FF mb-2 fff" key={index}>
                    <div className="row">
                      <div className="col-sm-1 text-center">
                        <img
                          src={messages_data.profile?.source || '/images/Avatars-1.png'}
                          alt="Avatars-1"
                          className="w-40"
                          width={40}
                          height={40}
                        />
                      </div>
                      <div className="col-sm-9">
                        <a
                          href="#"
                          onClick={(e: any) =>
                            handleClickMessageStatusUpdate(
                              e,
                              messages_data.sender_id == user?.id ? messages_data.receiver_id : messages_data.sender_id,
                            )
                          }>
                          <p className="f-16 c-4D4D4D w-700 ">
                            {messages_data.employer_name} - {messages_data.company_name}
                          </p>
                        </a>
                        <p className="f-16 w-600">
                          {messages_data.message_description ? (
                            messages_data.message_status === 'unread' ? (
                              <span className="message2">{messages_data.message_description}</span>
                            ) : (
                              <span className="message1">{messages_data.message_description}</span>
                            )
                          ) : messages_data.attachment_path ? (
                            extension == 'pdf' ||
                            extension == 'xlxs' ||
                            extension == 'xlx' ||
                            extension == 'docx' ||
                            extension == 'doc' ? (
                              <a
                                href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${messages_data.attachment_path}`}
                                target="_blank"
                                style={{fontSize: '14px'}}>
                                <i className="fa-solid fa-file" style={{fontSize: '16px'}}></i>{' '}
                                {messages_data.attachment_path}
                              </a>
                            ) : (
                              <a
                                href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${messages_data.attachment_path}`}
                                target="_blank"
                                style={{fontSize: '14px'}}>
                                <i className="fa-solid fa-image" style={{fontSize: '16px'}}></i>{' '}
                                {messages_data.attachment_path}
                              </a>
                            )
                          ) : (
                            ''
                          )}
                        </p>
                      </div>
                      <div className="col-sm-2 text-right">
                        <p className="f-16 c-4D4D4D">
                          {moment.utc(messages_data.created_at).local().startOf('seconds').fromNow()}
                        </p>
                        <p>{messages_data.unreadMessage > 0 && messages_data.unreadMessage + ' new message'}</p>
                      </div>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="work-experience-fieild m-p-10 text-center">
                <img
                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blank-3.png'}
                  alt="blank-3"
                  className=""
                  width={210}
                  height={153}
                />
                <p className="f-22 c-BABABA mb-1">You don't seem to have any messages</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
