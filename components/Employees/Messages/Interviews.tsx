import React, {useState, useEffect, useContext} from 'react';
import {
  getUserInterviewss,
  updateInterviewStatus,
  getAllEmployeesReceiverMessages,
  getAllEmployeesReceiverArchivedMessages,
} from '../../../lib/frontendapi';
import moment from 'moment';
import Link from 'next/link';
import 'react-toastify/dist/ReactToastify.css';
import Image from 'next/image';
import AuthContext from '@/Context/AuthContext';
import ErrorHandler from '@/lib/ErrorHandler';
import {notification} from 'antd';

export default function Interviews() {
  const {user} = useContext(AuthContext);
  const [interview, setInterview] = useState([]);
  const [messagesData, setMessagesData]: any = useState([]);
  const [archivedMessagesData, setArchivedMessagesData]: any = useState([]);
  const [isLoadingOne, setIsLoadingOne] = useState(false);
  const [isLoadingTwo, setIsLoadingTwo] = useState(false);

  useEffect(() => {
    getUserInterviewss(user?.id)
      .then(res => {
        if (res.status == true) {
          setInterview(res.data);
        } else {
          setInterview([]);
        }
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });
    getAllEmployeesReceiverMessages(user?.id)
      .then(res => {
        if (res.status == true) {
          setMessagesData(res.data);
        } else {
          setMessagesData([]);
        }
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });
    getAllEmployeesReceiverArchivedMessages(user?.id)
      .then(res => {
        if (res.status == true) {
          setArchivedMessagesData(res.data);
        } else {
          setArchivedMessagesData([]);
        }
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });
  }, [user]);
  const handleAccept = async (id: any) => {
    try {
      setIsLoadingOne(true);
      await updateInterviewStatus(id, 'accepted');
      notification.success({message: 'Interview accepted successfully'});
      setIsLoadingOne(false);
      getUserInterviewss(user?.id)
        .then(res => {
          if (res.status == true) {
            setInterview(res.data);
          } else {
            setInterview([]);
          }
        })
        .catch(error => {
          ErrorHandler.showNotification(error);
        });
    } catch (error) {
      ErrorHandler.showNotification(error);
    }
  };
  const handleReject = async (id: any) => {
    try {
      setIsLoadingTwo(true);
      await updateInterviewStatus(id, 'rejected');
      notification.success({message: 'Interview rejected successfully'});
      setIsLoadingTwo(false);
      getUserInterviewss(user?.id)
        .then(res => {
          if (res.status == true) {
            setInterview(res.data);
          } else {
            setInterview([]);
          }
        })
        .catch(error => {
          ErrorHandler.showNotification(error);
        });
    } catch (error) {
      ErrorHandler.showNotification(error);
    }
  };
  return (
    <>
      <div className="dash-right">
        <h1>
          My <span className="span-color">Messages</span>
        </h1>
        <div className="row ">
          <div className="col-sm-7">
            <ul className="list-loc m-m-0 mt-4">
              <li>
                <Link href="/employees/messages">
                  Inbox <span className="tab-span-sa">{messagesData.length}</span>
                </Link>
              </li>
              <li className="active">
                <Link href="/employees/messages/interviews">
                  Interviews <span className="tab-span-sa c-0070F5">{interview.length}</span>
                </Link>
              </li>
              <li>
                <Link href="/employees/messages/archived">
                  Archived <span className="tab-span-sa">{archivedMessagesData.length}</span>
                </Link>
              </li>
            </ul>
          </div>
          <div className="col-sm-5"></div>
        </div>
        <div className="work-experience-fieild m-p-10 mt-2">
          <div className="row">
            <div className="col-sm-12">
              <p className="f-22 m-center">Interviews Scheduled</p>
            </div>
          </div>
          <div className="row  ">
            {interview.length > 0 ? (
              interview.map((interview: any, index: any) => {
                return (
                  <div className="col-sm-6" key={index}>
                    <div className="p-3 bg-EBF4FF">
                      <div className="row">
                        <div className="col-sm-4">
                          <div className="bg-D9D9D9 work-senior p-2 text-center">
                            <p className="f-12 c-2C2C2C w-700 mb-sp">
                              {moment(interview.interview_schedule_date).format('MMMM')}
                            </p>
                            <h3 className="f-54 c-191919 mt-3 mb-3">
                              {moment(interview.interview_schedule_date).format('D')}
                            </h3>
                            <p className="f-12 c-2C2C2C w-700  mb-sp">
                              {moment(interview.interview_schedule_date).format('dddd')}
                            </p>
                          </div>
                        </div>
                        <div className="col-sm-8">
                          <div className="Senior-div work-senior">
                            <p className="f-18 c-191919">{interview.job_title}</p>
                            <p className="f-16 c-0070F5 w-600">{interview.company_name}</p>
                            <p className="f-12 c-747474 mb-sp mt-sp-add w-600">
                              <i className="fa-regular fa-clock icon-w-12"></i>{' '}
                              {moment(interview.interview_schedule_from_time, 'hh:mm A').format('hh:mm A')} -{' '}
                              {moment(interview.interview_schedule_to_time, 'hh:mm A').format('hh:mm A')}
                            </p>
                            <p className="f-12 c-747474 mb-sp mt-sp-add w-400">
                              <i className="fa-regular fa-file icon-w-12"></i> Here’s your interview link:
                              <br />{' '}
                              <a href={interview.meeting_link} className="c-0070F5 ">
                                {' '}
                                {interview.meeting_link}
                              </a>
                            </p>
                          </div>
                        </div>
                      </div>
                      <div className="row mt-3">
                        <div className="col-sm-5">
                          {interview.interview_status == 'scheduled' && (
                            <button
                              className="btn-app bg-D04E4F-app w-100 w-700 border-radius-4"
                              onClick={() => handleReject(interview.id)}>
                              {isLoadingTwo ? 'processing...' : 'DECLINE'}
                              <i className="fa-solid fa-circle-xmark c-FD7373"></i>
                            </button>
                          )}
                        </div>
                        <div className="col-sm-7">
                          {interview.interview_status == 'scheduled' && (
                            <button
                              className="btn-app bg-3D9F79-app w-100 w-700 border-radius-4"
                              onClick={() => handleAccept(interview.id)}>
                              {isLoadingOne ? 'processing...' : 'ACCEPT'}
                              <i className="fa-solid fa-circle-check  c-3D9F79"></i>
                            </button>
                          )}
                        </div>
                        <div className="col-sm-12">
                          {interview.interview_status == 'accepted' && (
                            <div className="col-sm-12">
                              <button
                                className="btn-app bg-3D9F79-app w-100 w-700 border-radius-4 align-center"
                                style={{textAlign: 'center'}}
                                disabled>
                                ACCEPTED <i className="fa-solid fa-circle-check  c-3D9F79"></i>
                              </button>
                            </div>
                          )}
                          {interview.interview_status == 'rejected' && (
                            <button
                              className="btn-app bg-D04E4F-app w-100 w-700 border-radius-4"
                              style={{textAlign: 'center'}}>
                              DECLINED <i className="fa-solid fa-circle-xmark c-FD7373"></i>
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="work-experience-fieild m-p-10 text-center">
                <img src="/images/blank-4.png" alt="blank-4" className="" width={518} height={149} />
                <p className="f-22 c-BABABA mb-1">You don't seem to have any interviews</p>
                <p className="f-18 c-BABABA">
                  Go to{' '}
                  <Link href="/employees/jobs" className="c-0070F5">
                    {' '}
                    Job
                  </Link>
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
