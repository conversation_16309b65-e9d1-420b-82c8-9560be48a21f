import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

export default function InterviewsBlank() {
  return (
    <>
      <div className="dash-right">
        <h1>
          My <span className="span-color">Messages</span>
        </h1>
        <div className="row ">
          <div className="col-sm-7">
            <ul className="list-loc m-m-0 mt-4">
              <li>
                <Link href="/employees/messages">
                  Inbox <span className="tab-span-sa">12</span>
                </Link>
              </li>
              <li className="active">
                <Link href="/employees/messages/interviews">
                  Interviews <span className="tab-span-sa c-0070F5">12</span>
                </Link>
              </li>
              <li>
                <Link href="/employees/messages/archived">
                  Archived <span className="tab-span-sa">12</span>
                </Link>
              </li>
            </ul>
          </div>
          <div className="col-sm-5">
            <ul className="blue-text-line mt-4 text-right">
              <li>
                <Link href="/common/emptyinterviews">Empty Interviews</Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="work-experience-fieild m-p-10 mt-2">
          <div className="row">
            <div className="col-sm-12">
              <p className="f-22 m-center">Interviews Scheduled</p>
            </div>
          </div>

          <div className="work-experience-fieild m-p-10 text-center">
            <img
              src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blank-4.png'}
              alt="blank-4"
              className=""
              width={518}
              height={149}
            />
            <p className="f-22 c-BABABA mb-1">You don't seem to have any interviews</p>
            <p className="f-18 c-BABABA">
              Go to{' '}
              <a href="#" className="c-0070F5">
                {' '}
                Job
              </a>
            </p>
          </div>
        </div>
      </div>
    </>
  );
}
