import React, {useState, useEffect, useContext} from 'react';
import {
  getUserInterviewss,
  getAllEmployeesReceiverMessages,
  getAllEmployeesReceiverArchivedMessages,
} from '../../../lib/frontendapi';
import moment from 'moment';
import Link from 'next/link';
import Image from 'next/image';
import AuthContext from '@/Context/AuthContext';
import <PERSON>rrorHandler from '@/lib/ErrorHandler';

export default function Archived() {
  const {user} = useContext(AuthContext);
  const [archivedMessagesData, setArchivedMessagesData]: any = useState([]);
  const [interview, setInterview] = useState([]);
  const [messagesData, setMessagesData]: any = useState([]);
  useEffect(() => {
    getUserInterviewss(user?.id)
      .then(res => {
        if (res.status == true) {
          setInterview(res.data);
        } else {
          setInterview([]);
        }
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });
    getAllEmployeesReceiverMessages(user?.id)
      .then(res => {
        if (res.status == true) {
          setMessagesData(res.data);
        } else {
          setMessagesData([]);
        }
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });
    getAllEmployeesReceiverArchivedMessages(user?.id)
      .then(res => {
        if (res.status == true) {
          setArchivedMessagesData(res.data);
        } else {
          setArchivedMessagesData([]);
        }
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });
  }, [user]);
  return (
    <>
      <div className="dash-right">
        <h1>
          My <span className="span-color"> Messages</span>
        </h1>
        <div className="row ">
          <div className="col-sm-7">
            <ul className="list-loc m-m-0 mt-4">
              <li>
                <Link href="/employees/messages">
                  Inbox <span className="tab-span-sa">{messagesData.length}</span>
                </Link>
              </li>
              <li>
                <Link href="/employees/messages/interviews">
                  Interviews <span className="tab-span-sa">{interview.length}</span>
                </Link>
              </li>
              <li className="active">
                <Link href="/employees/messages/archived">
                  Archived <span className="tab-span-sa  c-0070F5">{archivedMessagesData.length}</span>
                </Link>
              </li>
            </ul>
          </div>
          <div className="col-sm-5"></div>
        </div>
        <div className="data-management m-p-10 p-0">
          <div className="work-experience-fieild m-p-10">
            <div className="row">
              <div className="col-sm-6">
                <p className="f-22 m-center">Archived Messages</p>
              </div>
              <div className="col-sm-6 text-right">
                <p className="f-16 c-BABABA m-center">You have {archivedMessagesData.length} messages.</p>
              </div>
            </div>
            {archivedMessagesData.length > 0 ? (
              archivedMessagesData.map((archived_messages_data: any, index: any) => {
                let file_url = '';
                if (archived_messages_data.attachment_path) {
                  file_url =
                    process.env.NEXT_PUBLIC_IMAGE_URL +
                    'images/messageAttachmentFile/' +
                    archived_messages_data.attachment_path;
                }
                const filename = file_url.substring(file_url.lastIndexOf('/') + 1);
                const extension = filename.split('.').pop();
                return (
                  <div className="box-text-img bg-CFE5FF  mb-2" key={index}>
                    <div className="row">
                      <div className="col-sm-1 text-center">
                        <img
                          src={
                            archived_messages_data.employer_profile_image
                              ? `${process.env.NEXT_PUBLIC_IMAGE_URL}images/userprofileImg/${archived_messages_data.employer_profile_image}`
                              : `${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-1.png`
                          }
                          alt="Avatars-1"
                          className="w-40"
                          width={40}
                          height={40}
                        />
                      </div>
                      <div className="col-sm-9">
                        {archived_messages_data.user_role == 'admin' ? (
                          <Link href={'/employees/messages/archived/' + archived_messages_data.receiver_id}>
                            <p className="f-16 c-4D4D4D w-700 ">Talent Point</p>
                          </Link>
                        ) : archived_messages_data.user_role ? (
                          <Link href={'/employees/messages/archived/' + archived_messages_data.receiver_id}>
                            <p className="f-16 c-4D4D4D w-700 ">{archived_messages_data.employer_name}</p>
                          </Link>
                        ) : (
                          <Link href={'/employees/messages/archived/' + archived_messages_data.receiver_id}>
                            <p className="f-16 c-4D4D4D w-700 ">
                              {archived_messages_data.employer_name}.{archived_messages_data.company_name}
                            </p>
                          </Link>
                        )}
                        <p className="f-16 w-600">
                          {archived_messages_data.message_description ? (
                            archived_messages_data.message_description
                          ) : archived_messages_data.attachment_path ? (
                            extension == 'pdf' ||
                            extension == 'xlxs' ||
                            extension == 'xlx' ||
                            extension == 'docx' ||
                            extension == 'doc' ? (
                              <a
                                href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${archived_messages_data.attachment_path}`}
                                target="_blank"
                                style={{fontSize: '14px'}}>
                                <i className="fa-solid fa-file" style={{fontSize: '16px'}}></i>{' '}
                                {archived_messages_data.attachment_path}
                              </a>
                            ) : (
                              <a
                                href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${archived_messages_data.attachment_path}`}
                                target="_blank"
                                style={{fontSize: '14px'}}>
                                <i className="fa-solid fa-image" style={{fontSize: '16px'}}></i>{' '}
                                {archived_messages_data.attachment_path}
                              </a>
                            )
                          ) : (
                            ''
                          )}
                        </p>
                      </div>
                      <div className="col-sm-2 text-right">
                        <p className="f-16 c-4D4D4D">
                          {moment.utc(archived_messages_data.created_at).local().startOf('seconds').fromNow()}
                        </p>
                      </div>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="work-experience-fieild m-p-10 text-center">
                <img
                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blank-3.png'}
                  alt="blank-3"
                  className=""
                  width={210}
                  height={153}
                />
                <p className="f-22 c-BABABA mb-1">You don't seem to have any messages</p>
                <p className="f-18 c-BABABA">
                  Go to{' '}
                  <Link href="/employees/messages" className="c-0070F5">
                    {' '}
                    Messages
                  </Link>
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
