import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Image from 'next/image';
export default function Resume() {
  const router = useRouter();

  useEffect(() => {
    let resume_design = window.sessionStorage.getItem('resume-design');
    if (resume_design != 'yes') {
      router.push('/employees/resume');
    }
  }, [router]);

  const [resumeTemp, setResumeTemplate] = useState('template-one');
  const [showContent, setShowContent] = useState(true);
  const toggleContent = () => {
    setShowContent(!showContent);
  };

  const handleStartClick = () => {
    window.sessionStorage.setItem('resume-preview', 'yes');
    if (resumeTemp == 'template-one') {
      router.push('/employees/resume/template-one');
    } else {
      router.push('/employees/resume/template-two');
    }
  };

  return (
    <>
      <div className="dash-right">
        <h1>
          Choose a <span className="span-color"> design</span>
        </h1>
        <div className="d-card">
          <div className="build-download">
            {showContent && (
              <div className="row gx-5 ">
                <div className="col-lg-6 col-sm-12">
                  <div className="slider-img-plase">
                    <input
                      type="radio"
                      id="myCheckbox1"
                      name="service_type"
                      value={'template-one'}
                      className="step_radio_css "
                      checked={resumeTemp === 'template-one'}
                      onChange={e => setResumeTemplate('template-one')}
                    />
                    <label htmlFor="myCheckbox1" className="step_label_css">
                      <img
                        src={process.env.NEXT_PUBLIC_BASE_URL + 'images/template-one.jpg'}
                        className="resume-design-img"
                        alt="step-img-1"
                      />
                    </label>
                  </div>
                </div>

                <div className="col-lg-6 col-sm-12">
                  <div className="slider-img-plase">
                    <input
                      type="radio"
                      id="myCheckbox2"
                      name="service_type"
                      value={'template-two'}
                      className="step_radio_css"
                      checked={resumeTemp === 'template-two'}
                      onChange={e => setResumeTemplate('template-two')}
                    />

                    <label htmlFor="myCheckbox2" className="step_label_css">
                      <img
                        src={process.env.NEXT_PUBLIC_BASE_URL + 'images/tempate-two.jpg'}
                        className="resume-design-img"
                        alt="step-img-1"
                      />
                    </label>
                  </div>
                </div>

                <p className="start-now mt-5 text-center">
                  <a onClick={handleStartClick} role="button">
                    Next <i className="fa-solid fa-angles-right"></i>
                  </a>
                </p>
              </div>
            )}

            <i
              className={`fa-solid ${showContent ? 'fa-chevron-up' : 'fa-chevron-down'} up-down-icon`}
              onClick={toggleContent}></i>
          </div>
        </div>
      </div>
    </>
  );
}
