import { useState, useEffect, useContext } from 'react';
import { uploadResume, getSingleOwnResume, deleteResume, updateDefaultResume } from '../../../lib/employeeapi';
import swal from 'sweetalert';
import Link from 'next/link';
import { useRouter } from 'next/router';
import AuthContext from '@/Context/AuthContext';
import { notification, Button } from 'antd';
import <PERSON>rror<PERSON>andler from '@/lib/ErrorHandler';
import Image from 'next/image';

export default function Resume() {
  const router = useRouter();
  const { user } = useContext(AuthContext);
  const [allResumes, setAllResume] = useState([]);
  const [defaultResume, setDefaultResume] = useState(0);
  const [showContent, setShowContent] = useState(true);

  const toggleContent = () => {
    setShowContent(!showContent);
  };

  useEffect(() => {
    getSingleOwnResumeData(user?.id);
  }, [user]);

  const getSingleOwnResumeData = async (id: any) => {
    try {
      const res = await getSingleOwnResume(id);
      if (res.status == true) {
        setAllResume(res.data);
        if (res.data.length == 0) {
          setDefaultResume(1);
        }
      }
    } catch (error) {
      ErrorHandler.showNotification(error);
    }
  };

  const handleFileUpload = (event: any) => {
    const fileInput = event.target;
    const file = fileInput.files[0];
    if (file) {
      const allowedFormats = ['pdf'];
      const fileExtension = file.name.split('.').pop().toLowerCase();
      if (!allowedFormats.includes(fileExtension)) {
        notification.error({ message: 'Invalid file. Only PDF files are allowed.' });
        fileInput.value = '';
        return;
      }

      const maxSizeInBytes = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSizeInBytes) {
        notification.error({ message: 'File size exceeds the maximum limit (10MB).' });
        fileInput.value = '';
        return;
      }

      const data = { user_id: user?.id, default_resume: defaultResume };
      uploadResume(data, file)
        .then(response => {
          if (allResumes.length >= 4) {
            notification.error({ message: 'You have already uploaded four resumes.' });
            fileInput.value = '';
            return;
          }
          notification.success({
            message: response.message,
          });
          fileInput.value = '';
          getSingleOwnResumeData(user?.id);
        })
        .catch(error => {
          ErrorHandler.showNotification(error);
        });
    }
  };

  const handleDelete = (e: any, id: any) => {
    e.preventDefault();
    swal({
      title: 'Are you sure?',
      text: 'You want to delete your Resume',
      icon: 'warning',
      dangerMode: true,
      buttons: ['Cancel', 'Yes, I am sure!'],
    }).then(willDelete => {
      if (willDelete) {
        deleteResume(id)
          .then(res => {
            if (res.status === true) {
              swal('Your Resume has been deleted!', {
                icon: 'success',
              });
              getSingleOwnResumeData(user?.id);
            } else {
              notification.info({
                message: res.message,
              });
            }
          })
          .catch(err => {
            ErrorHandler.showNotification(err);
          });
      }
    });
  };

  const handleSetDefault = (resumeId: any) => {
    updateDefaultResume(resumeId)
      .then(response => {
        getSingleOwnResumeData(user?.id);
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });
  };

  const handleStartClick = () => {
    window.sessionStorage.setItem('resume-design', 'yes');
    router.push('/employees/resume/choose-design');
  };

  return (
    <>
      <div className="dash-right">
        <h1>
          My <span className="span-color"> Resume</span>
        </h1>
        <div className="d-card">
          <div className="build-download">
            <h2>Build & download your professional resume in seconds — for FREE! </h2>
            {showContent && (
              <div className="row g-3">
                <div className="col-sm-3">
                  <div className="slider-cv">
                    <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/cv.png'} alt="cv" />
                  </div>
                </div>
                <div className="col-sm-9">
                  <div className='bootsMaker'>
                    <h5>Boost your chances of landing your dream job with our professional resume maker</h5>
                    <h5 className="blue-title mt-4">How do I get it?</h5>
                    <p>
                      <b>Step 1:</b> Complete your online profile.
                    </p>
                    <p>
                      <b>Step 2:</b> Select a design.
                    </p>
                    <p>
                      <b>Step 3:</b> Preview your resume.
                    </p>
                    <p>
                      <b>Step 4:</b> Generate Pdf!
                    </p>

                    <p className="start-now mt-3">
                      <a onClick={handleStartClick} role="button">
                        Start Now <i className="fa-solid fa-angles-right"></i>
                      </a>
                    </p>
                  </div>
                </div>
              </div>
            )}
            <i
              className={`fa-solid ${showContent ? 'fa-chevron-up' : 'fa-chevron-down'} up-down-icon`}
              onClick={toggleContent}></i>
          </div>
          <div className="dash-card mt-5  p-3">
            {allResumes.map((resume: any, index) => (
              <div className="w-box bg-fff mt-2 p-3" key={index}>
                <div className="row">
                  <div className="col-sm-8">
                    <p className="f-18 w-600 mb-2">
                      {resume && resume.resume_pdf_path.replace(/[0-9]/g, '')}
                    </p>
                    <p className="f-16 c-999999">
                      Uploaded on{' '}
                      {new Date(resume.created_at).toLocaleString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        hour: 'numeric',
                        minute: 'numeric',
                      })}
                    </p>
                  </div>
                  <div className="col-sm-4 text-right">
                    <Button
                      className={resume.default_resume === 1 ? "default-set" : "default"}
                      onClick={resume.default_resume === 0 ? () => handleSetDefault(resume.id) : undefined}>
                      {resume.default_resume === 1 ? 'Default' : 'Set Default'}
                    </Button>
                    &nbsp;&nbsp;
                    <i
                      className="fa-regular fa-trash-can del-trash  mx-2"
                      onClick={e => handleDelete(e, resume.id)}></i>
                    <Link
                      target="_blank"
                      href={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/employee/resume/' + resume.resume_pdf_path}>
                      <span>
                        <i className="fa fa-eye view-resume-employee eye-resume"></i>
                      </span>
                    </Link>
                  </div>
                </div>
              </div>
            ))}
            <div className="text-right">
              <div className="uploade-btn">
                <input
                  type="file"
                  name="resume_pdf_path"
                  id="resume_pdf_path"
                  accept=".pdf, .docx"
                  onChange={handleFileUpload}
                />
                <button
                  className="btn-a primary-size-16 btn-bg-0055BA mt-2 mb-4 mobile-m-sp max-340"
                  style={{ height: 'auto' }}>
                  <i className="fa-solid fa-upload"></i> Upload your recent Resume/CV:
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
