import React, { useState, useEffect, useRef, useContext } from 'react';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import {
  getSingleUserSkill,
  getEducationsEmployee,
  getLanguage,
  getWorkExperienceEmployee,
  getportfolio,
} from '../../../lib/frontendapi';
import { useRouter } from 'next/router';
import AuthContext from '@/Context/AuthContext';
import ErrorHandler from '@/lib/ErrorHandler';

function App() {
  const router = useRouter();
  const { user } = useContext(AuthContext);
  const [workExperienceList, setWorkExperienceList] = useState([]);
  const [skill, setSkill] = useState([]);
  const [educationList, setEducationList] = useState([]);
  const [languageList, setLanguageList] = useState([]);
  const [portfolioList, setPortfolioList] = useState([]);

  useEffect(() => {
    let resume_preview = window.sessionStorage.getItem('resume-preview');
    if (resume_preview != 'yes') {
      router.push('/employees/resume');
    } else {
      if (user?.id) {
        fetchData(user.id);
      } else {
        ErrorHandler.showNotification('User ID is undefined');
      }
    }
  }, [user, router]);

  const fetchData = async (id: number) => {
    try {
      const [skillData, educationData, languageData, workExpData, portfolioData] = await Promise.all([
        getSingleUserSkill(id),
        getEducationsEmployee(id),
        getLanguage(id),
        getWorkExperienceEmployee(id),
        getportfolio(id),
      ]);

      if (skillData.status) setSkill(skillData.data);
      if (educationData.status) setEducationList(educationData.education);
      if (languageData.status) setLanguageList(languageData.data);
      if (workExpData.status) setWorkExperienceList(workExpData.data);
      if (portfolioData.status) setPortfolioList(portfolioData.data);
    } catch (error) {
      ErrorHandler.showNotification(error);
    }
  };

  const formatDate = (dateStr: string) =>
    new Date(dateStr).toLocaleDateString(undefined, { year: 'numeric', month: 'long' });
  const WorkExpFormatDate = (dateStr: string) => new Date(dateStr).toLocaleDateString(undefined, { year: 'numeric' });

  const reportTemplateRef = useRef(null);

  const handleGeneratePdf = async () => {
    window.sessionStorage.removeItem('resume-design');
    window.sessionStorage.removeItem('resume-preview');

    const content = reportTemplateRef.current;
    if (!content) return ErrorHandler.showNotification('Ref is not assigned to an element');

    try {
      // Capture the content with high quality
      const canvas = await html2canvas(content, {
        scale: 2, // Higher scale for better quality
        useCORS: true,
        logging: false,
        // letterRendering: true,
        allowTaint: true
      });

      // Get the dimensions
      const imgWidth = 210; // A4 width in mm (210mm × 297mm)
      const pageHeight = 297; // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      // Create PDF with proper dimensions
      const pdf = new jsPDF('p', 'mm', 'a4');

      let position = 0;
      let heightLeft = imgHeight;

      // Add image to PDF (first page)
      pdf.addImage(canvas.toDataURL('image/jpeg', 1.0), 'JPEG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      // Add new pages if content overflows
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(canvas.toDataURL('image/jpeg', 1.0), 'JPEG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      pdf.save(`${user?.name}_cv.pdf`);
    } catch (error) {
      ErrorHandler.showNotification('Error generating PDF: ' + error);
    }
  };
  const handleBackButton = () => router.push('/employees/resume/choose-design');
  return (
    <div>
      <div style={{ width: '80%', overflow: 'hidden', margin: '0 auto' }} className="mt-4">
        <div ref={reportTemplateRef}>
          <article className="resume-wrapper text-center position-relative" id="template_two">
            <div className="resume-wrapper-inner mx-auto text-start bg-white shadow-lg">
              <header className="resume-header pt-4 pt-md-0">
                <div className="row">
                  <div className="col">
                    <div className="row p-4 justify-content-center justify-content-md-between">
                      <div className="primary-info col-auto">
                        <h1 className="name mt-0 mb-1 text-white text-uppercase text-uppercase"> {user?.name}</h1>
                        <div className="title mb-3">{user?.current_position}</div>
                        <ul className="list-unstyled">
                          {user?.email && (
                            <li className="mb-2">
                              <i className="far fa-envelope fa-fw me-2" data-fa-transform="grow-3"></i>
                              {user?.email}
                            </li>
                          )}
                          {user?.contact_no && (
                            <li>
                              <i className="fas fa-mobile-alt fa-fw me-2" data-fa-transform="grow-6"></i>
                              {user?.contact_no}
                            </li>
                          )}
                        </ul>
                      </div>
                      <div className="secondary-info col-auto mt-2">
                        <ul className="resume-social list-unstyled">
                          {user?.linkedin_link && (
                            <li className="mb-2">
                              <span className="fa-container text-center me-2">
                                <i className="fab fa-linkedin-in fa-fw"></i>
                              </span>
                              {user?.linkedin_link.replace(/^(https?:\/\/)?(www\.)?/, '')}
                            </li>
                          )}
                          {user?.facebook_link && (
                            <li className="mb-2">
                              <span className="fa-container text-center me-2">
                                <i className="fab fa-facebook fa-fw"></i>
                              </span>
                              {user?.facebook_link.replace(/^(https?:\/\/)?(www\.)?/, '')}
                            </li>
                          )}
                          {user?.twitter_link && (
                            <li className="mb-2">
                              <span className="fa-container text-center me-2">
                                <i className="fab fa-twitter fa-fw"></i>
                              </span>
                              {user?.twitter_link.replace(/^(https?:\/\/)?(www\.)?/, '')}
                            </li>
                          )}
                          {user?.instagram_link && (
                            <li className="mb-2">
                              <span className="fa-container text-center me-2">
                                <i className="fab fa-instagram fa-fw"></i>
                              </span>
                              {user?.instagram_link.replace(/^(https?:\/\/)?(www\.)?/, '')}
                            </li>
                          )}
                          {user?.website_url && (
                            <li>
                              <span className="fa-container text-center me-2">
                                <i className="fas fa-globe"></i>
                              </span>
                              {user?.website_url.replace(/^(https?:\/\/)?(www\.)?/, '')}
                            </li>
                          )}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </header>
              <div className="resume-body p-5">
                {user?.bio && (
                  <section className="resume-section summary-section mb-5">
                    <h2 className="resume-section-title text-uppercase font-weight-bold pb-3 mb-3">Career Summary</h2>
                    <div className="resume-section-content">
                      <p className="mb-0">{user?.bio}</p>
                    </div>
                  </section>
                )}

                <div className="row">
                  <div className="col-lg-9">
                    {workExperienceList.length > 0 && (
                      <section className="resume-section experience-section mb-5">
                        <h2 className="resume-section-title text-uppercase font-weight-bold pb-3 mb-3">
                          Work Experience
                        </h2>
                        <div className="resume-section-content">
                          <div className="resume-timeline position-relative">
                            {workExperienceList.map((workexperience: any, index: any) => (
                              <article className="resume-timeline-item position-relative pb-5" key={index}>
                                <div className="resume-timeline-item-header mb-2">
                                  <div className="d-flex flex-column flex-md-row">
                                    <h3 className="resume-position-title font-weight-bold mb-1">
                                      {' '}
                                      {workexperience.title}
                                    </h3>
                                    <div className="resume-company-name ms-auto">{workexperience.company}</div>
                                  </div>
                                  <div className="resume-position-time">
                                    {WorkExpFormatDate(workexperience.start_date || '')}-{' '}
                                    {workexperience.currently_work_here == 0
                                      ? WorkExpFormatDate(workexperience.end_date || '')
                                      : 'Present'}
                                  </div>
                                </div>
                                <div className="resume-timeline-item-desc">
                                  <p> {workexperience.description}</p>
                                </div>
                              </article>
                            ))}
                          </div>
                        </div>
                      </section>
                    )}
                    {portfolioList.length > 0 && (
                      <section className="resume-section experience-section mb-5">
                        <h2 className="resume-section-title text-uppercase font-weight-bold pb-3 mb-3">PROJECTS</h2>
                        <div className="resume-section-content">
                          <div className="resume-timeline position-relative">
                            {portfolioList.map((portfolio: any, index: any) => (
                              <article className="resume-timeline-item position-relative pb-5" key={index}>
                                <div className="resume-timeline-item-header mb-2">
                                  <div className="d-flex flex-column flex-md-row">
                                    <h3 className="resume-position-title font-weight-bold mb-1"> {portfolio.title}</h3>
                                    <div className="resume-company-name ms-auto">
                                      {formatDate(portfolio.start_date || '')}-{' '}
                                      {portfolio.present == null ? formatDate(portfolio.end_date || '') : 'Present'}
                                    </div>
                                  </div>
                                </div>
                                <div className="resume-timeline-item-desc">
                                  <p> {portfolio.description}</p>
                                </div>
                              </article>
                            ))}
                          </div>
                        </div>
                      </section>
                    )}
                  </div>
                  <div className="col-lg-3">
                    {skill.length > 0 && (
                      <section className="resume-section skills-section mb-5">
                        <h2 className="resume-section-title text-uppercase font-weight-bold pb-3 mb-3">
                          Skills &amp; Tools
                        </h2>
                        <div className="resume-section-content">
                          <div className="resume-skill-item">
                            <h4 className="resume-skills-cat font-weight-bold">Technical</h4>
                            <ul className="list-unstyled mb-4">
                              {skill.map((ski: any, index) => (
                                <li key={index} className="list-inline-item mb-2 d-block">
                                  <span className="badge bg-secondary badge-pill"> {ski.skills}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </section>
                    )}
                    {educationList.length > 0 && (
                      <section className="resume-section education-section mb-5">
                        <h2 className="resume-section-title text-uppercase font-weight-bold pb-3 mb-3">Education</h2>
                        <div className="resume-section-content">
                          <ul className="list-unstyled">
                            {educationList.map((education: any, index: any) => (
                              <li className="mb-2" key={index}>
                                <div className="resume-degree font-weight-bold"> {education.education_title}</div>
                                <div className="resume-degree-org"> {education.degree}</div>
                                <div className="resume-degree-time">
                                  {' '}
                                  {formatDate(education.start_date || '')}-{' '}
                                  {education.currently_study_here == 0
                                    ? formatDate(education.end_date || '')
                                    : 'currently study here'}
                                </div>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </section>
                    )}
                    {languageList.length > 0 && (
                      <section className="resume-section language-section mb-5">
                        <h2 className="resume-section-title text-uppercase font-weight-bold pb-3 mb-3">Language</h2>
                        <div className="resume-section-content">
                          <ul className="list-unstyled resume-lang-list">
                            {languageList.map((langage: any, index: any) => (
                              <li className="mb-2" key={index}>
                                <span className="resume-lang-name font-weight-bold"> {langage.language} </span>{' '}
                                <small className="text-muted font-weight-normal">({langage.proficiency})</small>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </section>
                    )}
                    {user?.job_type && (
                      <section className="resume-section interests-section mb-5">
                        <h2 className="resume-section-title text-uppercase font-weight-bold pb-3 mb-3">
                          Job Preference
                        </h2>
                        <div className="resume-section-content">
                          <ul className="list-unstyled">
                            {user?.job_type.split(',').map((part, index) => (
                              <li key={index}>
                                <div className="font-weight-bold">
                                  {part == 'parttime' && 'Part Time'}
                                  {part == 'fulltime' && 'Full Time'}
                                  {part == 'contract' && 'Contract'}
                                  {part == 'freelance' && 'Freelance'}
                                </div>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </section>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </article>
        </div>
        <div className="text-end mb-4" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <p className="start-now mt-5 text-center">
            <a onClick={handleBackButton} role="button">
              <i className="fa-solid fa-angles-left"></i> Resume Samples
            </a>
          </p>
          <p className="start-now mt-5 text-center">
            <a onClick={handleGeneratePdf} role="button">
              Generate PDF <i className="fa-solid fa-angles-right"></i>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
export default App;
