import React, { useState, useEffect, useRef, useContext } from 'react';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import {
  getSingleUserSkill,
  getEducationsEmployee,
  getLanguage,
  getWorkExperienceEmployee,
  getportfolio,
} from '../../../lib/frontendapi';
import { useRouter } from 'next/router';
import AuthContext from '@/Context/AuthContext';
import ErrorHandler from '@/lib/ErrorHandler';

function App() {
  const router = useRouter();
  const { user } = useContext(AuthContext);
  const [workExperienceList, setWorkExperienceList] = useState([]);
  const [skill, setSkill] = useState([]);
  const [educationList, setEducationList] = useState([]);
  const [languageList, setLanguageList] = useState([]);
  const [portfolioList, setPortfolioList] = useState([]);

  useEffect(() => {
    let resume_preview = window.sessionStorage.getItem('resume-preview');
    if (resume_preview != 'yes') {
      router.push('/employees/resume');
    } else {
      if (user?.id) {
        fetchData(user.id);
      } else {
        ErrorHandler.showNotification('User ID is undefined');
      }
    }
  }, [user, router]);

  const fetchData = async (id: number) => {
    try {
      const [skillData, educationData, languageData, workExpData, portfolioData] = await Promise.all([
        getSingleUserSkill(id),
        getEducationsEmployee(id),
        getLanguage(id),
        getWorkExperienceEmployee(id),
        getportfolio(id),
      ]);

      if (skillData.status) setSkill(skillData.data);
      if (educationData.status) setEducationList(educationData.education);
      if (languageData.status) setLanguageList(languageData.data);
      if (workExpData.status) setWorkExperienceList(workExpData.data);
      if (portfolioData.status) setPortfolioList(portfolioData.data);
    } catch (error) {
      ErrorHandler.showNotification(error);
    }
  };

  const formatDate = (dateStr: string) =>
    new Date(dateStr).toLocaleDateString(undefined, { year: 'numeric', month: 'long' });

  const WorkExpFormatDate = (dateStr: string) => new Date(dateStr).toLocaleDateString(undefined, { year: 'numeric' });

  const birthdayDateFormat = (dateStr: string) =>
    new Date(dateStr).toLocaleDateString(undefined, { year: 'numeric', month: 'long', day: 'numeric' });

  const reportTemplateRef = useRef(null);

  const handleGeneratePdf = async () => {
    window.sessionStorage.removeItem('resume-design');
    window.sessionStorage.removeItem('resume-preview');

    const content = reportTemplateRef.current;
    if (!content) return ErrorHandler.showNotification('Ref is not assigned to an element');

    try {
      // Capture the content with high quality
      const canvas = await html2canvas(content, {
        scale: 2, // Higher scale for better quality
        useCORS: true,
        logging: false,
        // letterRendering: true,
        allowTaint: true
      });

      // Get the dimensions
      const imgWidth = 210; // A4 width in mm (210mm × 297mm)
      const pageHeight = 297; // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      // Create PDF with proper dimensions
      const pdf = new jsPDF('p', 'mm', 'a4');

      let position = 0;
      let heightLeft = imgHeight;

      // Add image to PDF (first page)
      pdf.addImage(canvas.toDataURL('image/jpeg', 1.0), 'JPEG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      // Add new pages if content overflows
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(canvas.toDataURL('image/jpeg', 1.0), 'JPEG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      pdf.save(`${user?.name}_cv.pdf`);
    } catch (error) {
      ErrorHandler.showNotification('Error generating PDF: ' + error);
    }
  };
  const handleBackButton = () => router.push('/employees/resume/choose-design');

  return (
    <div>
      <div style={{ width: '90%', overflow: 'hidden', margin: '0 auto' }} className="mt-4">
        <div ref={reportTemplateRef} className="bg-white">
          <div className="main-wrapper">
            <div className="container px-3 px-lg-5">
              <article className="resume-wrapper mx-auto theme-bg-light p-5 mt-2">
                <div className="resume-header">
                  <div className="row align-items-center">
                    <div className="resume-title col-12 col-md-6 col-lg-8 col-xl-9">
                      <h2 className="resume-name mb-0 text-uppercase">{user?.name}</h2>
                      <div className="resume-tagline mb-3 mb-md-0">{user?.current_position}</div>
                    </div>

                    <div className="resume-contact col-12 col-md-6 col-lg-4 col-xl-3">
                      <ul className="list-unstyled mb-0">
                        {user?.contact_no && (
                          <li className="mb-2">
                            <i className="fas fa-phone-square fa-fw fa-lg me-2 "></i>
                            {user?.contact_no}
                          </li>
                        )}
                        {user?.email && (
                          <li className="mb-2">
                            <i className="fas fa-envelope-square fa-fw fa-lg me-2"></i>
                            {user?.email}
                          </li>
                        )}
                        {user?.website_url && (
                          <li className="mb-2">
                            <i className="fas fa-globe fa-fw fa-lg me-2"></i>
                            {user?.website_url.replace(/^(https?:\/\/)?(www\.)?/, '')}
                          </li>
                        )}
                        {user?.country_name && (
                          <li className="mb-2">
                            <i className="fas fa-map-marker-alt fa-fw fa-lg me-2"></i>
                            {user?.country_name}
                          </li>
                        )}
                        {user?.gender && (
                          <li className="mb-2 text-capitalize">
                            <i className="fa fa-transgender fa-fw fa-lg me-2 "></i>
                            {user?.gender}
                          </li>
                        )}
                        {user?.date_of_birth && (
                          <li className="mb-0">
                            <i className="fa fa-birthday-cake fa-fw fa-lg me-2"></i>{' '}
                            {birthdayDateFormat(user?.date_of_birth || '')}
                          </li>
                        )}
                      </ul>
                    </div>
                  </div>
                </div>
                <hr />
                <div className="resume-intro py-3">
                  <div className="row align-items-center">
                    <div className="col text-start">{user?.bio && <p className="mb-0">{user?.bio}</p>}</div>
                  </div>
                </div>
                <hr />
                <div className="resume-body">
                  <div className="row">
                    <div className="resume-main col-12 col-lg-8 col-xl-9 pe-0 pe-lg-5">
                      {workExperienceList.length > 0 && (
                        <section className="work-section py-3">
                          <h3 className="text-uppercase resume-section-heading mb-4">Work Experiences</h3>
                          {workExperienceList.map((workexperience: any, index: any) => (
                            <div className="item mb-3" key={index}>
                              <div className="item-heading row align-items-center mb-2">
                                <h4 className="item-title col-12 col-md-6 col-lg-8 mb-2 mb-md-0">
                                  {workexperience.title}
                                </h4>
                                <div className="item-meta col-12 col-md-6 col-lg-4 text-muted text-start text-md-end">
                                  {workexperience.company} | {WorkExpFormatDate(workexperience.start_date || '')}-{' '}
                                  {workexperience.currently_work_here == 0
                                    ? WorkExpFormatDate(workexperience.end_date || '')
                                    : 'Present'}
                                </div>
                              </div>
                              <div className="item-content">{workexperience.description}</div>
                            </div>
                          ))}
                        </section>
                      )}
                      {portfolioList.length > 0 && (
                        <section className="project-section py-3">
                          <h3 className="text-uppercase resume-section-heading mb-4">Projects</h3>
                          {portfolioList.map((portfolio: any, index: any) => (
                            <div className="item mb-3" key={index}>
                              <div className="item-heading row align-items-center mb-2">
                                <h4 className="item-title col-12 col-md-6 col-lg-8 mb-2 mb-md-0">{portfolio.title}</h4>
                                <div className="item-meta col-12 col-md-6 col-lg-4 text-muted text-start text-md-end">
                                  {formatDate(portfolio.start_date || '')}-{' '}
                                  {portfolio.present == null ? formatDate(portfolio.end_date || '') : 'Present'}
                                </div>
                              </div>
                              <div className="item-content">
                                <p>{portfolio.description}</p>
                              </div>
                            </div>
                          ))}
                        </section>
                      )}
                    </div>

                    <aside className="resume-aside col-12 col-lg-4 col-xl-3 px-lg-4 pb-lg-4">
                      {skill.length > 0 && (
                        <section className="skills-section py-3">
                          <h3 className="text-uppercase resume-section-heading mb-4">Skills</h3>
                          <div className="item">
                            <h4 className="item-title">Technical</h4>
                            <ul className="list-unstyled resume-skills-list">
                              {skill.map((ski: any, index) => (
                                <li key={index} className="mb-2">
                                  {ski.skills}
                                </li>
                              ))}
                            </ul>
                          </div>
                        </section>
                      )}

                      {educationList.length > 0 && (
                        <section className="education-section py-3">
                          <h3 className="text-uppercase resume-section-heading mb-4">Education</h3>
                          <ul className="list-unstyled resume-education-list">
                            {educationList.map((education: any, index: any) => (
                              <li className="mb-3" key={index}>
                                <div className="resume-degree font-weight-bold">{education.education_title}</div>
                                <div className="resume-degree-org text-muted">{education.degree}</div>
                                <div className="resume-degree-time text-muted">
                                  {formatDate(education.start_date || '')}-{' '}
                                  {education.currently_study_here == 0
                                    ? formatDate(education.end_date || '')
                                    : 'currently study here'}
                                </div>
                              </li>
                            ))}
                          </ul>
                        </section>
                      )}

                      {languageList.length > 0 && (
                        <section className="skills-section py-3">
                          <h3 className="text-uppercase resume-section-heading mb-4">Languages</h3>
                          <ul className="list-unstyled resume-lang-list">
                            {languageList.map((langage: any, index: any) => (
                              <li className="text-capitalize" key={index}>
                                {langage.language} <span className="text-muted">({langage.proficiency})</span>
                              </li>
                            ))}
                          </ul>
                        </section>
                      )}

                      {user?.job_type && (
                        <section className="education-section py-3">
                          <h3 className="text-uppercase resume-section-heading mb-4">Job Preference</h3>
                          <ul className="list-unstyled resume-awards-list">
                            {user?.job_type.split(',').map((part, index) => (
                              <li key={index}>
                                <div className="font-weight-bold">
                                  {part == 'parttime' && 'Part Time'}
                                  {part == 'fulltime' && 'Full Time'}
                                  {part == 'contract' && 'Contract'}
                                  {part == 'freelance' && 'Freelance'}
                                </div>
                              </li>
                            ))}
                          </ul>
                        </section>
                      )}
                      {(user?.linkedin_link || user?.twitter_link || user?.instagram_link || user?.facebook_link) && (
                        <section className="skills-section py-3">
                          <h3 className="text-uppercase resume-section-heading mb-4">Social Links</h3>
                          <ul className="list-unstyled mb-0">
                            {user?.linkedin_link && (
                              <li className="mb-2  mb-2">
                                <i className="fa-brands fa-linkedin fa-fw fa-lg me-2 d-inline"></i>
                                <a href={user?.linkedin_link}>
                                  {user?.linkedin_link.replace(/^(https?:\/\/)?(www\.)?linkedin\.com\/in\//, '')}
                                </a>
                              </li>
                            )}
                            {user?.twitter_link && (
                              <li className="mb-2">
                                <i className="fa-brands fa-x-twitter fa-fw fa-lg me-2 d-inline"></i>
                                <a href={user?.twitter_link}>
                                  {user?.twitter_link.replace(/^(https?:\/\/)?(www\.)?twitter\.com\//, '')}
                                </a>
                              </li>
                            )}
                            {user?.instagram_link && (
                              <li className="mb-2">
                                <i className="fa-brands fa-instagram fa-fw fa-lg me-2 d-inline"></i>
                                <a href={user?.instagram_link}>
                                  {user?.instagram_link.replace(/^(https?:\/\/)?(www\.)?instagram\.com\//, '')}
                                </a>
                              </li>
                            )}
                            {user?.facebook_link && (
                              <li className="mb-2">
                                <i className="fa-brands fa-facebook-f fa-fw fa-lg me-2 d-inline"></i>
                                <a href={user?.facebook_link}>
                                  {user?.facebook_link.replace(/^(https?:\/\/)?(www\.)?facebook\.com\//, '')}
                                </a>
                              </li>
                            )}
                          </ul>
                        </section>
                      )}
                    </aside>
                  </div>
                </div>
              </article>
            </div>
          </div>
        </div>
        <div
          className="text-end mb-4 "
          style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <p className="start-now mt-5 text-center">
            <a onClick={handleBackButton} role="button">
              <i className="fa-solid fa-angles-left"></i> Resume Samples
            </a>
          </p>
          <p className="start-now mt-5 text-center">
            <a onClick={handleGeneratePdf} role="button">
              Generate PDF <i className="fa-solid fa-angles-right"></i>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}

export default App;
