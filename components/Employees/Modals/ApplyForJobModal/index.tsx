import React, { useContext, useEffect, useState } from 'react';
import AuthContext from '@/Context/AuthContext';
import { applyJob } from '@/lib/frontendapi';
import PhoneInput from 'react-phone-input-2';
import { HtmlEditor } from '@/components/Common/HtmlEditor';
import <PERSON><PERSON>rHandler from '@/lib/ErrorHandler';
import { Col, DatePicker, Form, Input, Row, Select, notification } from 'antd';
import 'react-phone-input-2/lib/style.css';
import { getDefaultResume } from '@/lib/employeeapi';

interface ApplyJobProps {
  job?: any;
  onClose?: () => void;
  allCountries?: any;
  onSubmit?: any;
  totalAppliedJobs?: string | number;
}

export default function ApplyForJobModal({ job, allCountries, onClose, onSubmit, totalAppliedJobs }: ApplyJobProps) {
  const { user } = useContext(AuthContext);
  const currentDate = new Date();
  const MAX_DESCRIPTION_WORDS = 250;
  const [selectedResume, setSelectedResume] = useState(null);
  const [userDefaultResume, setUserDefaultResume] = useState(null);
  const [selectedCover, setSelectedCover] = useState(null);
  const [selectedChoice, setSelectedChoice] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [description, setDescription] = useState('');
  console.log("useEffect", user);
  const handleFileChange = (event: any) => {
    const file = event.target.files[0];
    const maxSizeInBytes = 10 * 1024 * 1024; // 10 MB
    if (file.size > maxSizeInBytes) {
      notification.error({ message: 'File size exceeds 10MB. Please choose a smaller file.' });
      setSelectedResume(null);
      return;
    }

    setSelectedResume(file);

    if (file) {
      const allowedFormats = ['pdf', 'doc', 'docx'];
      const fileExtension = file.name.split('.').pop().toLowerCase();

      if (!allowedFormats.includes(fileExtension)) {
        notification.error({ message: 'Invalid file. Only PDF or DOC files are allowed.' });
        setSelectedResume(null);
      } else {
        setSelectedChoice('above');
      }
    }
  };

  const submitForm = (values: any) => {
    const descriptionWords = description.split(/\s+/).filter(Boolean).length;
    if (descriptionWords > MAX_DESCRIPTION_WORDS) {
      notification.error({ message: `Description should not exceed ${MAX_DESCRIPTION_WORDS} words.` });
    } else {
      setIsLoading(true);
      const data = {
        ...values,
        company_id: job.company_id,
        jobpost_by_userid: job.user_id,
        user_id: user?.id,
        job_id: job.id,
        description: description,
        choice: selectedChoice,
        instant_apply: 0,
        resume_path: selectedResume,
      };

      applyJob(data)
        .then(res => {
          if (res.status) {
            onSubmit();
          }
        })
        .catch(err => {
          ErrorHandler.showNotification(err);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  };

  const handleCoverChange = (event: any) => {
    const file = event.target.files[0];

    const maxSizeInBytes = 10 * 1024 * 1024; // 10 MB
    if (file.size > maxSizeInBytes) {
      notification.error({ message: "'File size exceeds 10MB. Please choose a smaller file.'" });
      setSelectedCover(null);
      return;
    }
    setSelectedCover(file);
    if (file) {
      const allowedFormats = ['pdf', 'doc', 'docx'];
      const fileExtension = file.name.split('.').pop().toLowerCase();

      if (!allowedFormats.includes(fileExtension)) {
        notification.error({ message: "'Invalid file. Only PDF or DOC files are allowed.'" });
        setSelectedCover(null);
      }
    }
  };

  useEffect(() => {
    if (user?.id) {
      getDefaultResume(user?.id).then(res => {
        if (res.status) {
          setUserDefaultResume(res.data.resume_pdf_path);
        }
      });
    }
  }, [user]);

  return (
    <Form size="large" layout="vertical" className="form-experience-fieild" onFinish={submitForm} initialValues={user}>
      <div className=" text-start d-flex gap-2">
        <p className="f-22 mt-2">Upload your recent Resume/CV:</p>
        <div className="uploade-btn">
          <input type="file" name="resume" onChange={handleFileChange} />
          <button className="download ">
            <i className="fa-solid fa-upload"></i> Upload Resume
          </button>
        </div>
      </div>
      {selectedResume && (
        <div className="w-box bg-fff mt-3 mb-3 p-3">
          <div className="row">
            <div className="col-sm-8">
              <p className="f-18 w-600 mb-2">{user?.name}-Resume</p>
              <p className="f-16 c-999999">
                Uploaded on{' '}
                {currentDate.toLocaleString('en-US', {
                  month: 'short',
                  day: 'numeric',
                })}
              </p>
            </div>
            <div className="col-sm-4 text-right">
              <span
                className={selectedChoice == 'above' ? 'default' : 'selected'}
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  setSelectedChoice('above');
                }}>
                Choose
                { }
              </span>
              &nbsp;&nbsp;
            </div>
          </div>
        </div>
      )}

      {userDefaultResume ? (
        <div className="w-box bg-fff mt-3 mb-3 p-3">
          <div className="row">
            <div className="col-sm-8">
              <p className="f-18 w-600 mb-2">{user?.name}- Default Resume</p>
              <p className="f-16 c-999999">
                Uploaded on{' '}
                {new Date(user?.resumedate ?? new Date()).toLocaleString('en-US', {
                  month: 'short',
                  day: 'numeric',
                })}
              </p>
            </div>
            <div className="col-sm-4 text-right">
              <span
                className={selectedChoice == 'below' ? 'default' : 'selected'}
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  setSelectedChoice('below');
                }}>
                Choose
              </span>
              &nbsp;&nbsp;
            </div>
          </div>
        </div>
      ) : null}
      <Row gutter={10} className="mt-2">
        <Col md={12}>
          <Form.Item
            name={'name'}
            label={'Your Name'}
            rules={[
              {
                required: true,
              },
            ]}>
            <Input />
          </Form.Item>
        </Col>
        <Col md={12}>
          <Form.Item
            name={'email'}
            label={'Email ID'}
            rules={[
              {
                required: true,
              },
            ]}>
            <Input />
          </Form.Item>
        </Col>
        <Col md={12}>
          <Form.Item
            name={'contact_no'}
            label={'Contact Number'}
            rules={[
              {
                required: true,
              },
            ]}>
            <PhoneInput specialLabel="" country={'us'} />
          </Form.Item>
        </Col>
        <Col md={12}>
          <Form.Item
            name={'gender'}
            label={'Gender'}
            rules={[
              {
                required: true,
              },
            ]}>
            <Select
              options={[
                { label: 'Male', value: 'male' },
                { label: 'Female', value: 'female' },
                { label: 'Other', value: 'other' },
              ]}
            />
          </Form.Item>
        </Col>
        {/* <Col md={12}>
          <Form.Item
            name={'date_of_birth'}
            label={'Date of Birth'}
            rules={[
              {
                required: true,
              },
            ]}>
            <DatePicker mode="date" className="w-100" />
          </Form.Item>
        </Col>
        <Col md={12}>
          <Form.Item
            name={'where_currently_based'}
            label={'Where are you currently based?'}
            rules={[
              {
                required: true,
              },
            ]}>
            <Select
              options={allCountries.map((countries: any) => {
                if (countries.status === 'active') {
                  return { label: countries.country_name, value: countries.id };
                }
              })}
            />
          </Form.Item>
        </Col> */}
        <Col md={24}>
          <Form.Item name={'edit_job_description'} label={'Your Cover Letter'}>
            <HtmlEditor
              onChange={(name: any, value: any) => {
                setDescription(value);
              }}
            />
          </Form.Item>
        </Col>
      </Row>

      <div className="d-flex justify-content-center mt-4">
        <div className="uploade-btn ">
          <input type="file" name="resume" accept=".pdf, .docx" onChange={handleCoverChange} />
          <button className="download ">
            <i className="fa-solid fa-upload"></i> Upload Cover Letter
          </button>
        </div>
      </div>
      {selectedCover && (
        <div className="w-box bg-fff mt-3 mb-3 p-3">
          <div className="row">
            <div className="col-sm-8">
              <p className="f-18 w-600 mb-2">{user?.name}-Cover-Letter</p>
              <p className="f-16 c-999999">
                Uploaded on{' '}
                {currentDate.toLocaleString('en-US', {
                  month: 'short',
                  day: 'numeric',
                })}
              </p>
            </div>
          </div>
        </div>
      )}
      {Number(totalAppliedJobs) == 0 && (
        <p className="pt-4 text-center" style={{ color: '#D04E4F' }}>
          Note: An incomplete profile may affect your chances of landing your dream job.
        </p>
      )}
      <div className="text-right mt-3">
        <button className="cancel" onClick={onClose}>
          Cancel
        </button>
        <button className="save" disabled={isLoading}>
          {isLoading ? 'Please wait...' : 'Save'}
        </button>
      </div>
    </Form>
  );
}
