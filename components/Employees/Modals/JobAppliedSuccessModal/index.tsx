import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

interface ModalProps {
  onClose?: () => void;
}

export default function JobAppliedSuccessModal({onClose}: ModalProps) {
  return (
    <div className="popup-body">
      <div className="row">
        <div className="col-sm-10"> </div>
        <div className="col-sm-2 text-right">
          <button
            type="button"
            className="close-x  bg-0055BA border-design close-b-des"
            data-bs-dismiss="modal"
            aria-label="Close"
            onClick={onClose}>
            <i className="fa-solid fa-xmark" onClick={onClose}></i>
          </button>
        </div>
      </div>
      <div className="text-center pb-3 pt-2">
        <div className="pog-r w-120 m-0auto mb-4 ">
          <img src="/images/blue-round.png" alt="blue-round" className="w-120 fa-spin" width={120} height={121} />
          <img src="/images/check-i.png" alt="check-i" className="check-i" width={50} height={50} />
        </div>
        <h2 className="f-31 mb-3">
          Application <span className="span-color">Submitted</span>
        </h2>
        <p className="f-18 w-400">
          Go to{' '}
          <Link href="/employees/applications" className="c-0070F5 w-700  ">
            {' '}
            My Applications
          </Link>{' '}
          to view your application status.
        </p>
      </div>
    </div>
  );
}
