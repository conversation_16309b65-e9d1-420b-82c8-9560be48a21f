import React, {useState, useEffect, useRef, useContext} from 'react';
import {useRouter} from 'next/router';
import {useSession} from 'next-auth/react';
import moment from 'moment';
import Image from 'next/image';
import Link from 'next/link';
import {But<PERSON>, Popover, Space} from 'antd';

import {getNotifications, getAllCompanyAndJobs, updateNotificationStatus} from '@/lib/frontendapi';
import {updateJobStatus} from '@/lib/employeeapi';
import AuthContext from '@/Context/AuthContext';
import ErrorHandler from '@/lib/ErrorHandler';
import AuthUserMenu from '@/components/Common/AuthUserMenu';

export default function Header() {
  const router = useRouter();
  const [currentJobStatus, setCurrentJobStatus] = useState('');
  const {data: session} = useSession();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [readNotifications, setReadNotifications]: any = useState([]);
  const [searchInputValue, setSearchInputValue] = useState('');
  const [companiesData, setAllCompanyData] = useState([]);
  const [jobsData, setAllJobsData] = useState([]);
  const {user} = useContext(AuthContext);
  const [bellIconShow2, setBellIconShow2] = useState(false);
  const anchorRef = useRef<HTMLAnchorElement | null>(null);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);
  const searchRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (user) {
      const img = user?.profile_image
        ? process.env.NEXT_PUBLIC_IMAGE_URL + user?.profile_image?.source?.replace('/storage/', '')
        : `${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-1.png`;
      setPreviewImage(img);
      getNotifications()
        .then(res => {
          if (res.status === true) {
            const filteredNotifications = res.data.filter((item: {notify_to: any}) => item.notify_to === user.id);
            setReadNotifications(filteredNotifications);
          } else {
            setReadNotifications([]);
          }
        })
        .catch(error => {
          // ErrorHandler.showNotification(error);
        });
    }
  }, [session, user]);

  const showBellIconOnclick2 = (showVal: any) => {
    getNotifications()
      .then(res => {
        if (res.status === true) {
          // setReadNotifications(res.data);
          const filteredNotifications = res.data.filter((item: {notify_to: any}) => item.notify_to === user?.id);
          setReadNotifications(filteredNotifications);

          setBellIconShow2(showVal);
        } else {
          setReadNotifications([]);
        }
      })
      .catch(error => {
        // console.error('Error fetching notifications:', error);
      });
    router.push('/employees/notifications');
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      updateNotificationStatus(user?.id)
        .then(response => {})
        .catch(error => {});
    }, 5000);
    return () => clearTimeout(timer);
  }, [user]);

  const handleJobStatusUpdate = (e: any, status: any) => {
    e.preventDefault();
    if (user?.id) {
      const data = {
        id: user?.id,
        value: status,
      };
      updateJobStatus(data)
        .then(() => {
          setCurrentJobStatus(status);
          setIsDropdownOpen(false);
        })
        .catch(error => {
          ErrorHandler.showNotification(error);
        });
    }
  };

  function handleSearchInputChange(event: any) {
    const value = event.target.value;
    setSearchInputValue(value);
    if (value.length >= 3) {
      const data = {
        value: value,
      };
      getAllCompanyAndJobs(data)
        .then(response => {
          if (response.status == true) {
            setAllCompanyData(response.companies);
            setAllJobsData(response.jobs);
          }
        })
        .catch(error => {
          ErrorHandler.showNotification(error);
        });
    }
  }

  useEffect(() => {
    window.addEventListener('click', handleClick);
    return () => {
      window.addEventListener('click', handleClick);
    };
  }, []);

  const handleClick = (event: MouseEvent) => {
    if (anchorRef.current && event.target instanceof Node && !anchorRef.current.contains(event.target)) {
      showBellIconOnclick2(false);
    }
  };

  const handleMenuItemClick = () => {
    setIsMenuOpen(false);
  };

  useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (inputRef.current && !inputRef.current.contains(event.target as Node) && (searchRef.current && !searchRef.current.contains(event.target as Node))) {
          setSearchInputValue('');
        }
      };
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

  return (
    <>
      <header className="head-part mar-bot-75">
        <nav className="navbar navbar-expand-lg navbar-light  fixed-top bg-fff">
          <div className="container-fluid full-width">
            <div className="logo-width">
              <Link className="navbar-brand" href="/">
                <img src={'/images/Avatars-4.png'} alt="logo" className="logo-head" width={200} height={60} />
              </Link>
            </div>
            <Button
              className={`navbar-toggler collapsed ${isMenuOpen ? 'active' : ''}`}
              data-bs-toggle="collapse"
              data-bs-target="#navbarSupportedContent"
              aria-controls="navbarSupportedContent"
              aria-expanded="false"
              aria-label="Toggle navigation"
              onClick={() => setIsMenuOpen(!isMenuOpen)}>
              <span className="navbar-toggler-icon">
                <i className="fa-solid fa-bars"></i>
                <i className="fa-solid fa-x close-x"></i>
              </span>
            </Button>
            <div className={`collapse navbar-collapse j-end ${isMenuOpen ? 'show' : ''}`} id="navbarSupportedContent">
              <form className="d-flex mobile-single tab-scroll-view tab-flex-none">
                <div className="search-in tab-none">
                  <input
                    className="form-control me-2"
                    type="search"
                    placeholder="Search for jobs or companies"
                    aria-label="Search"
                    value={searchInputValue}
                    onChange={handleSearchInputChange}
                    ref={inputRef}
                  />
                  <i className="fa-solid fa-magnifying-glass glass-ser"></i>
                  {searchInputValue && (
                    <div className="company_jobs_search" ref={searchRef}>
                      <div id="search-results" className="companysas">
                        <p className="title_heading text-start">Company</p>
                        <ul
                          className="list-unstyled"
                          id="company"
                          style={{height: companiesData.length >= 3 ? '150px' : 'auto'}}>
                          {companiesData.length > 0 ? (
                            companiesData.map((companies: any, index: any) => {
                              return (
                                <a
                                  key={index}
                                  target="_blank"
                                  href={`/companies/${companies.company_slug}`}
                                  className="search_result_para">
                                  {' '}
                                  <li>{companies.company_name}</li>
                                </a>
                              );
                            })
                          ) : (
                            <li className="search_result_para">
                              No company records found or enter aleast three character to get the results
                            </li>
                          )}
                        </ul>
                      </div>
                      <hr></hr>
                      <div id="search-results" className="josas">
                        <p className="title_heading  text-start">Jobs</p>
                        <ul
                          className="list-unstyled"
                          id="company"
                          style={{height: jobsData.length >= 3 ? '150px' : 'auto'}}>
                          {jobsData.length > 0 ? (
                            jobsData.map((jobs: any, index: any) => {
                              return (
                                <a
                                  key={index}
                                  target="_blank"
                                  className="search_result_para"
                                  href={`/job/${jobs.job_slug}`}>
                                  {' '}
                                  <li>{jobs.job_title}</li>
                                </a>
                              );
                            })
                          ) : (
                            <li className="search_result_para">
                              No job records found or enter aleast three character to get the results
                            </li>
                          )}
                        </ul>
                      </div>
                    </div>
                  )}
                </div>
                <div
                  className="dask-tab-mobile-d-flex m-top-add-sp "
                  style={{
                    alignItems: 'center',
                  }}>
                  <div className="dask-none d-flex user-profile-mobile">
                    <div className="img-box-1">
                      <img
                        src={previewImage}
                        alt={user?.profile_image?.name || user?.name || 'Employee profile image'}
                        className="w-40"
                        width={25}
                        height={30}
                      />
                    </div>
                    <div className="text-name-mobile">
                      <h4 className="name-text">{user?.name}</h4>
                      <h5 className="roll">
                        {user?.current_position &&
                          user?.current_position.charAt(0).toUpperCase() + user?.current_position.slice(1)}
                      </h5>
                    </div>
                  </div>
                  <p className="head-icon notifications mobile-nati">
                    {bellIconShow2 ? (
                      <>
                        <a href="#" className="pog-r" onClick={() => showBellIconOnclick2(false)} ref={anchorRef}>
                          <i className="fa-solid fa-bell fill-bell"></i>
                          {readNotifications &&
                            readNotifications.length > 0 &&
                            readNotifications[readNotifications.length - 1].notify_to === user?.id &&
                            readNotifications[readNotifications.length - 1].is_read === 0 && (
                              <span className="round-bell"></span>
                            )}
                        </a>
                        <div className="box-noti">
                          <div className="bell-box">
                            <div className="row">
                              <div className="col-7">
                                <h4 className="not-title">Notifications</h4>
                              </div>
                              <div className="col-5">
                                <p className="mark-as">Mark as Read</p>
                              </div>
                            </div>
                            {readNotifications && readNotifications.length > 0
                              ? readNotifications.slice(0, 3).map((read_notification_data: any, index: any) => {
                                  const created_at = moment.utc(read_notification_data?.created_at);
                                  const currentTime = moment();
                                  const yesterday = moment().subtract(1, 'day');
                                  let timeFormatted;
                                  if (created_at.isSame(currentTime, 'day')) {
                                    timeFormatted = created_at.local().format('hh:mm A');
                                  } else if (created_at.isSame(yesterday, 'day')) {
                                    timeFormatted = 'Yesterday';
                                  } else {
                                    timeFormatted = created_at.local().format('MMMM D');
                                  }
                                  // Check if read_notification_data?.name is not null or undefined before using split
                                  const nameParts = read_notification_data?.name
                                    ? read_notification_data.name.split(' ')
                                    : [];
                                  const initials =
                                    nameParts.length === 1
                                      ? nameParts[0].substring(0, 2)
                                      : nameParts
                                          .map((word: any, index: any) => (index === 0 || index === 1 ? word[0] : ''))
                                          .join('');
                                  return (
                                    <div className="row mt-4" key={index}>
                                      <div className="col-2 pr-0">
                                        {read_notification_data?.profile_image ? (
                                          <img
                                            src={`${process.env.NEXT_PUBLIC_IMAGE_URL}images/userprofileImg/${read_notification_data?.profile_image}`}
                                            alt="Avatars-4"
                                            className="w-24"
                                            width={24}
                                            height={24}
                                          />
                                        ) : (
                                          <small
                                            title={read_notification_data?.name}
                                            className="text-uppercase w-24 notfication_name">
                                            {initials}
                                          </small>
                                        )}
                                      </div>
                                      <div className="col-10 text-left pl-0">
                                        {read_notification_data?.is_read === 0 ? (
                                          <>
                                            <p
                                              className="f-16 notification_font_weight"
                                              dangerouslySetInnerHTML={{
                                                __html: read_notification_data?.notification,
                                              }}></p>
                                          </>
                                        ) : (
                                          <>
                                            <p
                                              className="f-16"
                                              dangerouslySetInnerHTML={{
                                                __html: read_notification_data.notification,
                                              }}></p>
                                          </>
                                        )}
                                        <p className="f-12">{timeFormatted}</p>
                                      </div>
                                    </div>
                                  );
                                })
                              : ''}
                            <p>
                              <Link href="/employees/notifications" className="view-all">
                                View All
                              </Link>
                            </p>
                          </div>
                        </div>
                      </>
                    ) : (
                      <a href="javascript:void(0)" onClick={() => showBellIconOnclick2(true)} className="pog-r">
                        <i className="fa-regular fa-bell"></i>
                        {readNotifications &&
                          readNotifications.length > 0 &&
                          readNotifications[readNotifications.length - 1].notify_to === user?.id &&
                          readNotifications[readNotifications.length - 1].is_read === 0 && (
                            <span className="round-bell"></span>
                          )}
                      </a>
                    )}
                  </p>
                  <div className="d-none d-md-block">
                    <Popover content={<AuthUserMenu />} trigger={['click']}>
                      <Space>
                        <img
                          src={
                            user?.profile_image
                              ? user?.profile_image?.source
                              : `${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-1.png`
                          }
                          // src={user?.profile_image ? process.env.NEXT_PUBLIC_IMAGE_URL + user?.profile_image?.source?.replace("/storage/", '') : `${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-1.png`}
                          alt={user?.profile_image?.name || user?.name || 'Employee profile image'}
                          className="w-32"
                          width={32}
                          height={32}
                        />

                        <i className="fa-solid fa-ellipsis-vertical" style={{cursor: 'pointer'}}></i>
                      </Space>
                    </Popover>
                  </div>
                </div>
                <div className="left-bar dask-none">
                  <div className="text-center mt-4">
                    <div className="dash-profile-img mb-4 m-auto tab-none">
                      <img
                        src={
                          user?.profile_image
                            ? process.env.NEXT_PUBLIC_IMAGE_URL + user?.profile_image?.source?.replace('/storage/', '')
                            : `${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-1.png`
                        }
                        alt={user?.profile_image?.name || user?.name || 'Employee profile image'}
                        width={32}
                        height={32}
                      />
                    </div>
                    <div className="accordion text-left tab-none" id="accordionExample">
                      <div className="up-down-item w-270 m-auto">
                        <div className="accordion-item">
                          <h2 className="accordion-header" id="headingOne">
                            <Button
                              className={`accordion-button border-green ${
                                currentJobStatus === 'ready_to_interview' ? 'ready-to-interview-class' : ''
                              } ${currentJobStatus === 'not_looking' ? 'not-looking-class' : ''} ${
                                currentJobStatus === 'open_to_offer' ? 'Open-to-Offers-class' : ''
                              }`}
                              data-bs-toggle="collapse"
                              data-bs-target="#collapseOne"
                              aria-expanded="true"
                              aria-controls="collapseOne"
                              onClick={() => setIsDropdownOpen(!isDropdownOpen)}>
                              <img
                                src={
                                  currentJobStatus === 'ready_to_interview'
                                    ? '/images/icon-1.png'
                                    : currentJobStatus === 'not_looking'
                                      ? '/images/icon-3.png'
                                      : currentJobStatus === 'open_to_offer'
                                        ? '/images/icon-2.png'
                                        : ''
                                }
                                alt="icon-1"
                                className="w-16"
                                width={16}
                                height={16}
                              />
                              {currentJobStatus === 'ready_to_interview'
                                ? 'Ready To Interview'
                                : currentJobStatus === 'not_looking'
                                  ? 'Not Looking'
                                  : currentJobStatus === 'open_to_offer'
                                    ? 'Open to Offers'
                                    : currentJobStatus}
                            </Button>
                          </h2>
                          <div
                            id="collapseOne"
                            className={`accordion-collapse collapse ${isDropdownOpen ? 'show' : ''}`}
                            aria-labelledby="headingOne"
                            data-bs-parent="#accordionExample">
                            <div
                              className="accordion-body"
                              onClick={e => handleJobStatusUpdate(e, 'ready_to_interview')}
                              style={{cursor: 'pointer'}}>
                              <h5>
                                <img src="/images/icon-1.png" alt="icon-1" width={24} height={24} /> Ready to Interview
                              </h5>
                              <p>Actively looking for openings & ready to interview.</p>
                            </div>
                            <div
                              className="accordion-body f-D57B11"
                              onClick={e => handleJobStatusUpdate(e, 'open_to_offer')}
                              style={{cursor: 'pointer'}}>
                              <h5>
                                <img src="/images/icon-2.png" alt="icon-2" width={24} height={24} /> Open to Offers
                              </h5>
                              <p>Not actively looking but open to hear about new opportunities. </p>
                            </div>
                            <div
                              className="accordion-body f-D04E4F"
                              onClick={e => handleJobStatusUpdate(e, 'not_looking')}
                              style={{cursor: 'pointer'}}>
                              <h5>
                                <img src="/images/icon-3.png" alt="icon-3" width={24} height={24} /> Not Looking
                              </h5>
                              <p>Not actively looking for opportunities right now.</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <ul className="side-menu-left mt-4">
                      <li className={router.pathname == '/employees/dashboard' ? 'active' : ''}>
                        <Link href="/employees/dashboard" onClick={handleMenuItemClick}>
                          <i className="fa-regular fa-envelope-open"></i> Home
                        </Link>
                      </li>
                      <li
                        className={
                          router.pathname == '/employees/myprofile' ||
                          router.pathname == '/employees/myprofile/profile' ||
                          router.pathname == '/employees/myprofile/jobpreferences'
                            ? 'active'
                            : ''
                        }>
                        <Link href="/employees/myprofile" onClick={handleMenuItemClick}>
                          <i className="fa-regular fa-circle-user"></i> Profile
                        </Link>
                      </li>
                      <li className={router.pathname == '/employees/resume' ? 'active' : ''}>
                        <Link href="/employees/resume" onClick={handleMenuItemClick}>
                          <i className="fa-regular fa-file-lines"></i> Resume
                        </Link>
                      </li>
                      <li
                        className={
                          router.pathname == '/employees/jobs' || router.pathname == '/employees/jobs/savedjobs'
                            ? 'active'
                            : ''
                        }>
                        <Link href="/employees/jobs" onClick={handleMenuItemClick}>
                          <i className="fa-solid fa-briefcase"></i> Jobs
                        </Link>
                      </li>
                      <li className={router.pathname == '/employees/applications' ? 'active' : ''}>
                        <Link href="/employees/applications" onClick={handleMenuItemClick}>
                          <i className="fa-regular fa-folder"></i>Applications
                        </Link>
                      </li>
                      <li
                        className={
                          router.pathname == '/employees/messages' ||
                          router.pathname == '/employees/messages/interviews' ||
                          router.pathname == '/employees/messages/archived'
                            ? 'active'
                            : ''
                        }>
                        <Link href="/employees/messages" onClick={handleMenuItemClick}>
                          <i className="fa-regular fa-message"></i>Messages
                        </Link>
                      </li>
                      {user && (
                        <li className="nav-item">
                          <div className='d-block d-md-none'>
                            <Popover content={<AuthUserMenu />} trigger={['click']}>
                              <a
                                className={
                                  router.pathname == '/auth/login'
                                    ? 'nav-link active dropdown-toggle single-menu-space'
                                    : 'nav-link dropdown-toggle single-menu-space'
                                }
                                id="dropdownMenuButton1"
                                data-bs-toggle="dropdown"
                                aria-expanded="false"
                                style={{ cursor: 'pointer' }}>
                                {user.name}
                              </a>
                            </Popover>
                          </div>
                        </li>
                      )}
                    </ul>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </nav>
      </header>
    </>
  );
}
