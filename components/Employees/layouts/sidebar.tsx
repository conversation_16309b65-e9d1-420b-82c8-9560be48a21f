import React, {useState, useContext, useEffect} from 'react';
import {useRouter} from 'next/router';
import {updateJobStatus} from '../../../lib/employeeapi';
import Link from 'next/link';
import Image from 'next/image';
import AuthContext from '@/Context/AuthContext';
import UserProfileImage from '@/components/Common/UserProfileImage';
import {message} from 'antd';
import {useQueryClient} from 'react-query';
import {QUERY_GET_SINGLE_USER_DETAILS} from '@/modules/employees/constants';
import {useGetSingleUserDetails} from '@/modules/employees/query/useGetSingleUserDetails';

export default function Sidebar() {
  const router = useRouter();
  const {user} = useContext(AuthContext);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [jobStatus, setJobStatus] = useState(user?.jobStatus);
  const {data: userDetails} = useGetSingleUserDetails(user?.id);
  const queryClient = useQueryClient();
  const handleJobStatusUpdate = (e: any, status: any) => {
    e.preventDefault();
    if (user?.id) {
      const data = {
        id: user?.id,
        value: status,
      };
      updateJobStatus(data)
        .then(() => {
          queryClient.invalidateQueries([QUERY_GET_SINGLE_USER_DETAILS]);
          setIsDropdownOpen(false);
          setJobStatus(status);
          message.success('Saved');
        })
        .catch(error => {
          console.error(error);
        });
    }
  };

  useEffect(() => {
    setJobStatus(userDetails?.job_status);
  }, [userDetails?.job_status]);

  return (
    <>
      <div className="left-bar tab-none">
        <div className="text-center mt-4">
          <div className="dash-profile-img mb-2 m-auto">
            <div className="pro-diamond">
              <UserProfileImage user={user} showModel={false} company={user?.company} />
            </div>
          </div>
          <h4 className="name-text">{user?.name}</h4>
          <h5 className="roll">{user?.current_position && user?.current_position}</h5>
          <div className="accordion text-left" id="accordionExample">
            <div className="up-down-item w-270 m-auto">
              <div className="accordion-item left-accor">
                <h2 className="accordion-header" id="headingOne">
                  <button
                    className={`accordion-button border-green collapsed  ${
                      jobStatus === 'ready_to_interview' ? 'ready-to-interview-class ' : ''
                    } ${jobStatus === 'not_looking' ? 'not-looking-class' : ''} ${
                      jobStatus === 'open_to_offer' ? 'Open-to-Offers-class' : ''
                    }`}
                    type="button"
                    data-bs-toggle="collapse"
                    data-bs-target="#collapseOne"
                    aria-expanded="true"
                    aria-controls="collapseOne"
                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}>
                    <img
                      src={
                        !jobStatus || jobStatus === 'ready_to_interview'
                          ? '/images/icon-1.png'
                          : jobStatus === 'not_looking'
                            ? '/images/icon-3.png'
                            : jobStatus === 'open_to_offer'
                              ? '/images/icon-2.png'
                              : ''
                      }
                      alt="icon-1"
                      className="w-16"
                      width={16}
                      height={16}
                    />
                    {!jobStatus || jobStatus === 'ready_to_interview'
                      ? 'Ready To Interview'
                      : jobStatus === 'not_looking'
                        ? 'Not Looking'
                        : jobStatus === 'open_to_offer'
                          ? 'Open to Offers'
                          : jobStatus}
                  </button>
                </h2>
                <div
                  id="collapseOne"
                  className={`accordion-collapse collapse ${isDropdownOpen ? 'show' : ''}`}
                  aria-labelledby="headingOne"
                  data-bs-parent="#accordionExample">
                  <div
                    className="accordion-body"
                    onClick={e => handleJobStatusUpdate(e, 'ready_to_interview')}
                    style={{cursor: 'pointer'}}>
                    <h5>
                      <img src="/images/icon-1.png" alt="icon-1" className="w-24" width={24} height={24} /> Ready to
                      Interview
                    </h5>
                    <p>Actively looking for openings & ready to interview.</p>
                  </div>
                  <div
                    className="accordion-body f-D57B11"
                    onClick={e => handleJobStatusUpdate(e, 'open_to_offer')}
                    style={{cursor: 'pointer'}}>
                    <h5>
                      <img src="/images/icon-2.png" alt="icon-2" className="w-24" width={24} height={24} /> Open to
                      Offers
                    </h5>
                    <p>Not actively looking but open to hear about new opportunities. </p>
                  </div>
                  <div
                    className="accordion-body f-D04E4F"
                    onClick={e => handleJobStatusUpdate(e, 'not_looking')}
                    style={{cursor: 'pointer'}}>
                    <h5>
                      <img src="/images/icon-3.png" alt="icon-3" className="w-24" width={24} height={24} /> Not Looking
                    </h5>
                    <p>Not actively looking for opportunities right now.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <ul className="side-menu-left mt-4">
            <li className={router.pathname == '/employees/dashboard' ? 'active' : ''}>
              <Link href="/employees/dashboard">
                <img src="/images/default/dash-icon-1.svg" alt="icon-4a" className="icon-a" width={18} height={18} />
                <img
                  src="/images/selected/dash-icon-1.svg"
                  alt="icon-4"
                  className="icon-hover"
                  width={18}
                  height={19}
                />
                Home
              </Link>
            </li>
            <li
              className={
                router.pathname == '/employees/myprofile' ||
                router.pathname == '/employees/myprofile/profile' ||
                router.pathname == '/employees/myprofile/jobpreferences'
                  ? 'active'
                  : ''
              }>
              <Link href="/employees/myprofile">
                <img src="/images/default/dash-icon-2.svg" alt="icon-3a" className="icon-a" width={18} height={18} />
                <img
                  src="/images/selected/dash-icon-2.svg"
                  alt="icon-3a"
                  className="icon-hover"
                  width={21}
                  height={22}
                />
                Profile
              </Link>
            </li>
            <li
              className={
                router.pathname == '/employees/resume' ||
                router.pathname == '/employees/resume/choose-design' ||
                router.pathname == '/employees/resume/choose-design' ||
                router.pathname == '/employees/resume/template-two' ||
                router.pathname == '/employees/resume/template-one'
                  ? 'active'
                  : ''
              }>
              <Link href="/employees/resume">
                <img src="/images/default/dash-icon-3.svg" alt="icon-6a" className="icon-a" width={18} height={18} />
                <img
                  src="/images/selected/dash-icon-3.svg"
                  alt="icon-6"
                  className="icon-hover"
                  width={19}
                  height={21}
                />
                Resume
              </Link>
            </li>
            <li
              className={
                router.pathname == '/employees/jobs' || router.pathname == '/employees/jobs/savedjobs' ? 'active' : ''
              }>
              <Link href="/employees/jobs">
                <img src="/images/default/dash-icon-4.svg" alt="icon-5a" className="icon-a" width={18} height={18} />
                <img
                  src="/images/selected/dash-icon-4.svg"
                  alt="icon-5"
                  className="icon-hover"
                  width={20}
                  height={17}
                />
                Jobs
              </Link>
            </li>
            <li className={router.pathname == '/employees/applications' ? 'active' : ''}>
              <Link href="/employees/applications">
                <img src="/images/default/dash-icon-5.svg" alt="icon-7a" className="icon-a" width={18} height={18} />
                <img
                  src="/images/selected/dash-icon-5.svg"
                  alt="icon-7"
                  className="icon-hover"
                  width={22}
                  height={16}
                />
                Applications
              </Link>
            </li>
            <li
              className={
                router.pathname == '/employees/messages' ||
                router.pathname == '/employees/messages/interviews' ||
                router.pathname == '/employees/messages/archived' ||
                router.pathname === '/employees/messages/inbox/[id]'
                  ? 'active'
                  : ''
              }>
              <Link href="/employees/messages">
                <img src="/images/default/dash-icon-6.svg" alt="icon-1a" className="icon-a" width={18} height={18} />
                <img
                  src="/images/selected/dash-icon-6.svg"
                  alt="icon-1"
                  className="icon-hover"
                  width={24}
                  height={24}
                />
                Messages
              </Link>
            </li>
          </ul>
        </div>
      </div>
    </>
  );
}
