import React, {useState, useEffect, useContext} from 'react';
import {useRouter} from 'next/router';
import {notification} from 'antd';
import Link from 'next/link';
import {usePathname} from 'next/navigation';
import swal from 'sweetalert';
import Image from 'next/image';
import axios from 'axios';
import {
  getusersavedjobs,
  getAllSectors,
  getAllSkills,
  getAllCountries,
  getAlljobfilter,
  UpdateSaveJobFilterData,
  DeleteJobFilter,
  getAllJobsSearch,
} from '@/lib/frontendapi';
import {paginate} from '@/helpers/paginate';
import AuthContext from '@/Context/AuthContext';
import JobListItem from '@/components/Common/JobListItem';
import {Job, Country, Sector, Skill, experienceRanges, PaginationMeta} from '@/lib/types';
import ModalForm from '@/components/Common/ModalForm';
import ErrorHandler from '@/lib/ErrorHandler';
import JobFilterModal from './JobFilterModal';
import JobFilteredData from './JobFilteredData';
import Pagination from '../../../components/Common/Pagination';
import {searchJobs} from '@/lib/ApiAdapter';

import JsonLd from '../../../components/JsonLd';

import 'react-phone-input-2/lib/style.css';

export default function Jobs() {
  const {user} = useContext(AuthContext);
  const router = useRouter();
  const [filterModal, setFilterModal] = useState(false);
  const [deleteModal, setDeleteModal] = useState(false);
  const [savedJobsCount, setSavedJobsCount] = useState('');
  const [sectors, setSectors] = useState<Sector[]>([]);
  const [skill, setSkill] = useState<Skill[]>([]);
  const [selectedJobType, setSelectedJobType] = useState<any[]>([]);
  const [selectedSectors, setSelectedSectors] = useState<any[]>([]);
  const [selectedSkill, setSelectedSkill] = useState<any[]>([]);
  const [keywords, setKeywords] = useState('');
  const [location, setLocation] = useState('');
  const [Country, setCountry] = useState<Country[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalJobs, setTotalJobs] = useState<Job[]>([]);
  const [jobs, setJobs] = useState<Job[]>([]);
  const [experienceRange, setExperienceRange] = useState(0);
  const [experience, setExperience] = useState(experienceRanges[0].label);
  const [selectedCurrency, setSelectedCurrency] = useState('');
  const [maxSalary, setMaxSalary] = useState<number>(100000);
  const [selectedSalary, setSelectedSalary] = useState<number>(0);
  const [filterJobData, setFilterJobs] = useState([]);
  const [filterJobId, setFilterJobsId] = useState('1');
  const [filterJobSectionName, setFilterJobsSectionName] = useState('');
  const [filterJobType, setFilterJobsType] = useState('');
  const pageSize = 10;
  const pathname = usePathname();

  const onPageChange = (page: any) => {
    const cancelTokenSource = axios.CancelToken.source();
    setCurrentPage(page);
    const data = {
      keywords: keywords,
      location: location,
      skill: selectedSkill.join(','),
      job_type: selectedJobType.join(','),
      sector: selectedSectors.join(','),
      experience: experienceRange != 0 ? experience : null,
      currency: selectedSalary > 0 ? selectedCurrency : null,
      minSalary: selectedSalary > 0 ? selectedSalary : null,
      maxSalary: maxSalary,
      user_id: user?.id,
      filter_id: filterJobId,
      section_name: filterJobSectionName,
    };
    searchJobs(data, cancelTokenSource)
      .then(response => {
        setTotalJobs(response.data);
        const paginatedPosts = paginate(response.data, page, pageSize);
        //setPaginationMeta(response.meta);
        setJobs(paginatedPosts);
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });
    return cancelTokenSource.cancel;
  };

  const handleFilterModalOpen = () => {
    setFilterModal(true);
  };

  const handleFilterModalClose = () => {
    setFilterModal(false);
  };

  const handleDeleteModal = () => {
    setDeleteModal(false);
  };

  useEffect(() => {
    if (user?.id) {
      const data = {
        user_id: user?.id,
      };
      getusersavedjobs(data)
        .then(res => {
          if (res.status == true) {
            setSavedJobsCount(res.saved_jobs.length);
          } else {
            setSavedJobsCount('');
          }
        })
        .catch(error => {
          ErrorHandler.showNotification(error);
        });

      getAllCountries()
        .then(res => {
          if (res) {
            setCountry(res);
          } else {
            setCountry([]);
          }
        })
        .catch(error => {
          ErrorHandler.showNotification(error);
        });

      getAllJobFilterData(user?.id);
      getAllSectorsData();
      getAllSkillsData();
    }
  }, [user]);

  const getAllSectorsData = async () => {
    try {
      const res = await getAllSectors();
      if (res.success == true) {
        setSectors(res.sectors);
      } else {
        setSectors([]);
      }
    } catch (error) {
      ErrorHandler.showNotification(error);
    }
  };

  const getAllSkillsData = async () => {
    try {
      const res = await getAllSkills();
      if (res.success == true) {
        setSkill(res.data);
      } else {
        setSkill([]);
      }
    } catch (error) {
      ErrorHandler.showNotification(error);
    }
  };

  const getAllJobFilterData = async (id: any) => {
    try {
      if (id) {
        const res = await getAlljobfilter(id);
        if (res.status == true) {
          setFilterJobs(res.data);
          if (!filterJobType) {
            if (res.data) {
              setFilterJobsId(res.data[0].id);
              setFilterJobsSectionName(res.data[0].section_name);
              handleJobFilterNav(res.data[0]);
            }
          }
        } else {
          setFilterJobs([]);
        }
      }
    } catch (error) {
      ErrorHandler.showNotification(error);
      console.log(error);
    }
  };

  const handleJobFilterData = (data: any) => {
    UpdateSaveJobFilterData(data)
      .then(res => {
        if (res.status == true) {
          getAllJobFilterData(user?.id);
          setFilterJobsId(res.id);
          setDeleteModal(false);
        } else {
          setJobs([]);
        }
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });
  };

  const handleSubmitJobFilter = (e: any) => {
    e.preventDefault();
    const data = {
      filter_id: filterJobType == 'edit' ? filterJobId : '',
      section_name: filterJobSectionName,
      user_id: user?.id,
      job_title: '',
      country_id: '',
      currency: '',
      salary: '',
      experience: '',
      skills: '',
      sector: '',
      job_type: '',
    };
    UpdateSaveJobFilterData(data)
      .then(res => {
        if (res.status == true) {
          getAllJobFilterData(user?.id);
          setFilterJobsId(res.id);
          setDeleteModal(false);
          handleJobFilterNav(res.data);
        } else {
          setJobs([]);
        }
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });
  };

  const handleJobFilterNav = (job: any) => {
    const cancelTokenSource = axios.CancelToken.source();
    setFilterJobsId(job.id);
    setFilterJobsSectionName(job.section_name);
    if (job.country_id) {
      setLocation(job.country_id);
    } else {
      setLocation('');
    }
    if (job.job_title) {
      setKeywords(job.job_title);
    } else {
      setKeywords('');
    }
    if (job.currency) {
      setSelectedCurrency(job.currency);
      setSelectedSalary(job.salary > 0 ? job.salary : 0);
    } else {
      setSelectedCurrency('');
      setSelectedSalary(0);
    }
    if (job.experience) {
      if (job.experience == 'fresher') {
        setExperienceRange(1);
      }
      if (job.experience == '0-1') {
        setExperienceRange(2);
      }
      if (job.experience == '2-3') {
        setExperienceRange(3);
      }
      if (job.experience == '3-5') {
        setExperienceRange(4);
      }
      if (job.experience == '5-7') {
        setExperienceRange(4);
      }
      if (job.experience == '7-10') {
        setExperienceRange(6);
      }
      if (job.experience == '10-15') {
        setExperienceRange(7);
      }
      if (job.experience == '15-20') {
        setExperienceRange(8);
      }
      if (job.experience == '20+') {
        setExperienceRange(9);
      }
      setExperience(job.experience);
    } else {
      setExperience('Choose Experience');
      setExperienceRange(0);
    }
    if (job.skills) {
      setSelectedSkill(job.skills.split(','));
    } else {
      setSelectedSkill([]);
    }
    if (job.sector) {
      setSelectedSectors(job.sector.split(','));
    } else {
      setSelectedSectors([]);
    }
    if (job.job_type) {
      setSelectedJobType(job.job_type.split(','));
    } else {
      setSelectedJobType([]);
    }
    const data = {
      keywords: job.job_title ? job.job_title : null,
      location: job.country_id ? job.country_id : null,
      skill: job.skills ? job.skills : null,
      job_type: job.job_type ? job.job_type : null,
      sector: job.sector ? job.sector : null,
      currency: job.currency ? job.currency : null,
      minSalary: job.salary ? job.salary : null,
      experience: job.experience ? job.experience : null,
      user_id: user?.id,
      filter_id: job.id,
    };
    getAllJobsSearch(data)
      .then(res => {
        if (res.status === true) {
          setTotalJobs(res.data);
          const paginatedPosts = paginate(res.data, 1, pageSize);
          setJobs(paginatedPosts);
          handleFilterModalClose();
        } else {
          setJobs([]);
        }
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });
    searchJobs(data, cancelTokenSource)
      .then(response => {
        setTotalJobs(response.data);
        const paginatedPosts = paginate(response.data, 1, pageSize);
        // setPaginationMeta(response.meta);
        setJobs(paginatedPosts);
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });
    return cancelTokenSource.cancel;
  };

  const handleDeleteJobFilter = (e: any, name = '') => {
    e.preventDefault();
    if (name == 'Section name' || filterJobData.length < 2) {
      notification.info({message: "You can't delete this item!"});
      setDeleteModal(false);
      return;
    }
    swal({
      title: 'Are you sure?',
      text: 'You want to delete the Job filter Section',
      icon: 'warning',
      dangerMode: true,
      buttons: ['Cancel', 'Yes, I am sure!'],
    }).then(willDelete => {
      if (willDelete) {
        const data = {
          id: filterJobId,
        };
        DeleteJobFilter(data)
          .then(res => {
            if (res.status === true) {
              notification.success({
                message: res.message,
              });
              getAllJobFilterDataDelete(user?.id);
              setDeleteModal(false);
            } else {
              ErrorHandler.showNotification('Deletion failed');
            }
          })
          .catch(err => {
            ErrorHandler.showNotification(err);
          });
      } else {
        ErrorHandler.showNotification('Deletion cancelled');
      }
    });
  };

  const getAllJobFilterDataDelete = async (id: any) => {
    try {
      const res = await getAlljobfilter(id);
      if (res.status == true) {
        setFilterJobs(res.data);
        setFilterJobsId(res.data[0].id);
        setFilterJobsSectionName(res.data[0].section_name);
        handleJobFilterNav(res.data[0]);
      } else {
        setFilterJobs([]);
      }
    } catch (error) {
      ErrorHandler.showNotification(error);
    }
  };

  const arr = [];
  const len = jobs.length;
  for (let i = 0; i < len; i++) {
    arr.push({
      '@type': 'ListItem',
      position: i,
      url: process.env.NEXT_PUBLIC_BASE_URL + '/job/' + jobs[i].job_slug,
    });
  }

  const onSubmit = (data: any) => {
    const cancelTokenSource = axios.CancelToken.source();
    // getAllJobsSearch(data)
    //   .then(res => {
    //     if (res.status === true) {
    //       setTotalJobs(res.data);
    //       const paginatedPosts = paginate(res.data, 1, pageSize);
    //       setJobs(paginatedPosts);
    //       handleJobFilterData(data);
    //       handleFilterModalClose();
    //     }
    //   })
    //   .catch(error => {
    //     ErrorHandler.showNotification(error);
    //   });
    searchJobs(data, cancelTokenSource)
      .then(response => {
        setTotalJobs(response.data);
        const paginatedPosts = paginate(response.data, 1, pageSize);
        //setPaginationMeta(response.meta);
        setJobs(paginatedPosts);
        handleJobFilterData(data);
        handleFilterModalClose();
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });
    return cancelTokenSource.cancel;
  };

  return (
    <>
      <div className="dash-right">
        <h1>
          My <span className="span-color"> Jobs</span>
        </h1>
        <div className="row m-column-reverse">
          <div className="col-sm-4">
            <ul className="list-loc m-m-0 mt-4">
              <li className="active">
                <Link href="/employees/jobs">All</Link>
              </li>
              <li>
                <Link href="/employees/jobs/savedjobs">
                  Saved <span className="tab-span-sa">{savedJobsCount}</span>
                </Link>
              </li>
            </ul>
          </div>
          <div className="col-sm-8 border-none-popup">
            <ModalForm open={deleteModal} onCancel={handleDeleteModal} title={'Edit Search Name'}>
              <form onSubmit={handleSubmitJobFilter} className="common_form_error" id="menu_form">
                <div className="popup-body">
                  <p className="f-12 c-2C2C2C">{filterJobType == 'edit' ? 'Edit Search Name' : 'Saved Search Name'}</p>
                  <input
                    type="text"
                    placeholder="Search Name"
                    className="big-input mb-0"
                    onChange={e => setFilterJobsSectionName(e.target.value)}
                    value={filterJobSectionName}
                    required></input>
                  <div className="text-right mt-3">
                    {filterJobType === 'edit' && (
                      <button className="cancel" onClick={e => handleDeleteJobFilter(e, filterJobSectionName)}>
                        Delete
                      </button>
                    )}
                    <button type="submit" className="save">
                      {filterJobType == 'edit' ? 'Update' : 'Save'}
                    </button>
                  </div>
                </div>
              </form>
            </ModalForm>
          </div>
        </div>
        <div className="data-management m-p-10">
          <div className="tab-filter mt-4">
            <nav>
              <div className="nav nav-tabs" id="nav-tab" role="tablist">
                {filterJobData.map((job: any, index: any) => (
                  <button
                    key={index}
                    className={`nav-link ${filterJobId === job.id ? 'active' : ''} ${index === 0 ? '' : 'mx-1'}`}
                    type="button"
                    style={{color: filterJobId === job.id ? '#0d6efd' : 'gray'}}
                    onClick={() => {
                      handleJobFilterNav(job);
                      setFilterJobsType('edit');
                    }}>
                    {job.section_name ? job.section_name : 'Search Name'}
                    &nbsp;
                    <img
                      src="/images/pen.png"
                      alt="pen"
                      className="lock-img mt-0"
                      width={12}
                      height={12}
                      onClick={e => {
                        e.stopPropagation();
                        setFilterJobsType('edit');
                        setFilterJobsSectionName(job.section_name);
                        setDeleteModal(true);
                      }}
                    />
                  </button>
                ))}
                <button
                  className="nav-link"
                  onClick={() => {
                    setDeleteModal(true);
                    setFilterJobsType('add');
                    setFilterJobsSectionName('');
                  }}>
                  +
                </button>
              </div>
            </nav>
            <div className="tab-content" id="nav-tabContent">
              <div className="tab-pane fade show active" id="nav-home" role="tabpanel" aria-labelledby="nav-home-tab">
                <div className="filter">
                  <div className="filter-sp">
                    {filterJobData
                      .filter((job_filter: any) => filterJobId == job_filter.id)
                      .map((job_filter: any, index: any) => (
                        <JobFilteredData
                          job_filter={job_filter}
                          key={index}
                          index={index}
                          Country={Country}
                          filterJobId={filterJobId}
                          filterJobSectionName={filterJobSectionName}
                          sectors={sectors}
                          skill={skill}
                          onSubmit={onSubmit}
                        />
                      ))}
                  </div>
                  <div className="filter-bottom" style={{cursor: 'pointer'}}>
                    <p
                      onClick={() => {
                        handleFilterModalOpen();
                      }}>
                      <i className="fa-solid fa-angles-down"></i> Filter
                    </p>
                    <ModalForm open={filterModal} onCancel={handleFilterModalClose} title={'Filter by'}>
                      <JobFilterModal
                        Country={Country}
                        filterJobId={filterJobId}
                        filterJobSectionName={filterJobSectionName}
                        sectors={sectors}
                        skill={skill}
                        onSubmit={onSubmit}
                      />
                    </ModalForm>
                  </div>
                </div>
                {jobs.length > 0 ? (
                  jobs.map((jobs: any, index: any) => (
                    <JobListItem
                      job={jobs}
                      index={index}
                      key={index}
                      jobInCityName={pathname.substring(1)}
                      jobPosting={false}
                    />
                  ))
                ) : (
                  <div className="filter filter-sp m-center mt-4 b-r-16">
                    <div className="row">
                      <div>No jobs founds</div>
                    </div>
                  </div>
                )}
                <JsonLd
                  data={{
                    '@context': 'https://schema.org',
                    '@type': 'ItemList',
                    itemListElement: arr,
                  }}
                />
                <Pagination
                  items={totalJobs.length}
                  currentPage={currentPage}
                  pageSize={pageSize}
                  onPageChange={onPageChange}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
