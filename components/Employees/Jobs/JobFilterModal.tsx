import React, { useContext, useState } from 'react';
import { ApplyJobFilterProps, Sector, Skill, experienceRanges } from '../../../lib/types'
import AuthContext from "@/Context/AuthContext";
import { getAllSectors, getAllSkills } from '../../../lib/frontendapi';
import { notification } from 'antd';

export default function JobFilterModal({ sectors, Country, skill, onSubmit, filterJobId, filterJobSectionName }: ApplyJobFilterProps) {

  const { user } = useContext(AuthContext);
  const [filteredSectors, setFilteredSectors] = useState<Sector[]>([]);
  const [filteredskill, setFilteredSkill] = useState<Skill[]>([]);
  const [selectedJobType, setSelectedJobType] = useState<any[]>([]);
  const [selectedSectors, setSelectedSectors] = useState<any[]>([]);
  const [selectedSkill, setSelectedSkill] = useState<any[]>([]);
  const [keywords, setKeywords] = useState("");
  const [location, setLocation] = useState("");
  const [experienceRange, setExperienceRange] = useState(0);
  const [experience, setExperience] = useState(experienceRanges[0].label);
  const [selectedCurrency, setSelectedCurrency] = useState('');
  const [maxSalary, setMaxSalary] = useState<number>(100000);
  const [selectedSalary, setSelectedSalary] = useState<number>(0);
  const [searchSectorKeywords, setSearchSectorKeywords] = useState('');
  const [searchSkillKeyWords, setSearchSkillKeyWords] = useState('');


  const handleCurrencyChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    if (event.target.value != '') {
      setSelectedCurrency(event.target.value);
    } else {
      setSelectedSalary(0)
      setSelectedCurrency('')
    }
  };

  const handleSalaryChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (selectedCurrency) {
      setSelectedSalary(Number(event.target.value));
    } else {
      notification.info({
        message: "Please choose a currency first"
      });
    }
  };

  const handleExperienceChange = (event: any) => {
    const selectedRange = Number(event.target.value);
    setExperienceRange(selectedRange);
    const selectedExperience = experienceRanges.find((range) => range.id === selectedRange);
    if (selectedExperience) {
      setExperience(selectedExperience.label);
    }
  };


  const handleChangeSearchBySector = (e: any, sectorId: any) => {
    if (e.target.checked) {
      setSelectedSectors((prevSectors: any[]) => [...prevSectors, sectorId]);
    } else {
      const updatedSectors = selectedSectors.filter((id) => id !== sectorId);
      setSelectedSectors(updatedSectors);
    }
  };

  const onSearchSkill = (e: React.ChangeEvent<HTMLInputElement>) => {
    const keywords = e.target.value;
    setSearchSkillKeyWords(keywords);
    if (keywords) {
      const filteredSkills = skill.filter((skill: any) =>
        skill.skills.toLowerCase().includes(keywords.toLowerCase())
      );
      setFilteredSkill(filteredSkills);
    } else {
      getAllSkills()
    }
  };

  const onSearchSector = (e: React.ChangeEvent<HTMLInputElement>) => {
    const keywords = e.target.value;
    setSearchSectorKeywords(keywords);
    if (keywords) {
      const filteredSector = sectors.filter((sectors: any) =>
        sectors.sector_name.toLowerCase().includes(keywords.toLowerCase())
      );
      setFilteredSectors(filteredSector);
    } else {
      getAllSectors()
    }
  };


  const handleChangeSearchSkill = (e: any, skillId: number) => {
    if (e.target.checked) {
      setSelectedSkill((prevSkills: any[]) => [...prevSkills, skillId]);
    } else {
      const updatedSkills = selectedSkill.filter((id) => id !== skillId);
      setSelectedSkill(updatedSkills);
    }
  };

  const handleChangeJobType = (e: any) => {
    const jobType = e.target.value;
    setSelectedJobType((prevJobTypes: any) => {
      if (e.target.checked) {
        return [...prevJobTypes, jobType];
      } else {
        return prevJobTypes.filter((type: any) => type !== jobType);
      }
    });
  };

  const handleSubmit = () => {
    const data = {
      keywords: keywords,
      location: location,
      skill: selectedSkill.join(','),
      job_type: selectedJobType.join(','),
      sector: selectedSectors.join(','),
      experience: experienceRange != 0 ? experience : null,
      currency: selectedSalary > 0 ? selectedCurrency : null,
      minSalary: selectedSalary > 0 ? selectedSalary : null,
      maxSalary: maxSalary,
      user_id: user?.id,
      filter_id: filterJobId,
      section_name: filterJobSectionName
    };
    onSubmit(data)
  };


  return (
    <>
      <div className="popup-body filter-pop text-left">
        <div className="row">
          <div className="col-sm-6 text-left">
            <p className="f-22 c-0055BA w-700">Filter By</p>
          </div>
          <div className="col-sm-6 text-right"></div>
        </div>
        <div className="row">
          <div className="col-sm-6">
            <div className="form-part mt-4">
              <input
                type="text"
                placeholder="Job Title"
                className="medium-input"
                onChange={(e: any) =>
                  setKeywords(e.target.value)
                }
                value={keywords}
              />
            </div>
          </div>
          <div className="col-sm-6">
            <div className="form-part mt-4">
              <select
                className="form-control medium-input"
                onChange={(e: any) =>
                  setLocation(e.target.value)
                }
                value={location}
              >
                <option value="">Select Location</option>
                {Country.length > 0 ? (
                  Country.map((CountryData: any, index: any) => {
                    if (CountryData.status === 'active') {
                      return (
                        <option
                          value={CountryData.id}
                          key={index}
                        >
                          {CountryData.country_name}
                        </option>
                      );
                    }
                  })
                ) : (
                  <option value="">Select Location</option>
                )}
              </select>
            </div>
          </div>
        </div>
        <div className="row">
          <div className="col-sm-6">
            <div className="salary-box h-2 mt-4">
              <select className="choose-currency mb-4" value={selectedCurrency} onChange={handleCurrencyChange}>
                <option value="">Choose Currency</option>
                {Country.map((country: any, index: any) => {
                  if (country.currency !== null) {
                    if (country.status === 'active') {
                      return (
                        <option key={index} value={country.currency}>
                          {country.currency}
                        </option>
                      );
                    }
                  }
                  return null;
                })}
              </select>
              <p className="f-16 w-600 c-2C2C2C">Salary</p>
              <p className="f-12 c-2C2C2C">
                {`${selectedCurrency} 0 - ${selectedCurrency} ${maxSalary}`}
              </p>
              {selectedSalary > 0 && (
                <p className="f-12 c-2C2C2C">
                  {selectedSalary} {selectedCurrency}
                </p>
              )}
              <input
                type="range"
                className="form-range w-75"
                id="customRange1"
                min="0"
                max={maxSalary.toString()}
                value={selectedSalary.toString()}
                onChange={handleSalaryChange}
              />
            </div>
          </div>
          <div className="col-sm-6">
            <div className="salary-box h-2 mt-4">
              <p className="f-16 w-600 c-2C2C2C pb-2">Experience</p>
              <p className="f-12 c-2C2C2C">{experience} {(experienceRange !== 0 && experienceRange !== 1) && (experienceRange == 2 ? 'year' : 'years')} </p>
              <input
                type="range"
                className="form-range w-75"
                id="customRange1"
                min="0"
                max="9"
                step="1"
                value={experienceRange}
                onChange={handleExperienceChange}
              />
            </div>
          </div>
        </div>
        <div className="row">
          <div className="col-sm-6">
            <div className="salary-box mt-4">
              <p className="f-16 w-600 c-2C2C2C">Sector</p>
              <div className="form-in position-relative input-bababa ">
                <i className="fa-solid fa-magnifying-glass  glass-search pad-sp"></i>
                <input
                  type="text"
                  placeholder="Search Sectors"
                  className="medium-input left-sp mt-2 mb-2 c-999999"
                  value={searchSectorKeywords}
                  onChange={(e) => onSearchSector(e)}
                />
              </div>
              <div className="form-in" id='scrolldata'>
                {
                  filteredSectors.length > 0 ? (
                    filteredSectors.map((sectors_data: any, index: any) => {
                      return (
                        <div className="form-master-field dflex" key={index}>
                          <input
                            type="checkbox"
                            placeholder="Placeholder"
                            className="master-fields checkbox border-0 mb-0"
                            onChange={(e) => {
                              handleChangeSearchBySector(
                                e,
                                sectors_data.id.toString()
                              );
                            }}
                            checked={selectedSectors.includes(sectors_data.id.toString())}
                          />
                          <label className="check-label c-4D4D4D mb-0">
                            {sectors_data.sector_name}
                          </label>
                        </div>
                      );
                    })
                  ) : (
                    sectors.map((sectors_data: any, index: any) => {
                      return (
                        <div className="form-master-field dflex" key={index}>
                          <input
                            type="checkbox"
                            placeholder="Placeholder"
                            className="master-fields checkbox border-0 mb-0"
                            onChange={(e) => {
                              handleChangeSearchBySector(
                                e,
                                sectors_data.id.toString()
                              );
                            }}
                            checked={selectedSectors.includes(sectors_data.id.toString())}
                          />
                          <label className="check-label c-4D4D4D mb-0">
                            {sectors_data.sector_name}
                          </label>
                        </div>
                      );
                    })
                  )
                }
              </div>
            </div>
          </div>
          <div className="col-sm-6">
            <div className="salary-box mt-3">
              <p className="f-16 w-600 c-2C2C2C pb-2">Skills</p>
              <div className="form-in position-relative input-bababa ">
                <i className="fa-solid fa-magnifying-glass  glass-search pad-sp"></i>
                <input
                  type="text"
                  placeholder="Search Skills"
                  className="medium-input left-sp mt-2 mb-2 c-999999"
                  value={searchSkillKeyWords}
                  onChange={(e) => onSearchSkill(e)}
                />
              </div>
              <div className="form-in" id='scrolldata'>
                {filteredskill.length > 0 ? (
                  filteredskill.map((skills: any, index: any) => {
                    return (
                      <div className="form-master-field dflex" key={index}>
                        <input
                          type="checkbox"
                          placeholder="Placeholder"
                          className="master-fields checkbox border-0 mb-0"
                          onChange={(e) => {
                            handleChangeSearchSkill(e, skills.id.toString());
                          }}
                          checked={selectedSkill.includes(skills.id.toString())}
                        />
                        <label className="check-label c-4D4D4D mb-0">{skills.skills}</label>
                      </div>
                    );
                  })
                ) : (
                  skill.length > 0 ? (
                    skill.map((skills: any, index: any) => {
                      return (
                        <div className="form-master-field dflex" key={index}>
                          <input
                            type="checkbox"
                            placeholder="Placeholder"
                            className="master-fields checkbox border-0 mb-0"
                            onChange={(e) => {
                              handleChangeSearchSkill(e, skills.id.toString());
                            }}
                            checked={selectedSkill.includes(skills.id.toString())}
                          />
                          <label className="check-label c-4D4D4D mb-0">{skills.skills}</label>
                        </div>
                      );
                    })
                  ) : null
                )}
              </div>
            </div>
          </div>
          <div className="col-sm-6">
            <div className="salary-box mt-4">
              <p className="f-16 w-600 c-2C2C2C">Job Type</p>
              <div className="form-master-field dflex ">
                <input
                  type="checkbox"
                  placeholder="Placeholder"
                  className="master-fields checkbox border-0 mb-0"
                  value="fulltime"
                  onChange={handleChangeJobType}
                  checked={selectedJobType.includes("fulltime")}
                />
                <label className="check-label c-4D4D4D mb-0">
                  Full-Time
                </label>
              </div>
              <div className="form-master-field dflex ">
                <input
                  type="checkbox"
                  placeholder="Placeholder"
                  className="master-fields checkbox border-0 mb-0"
                  value="parttime"
                  onChange={handleChangeJobType}
                  checked={selectedJobType.includes("parttime")}
                />
                <label className="check-label c-4D4D4D mb-0">
                  Part-time
                </label>
              </div>
              <div className="form-master-field dflex ">
                <input
                  type="checkbox"
                  placeholder="Placeholder"
                  className="master-fields checkbox border-0 mb-0"
                  value="contract"
                  onChange={handleChangeJobType}
                  checked={selectedJobType.includes("contract")}
                />
                <label className="check-label c-4D4D4D mb-0">
                  Contract
                </label>
              </div>
              <div className="form-master-field dflex ">
                <input
                  type="checkbox"
                  placeholder="Placeholder"
                  className="master-fields checkbox border-0 mb-0"
                  value="freelance"
                  onChange={handleChangeJobType}
                  checked={selectedJobType.includes("freelance")}
                />
                <label className="check-label c-4D4D4D mb-0">
                  FreeLancer
                </label>
              </div>
            </div>
          </div>
        </div>
        <div className="text-center mt-5">
          <button
            className="btn-a primary-size-16 btn-bg-0055BA"
            onClick={() => {
              handleSubmit();
            }}
          >
            View Results
          </button>
        </div>
      </div>
    </>
  )
}

