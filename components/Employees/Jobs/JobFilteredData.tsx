import React, { useState, useContext } from 'react';
import AuthContext from "@/Context/AuthContext";
import { ApplyJobFilterProps} from '../../../lib/types'


export default function JobFilteredData({ job_filter, index, onSubmit,sectors, Country, skill, filterJobId, filterJobSectionName }:ApplyJobFilterProps) {

  const { user } = useContext(AuthContext);
  const [selectedJobType, setSelectedJobType] = useState<any[]>([]);
  const [selectedSectors, setSelectedSectors] = useState<any[]>([]);
  const [selectedSkill, setSelectedSkill] = useState<any[]>([]);
  const [keywords, setKeywords] = useState("");
  const [location, setLocation] = useState("");
  const [selectedCurrency, setSelectedCurrency] = useState('');
  const [selectedSalary, setSelectedSalary] = useState<number>(0);


  const handleClick = (type: any, id: any) => {
    let updatedSkill = [];
    let updatedSector = [];
    let updatedJobType = [];

    if (type == "skill") {
      updatedSkill = selectedSkill.filter((skillId) => skillId != id);
      setSelectedSkill(updatedSkill)
    }
    if (type == "sector") {
      updatedSector = selectedSectors.filter((SectorId) => SectorId != id);
      setSelectedSectors(updatedSector)
    }
    if (type == "jobtype") {
      updatedJobType = selectedJobType.filter((jobtype) => jobtype != id);
      setSelectedJobType(updatedJobType)
    }
    if (type == 'job_title') {
      setKeywords("");
    }
    if (type == 'salary') {
      setSelectedCurrency("");
      setSelectedSalary(0);
    }
    if (type == 'country_id') {
      setLocation("");
    }
    const data = {
      keywords: type == 'job_title' ? '' : keywords,
      location: type == 'country_id' ? '' : location,
      skill: type == "skill" ? updatedSkill.join(",") : selectedSkill.join(","),
      job_type: type == "jobtype" ? updatedJobType.join(",") : selectedJobType.join(","),
      sector: type == "sector" ? updatedSector.join(",") : selectedSectors.join(","),
      currency: type == 'salary' ? '' : selectedCurrency,
      minSalary: type == 'salary' ? '' : selectedSalary > 0 ? selectedSalary : '',
      user_id: user?.id,
      filter_id: filterJobId,
      section_name: filterJobSectionName
    };
    onSubmit(data);
  };


  return (
    <>
      <div key={index} className="ASd">
        {job_filter.job_title && (
          <p className="cat d-inline-block">
            {job_filter.job_title}<i className="fa-solid fa-xmark" onClick={() => handleClick("job_title", "")}></i>
          </p>
        )}
        {job_filter.country_id && (
          <p className="cat d-inline-block mx-2">
            {Country.find((country: any) => country.id == job_filter.country_id && country.status == 'active')?.country_name}
            <i className="fa-solid fa-xmark" onClick={() => handleClick("country_id", "")}></i>
          </p>
        )}
        {job_filter.salary && (
          <p className="cat d-inline-block mx-2">
            0 - {job_filter.salary} {job_filter.currency}<i className="fa-solid fa-xmark" onClick={() => handleClick("salary", "")}></i>
          </p>
        )}
        {job_filter.experience && (
          <p className="cat d-inline-block mx-2">
            {job_filter.experience} {job_filter.experience !== 'fresher' && (job_filter.experience != '0-1' ? 'years' : 'year')}<i className="fa-solid fa-xmark" onClick={() => handleClick("experience", "")}></i>
          </p>
        )}
        {job_filter.skills && (
          <>
            {job_filter.skills.split(",").map((skillId: any, index: any) => {
              const skillObj = skill.find((s: { id: number; }) => s.id == Number(skillId));
              return (
                <p className="cat d-inline-block mx-2" key={index}>
                  {skillObj ? <span>{skillObj.skills}</span> : null}
                  <i className="fa-solid fa-xmark" onClick={() => handleClick("skill", skillId)}></i>
                </p>
              );
            })}
          </>
        )}
        {job_filter.sector && (
          <>
            {job_filter.sector.split(",").map((sectorId: any, index: any) => {
              const sector = sectors.find((s: { id: number; }) => s.id == Number(sectorId));
              return (
                <p className="cat d-inline-block mx-2" key={index}>
                  {sector ? <span>{sector.sector_name}</span> : null}
                  <i className="fa-solid fa-xmark" onClick={() => handleClick("sector", sectorId)}></i>
                </p>
              );
            })}
          </>
        )}
        {job_filter.job_type && (
          <>
            {job_filter.job_type.split(",").map((jobtype: any, index: any) => {
              return (
                <p className="cat d-inline-block mx-2" key={index}>
                  {jobtype ? <span>{jobtype}</span> : null}
                  <i className="fa-solid fa-xmark" onClick={() => handleClick("jobtype", jobtype)}></i>
                </p>
              );
            })}
          </>
        )}
      </div>
    </>
  )
}
