import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

export default function Jobsblank() {
  return (
    <>
      <div className="dash-right">
        <h1>
          My <span className="span-color"> Jobs</span>
        </h1>
        <div className="row m-column-reverse">
          <div className="col-sm-7">
            <ul className="list-loc m-m-0 mt-4">
              <li>
                <Link href="/employees/jobs">All</Link>
              </li>
              <li className="active">
                <Link href="/employees/jobs/savedjobs">
                  Saved <span className="tab-span-sa">12</span>
                </Link>
              </li>
            </ul>
          </div>
          <div className="col-sm-5">
            <ul className="blue-text-line mt-2 text-right">
              <li>
                <a href="#">View Public Profile</a>
              </li>
            </ul>
          </div>
        </div>

        <div className="table-part ">
          <div className="work-experience-fieild m-p-10 text-center">
            <img
              src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blank-1.jpg'}
              alt="blank-1"
              className=""
              width={447}
              height={236}
              // layout='responsive'
            />
            <p className="f-22 c-BABABA mb-1">You don't seem to have any active jobs.</p>
            <p className="f-18">
              <a href="#" className="c-0070F5">
                Post A Job
              </a>
            </p>
          </div>
        </div>
      </div>
    </>
  );
}
