import React, {useState, useEffect, useContext} from 'react';
import {usePathname} from 'next/navigation';
import 'react-toastify/dist/ReactToastify.css';
import {getusersavedjobs} from '../../../lib/frontendapi';
import 'react-phone-input-2/lib/style.css';
import Link from 'next/link';
import Image from 'next/image';
import AuthContext from '@/Context/AuthContext';
import JobListItem from '@/components/Common/JobListItem';
import ErrorHandler from '@/lib/ErrorHandler';

export default function SavedJobs() {
  const {user} = useContext(AuthContext);
  const [savedJobs, setSavedJobs] = useState([]);
  const [savedJobsCount, setSavedJobsCount] = useState('');
  const pathname = usePathname();
  useEffect(() => {
    const data = {
      user_id: user?.id,
    };
    getusersavedjobs(data)
      .then(res => {
        if (res.status == true) {
          setSavedJobs(res.saved_jobs);
          setSavedJobsCount(res.count);
        } else {
          setSavedJobs([]);
          setSavedJobsCount('');
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  }, [user]);

  return (
    <>
      <div className="dash-right">
        <h1>
          My <span className="span-color"> Jobs</span>
        </h1>
        <div className="row m-column-reverse">
          <div className="col-sm-7">
            <ul className="list-loc m-m-0 mt-4">
              <li>
                <Link href="/employees/jobs">All</Link>
              </li>
              <li className="active ">
                <Link href="/employees/jobs/savedjobs">
                  Saved <span className="tab-span-sa c-0070F5">{savedJobs?.length}</span>
                </Link>
              </li>
            </ul>
          </div>
          <div className="col-sm-5">
            <ul className="blue-text-line mt-4 text-right"></ul>
          </div>
        </div>
        <div className="data-management m-p-10">
          <div className="tab-filter mt-4">
            <nav></nav>
            <div className="tab-content" id="nav-tabContent">
              <div className="tab-pane fade show active" id="nav-home" role="tabpanel" aria-labelledby="nav-home-tab">
                {savedJobs.length > 0 ? (
                  savedJobs.map((saved_jobs: any, index: any) => (
                    <JobListItem
                      job={saved_jobs}
                      index={index}
                      key={index}
                      jobInCityName={pathname.substring(1)}
                      jobPosting={false}
                    />
                  ))
                ) : (
                  <div className="filter filter-sp m-center mt-4 b-r-16">
                    <div className="row">
                      <div className="work-experience-fieild m-p-10 text-center">
                        <img src="/images/blank-1.jpg" alt="blank-1" className="find-img" width={447} height={236} />
                        <p className="f-22 c-BABABA mb-1">You don't seem to have any saved jobs.</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
