import React, {useState, useEffect, useContext} from 'react';
import {getNotifications, updateNotificationStatus} from '../../../lib/frontendapi';
import moment from 'moment';
import Pagination from '../../../components/Common/Pagination';
import {paginate} from '../../../helpers/paginate';
import AuthContext from '@/Context/AuthContext';
import <PERSON>rror<PERSON>and<PERSON> from '@/lib/ErrorHandler';
import Image from 'next/image';

export default function Notifications() {
  const {user} = useContext(AuthContext);
  const [totalNotifications, setTotalNotifications] = useState([]);
  const [userProfileImage, setUserProfileImage] = useState('');
  const [notifications, setNotifications] = useState([]);
  const [currentUserName, setCurrentUsername] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  const onPageChange = (page: any) => {
    setCurrentPage(page);
    getNotifications({notify_to: user?.id})
      .then(res => {
        if (res.status == true) {
          const filteredNotifications = res.data.filter((item: {notify_to: any}) => item.notify_to === user?.id);
          const paginatedPosts = paginate(filteredNotifications, page, pageSize);
          setNotifications(paginatedPosts);
          setTotalNotifications(filteredNotifications);
        } else {
          setNotifications([]);
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  };

  useEffect(() => {
    if (user) {
      setUserProfileImage(user?.profile_image?.source);
      setCurrentUsername(user?.name);
    }

    getNotifications()
      .then(res => {
        if (res.status == true) {
          const filteredNotifications = res.data.filter((item: {notify_to: any}) => item.notify_to === user?.id);
          const paginatedPosts = paginate(filteredNotifications, currentPage, pageSize);
          setNotifications(paginatedPosts);
          setTotalNotifications(filteredNotifications);
        } else {
          setNotifications([]);
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  }, [user, currentPage, pageSize]);

  useEffect(() => {
    const timer = setTimeout(() => {
      updateNotificationStatus(user?.id)
        .then(response => {})
        .catch(error => {});
    }, 1000);
    return () => clearTimeout(timer);
  }, [user]);

  let username_letter = currentUserName
    .split(/\s/)
    .reduce((response: any, word: any) => (response += word.slice(0, 1)), '');
  return (
    <>
      <div className="dash-right">
        <h1>
          My <span className="span-color"> Notifications</span>
        </h1>

        <div className="row mt-4 ">
          <div className="col-sm-6"></div>
          <div className="col-sm-6 text-right"></div>
        </div>

        <div className="data-management  mb-3 m-p-10">
          <div className="work-experience-fieild m-p-10">
            {notifications.length > 0 ? (
              notifications.slice(0, 10).map((notification_data: any, index: any) => {
                const created_at = moment.utc(notification_data.created_at);
                const currentTime = moment();
                const yesterday = moment().subtract(1, 'day');
                let timeFormatted;

                if (created_at.isSame(currentTime, 'day')) {
                  timeFormatted = created_at.local().format('hh:mmA');
                } else if (created_at.isSame(yesterday, 'day')) {
                  timeFormatted = 'Yesterday';
                } else {
                  timeFormatted = created_at.local().format('MMMM D');
                }
                return (
                  <div className="box-text-img bg-CFE5FF mb-2" key={index}>
                    <div className="row">
                      <div className="col w-col-noti mb-3">
                        {notification_data.profile_image ? (
                          <img
                            src={`${process.env.NEXT_PUBLIC_IMAGE_URL}images/userprofileImg/${notification_data.profile_image}`}
                            alt="Avatars-notification_data"
                            className="w-40 m-none"
                            width={40}
                            height={40}
                          />
                        ) : (
                          <div
                            title={notification_data.name}
                            className="text-uppercase w-75 notfication_name_two m-none">
                            {notification_data.name
                              ? notification_data.name.split(' ').length === 1
                                ? notification_data.name.substring(0, 2)
                                : notification_data.name
                                    .split(' ')
                                    .map((word: any, index: any) => (index === 0 || index === 1 ? word[0] : ''))
                                    .join('')
                              : ''}
                          </div>
                        )}
                      </div>
                      <div className="col-lg-10 col-md-8">
                        <div className="text-align-center m-2">
                          <span
                            className={`${notification_data.is_read === 0 ? 'notification_font_weight' : 'normal'}`}
                            dangerouslySetInnerHTML={{
                              __html: notification_data.notification,
                            }}></span>
                        </div>
                      </div>
                      <div className="col-lg-1 col-md-2 text-right mt-2">
                        <p className="f-12 c-999999  ">{timeFormatted}</p>
                      </div>
                    </div>
                  </div>
                );
              })
            ) : (
              <>
                <div className="row">
                  <div className="col-sm-12">
                    <p className="f-22 m-center">Inbox</p>
                  </div>
                </div>
                <div className="text-center">
                  <img src="/images/blank-5.png" alt="blank-5" width={150} height={150} />
                  <p className="f-22 c-BABABA mb-2">No New Notifications</p>
                  <p className="f-18 c-BABABA w-400">
                    Check this section for job updates, and
                    <br /> general notifications.{' '}
                  </p>
                </div>
              </>
            )}
            <Pagination
              items={totalNotifications.length}
              currentPage={currentPage}
              pageSize={pageSize}
              onPageChange={onPageChange}
            />
          </div>
        </div>
      </div>
    </>
  );
}
