import <PERSON>rror<PERSON>and<PERSON> from '@/lib/ErrorHandler';
import {getUserDetailsslug} from '@/lib/frontendapi';
import {Education, Portfolio, skill, language, User} from '@/lib/types';
import axios from 'axios';
import {useEffect, useState} from 'react';
import {Space} from 'antd';

export type ProfileProps = {
  userData: User;
  workExperience: any;
  education: Education[];
  skills: skill[];
  portfolio: Portfolio[];
  language: language[];
};

export default function ProfileInformation({
  userData,
  workExperience,
  education,
  portfolio,
  skills,
  language,
}: ProfileProps) {
  const birthdate = userData?.date_of_birth;

  function calculateAge(birthdate: any) {
    const currentDate = new Date();
    const birthDate = new Date(birthdate);
    let age = currentDate.getFullYear() - birthDate.getFullYear();
    if (
      currentDate.getMonth() < birthDate.getMonth() ||
      (currentDate.getMonth() === birthDate.getMonth() && currentDate.getDate() < birthDate.getDate())
    ) {
      age--;
    }
    return age;
  }

  const age = calculateAge(birthdate);

  return (
    <>
      <div className="text-right link-right-icons">
        <p>
          {userData?.website_url ? (
            <a href={userData?.website_url}>
              <i className="fa-solid fa-globe"></i>
            </a>
          ) : null}
          {userData?.linkedin_link ? (
            <a href={userData?.linkedin_link}>
              <i className="fa-brands fa-linkedin"></i>
            </a>
          ) : null}
          {userData?.facebook_link ? (
            <a href={userData?.facebook_link}>
              <i className="fa-brands fa-facebook"></i>
            </a>
          ) : null}
          {userData?.twitter_link ? (
            <a href={userData?.twitter_link}>
              <i className="fa-brands fa-x-twitter"></i>
            </a>
          ) : null}
          {userData?.instagram_link ? (
            <a href={userData?.instagram_link}>
              <i className="fa-brands fa-instagram"></i>
            </a>
          ) : null}
        </p>
      </div>
      <div className="row">
        <div className="col-sm-8">
          <div className="row">
            <div className="col-sm-12 col-12 m-center">
              <h4 className="em-name f-54">{userData?.name ? userData?.name : 'Candidate Name'}</h4>
              <h5 className="em-work f-37 w-500">
                {userData?.current_position ? userData?.current_position : 'Position'}{' '}
                {userData?.company ? `@${userData?.company}` : ''}
              </h5>
              <br />
              <Space size={'large'}>
                {userData?.degree && (
                  <div className="f-16 c-999999">
                    <i className="fa-solid fa-graduation-cap"></i> {userData?.degree}
                  </div>
                )}
                {(userData?.isShowEmail || userData?.isShowEmail === 1) && (
                  <div>
                    <i className="fa-regular fa-envelope"></i>
                    <a href={`mailto:${userData?.email}`}>&nbsp;&nbsp;{userData?.email || '<EMAIL>'}</a>
                  </div>
                )}
                {(userData?.showcontact_no || userData?.showcontact_no === 1) && (
                  <div className="f-16 c-999999">
                    <i className="fa-solid fa-phone-volume"></i>&nbsp;&nbsp;
                    <a target="_blank" href={'https://wa.me/' + userData?.contact_no}>
                      {userData?.contact_no || 'No number provided'}
                    </a>
                  </div>
                )}
              </Space>
              <br />
              <br />
              <ul className="skills mb-4">
                <li>
                  <p className="f-16 c-999999">
                    <i className="fa-solid fa-id-badge"></i> Age: {age ? age : '23'}
                  </p>
                </li>
                <li>
                  <p className="f-16 c-999999">
                    <i className="fa-regular fa-circle-user"></i> {userData?.gender ? userData?.gender : 'Male'}{' '}
                  </p>
                </li>
                <li>
                  <p className="f-16 c-999999">
                    <i className="fa-solid fa-location-dot"></i>{' '}
                    {userData?.country_name ? userData?.country_name : 'Country'}
                  </p>
                </li>
              </ul>
            </div>
            <div className="col-12">
              <h4 className="f-16 c-000">About</h4>
              <p className="f-18 w-400 c-4D4D4D open-sans">{userData?.bio || 'No bio available yet'}</p>
            </div>
          </div>
          <br />
          {workExperience.length > 0 && <p className="f-16 c-000 mb-0">Work Experience</p>}
          {workExperience.map((work: any, index: any) => (
            <div key={index}>
              <p className="f-22 w-700 c-2C2C2C mt-2 mb-1">{work.title}</p>
              <p className="f-18 c-0055BA w-600 mb-2">{work.company}</p>
              <p className="f-16 c-999999 mb-2">
                {new Date(work.start_date).toLocaleString('en-US', {month: '2-digit', year: 'numeric'})} -{' '}
                {new Date(work.end_date).toLocaleString('en-US', {month: '2-digit', year: 'numeric'})}
              </p>
              <p className="f-18 c-4D4D4D w-400 open-sans"> {work.description}</p>
            </div>
          ))}
          <br />
          <br />
          <br />
          {education.length > 0 && <p className="f-16 c-000">Education</p>}
          {education.map((edu, index) => (
            <div className="row" key={index}>
              <div className="col-sm-6 mt-2">
                <p className="f-22 mt-4 m-sp-0 w-700 c-2C2C2C">{edu.education_title}</p>
                <p className="f-18 c-0055BA w-600  mb-2">{edu.degree}</p>
                <p className="f-16 c-999999 mb-2">
                  {new Date(edu.start_date).toLocaleString('en-US', {month: '2-digit', year: 'numeric'})} -{' '}
                  {new Date(edu.end_date).toLocaleString('en-US', {month: '2-digit', year: 'numeric'})}
                </p>
                <p className="f-16 c-999999">
                  Scored: {edu.your_score}/{edu.max_score}
                </p>
              </div>
            </div>
          ))}
          <br />
          {skills.length > 0 && <p className="f-16 c-000 ">Skills</p>}
          <ul className="skills skills-f-18 mt-3">
            {skills.map((ski, index) => (
              <li key={index}>
                <p className="cat">{ski.skills}</p>
              </li>
            ))}
          </ul>
          <br />
          {portfolio.length > 0 ? <p className="f-16 c-000 ">Portfolio/Projects</p> : ''}
          {portfolio.map((port, index) => (
            <div key={index}>
              <p className="f-22 mt-2 m-sp-0 w-700 c-2C2C2C">{port.title}</p>
              <p className="f-18 c-0055BA w-600  mb-2 ">
                <i className="fa-solid fa-link"></i>{' '}
                <a href={port.portfolio_link} target="_blank">
                  {port.portfolio_link}
                </a>
              </p>
              <p className="f-16 c-999999 mb-2">
                {new Date(port.start_date).toLocaleString('en-US', {month: '2-digit', year: 'numeric'})} -{' '}
                {new Date(port.end_date).toLocaleString('en-US', {month: '2-digit', year: 'numeric'})}
              </p>
              <p className="f-16  c-4D4D4D open-sans">{port.description}</p>
            </div>
          ))}
          <br />
          {language.length > 0 ? <p className="f-16 c-000 ">Languages</p> : ''}
          {language.map((lang, index) => (
            <div key={index}>
              <p className="f-22 mt-4 m-sp-0 w-700 c-2C2C2C">{lang.language}</p>
              <p className="f-18 c-0055BA w-600  mb-2 ">{lang.proficiency}</p>
            </div>
          ))}
        </div>
      </div>
    </>
  );
}
