import React, { useState, useEffect, useContext } from 'react';
import { updateUserDetails, updateTwoFactorAuth } from '../../../lib/frontendapi';
import { updateShowContact, updateShowEmail } from '../../../lib/employeeapi';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import PopupModal from '../../../components/Common/PopupModal';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import AuthContext from '@/Context/AuthContext';
import ErrorHandler from '@/lib/ErrorHandler';
import { notification } from 'antd';
import Image from 'next/image';

export default function Account() {
  const { user } = useContext(AuthContext);
  const [userProfileImage, setUserProfileImage] = useState('');
  const [userEmail, setUserEmail] = useState('');
  const [userName, setUserName] = useState('');
  const [selectedFileName, setSelectedFileName] = useState('');
  const [uploadError, setUploadError] = useState('');
  const [previewImage, setPreviewImage] = useState('');
  const [displayContactNumber, setDisplayContactNumber] = useState<any>('');
  const [displayEmail, setDisplayEmail] = useState<any>('');
  const [modalConfirm7, setModalConfirm7] = useState(false);
  const [twofactorauth, setTwoFactorAuth] = useState<any>('');
  const { data: session }: any = useSession();

  useEffect(() => {
    if (user) {
      setUserProfileImage(user?.profile_image?.source);
      setUserEmail(user?.email);
      setUserName(user?.name);
      setTwoFactorAuth(user?.is2FA);
      setDisplayContactNumber(user?.showcontact_no);
      setDisplayEmail(user?.isShowEmail);
    }
    const storedValue = localStorage.getItem('displayContactNumber');
    const storeEmailValue = localStorage.getItem('displayEmail');
    if (storedValue) {
      setDisplayContactNumber(storedValue === 'true');
    }
    if (storeEmailValue) {
      setDisplayEmail(storeEmailValue === 'true');
    }
  }, [user]);

  const openModalConfirm7 = () => {
    setModalConfirm7(true);
  };

  const modalConfirmClose7 = () => {
    setModalConfirm7(false);
  };

  const submitForm = (event: any) => {
    event.preventDefault();
    const data = {
      name: userName,
    };
    if (userProfileImage && userProfileImage.length > 0) {
      updateUserDetails(user?.id, data) // Image upload work pending
        .then(res => {
          if (res.status == true) {
            localStorage.removeItem('userProfileImage');
            localStorage.setItem('userProfileImage', res.data);
            notification.success({
              message: res.message,
            });
            setTimeout(function () {
              window.location.reload();
            }, 3000);
          } else {
            notification.info({
              message: res.message,
            });
          }
        })
        .catch(error => {
          ErrorHandler.showNotification(error);
        });
    } else {
      updateUserDetails(user?.id, data)
        .then(res => {
          if (res.status == true) {
            localStorage.removeItem('userProfileImage');
            localStorage.setItem('userProfileImage', res.data);
            notification.success({
              message: res.message,
            });
            setTimeout(function () {
              window.location.reload();
            }, 3000);
          } else {
            notification.info({
              message: res.message,
            });
          }
        })
        .catch(error => {
          ErrorHandler.showNotification(error);
        });
    }
  };

  const handleImageChange = (event: any) => {
    const fileInput = event.target;
    const selectedFile = event.target.files[0];
    if (selectedFile) {
      const maxSizeInBytes = 1 * 1024 * 1024; // 1MB
      if (event.target.files[0].type.includes('image')) {
        if (selectedFile.size > maxSizeInBytes) {
          notification.error({ message: 'File size exceeds the maximum limit (1MB).' });
          fileInput.value = '';
          return;
        } else {
          setPreviewImage(URL.createObjectURL(event.target.files[0]));
          setSelectedFileName(event.target.files[0].name);
          setUserProfileImage(event.target.files);
          setUploadError('');
        }
      } else {
        setUploadError('Please upload an image file (JPEG,JPG,PNG).');
      }
    }
  };

  const handleToggleContactNumber = () => {
    const updatedValue = !displayContactNumber;
    const data = {
      userId: user?.id,
      showcontact_no: updatedValue ? 1 : 0,
    };
    updateShowContact(data)
      .then(response => {
        setDisplayContactNumber(updatedValue);
      })
      .catch(error => {
        console.error('Error setting default resume:', error);
      });
  };

  const handleToggleEmail = () => {
    const updatedValue = !displayEmail;
    const data = {
      userId: user?.id,
      isShowEmail: updatedValue ? 1 : 0,
    };
    updateShowEmail(data)
      .then(response => {
        setDisplayEmail(updatedValue);
      })
      .catch(error => {
        console.error('Error setting default resume:', error);
      });
  };

  const handleToggle2Fa = () => {
    const updatedValue: any = !twofactorauth;
    const data = {
      userId: user?.id,
      is2FA: updatedValue ? 1 : 0,
    };

    updateTwoFactorAuth(data)
      .then(response => {
        setTwoFactorAuth(updatedValue);
      })
      .catch(error => {
        console.error('Error setting default resume:', error);
      });
  };
  return (
    <>
      <div className="dash-right">
        <h1>Settings </h1>
        <div className="row ">
          <div className="col-sm-12">
            <ul className="list-loc m-m-0 mt-4">
              <li className="active">
                <Link href="/employees/settings">Account</Link>
              </li>
              {session ? (
                ''
              ) : (
                <li>
                  <Link href="/employees/settings/changepassword">Password </Link>
                </li>
              )}
              <li>
                <Link href="/employees/settings/notifications">Notification </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="data-management m-p-10">
          <div className="work-experience-fieild m-p-10">
            <form className="form-experience-fieild" onSubmit={submitForm}>
              <div className="row">
                <div className="col-lg-2 col-md-3 col-12">
                  <div className="dash-profile-img mb-4 m-auto">
                    <p className="f-12 c-2C2C2C m-center">Profile Picture </p>
                    <img
                      src={
                        previewImage || userProfileImage
                          ? previewImage ||
                          `${process.env.NEXT_PUBLIC_IMAGE_URL}${userProfileImage?.replace("/storage/", '')}`
                          : `${process.env.NEXT_PUBLIC_BASE_URL}images/Profile_Picture.png`
                      }
                      alt={previewImage}
                      className={`img-circle ${!previewImage && 'Profile_Picture'}`}
                      style={{ width: '100%', height: '118px', objectFit: 'contain' }}
                    />
                  </div>
                </div>
                <div className="col-lg-10 col-md-9 col-12">
                  <div className="uploade-btn">
                    <div
                      className="btn-a primary-size-16 btn-bg-0055BA mt-5 mb-4 mobile-m-0 max-340"
                      onClick={openModalConfirm7}>
                      <i className="fa-solid fa-upload"></i> Upload A New Photo
                    </div>
                    <PopupModal
                      show={modalConfirm7}
                      handleClose={modalConfirmClose7}
                      customclass={'header-remove body-sp-0'}>
                      <div className="popup-body">
                        <h5 className="f-26 c-0055BA w-700 text-center mb-4">Upload Your Profile Picture</h5>
                        <div className="upload-file" id="upload-file1">
                          <div className="file-up">
                            <input
                              type="file"
                              name="profile_image"
                              id="doctor_profile"
                              onChange={handleImageChange}
                              accept=".jpg, .png , .jpeg"
                            />
                            <img
                              src={process.env.NEXT_PUBLIC_BASE_URL + 'images/cemra.png'}
                              alt="cemra"
                              className="cemrat"
                            />
                          </div>
                          <p className="upload-text">
                            Browse and choose the files you want to upload from your computer.
                          </p>
                          <p className="max-size">Maximum upload size is 1MB.</p>
                        </div>
                        {uploadError && (
                          <p className="error mt-2 ml-auto" style={{ color: 'red' }}>
                            {uploadError}
                          </p>
                        )}
                        {selectedFileName && <p className="text-dark">Selected File: {selectedFileName}</p>}
                        <div>
                          <div
                            style={{ color: '#2C2C2C' }}
                            className="d-flex justify-content-center align-items-center flex-column mt-3">
                            <p className="mb-0">
                              <span className="fw-bold">Pro Tip:</span>
                              <span style={{ fontWeight: 500 }}>Choose a recent, clear headshot with good</span>
                            </p>
                            <p style={{ fontWeight: 500 }}>lighting and dress professionally.</p>
                          </div>
                          <div style={{ fontSize: '12px', color: '#D04E4F', gap: '2px' }}>
                            <p style={{ marginLeft: '17px' }} className="ml-2 mb-0">
                              <span className="fw-bold">Please Note:</span>Avoid using casual or overly edited photos,
                              as your profile picture
                            </p>
                            <p className="text-center">
                              is often the first impression potential employers have of you.
                            </p>
                          </div>
                          <div className="modal-footer2">
                            <div className="row">
                              <div className="col-sm-4 col-5">
                                <button
                                  type="button"
                                  className="cancel-btn w-100"
                                  data-bs-dismiss="modal"
                                  onClick={modalConfirmClose7}>
                                  Cancel
                                </button>
                              </div>
                              <div className="col-sm-8 col-7">
                                <button type="submit" className="update-btn w-100">
                                  Update
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </PopupModal>
                  </div>
                </div>
              </div>
              <div className="row">
                <div className="col-sm-5">
                  <div className="form-experience-fieild">
                    <div className="form_field_sec">
                      <input type="email" placeholder="Email" className="fild-des" value={userEmail} readOnly />
                      <label>Email ID</label>
                    </div>
                    <div className="form_field_sec">
                      <input
                        type="text"
                        placeholder="Name"
                        className="fild-des"
                        value={userName}
                        onChange={(e: any) => setUserName(e.target.value)}
                      />
                      <label>Name</label>
                    </div>
                  </div>
                </div>
                <div className="col-sm-12">
                  <div className="form-experience-fieild">
                    <div className="row">
                      <div className="col-sm-10 col-8">
                        <label>PRIVACY</label>
                        <p className="f-22 c-2C2C2C w-500 mb-1">Display Contact Number</p>
                        <p className="f-16 c-747474">
                          Choose if you would like to display your contact number on your public profile.
                        </p>
                      </div>
                      <div className="col-sm-2 col-4 text-right">
                        <label className="switch btn-swith">
                          <input type="checkbox" checked={displayContactNumber} onChange={handleToggleContactNumber} />
                          <span className="slider round"></span>
                        </label>
                      </div>
                    </div>
                    <div className="row mt-4">
                      <div className="col-sm-10 col-8">
                        <p className="f-22 c-2C2C2C w-500 mb-1">Display Email</p>
                        <p className="f-16 c-747474">
                          Choose if you would like to display your contact number on your public profile.
                        </p>
                      </div>
                      <div className="col-sm-2 col-4 text-right">
                        <label className="switch btn-swith">
                          <input type="checkbox" checked={displayEmail} onChange={handleToggleEmail} />
                          <span className="slider round"></span>
                        </label>
                      </div>
                    </div>
                    <div className="row mt-4">
                      <div className="col-sm-10 col-8">
                        <p className="f-22 c-2C2C2C w-500 mb-1">Two-Factor Authentication (2FA)</p>
                        <p className="f-16 c-747474">
                          2FA adds an extra layer of protection, making it more challenging for hackers to compromise
                          user accounts.
                        </p>
                      </div>
                      <div className="col-sm-2 col-4 text-right">
                        <label className="switch btn-swith">
                          <input type="checkbox" checked={twofactorauth} onChange={handleToggle2Fa} />
                          <span className="slider round"></span>
                        </label>
                      </div>
                    </div>
                    <div className="text-right mt-5">
                      <button className="cancel">Cancel</button>
                      <button className="save">Save</button>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
        <ToastContainer />
      </div>
    </>
  );
}
