import React, { useState, useContext } from 'react';
import { changepassword } from '../../../lib/frontendapi';
import { useForm } from 'react-hook-form';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Link from 'next/link';
import AuthContext from "@/Context/AuthContext";
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "@/lib/ErrorHandler";
import { notification } from 'antd';


export default function ChangePassword() {
    const { user } = useContext(AuthContext);
    const { register, handleSubmit, watch, formState: { errors }, }: any = useForm();
    const [currentPassword, setCurrentPassword]: any = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [retypePassword, setRetypePassword] = useState('');

    const submitForm = (data: any) => {
        const password_data = {
            user_id: user?.id,
            currentPassword: data.current_password,
            newPassword: data.new_password,
            retypePassword: data.retype_password
        }
        changepassword(password_data)
        .then(res => {
            if (res.status == true) {
              notification.success({
                message: res.message
              });
            } else {
              notification.error({
                message: res.message
              });
            }
        })
        .catch(error => {
            ErrorHandler.showNotification(error);
        });
    }
    return (
        <>
            <div className="dash-right">
                <h1>Settings </h1>
                <div className='row '>
                    <div className='col-sm-12'>
                        <ul className='list-loc m-m-0 mt-4'>
                            <li><Link href="/employees/settings">Account</Link></li>
                            <li className='active'><Link href="/employees/settings/changepassword">Password </Link></li>
                            <li><Link href="/employees/settings/notifications">Notification </Link></li>
                        </ul>
                    </div>
                </div>

                <div className='data-management m-p-10'>
                    <div className='work-experience-fieild m-p-10'>
                        <p className='f-12 c-2C2C2C m-center w-700'>RESET PASSWORD</p>
                        <div className='row'>
                            <form className='form-experience-fieild' onSubmit={handleSubmit(submitForm)}>
                                <div className='col-sm-5'>
                                    <div className='form_field_sec'>
                                        <input type='password' placeholder='Current Password' className='fild-des mb-2' {...register('current_password', { required: true })} onChange={(e: any) => setCurrentPassword(e.target.value)} />
                                        <label>Current Password</label>
                                    </div>
                                    {errors.current_password && errors.current_password.type === 'required' && <p className="text-danger" style={{ "textAlign": "left" }}>Current Password is required.</p>}
                                    <div className='form_field_sec'>
                                        <input type='password' placeholder='New Password' className='fild-des mb-2' {...register('new_password', { required: true })} onChange={(e: any) => setNewPassword(e.target.value)} />
                                        <label>New Password</label>
                                    </div>
                                    {errors.new_password && errors.new_password.type === 'required' && <p className="text-danger" style={{ "textAlign": "left" }}>New Password is required.</p>}
                                    <div className='form_field_sec'>
                                        <input
                                            type='password'
                                            placeholder='Re-type Password'
                                            className='fild-des mb-2'
                                            {...register('retype_password', {
                                                required: 'Re-type password is required.',
                                                validate: (val: string) =>
                                                    val === watch('new_password') || 'Your passwords do not match',
                                            })}
                                            onChange={(e) => setRetypePassword(e.target.value)}
                                        />
                                        <label>Re-type Password</label>
                                    </div>
                                    {errors.retype_password && (
                                        <p className="text-danger" style={{ textAlign: "left" }}>
                                            {errors.retype_password.message}
                                        </p>
                                    )}

                                </div>
                                <div className='col-sm-12'>
                                    <div className='text-right mt-5'>
                                        <button className='cancel'>Cancel</button>
                                        <button className='save' type="submit">Save</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <ToastContainer />
            </div>
        </>
    )
}
