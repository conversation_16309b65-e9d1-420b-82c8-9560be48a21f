import React, { useState, useEffect, useContext } from 'react';
import { getSettings, updateAccountAccess, updateNewsLetterAccess, updateRecommendationsAccess, updateAnnouncementsAccess, updateMessageFromRecruitersAccess } from '../../../lib/frontendapi';
import Link from 'next/link'
import { useSession } from "next-auth/react";
import AuthContext from "@/Context/AuthContext";
import <PERSON>rrorHandler from "@/lib/ErrorHandler";

export default function Notifications() {
    const { user } = useContext(AuthContext);
    const [accountAccess, setAccountAccess] = useState('');
    const [newsletterAccess, setNewsletterAccess] = useState('');
    const [recommendationsAccess, setRecommendationsAccess] = useState('');
    const [announcementsAccess, setAnnouncementsAccess] = useState('');
    const [messageFromRecruitersAccess, setMessageFromRecruitersAccess] = useState('');
    const { data: session }:any = useSession();

    useEffect(() => {
        const data = {
            user_id: user?.id
        }
        getSettings(data)
            .then(res => {
                if (res.status == true && res.data ) {
                    setAccountAccess(res.data.account_access);
                    setNewsletterAccess(res.data.newsletter_access);
                    setRecommendationsAccess(res.data.recommendations_access);
                    setAnnouncementsAccess(res.data.announcements_access);
                    setMessageFromRecruitersAccess(res.data.message_from_candidate_access);
                } else {
                    setAccountAccess('');
                    setNewsletterAccess('');
                    setRecommendationsAccess('');
                    setAnnouncementsAccess('');
                    setMessageFromRecruitersAccess('');
                }
            })
            .catch(error => {
                ErrorHandler.showNotification(error);
            });
    }, [user]);
    const handleChangeAccountAccess = (e: any) => {
        setAccountAccess(e.target.checked);
        const checked = e.target.checked;
        let account_access_value = '';
        if (checked) {
            account_access_value = '1';
        }
        if (!checked) {
            account_access_value = '0';
        }
        const data = {
            user_id: user?.id,
            account_access: account_access_value
        }
        updateAccountAccess(data)
            .then(res => {
                if (res.status == true) {
                    console.log(res.message);
                    setTimeout(() => {
                        window.location.reload();
                    }, 3000);
                } else {
                    console.log(res.message);
                }
            })
            .catch(error => {
                ErrorHandler.showNotification(error);
            });
    }
    const handleChangeNewsLetterAccess = (e: any) => {
        const checked = e.target.checked;
        let news_letter_value = '';
        if (checked) {
            news_letter_value = '1';
        }
        if (!checked) {
            news_letter_value = '0';
        }
        const data = {
            user_id: user?.id,
            news_letter: news_letter_value
        }
        updateNewsLetterAccess(data)
            .then(res => {
                if (res.status == true) {
                    console.log(res.message);
                    setTimeout(() => {
                        window.location.reload();
                    }, 3000);
                } else {
                    console.log(res.message);
                }
            })
            .catch(error => {
                ErrorHandler.showNotification(error);
            });
    }
    const handleChangeRecommendationsAccess = (e: any) => {
        const checked = e.target.checked;
        let recommendations_value = '';
        if (checked) {
            recommendations_value = '1';
        }
        if (!checked) {
            recommendations_value = '0';
        }
        const data = {
            user_id: user?.id,
            recommendations: recommendations_value
        }
        updateRecommendationsAccess(data)
            .then(res => {
                if (res.status == true) {
                    console.log(res.message);
                    setTimeout(() => {
                        window.location.reload();
                    }, 3000);
                } else {
                    console.log(res.message);
                }
            })
            .catch(error => {
                ErrorHandler.showNotification(error);
            });
    }
    const handleChangeAnnouncementsAccess = (e: any) => {
        const checked = e.target.checked;
        let announcements_value = '';
        if (checked) {
            announcements_value = '1';
        }
        if (!checked) {
            announcements_value = '0';
        }
        const data = {
            user_id: user?.id,
            announcements: announcements_value
        }
        updateAnnouncementsAccess(data)
            .then(res => {
                if (res.status == true) {
                    console.log(res.message);
                    setTimeout(() => {
                        window.location.reload();
                    }, 3000);
                } else {
                    console.log(res.message);
                }
            })
            .catch(error => {
                ErrorHandler.showNotification(error);
            });
    }
    const handleChangeMessageFromRecruitersAccess = (e: any) => {
        const checked = e.target.checked;
        let message_from_recruiters_val_value = '';
        if (checked) {
            message_from_recruiters_val_value = '1';
        }
        if (!checked) {
            message_from_recruiters_val_value = '0';
        }
        const data = {
            user_id: user?.id,
            message_from_recruiters: message_from_recruiters_val_value
        }
        updateMessageFromRecruitersAccess(data)
            .then(res => {
                if (res.status == true) {
                    console.log(res.message);
                    setTimeout(() => {
                        window.location.reload();
                    }, 3000);
                } else {
                    console.log(res.message);
                }
            })
            .catch(error => {
                ErrorHandler.showNotification(error);
            });
    }

    return (
        <>
            <div className="dash-right">
                <h1>Settings </h1>
                <div className='row '>
                    <div className='col-sm-12'>
                        <ul className='list-loc m-m-0 mt-4'>
                            <li><Link href="/employees/settings">Account</Link></li>
                            {session
                                ?
                                    ''
                                :
                                    <li><Link href="/employees/settings/changepassword">Password </Link></li>
                            }
                            <li className='active'><Link href="/employees/settings/notifications">Notification </Link></li>
                        </ul>
                    </div>
                </div>

                <div className='data-management m-p-10'>
                    <div className='work-experience-fieild m-p-10'>
                        <div className='row'>
                            <div className='col-sm-12'>
                                <form className='form-experience-fieild'>
                                    <div className='row'>
                                        <div className='col-sm-10 col-8'>
                                            <p className='f-22 c-2C2C2C w-500 mb-1'>Account Settings:</p>
                                            <p className='f-16 c-747474'>Receive account-related notifications and updates via email.</p>
                                        </div>
                                        <div className='col-sm-2 col-4 text-right'>
                                            <label className="switch btn-swith">
                                                <input type="checkbox" onChange={(e: any) => handleChangeAccountAccess(e)} checked={accountAccess == '1'} />
                                                <span className="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div className='row mt-4'>
                                        <div className='col-sm-10 col-8'>
                                            <p className='f-22 c-2C2C2C w-500 mb-1'>Newsletter Preferences:</p>
                                            <p className='f-16 c-747474'>Subscribe to our newsletter to receive the latest updates, news, and promotions.</p>
                                        </div>
                                        <div className='col-sm-2 col-4 text-right'>
                                            <label className="switch btn-swith">
                                                <input type="checkbox" onChange={(e: any) => handleChangeNewsLetterAccess(e)} checked={newsletterAccess == '1'} />
                                                <span className="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div className='row mt-4'>
                                        <div className='col-sm-10 col-8'>
                                            <p className='f-22 c-2C2C2C w-500 mb-1'>Recommendations:</p>
                                            <p className='f-16 c-747474'>Allow us to provide you with personalized recommendations based on your usage.</p>
                                        </div>
                                        <div className='col-sm-2 col-4 text-right'>
                                            <label className="switch btn-swith">
                                                <input type="checkbox" onChange={(e: any) => handleChangeRecommendationsAccess(e)} checked={recommendationsAccess == '1'} />
                                                <span className="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div className='row mt-4'>
                                        <div className='col-sm-10 col-8'>
                                            <p className='f-22 c-2C2C2C w-500 mb-1'>Announcements:</p>
                                            <p className='f-16 c-747474'>Stay informed about important announcements, new features, and improvements.</p>
                                        </div>
                                        <div className='col-sm-2 col-4 text-right'>
                                            <label className="switch btn-swith">
                                                <input type="checkbox" onChange={(e: any) => handleChangeAnnouncementsAccess(e)} checked={announcementsAccess == '1'} />
                                                <span className="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div className='row mt-3'>
                                        <div className='col-sm-10 col-8'>
                                            <p className='f-22 c-2C2C2C w-500 mb-1'>Message from Candidates:</p>
                                            <p className='f-16 c-747474'>Opt-in to receive messages from recruiters about potential job opportunities.</p>
                                        </div>
                                        <div className='col-sm-2 col-4 text-right'>
                                            <label className="switch btn-swith">
                                                <input type="checkbox" onChange={(e: any) => handleChangeMessageFromRecruitersAccess(e)} checked={messageFromRecruitersAccess == '1'} />
                                                <span className="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    )
}
