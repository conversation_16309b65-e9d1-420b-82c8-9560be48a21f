import React, {useState, useEffect, useContext} from 'react';
import {getApplications} from '../../../lib/frontendapi';
import moment from 'moment';
import Link from 'next/link';
import Image from 'next/image';
import AuthContext from '@/Context/AuthContext';

interface Application {
  id: number;
  company_name: string;
  type_of_position: string;
  job_title: string;
  apply_status: string;
  country_name: string;
  created_at: string;
}

export default function Applications() {
  const {user} = useContext(AuthContext);
  const [applications, setApplications] = useState<Application[]>([]);
  const [selectedFilter, setSelectedFilter] = useState('');
  const [selectedFilter1, setSelectedFilter1] = useState('');
  const handleFilterChange = (event: any) => {
    setSelectedFilter(event.target.value);
  };
  const handleFilterChange1 = (event: any) => {
    setSelectedFilter1(event.target.value);
  };
  useEffect(() => {
    getApplications(user?.id).then(res => {
      setApplications(res.data.data);
    });
  }, [user]);

  const filteredApplications = applications.filter(application => {
    if (selectedFilter && application.apply_status !== selectedFilter) {
      return false;
    }

    const today = moment().format('YYYY-MM-DD');
    const applicationDate = moment(application.created_at).format('YYYY-MM-DD');

    if (selectedFilter1 === 'today') {
      if (applicationDate == today) {
        return application.created_at === today;
      }
    } else {
      return true;
    }
    return true;
  });

  return (
    <>
      <div className="dash-right">
        <h1 className="data">
          My <span className="span-color">Applications</span>
        </h1>
        <div className="row m-column-reverse mt-3 mb-3">
          <div className="col-sm-3 pr-0 ">
            <div className="sort-d-flex mt-4">
              <p className="sort-by">Sort By:</p>
              <select className="all-recent w-75" onChange={handleFilterChange1}>
                <option value="today">Recent</option>
                <option value="Oldest">Oldest</option>
              </select>
            </div>
          </div>
          <div className="col-sm-3 pl-0 m-pl-12">
            <div className="sort-d-flex mt-4">
              <p className="sort-by">Filter By: </p>
              <select className="all-recent w-75" onChange={handleFilterChange}>
                <option value="">All</option>
                <option value="pending">Pending</option>
                <option value="selected">Shortlisted</option>
                <option value="declined">Declined</option>
              </select>
            </div>
          </div>
          <div className="col-sm-6"></div>
        </div>
        <div className="bg-fff p-4">
          <div className="table-part mt-4">
            {filteredApplications.length === 0 ? (
              <div className="m-p-10 text-center " style={{color: '#fff'}}>
                <img
                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blank-1.jpg'}
                  alt="blank-2"
                  className="find-img"
                  width={447}
                  height={236}
                  // layout='responsive'
                />
                <p className="f-22 c-BABABA mb-2  mt-2">You don't seem to have any active applications.</p>
                <p className="f-18">
                  Go to{' '}
                  <Link href="/employees/jobs" className="c-0070F5">
                    {' '}
                    Jobs
                  </Link>
                </p>
              </div>
            ) : (
              <table className="rwd-table">
                <tbody>
                  <tr>
                    <th>COMPANY</th>
                    <th>POSITION </th>
                    <th>APPLIED ON </th>
                    <th>STATUS </th>
                  </tr>
                  {filteredApplications.map((application: any, index: any) => {
                    return (
                      <tr key={index}>
                        <td data-th="COMPANY">
                          <a target="_blank" href={'/companies/' + application.company_slug}>
                            <p className="c-n">{application.company_name}</p>
                          </a>
                        </td>
                        <td data-th="POSITION">
                          <a target="_blank" href={'/job/' + application.job_slug}>
                            <p className="f-18 w-600">{application.job_title}</p>
                          </a>
                        </td>
                        <td data-th="APPLIED ON">
                          <p className="f-18 c-4D4D4D w-400">{moment(application.created_at).format('LL')}</p>
                        </td>
                        <td data-th="STATUS">
                          {application.apply_status === 'pending' && (
                            <button className="btn-app bg-D57B11-app">Pending</button>
                          )}
                          {application.apply_status === 'under_review' && (
                            <button className="btn-app bg-0055BA-app">Under Review</button>
                          )}
                          {application.apply_status === 'reviewed' && (
                            <button className="btn-app bg-0055BA-app">Reviewed</button>
                          )}
                          {application.apply_status === 'selected' && (
                            <button className="btn-app bg-3D9F79-app">Selected</button>
                          )}
                          {application.apply_status === 'declined' && (
                            <button className="btn-app bg-D04E4F-app">Rejected</button>
                          )}
                          {application.apply_status === 'closed' && (
                            <button className="btn-app bg-bababa-app">Closed</button>
                          )}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
