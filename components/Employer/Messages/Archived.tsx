import React, { useState, useEffect, useContext } from 'react';
import Link from 'next/link';
import { getAllEmployerReceiverArchivedMessages, getAllEmployerReceiverMessages, getAllInterviews } from '../../../lib/frontendapi';
import 'react-toastify/dist/ReactToastify.css';
import MobileViewArchivedMessagesListing from '@/components/Common/MobileViewArchivedMessagesListing';
import moment from 'moment';
import AuthContext from '@/Context/AuthContext';
import Image from 'next/image';

export default function Archived() {
    const { user } = useContext(AuthContext);
    const [inboxMessagesData, setInboxMessagesData]: any = useState([]);
    const [archivedMessagesData, setArchivedMessagesData]: any = useState([]);
    const [interviews, setInterviews] = useState([]);
    const [showMobileView, setShowMobileView] = useState(false);
    useEffect(() => {
        getAllEmployerReceiverArchivedMessages(user?.id)
            .then(res => {
                if (res.status == true) {
                    setArchivedMessagesData(res.data);
                } else {
                    setArchivedMessagesData([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
        const datas = {
            user_id: user?.id
        }
        getAllInterviews(datas)
            .then(res => {
                if (res.status == true) {
                    setInterviews(res.data);
                } else {
                    setInterviews([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
        getAllEmployerReceiverMessages(user?.id)
            .then(res => {
                if (res.status == true) {
                    setInboxMessagesData(res.data);
                } else {
                    setInboxMessagesData([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
        const data = {
            user_id: user?.id
        }
        let details = navigator.userAgent;
        let regexp = /android|iphone|kindle|ipad/i;
        let isMobileDevice = regexp.test(details);
        if (isMobileDevice) {
            //console.log("You are using a Mobile Device");
            setShowMobileView(true);
        } else {
            //console.log("You are using Desktop");
            setShowMobileView(false);
        }
    }, [user]);
    return (
        <>
            <div className="dash-right">
                {showMobileView == true
                    ?
                    <MobileViewArchivedMessagesListing />
                    :
                    <>
                        <h1>Messages</h1>
                        <div className='row '>
                            <div className='col-sm-9'>
                                <ul className='list-loc m-m-0 mt-4'>
                                    <li>
                                        <Link href="/employer/messages">Inbox <span className="tab-span-sa ">{inboxMessagesData.length}</span></Link>
                                    </li>
                                    <li>
                                        <Link href="/employer/messages/interviews">
                                            Interviews <span className="tab-span-sa">{interviews.length}</span>
                                        </Link>
                                    </li>

                                    <li className='active'>
                                        <Link href="/employer/messages/archived">
                                            Archived <span className="tab-span-sa c-0070F5">{archivedMessagesData.length}</span>
                                        </Link>
                                    </li>
                                </ul>
                            </div>
                            <div className="col-sm-3">
                                {/* <ul className='blue-text-line mt-4 text-right'>
                                        <li><a href="/employer/messages/emptyarchived">Empty Archived</a></li>
                                    </ul> */}
                            </div>
                        </div>
                        <div className='data-management m-p-10 p-0'>
                            <div className='work-experience-fieild m-p-10'>
                                <div className='row'>
                                    <div className='col-sm-6'>
                                        <p className='f-22 m-center'>Archived</p>
                                    </div>
                                    <div className='col-sm-6 text-right'>
                                        <p className='f-16 c-BABABA m-center'>You have {archivedMessagesData.length} messages.</p>
                                    </div>
                                </div>
                                {archivedMessagesData.length > 0
                                    ?
                                    archivedMessagesData.map((archived_messages_data: any, index: any) => {
                                        let file_url = '';
                                        if (archived_messages_data.attachment_path) {
                                            file_url = process.env.NEXT_PUBLIC_IMAGE_URL + 'images/messageAttachmentFile/' + archived_messages_data.attachment_path;
                                        }
                                        const filename = file_url.substring(file_url.lastIndexOf('/') + 1);
                                        const extension = filename.split('.').pop();
                                        return (
                                            <Link href={"/employer/messages/archived/" + archived_messages_data.sender_id} key={index}>
                                                <div className='box-text-img bg-CFE5FF  mb-2'>
                                                    <div className='row'>
                                                        <div className='col-sm-1 text-center'>
                                                            {archived_messages_data.candidate_profile_image
                                                                ?
                                                                <img src={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/userprofileImg/' + archived_messages_data.candidate_profile_image} alt="Avatars-1" className="w-40" />
                                                                :
                                                                <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/Avatars-1.png'} alt="Avatars-1" className="w-40" />
                                                            }
                                                        </div>
                                                        <div className='col-sm-9'>
                                                            <p className='f-16 c-4D4D4D w-700 '>{archived_messages_data.candidate_name}.{archived_messages_data.company_name}</p>
                                                            <p className='f-16 w-600'>
                                                                {archived_messages_data.message_description ? (
                                                                    archived_messages_data.message_description
                                                                ) : archived_messages_data.attachment_path ? (
                                                                    extension == 'pdf' || extension == 'xlxs' || extension == 'xlx' || extension == 'docx' || extension == 'doc' ? (
                                                                        <a href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${archived_messages_data.attachment_path}`} target='_blank' style={{ "fontSize": "14px" }}><i className="fa-solid fa-file" style={{ "fontSize": "16px" }}></i> {archived_messages_data.attachment_path}</a>
                                                                    ) :
                                                                        <a href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${archived_messages_data.attachment_path}`} target='_blank' style={{ "fontSize": "14px" }}><i className="fa-solid fa-image" style={{ "fontSize": "16px" }}></i> {archived_messages_data.attachment_path}</a>
                                                                ) : (
                                                                    ''
                                                                )
                                                                }
                                                            </p>
                                                            {/* <p className='f-16 w-600'>{archived_messages_data.message_description}</p> */}
                                                        </div>
                                                        <div className='col-sm-2 text-right'>
                                                            <p className='f-16 c-4D4D4D'>{moment.utc(archived_messages_data.created_at).local().startOf('seconds').fromNow()}</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </Link>
                                        )
                                    })
                                    :
                                    <div className='work-experience-fieild m-p-10 text-center'>
                                        <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blank-3.png'} alt="blank-3" className='' />
                                        <p className='f-22 c-BABABA mb-1'>You don't seem to have any messages</p>
                                        <p className='f-18 c-BABABA'>Go to  <a href="#" className='c-0070F5' > Jobs</a></p>
                                    </div>
                                }
                            </div>
                        </div>
                    </>
                }
            </div>
        </>
    )
}
