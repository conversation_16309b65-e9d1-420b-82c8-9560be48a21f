import React, { useState, useEffect, useRef, useContext } from 'react';
import { getCurrentUserData } from '../../../lib/session';
import {
  getAllEmployerSingleUserMessages,
  getAllReceiverUserMessages,
  sendMessage,
  updateArchivedMessages,
  getAllEmployerReceiverMessages,
  getAllInterviews,
  getAllEmployerReceiverArchivedMessages,
} from '../../../lib/frontendapi';
import Link from 'next/link';
import 'react-toastify/dist/ReactToastify.css';
import moment from 'moment';
import AuthContext from '@/Context/AuthContext';
import <PERSON>rrorHandler from '@/lib/ErrorHandler';
import Image from 'next/image';

export default function InboxNamePosition({ userId }: any) {
  const chatWindowRef: any = useRef(null);
  const [messageDesc, setMessageDesc] = useState('');
  const [messagesData, setMessagesData]: any = useState([]);
  const [allInboxMessagesData, setAllInboxMessagesData]: any = useState([]);
  const [chatMessagesData, setChatMessagesData]: any = useState([]);
  const [archivedMessagesData, setArchivedMessagesData]: any = useState([]);
  const [interviews, setInterviews] = useState([]);
  const [attachmentFile, setAttachmentFile] = useState('');
  const [senderId, setSenderId]: any = useState();
  const { user } = useContext(AuthContext);

  useEffect(() => {
    getAllEmployerSingleUserMessages(userId)
      .then(res => {
        if (res.status == true) {
          setMessagesData(res.data);
        } else {
          setMessagesData([]);
        }
      })
      .catch(err => {
        console.log(err);
      });

    console.log(messagesData);
    setSenderId(user?.id)
    const intervalId = setInterval(() => {
      if (user?.id) {
        setSenderId(user?.id);
        refreshChatMessages(user?.id);
      }
    }, 3000);

    return () => clearInterval(intervalId);

  }, [userId, user]);

  const refreshChatMessages = (senderId: string | number) => {
    const messages_chat_section = document.getElementById('messages_chat_section');
    let shouldScroll = 0;
    if (messages_chat_section) {
      let shouldScroll =
        messages_chat_section.scrollTop + messages_chat_section.clientHeight === messages_chat_section.scrollHeight;
    }
    if (!shouldScroll) {
      if (messages_chat_section) {
        messages_chat_section.scrollTop = messages_chat_section.scrollHeight;
      }
    }

    const data = {
      sender_id: senderId,
      receiver_id: userId,
    }

    getAllReceiverUserMessages(data)
      .then(res => {
        if (res.status == true) {
          setAllInboxMessagesData(res.data);
        } else {
          setChatMessagesData([]);
        }
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });
  };
  const onEnterPress = (e: any) => {
    if (e.which === 13 && e.shiftKey == false) {
      e.preventDefault();
      submitMessageForm(e);
    }
  };
  const submitMessageForm = (e: any) => {
    e.preventDefault();
    const messages_chat_section = document.getElementById('messages_chat_section');
    let shouldScroll = 0;
    if (messages_chat_section) {
      let shouldScroll =
        messages_chat_section.scrollTop + messages_chat_section.clientHeight === messages_chat_section.scrollHeight;
    }
    if (!shouldScroll) {
      if (messages_chat_section) {
        messages_chat_section.scrollTop = messages_chat_section.scrollHeight;
      }
    }
    $('.message_send_filed').val('');
    setMessageDesc('');
    let applicants_id = $('.hd_applicants_id').val();
    let candidate_id = userId;
    let job_id = $('.hd_job_id').val();
    let current_user_id = user?.id;
    const datas = {
      applicants_id: applicants_id,
      candidate_id: candidate_id,
      job_id: job_id,
      current_user_id: current_user_id,
      message_title: null,
      message_description: messageDesc,
      message_type: 'applyJob',
      message_status: 'unread',
    };

    sendMessage(datas, attachmentFile[0])
      .then(res => {
        if (res.status == true) {
        } else {
          if (res.errors) {
          }
        }
      })
      .catch(err => { });
  };
  const handleClickArchived = () => {
    const data = {
      sender_id: user?.id,
      receiver_id: userId,
    };
    updateArchivedMessages(data)
      .then(res => {
        if (res.status == true) {
          getAllEmployerReceiverMessages(user?.id)
            .then(res => {
              if (res.status == true) {
                setAllInboxMessagesData(res.data);
              } else {
                setAllInboxMessagesData([]);
              }
            })
            .catch(err => {
              console.log(err);
            });
          const data = {
            user_id: user?.id,
          };
          getAllInterviews(data)
            .then(res => {
              if (res.status == true) {
                setInterviews(res.data);
              } else {
                setInterviews([]);
              }
            })
            .catch(err => {
              console.log(err);
            });
          getAllEmployerReceiverArchivedMessages(user?.id)
            .then(res => {
              if (res.status == true) {
                setArchivedMessagesData(res.data);
              } else {
                setArchivedMessagesData([]);
              }
            })
            .catch(err => {
              console.log(err);
            });
        } else {
        }
      })
      .catch(err => { });
  };
  const handleImageChange = (event: any) => {
    if (event.target.files && event.target.files[0]) {
      // if (event.target.files[0].type != "image/jpeg" && event.target.files[0].type != "image/png" && event.target.files[0].type != "application/pdf" && event.target.files[0].type != "application/vnd.openxmlformats-officedocument.wordprocessingml.document") {
      //     setPreviewCompanyLogo(`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.png`);
      // } else {
      //     setPreviewCompanyLogo(URL.createObjectURL(event.target.files[0]));
      // }
      setAttachmentFile(event.target.files);
    }
  };
  return (
    <>
      <div className="dash-right">
        <h1>
          My <span className="span-color"> Messages</span>
        </h1>
        <div className="row ">
          <div className="col-sm-12">
            <ul className="list-loc m-m-0 mt-4">
              <li className="active">
                <Link href="/employer/messages">
                  Inbox <span className="tab-span-sa c-0070F5">{allInboxMessagesData?.length}</span>
                </Link>
              </li>
              <li>
                <Link href="/employer/messages/interviews">
                  Interviews <span className="tab-span-sa">{interviews.length}</span>
                </Link>
              </li>
              <li>
                <Link href="/employer/messages/archived">
                  Archived <span className="tab-span-sa">{allInboxMessagesData?.length}</span>
                </Link>
              </li>
            </ul>
          </div>
        </div>
        <div className="work-experience-fieild m-p-10 mt-2 p-0">
          <div className="head-message">
            <div className="row">
              <div className="col-sm-4 m-center">
                <Link href="/employer/messages">
                  <i className="fa-solid fa-arrow-left arrow-icon f-18 c-4D4D4D"></i>
                </Link>
              </div>
              <div className="col-sm-4 text-center">
                {messagesData?.receiver_user_role == 'admin' ? (
                  <p className="f-22 c-191919 w-700 mb-2 ">The Talent Point</p>
                ) : (
                  <>
                    <p className="f-22 c-191919 w-700 mb-2 ">{messagesData?.job_title}</p>
                    <p className="f-18 c-0070F5 w-600">{messagesData?.company_name}</p>
                  </>
                )}
              </div>
              <div className="col-sm-4 text-right  m-center">
                <ul className="list-message">
                  <li>
                    <a href="#">
                      {' '}
                      <i className="fa-regular fa-flag"></i> Report
                    </a>
                  </li>
                  <li>
                    <a href="#" onClick={handleClickArchived}>
                      <i className="fa-solid fa-file-arrow-down"></i> Archive
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div className="body-message">
            <div className="messages_chat_section" id="messages_chat_section" ref={chatWindowRef}>
              {allInboxMessagesData?.length > 0 ? (
                allInboxMessagesData?.map((chat_message_data: any, index: any) => {
                  let file_url = '';
                  if (chat_message_data.attachment_path) {
                    file_url =
                      process.env.NEXT_PUBLIC_IMAGE_URL +
                      'images/messageAttachmentFile/' +
                      chat_message_data.attachment_path;
                  }
                  const filename = file_url.substring(file_url.lastIndexOf('/') + 1);
                  const extension = filename.split('.').pop();
                  return (
                    <>
                      <div
                        key={index}
                        className={`single_message_section ${chat_message_data.sender_id == user?.id ? 'sender' : 'receiver'
                          }`}>
                        {chat_message_data.sender_id == user?.id ? (
                          <>
                            <div className="row mt-3">
                              <div className="col-sm-6 text-left mt-2">
                                <p className="f-16  open-sans ">
                                  {moment(chat_message_data.created_at).format('MMMM D, hh:mmA ')}
                                </p>
                              </div>
                              <div className="col-sm-6">
                                <p className="f-16  open-sans w-600">
                                  {' '}
                                  You &nbsp;
                                  {user?.profile_image ? (
                                    <img src={user?.profile_image?.source} alt="Avatars-1" className="w-40" />
                                  ) : (
                                    <img
                                      src={'/images/Avatars-1.png'}
                                      alt="Avatars-1"
                                      className="w-40"
                                    />
                                  )}
                                </p>
                              </div>
                            </div>
                            <div className="mt-4 mb-4">
                              <p className="f-16 c-4D4D4D" style={{ whiteSpace: 'pre-wrap' }}>
                                {/* {chat_message_data.message_description ? chat_message_data.message_description : <img src={process.env.NEXT_PUBLIC_IMAGE_URL+'images/messageAttachmentFile/'+chat_message_data.attachment_path} alt="attachment" style={{"width": "200px","height": "150px","borderRadius": "10px"}}/>} */}
                                {chat_message_data.message_description ? (
                                  chat_message_data.message_description
                                ) : chat_message_data.attachment_path ? (
                                  extension == 'pdf' ||
                                    extension == 'xlxs' ||
                                    extension == 'xlx' ||
                                    extension == 'docx' ||
                                    extension == 'doc' ? (
                                    <a
                                      href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${chat_message_data.attachment_path}`}
                                      target="_blank">
                                      <i className="fa-solid fa-file" style={{ fontSize: '85px' }}></i>
                                    </a>
                                  ) : (
                                    <a
                                      href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${chat_message_data.attachment_path}`}
                                      target="_blank">
                                      <img
                                        src={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${chat_message_data.attachment_path}`}
                                        alt="attachment"
                                        style={{ width: '200px', height: '150px', borderRadius: '10px' }}
                                      />
                                    </a>
                                  )
                                ) : (
                                  ''
                                )}
                              </p>
                            </div>
                          </>
                        ) : (
                          <>
                            <div className="row mt-3">
                              <div className="col-sm-6">
                                <p className="f-16  open-sans w-600">
                                  {chat_message_data.profile ? (
                                    <img src={chat_message_data.profile?.source} alt="Avatars-1" className="w-40" />
                                  ) : (
                                    <img
                                      src={'/images/Avatars-1.png'}
                                      alt="Avatars-1"
                                      className="w-40"
                                    />
                                  )}
                                  &nbsp; {chat_message_data.sender_name}.{chat_message_data.sender_current_postion}
                                </p>
                              </div>
                              <div className="col-sm-6 text-right mt-2">
                                <p className="f-16  open-sans ">
                                  {moment(chat_message_data.created_at).format('MMMM D, hh:mmA ')}
                                </p>
                              </div>
                            </div>
                            <div className="mt-4 mb-4">
                              {/* <p className='f-16 c-4D4D4D'>{chat_message_data.message_description}</p> */}
                              <p className="f-16 c-4D4D4D" style={{ whiteSpace: 'pre-wrap' }}>
                                {chat_message_data.message_description ? (
                                  chat_message_data.message_description
                                ) : chat_message_data.attachment_path ? (
                                  extension == 'pdf' ||
                                    extension == 'xlxs' ||
                                    extension == 'xlx' ||
                                    extension == 'docx' ||
                                    extension == 'doc' ? (
                                    <a
                                      href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${chat_message_data.attachment_path}`}
                                      target="_blank">
                                      <i className="fa-solid fa-file" style={{ fontSize: '85px' }}></i>
                                    </a>
                                  ) : (
                                    <a
                                      href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${chat_message_data.attachment_path}`}
                                      target="_blank">
                                      <img
                                        src={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${chat_message_data.attachment_path}`}
                                        alt="attachment"
                                        style={{ width: '200px', height: '150px', borderRadius: '10px' }}
                                      />
                                    </a>
                                  )
                                ) : (
                                  ''
                                )}
                              </p>
                            </div>
                          </>
                        )}
                      </div>
                    </>
                  );
                })
              ) : (
                <div className="mt-4 mb-4">
                  <p className="f-16 c-4D4D4D text-center">No messages to display.</p>
                </div>
              )}
            </div>
            <div className="send-fild">
              <form className="form-experience-fieild" onSubmit={submitMessageForm}>
                <div className="row m-center">
                  <input type="hidden" name="hd_job_id" className="hd_job_id" value={messagesData?.job_id} />
                  <input
                    type="hidden"
                    name="hd_applicants_id"
                    className="hd_applicants_id"
                    value={messagesData?.applied_id}
                  />
                  <div className="col-sm-1  col-12 ">
                    <div className="btn-a border-primary-size-16 border-0055BA p-3 mobile-w-100 text-center uploade-btn attachment_file">
                      <i
                        className="fa-solid fa-paperclip"
                        style={{ color: '#0055BA', fontSize: '16px', cursor: 'pointer' }}></i>
                      <input
                        type="file"
                        onChange={handleImageChange}
                        accept=".jpg, .png, .pdf, .docs, .doc"
                        className="attach_ment_file"
                        style={{ width: '60px', left: '0px', top: '3px' }}
                      />
                    </div>
                    {/* <button className="btn-a border-primary-size-16 border-0055BA p-3 mobile-w-100"><i className="fa-solid fa-paperclip"></i></button> */}
                  </div>
                  <div className="col-sm-9  col-12 ">
                    {/* <textarea placeholder='Your message goes here...' className='message-send mobile-w-100 mobile-add-sp message_send_filed' {...register('message_desc', { required: true})} onChange={(e:any) => setMessageDesc(e.target.value)}></textarea>
                                        {errors.message_desc && errors.message_desc.type === 'required' && <p className="text-danger" style={{"textAlign": "left"}}>Message Field is required.</p>}*/}
                    <textarea
                      placeholder="Your message goes here..."
                      className="message-send mobile-w-100 mobile-add-sp message_send_filed"
                      onKeyDown={onEnterPress}
                      onChange={(e: any) => setMessageDesc(e.target.value)}></textarea>
                  </div>
                  <div className="col-sm-2 col-12 text-right">
                    <button className="btn-a primary-size-16 btn-bg-0055BA mobile-w-100" type="submit">
                      Send
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
