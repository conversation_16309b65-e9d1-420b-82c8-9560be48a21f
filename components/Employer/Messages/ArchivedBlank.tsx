import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

export default function ArchivedBlank() {
    return (
        <>
            <div className="dash-right">
                <h1>Messages</h1>
                <div className='row '>
                    <div className='col-sm-12'>
                        <ul className='list-loc m-m-0 mt-4'>
                            <li className='active'>
                                <Link href="/employees/messages">Inbox <span className="tab-span-sa c-0070F5">12</span></Link>
                            </li>
                            <li>
                                <Link href="/employees/messages/interviews">Interviews <span className="tab-span-sa">12</span></Link>
                            </li>
                            <li>
                                <Link href="/employees/messages/archived">Archived <span className="tab-span-sa">12</span></Link>
                            </li>
                        </ul>
                    </div>
                </div>

                <div className='data-management m-p-10 p-0'>
                    <div className='work-experience-fieild m-p-10'>
                        <div className='row'>
                            <div className='col-sm-12'>
                                <p className='f-22 m-center'>Archived Messages </p>
                            </div>
                        </div>

                        <div className='text-center'>
                            <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blank-3.png'} alt="blank-3" />
                            <p className='f-22 c-BABABA mb-2'>You don't seem to have any archived messages</p>
                            <p className='f-18 c-BABABA'>Go to <a href="#" className='c-0055BA'>Jobs</a></p>
                        </div>

                    </div>

                </div>
            </div>
        </>
    )
}
