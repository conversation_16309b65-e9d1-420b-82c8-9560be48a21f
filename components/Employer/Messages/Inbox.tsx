import React, { useState, useEffect, useContext } from 'react';
import {
  getAllEmployerReceiverMessages,
  getAllInterviews,
  getAllEmployerReceiverArchivedMessages,
  updateMessagesReadUnReadStatus,
} from '../../../lib/frontendapi';
import Link from 'next/link';
import { useRouter } from 'next/router';
import moment from 'moment';
import MobileViewInboxMessagesListing from '@/components/Common/MobileViewInboxMessagesListing';
import AuthContext from '@/Context/AuthContext';
import ErrorHandler from '@/lib/ErrorHandler';
import Image from 'next/image';

export default function Inbox() {
  const { user } = useContext(AuthContext);
  const [chats, setChats]: any = useState([]);
  const [archivedMessagesData, setArchivedMessagesData]: any = useState([]);
  const [interviews, setInterviews] = useState([]);
  const router = useRouter();
  const [showMobileView, setShowMobileView] = useState(false);

  useEffect(() => {
    getAllEmployerReceiverMessages(user?.id)
      .then(res => {
        if (res.status == true) {
          setChats(res.data);
        }
      })
      .catch(err => {
        console.log(err);
      });

    const data = {
      user_id: user?.id,
    };

    getAllInterviews(data)
      .then(res => {
        if (res.status == true) {
          setInterviews(res.data);
        } else {
          setInterviews([]);
        }
      })
      .catch(err => {
        console.log(err);
      });

    getAllEmployerReceiverArchivedMessages(user?.id)
      .then(res => {
        if (res.status == true) {
          setArchivedMessagesData(res.data);
        } else {
          setArchivedMessagesData([]);
        }
      })
      .catch(err => {
        console.log(err);
      });

    let details = navigator.userAgent;
    let regexp = /android|iphone|kindle|ipad/i;
    let isMobileDevice = regexp.test(details);
    if (isMobileDevice) {
      setShowMobileView(true);
    } else {
      setShowMobileView(false);
    }
  }, [user]);
  const handleClickMessageStatusUpdate = (e: any, sender_id: any) => {
    if (sender_id == user?.id) {
      router.push('/employer/messages/inbox/' + sender_id);
    } else {
      const data = {
        sender_id: sender_id,
        receiver_id: user?.id,
      };
      updateMessagesReadUnReadStatus(data)
        .then(res => {
          if (res.status == true) {
            router.push('/employer/messages/inbox/' + sender_id);
          } else {
            router.push('/employer/messages/inbox/' + sender_id);
          }
        })
        .catch(err => {
          ErrorHandler.showNotification(err);
        });
    }
  };
  return (
    <>
      <div className="dash-right">
        {showMobileView == true ? (
          <MobileViewInboxMessagesListing />
        ) : (
          <>
            <h1>Messages</h1>
            <div className="row ">
              <div className="col-sm-9">
                <ul className="list-loc m-m-0 mt-4">
                  <li className="active">
                    <Link href="/employer/messages">
                      Inbox <span className="tab-span-sa c-0070F5">{chats.length}</span>
                    </Link>
                  </li>
                  <li>
                    <Link href="/employer/messages/interviews">
                      Interviews <span className="tab-span-sa">{interviews.length}</span>
                    </Link>
                  </li>
                  <li>
                    <Link href="/employer/messages/archived">
                      Archived <span className="tab-span-sa">{archivedMessagesData.length}</span>
                    </Link>
                  </li>
                </ul>
              </div>
            </div>
            <div className="data-management m-p-10 p-0">
              <div className="work-experience-fieild m-p-10">
                <div className="row">
                  <div className="col-sm-6">
                    <p className="f-22 m-center">Inbox</p>
                  </div>
                  <div className="col-sm-6 text-right">
                    <p className="f-16 c-BABABA m-center">
                      You have {chats.length} {chats.length !== 1 ? 'chats' : 'chat'} and {chats.unreadMessage} unread{' '}
                      {chats.unreadMessage !== 1 ? 'messages' : 'message'}
                    </p>
                  </div>
                </div>
                {chats.length > 0 ? (
                  chats.map((messages_data: any, index: any) => {
                    let file_url = '';
                    if (messages_data.attachment_path) {
                      file_url =
                        process.env.NEXT_PUBLIC_IMAGE_URL +
                        'images/messageAttachmentFile/' +
                        messages_data.attachment_path;
                    }
                    const filename = file_url.substring(file_url.lastIndexOf('/') + 1);
                    const extension = filename.split('.').pop();
                    return (
                      <a
                        href="#"
                        onClick={(e: any) => handleClickMessageStatusUpdate(e, messages_data.sender_id)}
                        key={index}>
                        <div className="box-text-img bg-CFE5FF  mb-2">
                          <div className="row">
                            <div className="col-sm-1 text-center">
                              <img
                                src={
                                  messages_data.profile?.source ||
                                  process.env.NEXT_PUBLIC_BASE_URL + 'images/Avatars-1.png'
                                }
                                alt="Avatars-1"
                                className="w-40"
                              />
                            </div>
                            <div className="col-sm-9">
                              <p className="f-16 c-4D4D4D w-700 ">
                                {messages_data.candidate_name}.{messages_data.candidate_position}
                              </p>
                              <p className="f-16 w-600">
                                {messages_data.message_description ? (
                                  messages_data.message_status === 'unread' ? (
                                    <span className="message2">{messages_data.message_description}</span>
                                  ) : (
                                    <span className="message1">{messages_data.message_description}</span>
                                  )
                                ) : messages_data.attachment_path ? (
                                  extension == 'pdf' ||
                                    extension == 'xlxs' ||
                                    extension == 'xlx' ||
                                    extension == 'docx' ||
                                    extension == 'doc' ? (
                                    <a
                                      href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${messages_data.attachment_path}`}
                                      target="_blank"
                                      style={{ fontSize: '14px' }}>
                                      <i className="fa-solid fa-file" style={{ fontSize: '16px' }}></i>{' '}
                                      {messages_data.attachment_path}
                                    </a>
                                  ) : (
                                    <a
                                      href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${messages_data.attachment_path}`}
                                      target="_blank"
                                      style={{ fontSize: '14px' }}>
                                      <i className="fa-solid fa-image" style={{ fontSize: '16px' }}></i>{' '}
                                      {messages_data.attachment_path}
                                    </a>
                                  )
                                ) : (
                                  ''
                                )}
                              </p>
                            </div>
                            <div className="col-sm-2 text-right">
                              <p className="f-16 c-4D4D4D">
                                {moment.utc(messages_data.created_at).local().startOf('seconds').fromNow()}
                              </p>
                              <p>{messages_data.unreadMessage > 0 && messages_data.unreadMessage + ' new message'}</p>{' '}
                            </div>
                          </div>
                        </div>
                      </a>
                    );
                  })
                ) : (
                  <div className="work-experience-fieild m-p-10 text-center">
                    <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blank-3.png'} alt="blank-3" className="" />
                    <p className="f-22 c-BABABA mb-1">You don't seem to have any messages</p>
                    <p className="f-18 c-BABABA">
                      Go to <a href="#" className="c-0070F5"> Jobs </a>
                    </p>
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </>
  );
}
