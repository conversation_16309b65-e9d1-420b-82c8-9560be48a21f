import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
export default function SoftwareEngineer() {
    return (
        <>
            <div className='dash-right'>
                <h1>Applicants</h1>

                <div className='row m-column-reverse'>
                    <div className='col-sm-9'>
                        <ul className='list-loc m-m-0 mt-4'>
                            <li>
                                <Link href="/employer/applicants">All Applicants</Link>
                            </li>
                            <li className='active'><a href="#">Software Engineer</a></li>
                            <li><a href="#">Another Job Post Name</a></li>
                            <li><a href="#">Short job Post</a></li>
                        </ul>
                    </div>
                    <div className='col-sm-3 text-right mb-2'>
                        <div className="dropdown">
                            <button className="dropdown-toggle btn-a primary-size-16 btn-bg-0055BA" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                <i className="fa-solid fa-circle-chevron-down sp-right"></i> Hiring Status
                            </button>
                            <ul className="dropdown-menu view-right" aria-labelledby="dropdownMenuButton1">
                                <li>
                                    <select className='hiring'>
                                        <option>Hiring Status </option>
                                        <option>Yes</option>
                                        <option>Maybe</option>
                                        <option>No</option>
                                    </select>
                                </li>
                                <li><a className="dropdown-item item-2" href="#">Create a saved list</a></li>
                                <li><a className="dropdown-item item-3" href="#">Remove</a></li>
                            </ul>
                        </div>
                    </div>
                </div>



                <div className='candidates-part'>
                    <div className='row m-column-reverse'>
                        <div className='col-sm-8'>
                            <ul className='list-loc m-m-0 mt-4'>
                                <li className='active'><a href="#">Saved Candidate List Name <i className="fa-solid fa-pencil"></i></a></li>
                                <li className='active'><a href="#">Custom List <i className="fa-solid fa-pencil"></i></a></li>
                            </ul>
                        </div>
                        <div className='col-sm-4 text-right mb-2'>
                            <button className="btn-a primary-size-16 btn-bg-0055BA"> <i className="fa-solid fa-bolt"></i>&nbsp;&nbsp; View Matched Candidates</button>
                        </div>
                    </div>
                    <div className='filter'>
                        <div className='filter-sp'>
                            <ul className="skills mt-3">
                                <li><p className="cat">Ready to Interview <i className="fa-solid fa-xmark"></i></p></li>
                                <li><p className="cat">Open to Offers <i className="fa-solid fa-xmark"></i></p></li>
                                <li><p className="cat">AED 0 - AED 8,000 <i className="fa-solid fa-xmark"></i></p></li>
                            </ul>
                            <ul className="skills">
                                <li><p className="cat">Dubai, United Arab Emirates <i className="fa-solid fa-xmark"></i></p></li>
                            </ul>
                        </div>
                        <div className='filter-bottom'>
                            <p><i className="fa-solid fa-angles-down"></i> Filter</p>
                        </div>
                    </div>

                    <div className='filter filter-sp m-center mt-4'>
                        <div className='row'>
                            <div className='col-sm-2 pr-0'>
                                <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/user-p.png'} alt=" user-p" className='logo-filter w-100' />
                            </div>
                            <div className='col-sm-7'>
                                <p className='p-18 blue-text mt-2'>Candidate Name</p>
                                <p className='p-16 black-text'>Current Role @Company-Name</p>
                                <ul className='full-time'>
                                    <li><i className="fa-solid fa-location-dot"></i> Ready to Interview</li>
                                    <li><i className="fa-solid fa-business-time"></i> Dubai - UAE </li>
                                </ul>
                                <div className='sort-d-flex mt-3'>
                                    <p className='Status-by  '>Hiring Status:</p>
                                    <select className='all-Status '>
                                        <option>Shortlisted</option>
                                        <option>Shortlisted 2</option>
                                        <option>Shortlisted 3</option>
                                        <option>Shortlisted 4</option>
                                    </select>
                                </div>
                            </div>
                            <div className='col-sm-3'>
                                <div className="dropdown">
                                    <button className="dropdown-toggle btn-a primary-size-16 btn-bg-0055BA" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i className="fa-solid fa-circle-chevron-down sp-right"></i> Dropdown button
                                    </button>
                                    <ul className="dropdown-menu view-right" aria-labelledby="dropdownMenuButton1">
                                        <li><a className="dropdown-item item-1" href="#">View public profile</a></li>
                                        <li><a className="dropdown-item item-2" href="#">Contact information</a></li>
                                        <li><a className="dropdown-item item-3" href="#">Delete profile</a></li>
                                    </ul>
                                </div>
                                <button className="download mt-4 w-100"><i className="fa-solid fa-download"></i> Download Resume</button>
                            </div>
                        </div>
                        <p className='posted'>Applied through Instant Apply!</p>
                    </div>

                    <div className='filter filter-sp m-center mt-2'>
                        <div className='row'>
                            <div className='col-sm-2 pr-0'>
                                <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/user-p.png'} alt=" user-p" className='logo-filter w-100' />
                            </div>
                            <div className='col-sm-7'>
                                <p className='p-18 blue-text mt-2'>Candidate Name</p>
                                <p className='p-16 black-text'>Current Role @Company-Name</p>
                                <ul className='full-time'>
                                    <li><i className="fa-solid fa-location-dot"></i> Ready to Interview</li>
                                    <li><i className="fa-solid fa-business-time"></i> Dubai - UAE </li>
                                </ul>
                                <div className='sort-d-flex mt-3'>
                                    <p className='Status-by  '>Hiring Status:</p>
                                    <select className='all-Status '>
                                        <option>Shortlisted</option>
                                        <option>Shortlisted 2</option>
                                        <option>Shortlisted 3</option>
                                        <option>Shortlisted 4</option>
                                    </select>
                                </div>
                            </div>
                            <div className='col-sm-3'>
                                <div className="dropdown">
                                    <button className="dropdown-toggle btn-a primary-size-16 btn-bg-0055BA" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i className="fa-solid fa-circle-chevron-down sp-right"></i> Dropdown button
                                    </button>
                                    <ul className="dropdown-menu view-right" aria-labelledby="dropdownMenuButton1">
                                        <li><a className="dropdown-item item-1" href="#">View public profile</a></li>
                                        <li><a className="dropdown-item item-2" href="#">Contact information</a></li>
                                        <li><a className="dropdown-item item-3" href="#">Delete profile</a></li>
                                    </ul>
                                </div>
                                <button className="download mt-4 w-100"><i className="fa-solid fa-download"></i> Download Resume</button>
                            </div>
                        </div>
                        <p className='posted'>Applied through Instant Apply!</p>
                    </div>

                    <div className='filter filter-sp m-center mt-2'>
                        <div className='row'>
                            <div className='col-sm-2 pr-0'>
                                <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/user-p.png'} alt=" user-p" className='logo-filter w-100' />
                            </div>
                            <div className='col-sm-7'>
                                <p className='p-18 blue-text mt-2'>Candidate Name</p>
                                <p className='p-16 black-text'>Current Role @Company-Name</p>
                                <ul className='full-time'>
                                    <li><i className="fa-solid fa-location-dot"></i> Ready to Interview</li>
                                    <li><i className="fa-solid fa-business-time"></i> Dubai - UAE </li>
                                </ul>
                                <div className='sort-d-flex mt-3'>
                                    <p className='Status-by  '>Hiring Status:</p>
                                    <select className='all-Status '>
                                        <option>Shortlisted</option>
                                        <option>Shortlisted 2</option>
                                        <option>Shortlisted 3</option>
                                        <option>Shortlisted 4</option>
                                    </select>
                                </div>
                            </div>
                            <div className='col-sm-3'>
                                <div className="dropdown">
                                    <button className="dropdown-toggle btn-a primary-size-16 btn-bg-0055BA" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i className="fa-solid fa-circle-chevron-down sp-right"></i> Dropdown button
                                    </button>
                                    <ul className="dropdown-menu view-right" aria-labelledby="dropdownMenuButton1">
                                        <li><a className="dropdown-item item-1" href="#">View public profile</a></li>
                                        <li><a className="dropdown-item item-2" href="#">Contact information</a></li>
                                        <li><a className="dropdown-item item-3" href="#">Delete profile</a></li>
                                    </ul>
                                </div>
                                <button className="download mt-4 w-100"><i className="fa-solid fa-download"></i> Download Resume</button>
                            </div>
                        </div>
                        <p className='posted'>Applied through Instant Apply!</p>
                    </div>

                    <div className='filter filter-sp m-center mt-2'>
                        <div className='row'>
                            <div className='col-sm-2 pr-0'>
                                <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/user-p.png'} alt=" user-p" className='logo-filter w-100' />
                            </div>
                            <div className='col-sm-7'>
                                <p className='p-18 blue-text mt-2'>Candidate Name</p>
                                <p className='p-16 black-text'>Current Role @Company-Name</p>
                                <ul className='full-time'>
                                    <li><i className="fa-solid fa-location-dot"></i> Ready to Interview</li>
                                    <li><i className="fa-solid fa-business-time"></i> Dubai - UAE </li>
                                </ul>
                                <div className='sort-d-flex mt-3'>
                                    <p className='Status-by  '>Hiring Status:</p>
                                    <select className='all-Status '>
                                        <option>Shortlisted</option>
                                        <option>Shortlisted 2</option>
                                        <option>Shortlisted 3</option>
                                        <option>Shortlisted 4</option>
                                    </select>
                                </div>
                            </div>
                            <div className='col-sm-3'>
                                <div className="dropdown">
                                    <button className="dropdown-toggle btn-a primary-size-16 btn-bg-0055BA" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i className="fa-solid fa-circle-chevron-down sp-right"></i> Dropdown button
                                    </button>
                                    <ul className="dropdown-menu view-right" aria-labelledby="dropdownMenuButton1">
                                        <li><a className="dropdown-item item-1" href="#">View public profile</a></li>
                                        <li><a className="dropdown-item item-2" href="#">Contact information</a></li>
                                        <li><a className="dropdown-item item-3" href="#">Delete profile</a></li>
                                    </ul>
                                </div>
                                <button className="download mt-4 w-100"><i className="fa-solid fa-download"></i> Download Resume</button>
                            </div>
                        </div>
                        <p className='posted'>Applied through Instant Apply!</p>
                    </div>
                </div>
            </div>
        </>
    )
}
