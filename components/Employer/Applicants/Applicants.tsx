import React, {useState, useEffect, useContext, useRef} from 'react';
import {
  getAllJobApplications,
  updateHiringStatus,
  getSingleJobApplications,
  getWorkExperienceEmployee,
  getEducationsEmployee,
  getSingleUserSkill,
  getportfolio,
  getAllLanguages,
  addInterview,
  sendMessage,
  checkAndUpdateResumesViewed,
  getApplyJobInterview, purchasePlan, getCurrentUserDetails
} from '../../../lib/frontendapi';
import Link from 'next/link';
import {useRouter} from 'next/router';
import {useForm} from 'react-hook-form';
import {paginate} from '@/helpers/paginate';
import {getCurrentUserAllJobs} from '@/lib/frontendapi';
import MobileViewEmployerApplicants from '@/components/Common/MobileViewEmployerApplicants';
import moment from 'moment';
import Pagination from '../../../components/Common/Pagination';
import PopupModal from '../../../components/Common/PopupModal';
import AuthContext from '@/Context/AuthContext';
import Image from 'next/image';
import {Spinner} from 'react-bootstrap';
import styles from './Applicants.module.css';
import {useClickOutside} from '@/hooks/useOutSideClick';
import {notification} from "antd";

export default function Applicants() {
  const router = useRouter();
  const {
    register,
    formState: {errors},
    handleSubmit,
    reset: resetForm1,
  } = useForm({
    mode: 'onChange',
  });
  const {
    register: register2,
    formState: {errors: errors2},
    handleSubmit: handleSubmit2,
    reset: resetForm2,
  } = useForm({
    mode: 'onChange',
  });
  const {user} = useContext(AuthContext);
  const [currentUser, setCurrentUser] = useState(user);
  const [totalJobs, setTotalJobs] = useState([]);
  const [jobid, setJobid] = useState(0);
  const [messageDesc, setMessageDesc] = useState('');
  const [isActive, setIsActive] = useState('All');
  const [applications, setApplications] = useState([]);
  const [singleApplications, setSingleApplications]: any = useState([]);
  const [hiringStatus, setHiringStatus] = useState('');
  const [hiringStatusYesCount, setHiringStatusYesCount] = useState('');
  const [hiringStatusNoCount, setHiringStatusNoCount] = useState('');
  const [hiringStatusAllCount, setHiringStatusAllCount] = useState('');
  const [hiringStatusMaybeCount, setHiringStatusMaybeCount] = useState('');
  const [hiringStatusInstantApplyCount, setHiringStatusInstantApplyCount] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalApplications, setTotalApplications] = useState([]);
  const [modalCandidateProfilePopup, setModalCandidateProfilePopup] = useState(false);
  const [modalCandidateMessagePopup, setModalCandidateMessagePopup] = useState(false);
  const [singleApplicationsWorkExperience, setSingleApplicationsWorkExperience] = useState([]);
  const [singleApplicationsEducation, setSingleApplicationsEducation] = useState([]);
  const [skills, setGetAllSkills] = useState([]);
  const [portfolio, setPortfolio] = useState([]);
  const [languages, setLanguages] = useState([]);
  const [userJobStatus, setUserJobStatus] = useState('');
  const [interviewScheduleDate, setInterviewScheduleDate] = useState('');
  const [interviewFromTime, setInterviewFromTime] = useState('');
  const [interviewToTime, setInterviewToTime] = useState('');
  const [interviewZoomLink, setInterviewZoomLink] = useState('');
  const [jobId, setJobId] = useState('');
  const [companyId, setCompanyId] = useState('');
  const [applicantId, setApplicantId] = useState('');
  const [current_user_id, setCurrentUserId] = useState('');
  const [companycvcount, setCompanyCvCount] = useState(0);
  const [applyJobInterviews, setApplyJobInterviews] = useState('');
  const [membershipstatus, setMemberShipStatus] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const pageSize = 5;
  const [candidateId, setCandidateId] = useState('');
  const [showPopupunsave, setShowPopupunsave] = useState(false);
  const [showPopupunsave1, setShowPopupunsave1] = useState(false);
  const [showmessage, setShowmessage] = useState('');
  const [showMobileView, setShowMobileView] = useState(false);
  const [isApplicantionLoading, setApplicationsLoading] = useState(false);
  const [toggleAction, setToggleAction] = useState({
    show: false,
    index: -1,
  });
  const dropdownRef = useRef(null);

  useClickOutside(dropdownRef, [], () => {
    setToggleAction({show: false, index: -1});
  });

  const [modalUpgradePlanMessage, setModalUpgradePlanMessage] = useState(false);

  const modalUpgradePlanMessageOpen = () => {
    setModalUpgradePlanMessage(true);
  };

  const modalUpgradePlanMessageClose = () => {
    setModalUpgradePlanMessage(false);
  };

  const getNextMonthInfo = () => {
    const now = new Date();
    const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);

    const day = nextMonth.getDate();
    const month = nextMonth.toLocaleString('en-US', { month: 'long' });
    const year = nextMonth.getFullYear();

    return { day, month, year };
  };

  const nextMonthInfo = getNextMonthInfo();

  const selectPlan = (e: any, plan_id: any) => {
    if (user) {
      const data = {
        user_id: user?.id,
        company_id: user?.company_id,
        plan_id: plan_id,
      };

      purchasePlan(data).then(res => {
        if (res.success) {
          notification.success({message: res.message});
          router.push('/payment');
        }
      });
    }
  };

  const onPageChange = (page: any) => {
    setCurrentPage(page);
    const data = {
      user_id: user?.id,
      company_id: user?.company_id,
      role: user?.role,
      hiring_selected_status: isActive,
    };
    getAllJobApplications(data)
      .then(res => {
        if (res.status == true) {
          setTotalApplications(res.data);
          const paginatedPosts = paginate(res.data, page, pageSize);
          setApplications(paginatedPosts);
        } else {
          setApplications([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  const getUsersJob = () => {
    const data = {
      company_id: user?.company_id,
      job_sort: '',
    };
    getCurrentUserAllJobs(data)
      .then(res => {
        if (res.status == true) {
          console.log('setTotalJobs', res.data);
          setTotalJobs(res.data);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  const handleChangeHiringStatus = (e: any, application_id: any) => {
    const data = {
      hiring_status: e.target.value,
    };
    updateHiringStatus(application_id, data)
      .then(res => {
        if (res.status == true) {
          setShowmessage(res.message);
          setShowPopupunsave(true);
          setTimeout(() => {
            setShowPopupunsave(false);
          }, 1000);
          const data = {
            user_id: user?.id,
            company_id: user?.company_id,
            role: user?.role,
            hiring_selected_status: isActive,
          };

          getAllJobApplicationsdata(data);
          // setHiringStatus(res.data.hiring_status);
          //window.location.reload();
        } else {
          setShowmessage(res.message);
          setShowPopupunsave1(true);
          setTimeout(() => {
            setShowPopupunsave1(false);
          }, 1000);
          setHiringStatus('');
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  useEffect(() => {
    const data = {
      user_id: user?.id,
      company_id: user?.company_id,
      role: user?.role,
      hiring_selected_status: isActive,
    };
    getUsersJob();

    getAllJobApplicationsdata(data);

    let details = navigator.userAgent;
    let regexp = /android|iphone|kindle|ipad/i;
    let isMobileDevice = regexp.test(details);
    if (isMobileDevice) {
      //console.log("You are using a Mobile Device");
      setShowMobileView(true);
    } else {
      //console.log("You are using Desktop");
      setShowMobileView(false);
    }

    if (user && user.id)
    {
      getCurrentUserDetails(user.id).then((response) => {
        setCurrentUser(response.user)
      })
    }
  }, [user]);

  const getAllJobApplicationsdata = async (data: any) => {
    try {
      setApplicationsLoading(true);
      const res = await getAllJobApplications(data);
      if (res.status == true) {
        setTotalApplications(res.data);
        const paginatedPosts = paginate(res.data, currentPage, pageSize);
        setApplications(paginatedPosts);
        setHiringStatusAllCount(res.hiring_status_all_count);
        setHiringStatusYesCount(res.hiring_status_yes_count);
        setHiringStatusNoCount(res.hiring_status_no_count);
        setHiringStatusMaybeCount(res.hiring_status_maybe_count);
        setHiringStatusInstantApplyCount(res.hiring_status_instant_apply_count);
        setApplicationsLoading(false);
      } else {
        setApplications([]);
        setHiringStatusAllCount('');
        setHiringStatusYesCount('');
        setHiringStatusNoCount('');
        setHiringStatusMaybeCount('');
        setHiringStatusInstantApplyCount('');
        setApplicationsLoading(false);
      }
    } catch (err) {
      console.log(err);
      setApplicationsLoading(false);
    } finally {
      setApplicationsLoading(false);
    }
  };

  const handleClickStatusAccordingApplicants = (e: any, hiring_selected_status: any) => {
    e.preventDefault();
    setIsActive(hiring_selected_status);
    let data = {};
    if (jobid != 0) {
      data = {
        user_id: user?.id,
        role: user?.role,
        company_id: user?.company_id,
        hiring_selected_status: hiring_selected_status,
        job_id: jobid,
      };
    } else {
      data = {
        user_id: user?.id,
        role: user?.role,
        company_id: user?.company_id,
        hiring_selected_status: hiring_selected_status,
      };
    }

    getAllJobApplications(data)
      .then(res => {
        if (res.status == true) {
          setTotalApplications(res.data);
          const paginatedPosts = paginate(res.data, currentPage, pageSize);
          setApplications(paginatedPosts);
          setHiringStatusAllCount(res.hiring_status_all_count);
          setHiringStatusYesCount(res.hiring_status_yes_count);
          setHiringStatusNoCount(res.hiring_status_no_count);
          setHiringStatusMaybeCount(res.hiring_status_maybe_count);
          setHiringStatusInstantApplyCount(res.hiring_status_instant_apply_count);
          //window.location.reload();
        } else {
          setApplications([]);
          setHiringStatusAllCount('');
          setHiringStatusYesCount('');
          setHiringStatusNoCount('');
          setHiringStatusMaybeCount('');
          setHiringStatusInstantApplyCount('');
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  const modalConfirmCandidateProfilePopupOpen = (
    e: any,
    applicants_id: any,
    candidate_id: any,
    job_id: any,
    company_id: any,
  ) => {
    // alert(candidate_id);

    setModalCandidateProfilePopup(true);
    setJobId(job_id);
    setCompanyId(company_id);
    setApplicantId(applicants_id);
    setCandidateId(candidate_id);
    handleViewCandidateProfile(applicants_id, candidate_id);
    const data = {
      applicants_id: applicants_id,
      candidate_id: candidate_id,
    };
    getSingleJobApplications(data)
      .then(res => {
        if (res.status == true) {
          setSingleApplications(res.data);
          if (res.data.job_status == 'ready_to_interview') {
            setUserJobStatus('Ready to Interview');
          } else if (res.data.job_status == 'open_to_offer') {
            setUserJobStatus('Open to Offer');
          } else {
            setUserJobStatus('Not Looking');
          }
        } else {
          setSingleApplications([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
    getWorkExperienceEmployee(candidate_id)
      .then(res => {
        if (res.status == true) {
          setSingleApplicationsWorkExperience(res.data);
        } else {
          setSingleApplicationsWorkExperience([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
    getEducationsEmployee(candidate_id)
      .then(res => {
        if (res.status == true) {
          setSingleApplicationsEducation(res.education);
        } else {
          setSingleApplicationsEducation([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
    getSingleUserSkill(candidate_id)
      .then(res => {
        if (res.status == true) {
          setGetAllSkills(res.data);
        } else {
          setGetAllSkills([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
    getportfolio(candidate_id)
      .then(res => {
        if (res.status == true) {
          setPortfolio(res.data);
        } else {
          setPortfolio([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
    getAllLanguages(candidate_id)
      .then(res => {
        if (res.status == true) {
          setLanguages(res.data);
        } else {
          setLanguages([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
    const datas = {
      applicants_id: applicants_id,
      job_id: job_id,
    };
    getApplyJobInterview(datas)
      .then(res => {
        if (res.status == true) {
          setApplyJobInterviews(res.data);
        } else {
          setApplyJobInterviews('');
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  const modalConfirmCandidateProfilePopupClose = () => {
    setModalCandidateProfilePopup(false);
  };
  const modalConfirmCandidateMessagePopupOpen = () => {
    setModalCandidateMessagePopup(true);
  };
  const modalConfirmCandidateMessagePopupClose = () => {
    setModalCandidateMessagePopup(false);
  };
  const submitInterviewForm = (data: any) => {
    setIsLoading(true);

    const datas = {
      job_id: jobId,
      company_id: companyId,
      applicant_id: candidateId,
      interview_schedule_date: data.schedule_date,
      interview_from_time: data.schedule_from_time,
      interview_to_time: data.schedule_to_time,
      meeting_link: data.schedule_zoom_link,
    };
    console.log(datas);
    addInterview(datas)
      .then(res => {
        if (res.status == true) {
          setShowmessage(res.message);
          setShowPopupunsave(true);
          setTimeout(() => {
            setShowPopupunsave(false);
          }, 1000);
          setTimeout(() => {
            modalConfirmCandidateProfilePopupClose();
          }, 1000);
          setIsLoading(false);
          resetForm2();
        } else {
          setShowmessage(res.message);
          setShowPopupunsave1(true);
          setTimeout(() => {
            setShowPopupunsave1(false);
          }, 1000);
          setIsLoading(true);
        }
      })
      .catch(err => {
        setShowmessage(err);
        setShowPopupunsave1(true);
        setTimeout(() => {
          setShowPopupunsave1(false);
        }, 1000);
      });
  };
  let current_date = new Date().toJSON().slice(0, 10);

  const handleClickDownloadResumeValidation = (e: any, resume_path: any, applicants_id: any) => {
    if (resume_path == null || resume_path == '') {
      setShowmessage('Candidate has not yet uploaded the resume.');
      setShowPopupunsave1(true);
      setTimeout(() => {
        setShowPopupunsave1(false);
      }, 1000);

      if (currentUser)
      {
        getCurrentUserDetails(currentUser.id).then((response) => {
          setCurrentUser(response.user)
        })
      }
    }
  };

  const submitMessageForm = (data: any) => {
    let applicants_id = $('.hd_applicants_id').val();
    let candidate_id = $('.hd_candidate_id').val();
    let job_id = $('.hd_job_id').val();
    let current_user_id = user?.id;
    const datas = {
      applicants_id: applicants_id,
      candidate_id: candidate_id,
      job_id: job_id,
      current_user_id: current_user_id,
      message_title: null,
      message_description: data.message_desc,
      attachment_path: null,
      message_type: 'applyJob',
      message_status: 'unread',
    };
    sendMessage(datas)
      .then(res => {
        if (res.status == true) {
          setShowmessage(res.message);
          setShowPopupunsave(true);
          setTimeout(() => {
            setShowPopupunsave(false);
          }, 1000);
          resetForm1();
          setTimeout(() => {
            modalConfirmCandidateMessagePopupClose();
          }, 1000);
        } else {
          setShowmessage(res.message);
          setShowPopupunsave1(true);
          setTimeout(() => {
            setShowPopupunsave1(false);
          }, 1000);
        }
      })
      .catch(err => {
        setShowmessage(err);
        setShowPopupunsave1(true);
        setTimeout(() => {
          setShowPopupunsave1(false);
        }, 1000);
      });
  };
  const jobApplicantsSorted = (id: any) => {
    setJobid(id);
    setIsActive('All');
    const data = {
      user_id: user?.id,
      role: user?.role,
      company_id: user?.company_id,
      hiring_selected_status: 'All',
      job_id: id,
    };
    getAllJobApplications(data)
      .then(res => {
        if (res.status == true) {
          setTotalApplications(res.data);
          const paginatedPosts = paginate(res.data, currentPage, pageSize);
          setApplications(paginatedPosts);
          setHiringStatusAllCount(res.hiring_status_all_count);
          setHiringStatusYesCount(res.hiring_status_yes_count);
          setHiringStatusNoCount(res.hiring_status_no_count);
          setHiringStatusMaybeCount(res.hiring_status_maybe_count);
          setHiringStatusInstantApplyCount(res.hiring_status_instant_apply_count);
          //window.location.reload();
        } else {
          setApplications([]);
          setHiringStatusAllCount('');
          setHiringStatusYesCount('');
          setHiringStatusNoCount('');
          setHiringStatusMaybeCount('');
          setHiringStatusInstantApplyCount('');
        }
      })
      .catch(err => {
        console.log(err);
      });
    console.log('jobApplicantsSorted', data);
  };

  const handleViewCandidateProfile = (id: any, candidate_id: any) => {
    const data = {
      user_id: user?.id,
      user_role: user?.role,
      company_id: user?.company_id,
      applicant_id: id,
    };

    checkAndUpdateResumesViewed(data)
      .then(res => {
        if (res.status === true) {
          getAllJobApplicationsdata({
            user_id: user?.id,
            company_id: user?.company_id,
            role: user?.role,
          });
          window.localStorage.setItem('company_resume_count', res.company_resume_count);
          setCompanyCvCount(res.company_resume_count);
        } else {
        }
      })
      .catch(err => {
        console.log(err);
      });

    if (currentUser)
    {
      getCurrentUserDetails(currentUser.id).then((response) => {
        setCurrentUser(response.user)
      })
    }
  };

  return (
    <>
      <div className="dash-right">
        <h1>Applicants</h1>
        <div className="row">
          {/* <div className="col-sm-9">
            <ul className="list-loc m-m-0 mt-4">
              <li className="active">
                <a href="#">All Applicants</a>
              </li>
            </ul>
          </div> */}
          <div className="col-sm-3  text-right">
            {/* <ul className='blue-text-line mt-4 text-right'>
                            <li><a href="/employer/applicants/emptyapplicants">Empty Applicants</a></li>
                        </ul> */}
          </div>
        </div>
        <div className="candidates-part">
          <ul className="row-btn applicant">
            <li>
              <button
                className={isActive == 'All' ? 'tab-btn-dash active-blue' : 'tab-btn-dash'}
                onClick={(e: any) => handleClickStatusAccordingApplicants(e, 'All')}>
                All ({hiringStatusAllCount || 0})
              </button>
            </li>
            <li>
              <button
                className={isActive == 'Yes' ? 'tab-btn-dash active-blue' : 'tab-btn-dash'}
                onClick={(e: any) => handleClickStatusAccordingApplicants(e, 'Yes')}>
                Yes ({hiringStatusYesCount || 0})
              </button>
            </li>
            <li>
              <button
                className={isActive == 'Maybe' ? 'tab-btn-dash active-blue' : 'tab-btn-dash'}
                onClick={(e: any) => handleClickStatusAccordingApplicants(e, 'Maybe')}>
                Maybe ({hiringStatusMaybeCount || 0})
              </button>
            </li>
            <li>
              <button
                className={isActive == 'No' ? 'tab-btn-dash active-blue' : 'tab-btn-dash'}
                onClick={(e: any) => handleClickStatusAccordingApplicants(e, 'No')}>
                No ({hiringStatusNoCount || 0})
              </button>
            </li>
            <li>
              <button
                className={isActive == 'InstantApply' ? 'tab-btn-dash active-blue' : 'tab-btn-dash'}
                onClick={(e: any) => handleClickStatusAccordingApplicants(e, 'InstantApply')}>
                Instant Apply ({hiringStatusInstantApplyCount || 0})
              </button>
            </li>
            <li className="jobs-applicant">
              <select
                className="all-Status w-60-sp"
                name="posted_jobs"
                // value={sector_id}
                onChange={e => jobApplicantsSorted(e.target.value)}>
                <option value="">select job post</option>
                {totalJobs.length > 0
                  ? totalJobs.map((job: any) => (
                      <option key={job.id} value={job.id}>
                        {job.job_title}
                      </option>
                    ))
                  : ''}
              </select>
            </li>
          </ul>
          {showMobileView ? (
            isApplicantionLoading ? (
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '500px',
                }}>
                <Spinner animation="border" role="status" />
              </div>
            ) : (
              <MobileViewEmployerApplicants applicationData={applications} />
            )
          ) : isApplicantionLoading ? (
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '500px',
              }}>
              <Spinner animation="border" role="status" />
            </div>
          ) : applications.length > 0 ? (
            applications.map((applicationData: any, index: any) => {
              return (
                <div className="filter filter-sp m-center mt-4" key={index}>
                  <div className="row">
                    <div className="col w-150-col pr-0">
                      {applicationData.profile ? (
                        <img
                          src={applicationData?.profile?.source}
                          alt=" user-p"
                          width={100}
                          height={100}
                          layout="responsive"
                        />
                      ) : (
                        <img
                          src={process.env.NEXT_PUBLIC_BASE_URL + '/images/user-p.png'}
                          alt=" user-p"
                          className="logo-filter w-100 img-hh"
                        />
                      )}

                      {applicationData.applicant_id && (
                        <span className=" mt-2 w-100 badge bg-success float-start">
                          Seen by{' '}
                          {applicationData.viewed_user_id == current_user_id
                            ? 'me'
                            : applicationData.viewed_user_name.split(' ')[0]}
                        </span>
                      )}
                    </div>
                    <div className="col m-center sp-m-3">
                      <p className="p-18 blue-text mt-2">
                        <a href={'/candidate-profile/' + applicationData.employee_profile_slug} target="_blank">
                          {applicationData.name}
                        </a>
                      </p>
                      <p className="p-16 black-text line-height-22">
                        {/*applicationData.candidate_current_position@*/}
                        <a target="_blank" href={'/companies/' + applicationData.company_slug}>
                          {applicationData.company_name}
                        </a>
                      </p>
                      <ul className="full-time">
                        <li>
                          <i className="fa-solid fa-business-time"></i>{' '}
                          {applicationData.job_status === 'ready_to_interview'
                            ? 'Ready to Interview'
                            : applicationData.job_status === 'open_to_offer'
                              ? 'Open to Offer'
                              : 'Not Looking'}{' '}
                        </li>
                        <li>
                          <i className="fa-solid fa-location-dot"></i> {applicationData.country_name}
                        </li>
                      </ul>
                      <div className="sort-d-flex mt-3">
                        <p className="Status-by  ">Hiring Status:</p>
                        <select
                          className="all-Status w-60-sp"
                          value={applicationData.hiring_status}
                          onChange={(e: any) => handleChangeHiringStatus(e, applicationData.id)}>
                          <option value="Yes">Yes</option>
                          <option value="No">No</option>
                          <option value="Maybe">Maybe</option>
                          {/* <option value="InstantApply" selected={applicationData.hiring_status == 'InstantApply'}>Instant Apply</option> */}
                        </select>
                        <p className="Status-by p-2 pt-0 pb-0">
                          {applicationData.job_title ? (
                            <a target="_blank" href={'/job/' + applicationData.job_slug}>
                              {'(' + applicationData.job_title.substring(0, 80) + '...)'}
                            </a>
                          ) : (
                            ''
                          )}
                        </p>
                      </div>
                      {/* <small className='Status-by mt-1'>Job posted by : {current_user_id == applicationData.jobpost_by_userId ? "Me" : applicationData.job_posted_by_name}</small> */}
                    </div>
                    <div className="col max-268-col text-center m-center">
                      { currentUser && (!(currentUser.company && currentUser.company.available_resume_count) && !currentUser.available_resume_count && applicationData.viewed_user_id === null) ? (
                              <button
                                      className="dropdown-toggle btn-a primary-size-16 btn-bg-0055BA w-100 max-100-p"
                                      onClick={modalUpgradePlanMessageOpen}
                                      type="button"
                                      id="dropdownMenuButton1"
                                      data-bs-toggle="dropdown"
                                      aria-expanded="false"
                              >
                                View Application
                              </button>
                      ) : (
                              <button
                                      onClick={(e: any) =>
                                              modalConfirmCandidateProfilePopupOpen(
                                                      e,
                                                      applicationData.id,
                                                      applicationData.user_id,
                                                      applicationData.job_id,
                                                      applicationData.company_id,
                                              )
                                      }
                                      className="dropdown-toggle btn-a primary-size-16 btn-bg-0055BA w-100 max-100-p"
                                      type="button"
                                      id="dropdownMenuButton1"
                                      data-bs-toggle="dropdown"
                                      aria-expanded="false">
                                View Application
                              </button>
                      )}

                      {currentUser && (!(currentUser.company && currentUser.company.available_resume_count) && !currentUser.available_resume_count && applicationData.viewed_user_id === null) ? (
                        <button className="download mt-2 w-100" onClick={modalUpgradePlanMessageOpen}>
                          <i className="fa-solid fa-download"></i> Download Resume
                        </button>
                      ) : applicationData.resume_path ? (
                        <a
                                target="_blank"
                                href={
                                        process.env.NEXT_PUBLIC_IMAGE_URL + 'images/employee/resume/' + applicationData.resume_path
                                }
                                onClick={() => handleViewCandidateProfile(applicationData.id, applicationData.user_id)}>
                          <button className="download mt-2 w-100">
                            <i className="fa-solid fa-download"></i> Download Resume
                          </button>
                        </a>
                      ) : (
                        <a href="#">
                          <button
                                  className="download mt-2 w-100"
                                  onClick={(e: any) =>
                                          handleClickDownloadResumeValidation(
                                                  e,
                                                  applicationData.resume_path,
                                                  applicationData.id,
                                          )
                                  }>
                            <i className="fa-solid fa-download"></i> Download Resume
                          </button>
                        </a>
                      )}
                      <div className="dropdown mt-2 text-end">
                        <button
                          className="dropdown-toggle  action_btn"
                          type="button"
                          id="dropdownMenuButton1"
                          data-bs-toggle="dropdown"
                          onClick={() =>
                            setToggleAction({
                              show: !toggleAction.show,
                              index: index,
                            })
                          }
                          aria-expanded="false">
                          Actions <i className="fa-solid fa-chevron-down"></i>
                        </button>
                        {toggleAction.show && toggleAction.index === index && (
                          <ul className={styles.dropdownMenu} aria-labelledby="dropdownMenuButton1" ref={dropdownRef}>
                            {/* <li>
                              <a className="dropdown-item item-1" href="#">
                                Schedule Interview
                              </a>
                            </li> */}
                            <li>
                              {currentUser && (!(currentUser.company && currentUser.company.available_resume_count) && !currentUser.available_resume_count && applicationData.viewed_user_id === null) ? (
                                      <a className="dropdown-item item-2" onClick={modalUpgradePlanMessageOpen}>
                                        View candidate profile
                                      </a>
                              ) : (
                                      <a
                                              target="_blank"
                                              className="dropdown-item item-2"
                                              href={'/candidate-profile/' + applicationData.employee_profile_slug}
                                              onClick={() => handleViewCandidateProfile(applicationData.id, applicationData.user_id)}>
                                        View candidate profile
                                      </a>
                              )}
                            </li>

                            {/* <li>
                              <a
                                target="_blank"
                                className="dropdown-item item-2"
                                href={'/employer/messages/inbox/' + applicationData.user_id}
                                onClick={() => handleViewCandidateProfile(applicationData.user_id)}>
                                Message
                              </a>
                            </li> */}
                          </ul>
                        )}
                      </div>
                    </div>
                  </div>
                  {applicationData.instant_apply == '1' ? (
                    <p className="posted">Applied through Instant Apply!</p>
                  ) : (
                    <p className="posted"></p>
                  )}
                </div>
              );
            })
          ) : (
            <div className="m-p-10 text-center mt-5">
              <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blank-2.jpg'} alt="blank-2" className="" />
              <p className="f-22 c-BABABA mb-1">No Applicants Found</p>
              <p className="f-18">
                <Link href="/employer/jobs" className="c-0070F5">
                  Post A Job
                </Link>
              </p>
            </div>
          )}
          <Pagination
            items={totalApplications.length}
            currentPage={currentPage}
            pageSize={pageSize}
            onPageChange={onPageChange}
          />
        </div>
        <PopupModal
          show={modalCandidateProfilePopup}
          handleClose={modalConfirmCandidateProfilePopupClose}
          customclass={'   modal-lg  header-remove body-sp-0'}>
          <div className="popup-body candidate_application_popup_body b-r-8">
            <div className="row">
              <div className="col-sm-8">
                <i
                  className="fa-solid fa-arrow-left f-20 c-0055BA"
                  onClick={modalConfirmCandidateProfilePopupClose}></i>
                <p className="f-37 w-700 c-2C2C2C mb-2">
                  <Link href={'/candidate-profile/' + singleApplications?.slug}>{singleApplications?.name}</Link>
                </p>
                <p className="f-22  c-0055BA w-500 Archivo">
                  {singleApplications?.current_position}@
                  <Link href={'/companies/' + singleApplications?.company_slug}>
                    {singleApplications?.company_name}
                  </Link>
                </p>
              </div>
              <div className="col-sm-4 text-right">
                <p className="f-16 w-400 c-4D4D4D">Hiring Status: </p>
                <select
                  className="choose-currency currencymm mt-4 w-100 mb-3"
                  defaultValue={singleApplications?.hiring_status}
                  onChange={(e: any) => handleChangeHiringStatus(e, singleApplications?.applicants_id)}>
                  <option value="Yes">Yes</option>
                  <option value="No">No</option>
                  <option value="Maybe">Maybe</option>
                  {/* <option value="InstantApply">Instant Apply</option> */}
                </select>
                <button
                  className="btn-a primary-size-16 btn-bg-0055BA w-100"
                  onClick={modalConfirmCandidateMessagePopupOpen}>
                  Message
                </button>
              </div>
            </div>
            <hr className="hr-line" />
            <div className="tab-popup callum ">
              <ul className="nav nav-pills mb-3" id="pills-tab" role="tablist">
                <li className="nav-item" role="presentation">
                  <button
                    className="nav-link active f-16"
                    id="pills-home-tab"
                    data-bs-toggle="pill"
                    data-bs-target="#pills-home"
                    type="button"
                    role="tab"
                    aria-controls="pills-home"
                    aria-selected="true">
                    Application
                  </button>
                </li>
                {applyJobInterviews.length > 0 ? (
                  ''
                ) : (
                  <li className="nav-item" role="presentation">
                    <button
                      className="nav-link"
                      id="pills-profile-tab"
                      data-bs-toggle="pill"
                      data-bs-target="#pills-profile"
                      type="button"
                      role="tab"
                      aria-controls="pills-profile"
                      aria-selected="false">
                      Interview
                    </button>
                  </li>
                )}
              </ul>
              <div className="tab-content" id="pills-tabContent">
                <div
                  className="tab-pane fade show active"
                  id="pills-home"
                  role="tabpanel"
                  aria-labelledby="pills-home-tab">
                  <div className="row" style={{marginBottom: '15px'}}>
                    <div className="col-sm-8">
                      <ul className="skills pop-skils">
                        <li>
                          <p className="f-16 c-999999 w-400">
                            <i className="fa-regular fa-envelope"></i>{' '}
                            <a href={`mailto:${singleApplications?.email}`}>{singleApplications?.email}</a>
                          </p>
                        </li>

                        <li>
                          <p className="f-16 c-999999 w-400">
                            <i className="fa-solid fa-phone"></i>{' '}
                            <a href={`tel:${singleApplications?.contact_no}`}>{singleApplications?.contact_no}</a>
                          </p>
                        </li>

                        <li>
                          <p className="f-16 c-999999 w-400">
                            <i className="fa-solid fa-briefcase"></i> {userJobStatus}
                          </p>
                        </li>
                        <li>
                          <p className="f-16 c-999999 w-400">
                            <i className="fa-solid fa-location-dot"></i>{' '}
                            {singleApplications?.where_currently_based ? singleApplications.country_name : ''}
                          </p>
                        </li>
                        <li>
                          <p className="f-16 c-999999 w-400">
                            <i className="fa-regular fa-user"></i> Age:{' '}
                            {moment().diff(singleApplications?.date_of_birth, 'years')}
                          </p>
                        </li>
                        <li>
                          <p className="f-16 c-999999 w-400">
                            <i className="fa-regular fa-circle-user"></i> {singleApplications?.gender}
                          </p>
                        </li>
                        <li>
                          <p className="f-16 c-999999 w-400">
                            <i className="fa-solid fa-graduation-cap"></i> {singleApplications?.current_position}
                          </p>
                        </li>
                      </ul>
                    </div>
                  </div>
                  {singleApplications?.resume_path ? (
                    <>
                      <p className="f-16 c-000">Resume</p>
                      <div
                        className="row"
                        style={{
                          border: '2px solid #ccc',
                          padding: '20px 0px 20px 10px',
                          borderRadius: '10px',
                          margin: '0px 0px 15px 0px',
                        }}>
                        <div className="col-sm-10">
                          <p className="f-22  c-0055BA w-500 Archivo m-0">
                            <a
                              href={
                                process.env.NEXT_PUBLIC_IMAGE_URL +
                                'images/employee/resume/' +
                                singleApplications?.resume_path
                              }
                              target="_blank">
                              {singleApplications?.resume_path}
                            </a>
                          </p>
                          <p className="f-16">Uploaded on {moment(singleApplications?.created_at).format('MMM DD')}</p>
                        </div>
                        <div className="col-sm-2 text-right">
                          <button
                            style={{
                              padding: '7px 20px 7px 20px',
                              background: '#ebf1f9',
                              border: '2px solid #0055ba',
                              color: '#0055ba',
                              fontSize: '18px',
                              fontWeight: '600',
                              borderRadius: '10px',
                            }}>
                            <a
                              href={
                                process.env.NEXT_PUBLIC_IMAGE_URL +
                                'images/employee/resume/' +
                                singleApplications?.resume_path
                              }
                              target="_blank">
                              <i style={{color: '#0055ba'}} className="fa fa-download"></i>
                            </a>
                          </button>
                        </div>
                      </div>
                    </>
                  ) : (
                    ''
                  )}

                  {singleApplications?.instant_apply === 0 && (
                    <>
                      <p className="f-16 c-000">Cover Letter</p>
                      {singleApplications?.cover_letter ? (
                        <>
                          <div
                            className="row"
                            style={{
                              border: '2px solid #ccc',
                              padding: '20px 0px 20px 10px',
                              borderRadius: '10px',
                              margin: '0px',
                            }}>
                            <div className="col-sm-10">
                              <p className="f-22 c-0055BA w-500 Archivo m-0">
                                <a
                                  href={
                                    process.env.NEXT_PUBLIC_IMAGE_URL +
                                    'images/employee/cover_letter/' +
                                    singleApplications?.cover_letter
                                  }
                                  target="_blank">
                                  {singleApplications?.cover_letter}
                                </a>
                              </p>
                              <p className="f-16">
                                Uploaded on {moment(singleApplications?.created_at).format('MMM DD')}
                              </p>
                            </div>
                            <div className="col-sm-2 text-right">
                              <button
                                style={{
                                  padding: '7px 20px 7px 20px',
                                  background: '#ebf1f9',
                                  border: '2px solid #0055ba',
                                  color: '#0055ba',
                                  fontSize: '18px',
                                  fontWeight: '600',
                                  borderRadius: '10px',
                                }}>
                                <a
                                  href={
                                    process.env.NEXT_PUBLIC_IMAGE_URL +
                                    'images/employee/cover_letter/' +
                                    singleApplications?.cover_letter
                                  }
                                  target="_blank">
                                  <i style={{color: '#0055ba'}} className="fa fa-download"></i>
                                </a>
                              </button>
                            </div>
                          </div>
                          <div
                            className="row mt-2"
                            style={{border: '2px solid #ccc', padding: '10px', borderRadius: '5px', margin: '0px'}}>
                            <p className="p-0" dangerouslySetInnerHTML={{__html: singleApplications?.description}}></p>
                          </div>
                        </>
                      ) : (
                        <>
                          <span dangerouslySetInnerHTML={{__html: singleApplications?.description}}></span>
                        </>
                      )}
                    </>
                  )}

                  <div className="mt-4">
                    {singleApplications?.bio ? (
                      <>
                        <p className="f-16 c-000">About</p>
                        <p className="f-18 c-4D4D4D w-400  mb-4">{singleApplications.bio}</p>
                      </>
                    ) : (
                      ''
                    )}
                    {singleApplicationsWorkExperience?.length > 0 ? <p className="f-16 c-000">Work Experience</p> : ''}
                    {singleApplicationsWorkExperience.length > 0
                      ? singleApplicationsWorkExperience.map((single_applications_workexperience: any, index: any) => {
                          return (
                            <>
                              <p className="f-22 w-700 c-2C2C2C mt-2 mb-2">
                                {single_applications_workexperience.title}
                              </p>
                              <p className="f-18 c-0055BA mb-2">{single_applications_workexperience.company}</p>
                              <p className="f-16 c-999999">
                                {single_applications_workexperience.start_date} -{' '}
                                {single_applications_workexperience.currently_work_here == '1'
                                  ? 'Currently here'
                                  : single_applications_workexperience.end_date}
                              </p>
                              <p className="f-18 c-4D4D4D w-400 mb-3">
                                {single_applications_workexperience.description}
                              </p>
                              {/* <p className='f-22 w-700 c-2C2C2C mt-2 mb-2'>Junior Software Engineer</p>
                                                            <p className='f-18 c-0055BA mb-2'>Twitter</p> */}
                            </>
                          );
                        })
                      : ''}
                    {singleApplicationsEducation.length > 0 ? <p className="f-16 c-000">Education</p> : ''}
                    <div className="row mb-3">
                      {singleApplicationsEducation.length > 0
                        ? singleApplicationsEducation.map((single_applications_education: any, index: any) => {
                            return (
                              <div className="col-sm-6" key={index}>
                                <p className="f-22 w-700 c-2C2C2C mt-2 mb-2">{single_applications_education.degree}</p>
                                <p className="f-18 c-0055BA mb-2">{single_applications_education.education_title}</p>
                                <p className="f-16 c-999999">
                                  {single_applications_education.start_date} -{' '}
                                  {single_applications_education.currently_study_here == '1'
                                    ? 'Currently here'
                                    : single_applications_education.end_date}
                                </p>
                                <p className="f-16 c-999999">
                                  Scored: {single_applications_education.your_score}/
                                  {single_applications_education.max_score}
                                </p>
                              </div>
                            );
                          })
                        : ''}
                    </div>
                    {skills.length > 0 ? <p className="f-16 c-000">Skills</p> : ''}
                    <ul className="skills_sections pb-0 scr-x">
                      {skills.length > 0
                        ? skills.map((skills_data: any, index: any) => {
                            return <li key={index}>{skills_data.skills}</li>;
                          })
                        : ''}
                    </ul>
                    {portfolio.length > 0 ? <p className="f-16 c-000">Portfolio/Projects</p> : ''}
                    {portfolio.length > 0
                      ? portfolio.map((portfolio_data: any, index: any) => {
                          return (
                            <>
                              <p className="f-22 w-700 c-2C2C2C mt-2 mb-2">{portfolio_data?.title}</p>
                              <p className="f-18 c-0055BA mb-2">
                                <i className="fa-solid fa-link-simple"></i>{' '}
                                <a href={portfolio_data?.portfolio_link} target="_blank">
                                  {portfolio_data?.portfolio_link}
                                </a>
                              </p>
                              <p className="f-16 c-999999">
                                {portfolio_data?.start_date} -{' '}
                                {portfolio_data?.present == '1' ? 'Present' : portfolio_data?.end_date}
                              </p>
                              <p className="f-18 c-4D4D4D w-400 mb-3">{portfolio_data?.description}</p>
                            </>
                          );
                        })
                      : ''}
                    {languages?.length > 0 ? <p className="f-16 c-000">Languages</p> : ''}
                    {languages?.length > 0
                      ? languages.map((languages_data: any, index: any) => {
                          return (
                            <>
                              <p className="f-22 w-700 c-2C2C2C mt-2 mb-2">{languages_data?.language}</p>
                              <p className="f-18 c-0055BA pb-4">{languages_data?.proficiency}</p>
                            </>
                          );
                        })
                      : ''}
                  </div>
                </div>
                <div className="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab">
                  <p className="f-18 c-747474 w-400 time-mon-fri">
                    Based on your schedule you are available from <strong>Monday</strong> to <strong>Friday</strong>{' '}
                    <strong>between 09:00AM - 03:00PM</strong>
                  </p>
                  <p>
                    <a href="#" className="f-16 w-700 c-0070F5">
                      Edit Availability
                    </a>
                  </p>
                  <form className="form-experience-fieild mt-4" onSubmit={handleSubmit2(submitInterviewForm)}>
                    <label>Interview Schedule Date*</label>
                    <input
                      type="date"
                      placeholder="Schedule Date"
                      className="fild-des"
                      {...register2('schedule_date', {required: true})}
                      onChange={(e: any) => setInterviewScheduleDate(e.target.value)}
                      min={current_date}
                    />
                    {errors2.schedule_date && errors2.schedule_date.type === 'required' && (
                      <p className="text-danger error-m" style={{textAlign: 'left'}}>
                        Schedule Date is required.
                      </p>
                    )}
                    <div className="row">
                      <div className="col-sm-6">
                        <label>From Time*</label>
                        <input
                          type="time"
                          placeholder="9:00 AM"
                          className="fild-des"
                          {...register2('schedule_from_time', {required: true})}
                          onChange={(e: any) => setInterviewFromTime(e.target.value)}
                        />
                        {errors2.schedule_from_time && errors2.schedule_from_time.type === 'required' && (
                          <p className="text-danger error-m" style={{textAlign: 'left'}}>
                            Schedule From Time is required.
                          </p>
                        )}
                      </div>
                      <div className="col-sm-6">
                        <label>To Time*</label>
                        <input
                          type="time"
                          placeholder="3:00 PM"
                          className="fild-des"
                          {...register2('schedule_to_time', {required: true})}
                          onChange={(e: any) => setInterviewToTime(e.target.value)}
                        />
                        {errors2.schedule_to_time && errors2.schedule_to_time.type === 'required' && (
                          <p className="text-danger error-m" style={{textAlign: 'left'}}>
                            Schedule To Time is required.
                          </p>
                        )}
                      </div>
                    </div>
                    <label>Zoom Link*</label>
                    <input
                      type="text"
                      placeholder="linke.linke.zooooom"
                      className="fild-des"
                      {...register2('schedule_zoom_link', {
                        required: true,
                        pattern:
                          /^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/,
                      })}
                      onChange={(e: any) => setInterviewZoomLink(e.target.value)}
                    />
                    {errors2.schedule_zoom_link && errors2.schedule_zoom_link.type === 'required' && (
                      <p className="text-danger error-m" style={{textAlign: 'left'}}>
                        Schedule Zoom Link is required.
                      </p>
                    )}
                    {errors2.schedule_zoom_link?.type === 'pattern' && (
                      <p className="text-danger   error-m" style={{textAlign: 'left'}}>
                        Enter a valid zoom Link!
                      </p>
                    )}
                    <div className="text-right mt-3">
                      <button
                        className="btn-a primary-size-16 btn-bg-0055BA w-100 text-white"
                        id="schedule_inter"
                        type="submit"
                        disabled={isLoading}>
                        {isLoading ? 'Please wait...' : 'Schedule interview'}{' '}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </PopupModal>
        <br />
        <br />
        <PopupModal
          show={modalCandidateMessagePopup}
          handleClose={modalConfirmCandidateMessagePopupClose}
          customclass={'    modal-lg  header-remove body-sp-0'}>
          <div className="popup-body">
            <p className="f-31 c-191919 text-left">Message {singleApplications?.name}</p>
            <hr className="hr-line"></hr>
            <div className="form-experience-fieild">
              <form className="form-experience-fieild" onSubmit={handleSubmit(submitMessageForm)}>
                <input
                  type="hidden"
                  name="hd_candidate_id"
                  className="hd_candidate_id"
                  value={singleApplications?.user_id}
                />
                <input type="hidden" name="hd_job_id" className="hd_job_id" value={singleApplications?.job_id} />
                <input
                  type="hidden"
                  name="hd_applicants_id"
                  className="hd_applicants_id"
                  value={singleApplications?.applicants_id}
                />
                <p className="f-12 c-2C2C2C">Your Message</p>
                <textarea
                  placeholder="Your Message"
                  className="fild-des"
                  {...register('message_desc', {required: true})}
                  onChange={(e: any) => setMessageDesc(e.target.value)}></textarea>
                {errors?.message_desc && errors?.message_desc.type === 'required' && (
                  <p className="text-danger" style={{textAlign: 'left'}}>
                    Message Field is required.
                  </p>
                )}
                <div className="text-right mt-3">
                  <div className="row">
                    <div className="col-4">
                      <button className="cancel  w-100" onClick={modalConfirmCandidateMessagePopupClose}>
                        Cancel
                      </button>
                    </div>
                    <div className="col-8">
                      <button className="save w-100" type="submit">
                        Send
                      </button>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </PopupModal>

        <PopupModal
                show={modalUpgradePlanMessage}
                handleClose={modalUpgradePlanMessageClose}
                customclass={'    modal-lg  header-remove body-sp-0'}>
          <div className="popup-body">
            <p className="f-31 c-191919 text-left">CV Views Limit Reached</p>
            <hr className="hr-line"></hr>
            <div className="form-experience-fieild">
              <div className="text-left mt-3">
                <p>You've used all your CV views for now. Your views will reset
                  on <strong>{`${nextMonthInfo.day} ${nextMonthInfo.month}, ${nextMonthInfo.year}`}</strong></p>
                <p>To get <strong>unlimited CV views</strong> and unlock more powerful features, upgrade
                  to <strong>Enterprise</strong></p>

                <div className="row justify-content-center">
                  <div className="col-4">
                    <button
                            className="btn-a primary-size-16 btn-bg-0055BA  tab-add-sp mt-3 w-100"
                            onClick={e => selectPlan(e, 3)}>
                      <i className="fa-solid fa-bolt"></i> Upgrade to Enterprise
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </PopupModal>
      </div>
    </>
  );
}
