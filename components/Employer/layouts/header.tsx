import React, {useState, useEffect, useRef, useContext, useCallback} from 'react';
import {useRouter} from 'next/router';
import Link from 'next/link';
import moment from 'moment';
import {
  getNotifications,
  changeReadUnreadStatus,
  getReadNotifications,
  getAllCandidateHeaderSearch,
  checkAndUpdateResumesViewed,
} from '@/lib/frontendapi';
import AuthContext from '@/Context/AuthContext';
import AuthUserMenu from '@/components/Common/AuthUserMenu';
import PlanPopup from '../../../components/Common/PlanPopup';
import {Popover, Space, notification} from 'antd';
import Image from 'next/image';
import <PERSON><PERSON>rHandler from '@/lib/ErrorHandler';

 const DEMO_USER_IMAGE = `${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-1.png`

export default function Header() {
  const {user}: any = useContext(AuthContext);
  const router = useRouter();
  const anchorRef = useRef<HTMLAnchorElement | null>(null);
  const [notifications, setNotifications] = useState([]);
  const [searchInputValue, setSearchInputValue] = useState('');
  const [employeeData, setEmployeeData] = useState([]);
  const [companyCvCount, setCompanyCvCount] = useState(0);
  const [bellIconShow, setBellIconShow] = useState(false);
  const [modalConfirm, setModalConfirm] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);
  const searchRef = useRef<HTMLDivElement | null>(null);

  const showBellIconOnclick2 = (showVal: any, status: any) => {
    setBellIconShow(showVal);
    if (status == 'read') {
      const data = {
        notify_to: user?.id,
      };
      changeReadUnreadStatus(data)
        .then(res => {
          if (res.status == true) {
            console.log(res);
          } else {
          }
        })
        .catch(err => {
          ErrorHandler.showNotification(err);
        });

      getNotifications(data).then(res => {
        if (res.status == true) {
          setNotifications(res.data);
        } else {
          setNotifications([]);
        }
      });
    } else {
    }
  };
  const modalConfirmOpen = () => {
    setModalConfirm(true);
  };

  useEffect(() => {
    if (user) {
      if (user?.company) {
        setPreviewImage(user.company.logo?.source || user?.profile_image?.source || DEMO_USER_IMAGE);
      } else if (user?.company?.logo?.source) {
        setPreviewImage(user.company.logo?.source || DEMO_USER_IMAGE);
      } else {
        setPreviewImage(user?.profile_image?.source || DEMO_USER_IMAGE);
      }
    }
  }, [user]);

  useEffect(() => {
    if (user?.id) {
      if (user.company) {
        setPreviewImage(user?.company?.logo?.source);
      } else {
        setPreviewImage(`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-1.png`);
      }
      const data = {
        notify_to: user?.id,
        isRead: false,
      };
      getNotifications(data)
        .then(res => {
          setNotifications(res.data);
        })
        .catch(err => {
          console.log(err);
        });
    }
  }, [bellIconShow, user]);

  function handleSearchInputChange(event: any) {
    const value = event.target.value.trim();
    setSearchInputValue(value);
    if (value.length >= 3) {
      const data = {
        value: value,
        user_id: user?.id,
        company_id: user?.company_id,
      };
      getAllCandidateHeaderSearch(data)
        .then(response => {
          if (response.status == true) {
            setEmployeeData(response.employee);
          }
        })
        .catch(error => {
          console.error(error);
        });
    }
  }

  const handleViewCandidateProfile = (id: any) => {
    const data = {
      user_id: user?.id,
      user_role: user?.role,
      company_id: user?.company_id,
      applicant_id: id,
    };

    checkAndUpdateResumesViewed(data)
      .then(res => {
        if (res.status === true) {
          setSearchInputValue('');
          setCompanyCvCount(res.company_resume_count);
        } else {
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  const handleCvCountErrorMsg = () => {
    let msg;
    if (user?.membership === true) {
      msg = 'Hi,memebership plan has been expired. Please upgrade your plan to see more candidates';
    } else {
      msg = 'Hi, you have consumed all CV count. Please upgrade your plan to see more candidates';
    }
    notification.info({message: msg});
  };

  useEffect(() => {
    window.addEventListener('click', handleClick);
    return () => {
      window.addEventListener('click', handleClick);
    };
  });

  const handleClick = (event: MouseEvent) => {
    if (anchorRef.current && event.target instanceof Node && !anchorRef.current.contains(event.target)) {
      showBellIconOnclick2(false, '');
    }
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };
  const handleMenuItemClick = () => {
    closeMenu();
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (inputRef.current && !inputRef.current.contains(event.target as Node) && (searchRef.current && !searchRef.current.contains(event.target as Node))) {
        setSearchInputValue('');
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <>
      <header className="head-part mar-bot-75 z-value2">
        <nav className="navbar navbar-expand-lg navbar-light  fixed-top bg-fff">
          <div className="container-fluid full-width">
            <div className="logo-width">
              <Link className="navbar-brand" href="/">
                <img src={'/images/Avatars-4.png'} alt="logo" className="logo-head" loading="lazy" />
              </Link>
            </div>
            <button
              className={`navbar-toggler collapsed ${isMenuOpen ? 'active' : ''}`}
              type="button"
              data-bs-toggle="collapse"
              data-bs-target="#navbarSupportedContent"
              aria-controls="navbarSupportedContent"
              aria-expanded="false"
              aria-label="Toggle navigation"
              onClick={toggleMenu}>
              <span className="navbar-toggler-icon">
                <i className="fa-solid fa-bars"></i>
                <i className="fa-solid fa-x close-x"></i>
              </span>
            </button>

            <div className={`collapse navbar-collapse j-end ${isMenuOpen ? 'show' : ''}`} id="navbarSupportedContent">
              <form className="d-flex mobile-single tab-scroll-view tab-flex-none">
                <div className="search-in tab-none">
                  <input
                    className="form-control me-2"
                    type="search"
                    placeholder="Search candidate"
                    aria-label="Search"
                    value={searchInputValue}
                    onChange={handleSearchInputChange}
                    ref={inputRef}
                  />
                  <i className="fa-solid fa-magnifying-glass glass-ser"></i>
                  {searchInputValue && (
                    <div className="company_jobs_search" ref={searchRef}>
                      <div id="search-results" className="companysas">
                        <p className="title_heading text-start">Candidates</p>
                        <ul
                          className="list-unstyled"
                          id="company"
                          style={{height: employeeData.length >= 3 ? '150px' : 'auto'}}>
                          {employeeData.length > 0 ? (
                            employeeData.map((employee: any, index: any) => (
                              <React.Fragment key={index}>
                                {user?.membership === true && (companyCvCount != 0 || employee.applicant_id) ? (
                                  <a
                                    target="_blank"
                                    href={`/candidate-profile/${employee.slug}`}
                                    className="search_result_para 1"
                                    onClick={() => handleViewCandidateProfile(employee.user_id)}>
                                    <li>
                                      {employee.name}{' '}
                                      {employee.applicant_id && (
                                        <span className="badge bg-success float-end">
                                          Seen by{' '}
                                          {employee.viewed_user_id == user?.id
                                            ? 'me'
                                            : employee.viewed_user_name.split(' ')[0]}
                                        </span>
                                      )}
                                    </li>
                                  </a>
                                ) : (
                                  <li className="search_result_para 2" onClick={handleCvCountErrorMsg} role="button">
                                    {employee.name}{' '}
                                    {employee.applicant_id && (
                                      <span className="badge bg-success">
                                        Seen by{' '}
                                        {employee.viewed_user_id == user?.id
                                          ? 'me'
                                          : employee.viewed_user_name.split(' ')[0]}
                                      </span>
                                    )}
                                  </li>
                                )}
                              </React.Fragment>
                            ))
                          ) : (
                            <li className="search_result_para">
                              No candidate records found or enter at least three characters to get the results
                            </li>
                          )}
                        </ul>
                      </div>
                    </div>
                  )}
                </div>
              </form>

              {(user?.plan == 1 || user?.membership == false) && (
                <button className="btn signup mobile-w-100   m-r-l-sp tab-none" onClick={modalConfirmOpen}>
                  Upgrade
                </button>
              )}

              <PlanPopup show={modalConfirm} setModalConfirm={(bool: any) => setModalConfirm(bool)} />
              <div className="dask-tab-mobile-d-flex m-top-add-sp ">
                <div className="dask-none d-flex user-profile-mobile ">
                  <div className="img-box-1">
                    <img
                      src={
                       previewImage
                      }
                      alt={user?.profile_image?.name || user?.name || 'Employee profile image'}
                      className="w-40"
                    />
                  </div>
                  <div className="text-name-mobile">
                    <h4 className="name-text">{user?.name}</h4>
                    <h5 className="roll">{user?.company && user?.company?.designation}</h5>
                  </div>
                </div>
                <p className="head-icon notifications mobile-nati">
                  {bellIconShow ? (
                    <>
                      <a href="#" className="pog-r" onClick={e => showBellIconOnclick2(false, '')} ref={anchorRef}>
                        <i className="fa-solid fa-bell fill-bell"></i>
                        {notifications?.length > 0 ? <span className="round-bell"></span> : ''}
                      </a>
                      <div className="box-noti">
                        <div className="bell-box">
                          <div className="row">
                            <div className="col-7">
                              <h4 className="not-title">Notifications</h4>
                            </div>
                            <div className="col-5">
                              {notifications?.length > 0 ? (
                                <p className="mark-as" onClick={e => showBellIconOnclick2(true, 'read')}>
                                  Mark as Read
                                </p>
                              ) : (
                                ''
                              )}
                            </div>
                          </div>
                          {notifications?.length > 0 ? (
                            notifications?.slice(0, 3).map((read_notification_data: any, index: any) => {
                              return (
                                <div className="row mt-4" key={index}>
                                  <div className="col-2 pr-0">
                                    {read_notification_data.profile_image ? (
                                      <img
                                        src={`${process.env.NEXT_PUBLIC_IMAGE_URL}images/userprofileImg/${read_notification_data.profile_image}`}
                                        alt="Avatars-4"
                                        className="w-24"
                                      />
                                    ) : (
                                      <small
                                        title={read_notification_data.name}
                                        className="text-uppercase w-24 notfication_name">
                                        {read_notification_data?.name?.split(' ').length === 1
                                          ? read_notification_data.name.substring(0, 2)
                                          : read_notification_data.name
                                              ?.split(' ')
                                              ?.map((word: any, index: any) =>
                                                index === 0 || index === 1 ? word[0] : '',
                                              )
                                              .join('')}
                                      </small>
                                    )}
                                  </div>
                                  <div className="col-10 text-left pl-0">
                                    {read_notification_data.is_read === 0 ? (
                                      <>
                                        <p
                                          className="f-16 notification_font_weight"
                                          dangerouslySetInnerHTML={{__html: read_notification_data.notification}}></p>
                                      </>
                                    ) : (
                                      <>
                                        <p
                                          className="f-16"
                                          dangerouslySetInnerHTML={{__html: read_notification_data.notification}}></p>
                                      </>
                                    )}
                                    <p className="f-12">
                                      {moment
                                        .utc(read_notification_data.created_at)
                                        .local()
                                        .startOf('seconds')
                                        .fromNow()}
                                    </p>
                                  </div>
                                </div>
                              );
                            })
                          ) : (
                            <div className="text-center">
                              <img src={'/images/blank-5.png'} alt="blank-5" width="80px" />
                              <p className="f-16 c-2C2C2C mb-2">No New Notifications</p>
                              <p className="f-12 c-BABABA w-400">
                                Check this section for job updates, and general
                                <br /> notifications.{' '}
                              </p>
                            </div>
                          )}
                          <p>
                            <Link href="/employer/notifications" className="view-all">
                              View All
                            </Link>
                          </p>
                        </div>
                      </div>
                    </>
                  ) : (
                    <a href="#" onClick={e => showBellIconOnclick2(true, '')} className="pog-r">
                      <i className="fa-regular fa-bell"></i>
                      {notifications?.length > 0 ? <span className="round-bell"></span> : ''}
                    </a>
                  )}
                </p>
                <div
                  className="d-none d-md-flex"
                  style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  <Popover content={<AuthUserMenu />} trigger={['click']}>
                    <Space>
                      <img
                        // src={
                        //   user?.profile_image
                        //     ? user?.profile_image?.source
                        //     : `${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-1.png`
                        // }
                        src={previewImage}
                        alt={user?.profile_image?.name || user?.name || 'Employee profile image'}
                        className="w-32"
                        width={32}
                        height={32}
                      />
                      <i className="fa-solid fa-ellipsis-vertical" style={{cursor: 'pointer'}}></i>
                    </Space>
                  </Popover>
                </div>
              </div>
              <div className="left-bar dask-none">
                <div className="text-center mt-4">
                  <ul className="side-menu-left mt-4">
                    <li
                      className={router.pathname == '/employer/dashboard' ? 'active' : ''}
                      onClick={() => {
                        handleMenuItemClick();
                      }}>
                      <Link href="/employer/dashboard">
                        <img src={'/images/dash/icon-4a.png'} alt="icon-4a" className="icon-a" />
                        <img src={'/images/dash/icon-4.png'} alt="icon-4" className="icon-hover" />
                        Home
                      </Link>
                    </li>
                    <li
                      className={
                        router.pathname == '/employer/company' ||
                        router.pathname == '/employer/company/profile' ||
                        router.pathname == '/employer/company/followers' ||
                        router.pathname == '/employer/company/insights'
                          ? 'active'
                          : ''
                      }
                      onClick={() => {
                        handleMenuItemClick();
                      }}>
                      <Link href="/employer/company">
                        <img src={'/images/dash/icon-3a.png'} alt="icon-3a" className="icon-a" />
                        <img src={'/images/dash/icon-3.png'} alt="icon-3" className="icon-hover" />
                        Company Profile
                      </Link>
                    </li>
                    <li
                      className={
                        router.pathname == '/employer/jobs' || router.pathname == '/employer/jobs/editjobs'
                          ? 'active'
                          : ''
                      }
                      onClick={() => {
                        handleMenuItemClick();
                      }}>
                      <Link href="/employer/jobs">
                        <img src={'/images/dash/icon-5a.png'} alt="icon-5a" className="icon-a" />
                        <img src={'/images/dash/icon-5.png'} alt="icon-5" className="icon-hover" />
                        Jobs
                      </Link>
                    </li>
                    <li
                      className={router.pathname == '/employer/applicants' ? 'active' : ''}
                      onClick={() => {
                        handleMenuItemClick();
                      }}>
                      <Link href="/employer/applicants">
                        <img src={'/images/dash/icon-7a.png'} alt="icon-7a" className="icon-a" />
                        <img src={'/images/dash/icon-7.png'} alt="icon-7" className="icon-hover" />
                        Applications
                      </Link>
                    </li>
                    <li
                      className={router.pathname == '/employer/messages' ? 'active' : ''}
                      onClick={() => {
                        handleMenuItemClick();
                      }}>
                      <Link href="/employer/messages">
                        <img src={'/images/dash/icon-1a.png'} alt="icon-1a" className="icon-a" />
                        <img src={'/images/dash/icon-1.png'} alt="icon-1" className="icon-hover" />
                        Messages
                      </Link>
                    </li>
                    <li className={router.pathname == '/employer/billing/billingsummary' ? 'active' : ''}>
                      <Link href="/employer/billing/billingsummary">
                        <img src={'/images/default/dash-icon-3.svg'} alt="icon-1a" className="icon-a" />
                        <img src={'/images/selected/dash-icon-3.svg'} alt="icon-1" className="icon-hover" />
                        Billing Summary
                      </Link>
                    </li>
                    {user && (
                      <li className="nav-item">
                        <div className='d-block d-md-none'>
                          <Popover content={<AuthUserMenu />} trigger={['click']}>
                            <a
                              className={
                                router.pathname == '/auth/login'
                                  ? 'nav-link active dropdown-toggle single-menu-space'
                                  : 'nav-link dropdown-toggle single-menu-space'
                              }
                              id="dropdownMenuButton1"
                              data-bs-toggle="dropdown"
                              aria-expanded="false"
                              style={{ cursor: 'pointer' }}>
                              {user.name}
                            </a>
                          </Popover>
                        </div>
                      </li>
                    )}
                  </ul>
                  <a href="#">
                    <button className="btn-a primary-size-16 btn-bg-0055BA mr-1 tab-w-100 dask-none m-none">
                      Upgrade
                    </button>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </nav>
      </header>
    </>
  );
}
