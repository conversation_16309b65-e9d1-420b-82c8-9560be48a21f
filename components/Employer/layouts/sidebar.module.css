.sidebar_double_arrow {
  position: absolute;
  right: 16px;
  top: 16px;
  cursor: pointer;
  transition: all 0.5s;
}
.sidebar_double_arrow.closed_sidebar {
  position: relative !important;
  left: 50%;
  top: 0 !important;
  margin-top: 16px;
  margin-bottom: 20px;
  width: 20px;
  transform: translate(-50%, 0);
}
.sidebar_double_arrow.closed_sidebar img {
  transform: rotate(180deg);
}
.sidebar_double_arrow .active_arrow {
  background-color: #ebf4ff;
  border-radius: 6px;
}
.sidebar_double_arrow .active_arrow {
  display: none;
}

.sidebar_double_arrow:hover .active_arrow {
  display: block;
}

.sidebar_double_arrow:hover .inactive_arrow {
  display: none !important;
}

.left_sideBar {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  position: relative !important;
  width: auto !important;
}
.sidebar_list_active li {
  border-right: none !important;
  border-radius: 100% !important;
  height: 48px;
  width: 48px;
  background: var(--color_3) !important;
}
.sidebar_list a {
  transition: none !important;
}

.sidebar_list a:hover {
  border-right: none !important;
  border-radius: 100% !important;
  height: 48px;
  width: 48px;
}
.sidebar_list a img {
  margin-right: 0px !important;
  height: 24px !important;
  width: 24px !important;
}
.sidebar_list a i {
  margin-right: 0px !important;
  height: 24px !important;
  width: 24px !important;
  display: flex;
  justify-content: center;
  align-items: center;
}
.sidebar_list .icon-a,
.sidebar_list .icon-hover {
  width: 18px;
  height: 18px;
  transition: 0.5s;
}

.sidebar_list_active .icon-a {
  display: none !important;
}

.sidebar_list_active .icon-hover {
  display: block !important;
}
