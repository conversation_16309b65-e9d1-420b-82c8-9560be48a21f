import React, {useState, useContext, Dispatch, SetStateAction} from 'react';
import {useRouter} from 'next/router';
import Link from 'next/link';
import AuthContext from '@/Context/AuthContext';
import UserProfileImage from '@/components/Common/UserProfileImage';
import Image from 'next/image';
import styles from './sidebar.module.css';

interface SidebarProps {
  isSidebarCollapsed: boolean;
  setIsSidebarCollapsed: Dispatch<SetStateAction<boolean>>;
}

export default function Sidebar({isSidebarCollapsed, setIsSidebarCollapsed}: SidebarProps) {
  const {user} = useContext(AuthContext);
  const router = useRouter();

  const handleSideBarCollapse = () => {
    setIsSidebarCollapsed(prevState => !prevState);
  };

  return (
    <div className={`left-bar tab-none z-number-9 ${isSidebarCollapsed && styles.left_sideBar}`}>
      <div className="text-center mt-4">
        <div
          className={`${styles['sidebar_double_arrow']} ${isSidebarCollapsed && styles['closed_sidebar']}`}
          onClick={handleSideBarCollapse}>
          <img src={'/icons/double_arrow.svg'} alt="Expand sidebar" width={20} height={20} />
        </div>
        <div className="dash-profile-img mb-2 m-auto">
          <div className="pro-diamond">
            <UserProfileImage
              user={user}
              showModel={false}
              isSidebarCollapsed={isSidebarCollapsed}
              width={isSidebarCollapsed ? 56 : 118}
              height={isSidebarCollapsed ? 56 : 118}
              company={user?.company}
            />
          </div>
        </div>

        {!isSidebarCollapsed && (
          <>
            <h4 className="name-text">
              {user?.company &&
                user?.company.company_name.charAt(0).toUpperCase() + user?.company.company_name.slice(1)}
            </h4>
            <h5 className="roll">{user?.name}</h5>
            <p className="f-16 c-999999">
              {user?.company?.designation &&
                user?.company.designation.charAt(0).toUpperCase() + user?.company.designation.slice(1)}
            </p>
          </>
        )}
        <ul className="side-menu-left mt-4 tab-none">
          <SidebarLink
            href="/employer/dashboard"
            iconDefault="/images/default/dash-icon-1.svg"
            iconActive="/images/selected/dash-icon-1.svg"
            label="Home"
            isActive={router.pathname === '/employer/dashboard'}
            isCollapsed={isSidebarCollapsed}
          />
          <SidebarLink
            href="/employer/company"
            iconDefault="/images/default/dash-icon-2.svg"
            iconActive="/images/selected/dash-icon-2.svg"
            label="Company Profile"
            isActive={
              router.pathname === '/employer/company' ||
              router.pathname === '/employer/company/profile' ||
              router.pathname === '/employer/company/followers' ||
              router.pathname === '/employer/company/insights'
            }
            isCollapsed={isSidebarCollapsed}
          />
          <SidebarLink
            href="/employer/jobs"
            iconDefault="/images/default/dash-icon-4.svg"
            iconActive="/images/selected/dash-icon-4.svg"
            label="Jobs"
            isActive={router.pathname === '/employer/jobs' || router.pathname === '/employer/jobs/editjobs'}
            isCollapsed={isSidebarCollapsed}
          />
          <SidebarLink
            href="/employer/candidates"
            iconDefault="/images/default/search.svg"
            iconActive="/images/selected/Searchfill.svg"
            label="Resume Search"
            isActive={router.pathname === '/employer/candidates'}
            isCollapsed={isSidebarCollapsed}
          />
          <SidebarLink
            href="/employer/applicants"
            iconDefault="/images/default/dash-icon-5.svg"
            iconActive="/images/selected/dash-icon-5.svg"
            label="Applicants"
            isActive={router.pathname === '/employer/applicants'}
            isCollapsed={isSidebarCollapsed}
          />
          <SidebarLink
            href="/employer/messages"
            iconDefault="/images/default/dash-icon-6.svg"
            iconActive="/images/selected/dash-icon-6.svg"
            label="Messages"
            isActive={
              router.pathname === '/employer/messages' ||
              router.pathname === '/employer/messages/inbox/[id]' ||
              router.pathname === '/employer/messages/interviews' ||
              router.pathname === '/employer/messages/archived'
            }
            isCollapsed={isSidebarCollapsed}
          />
          <SidebarLink
            href="/employer/billing/billingsummary"
            iconDefault="/images/default/dash-icon-3.svg"
            iconActive="/images/selected/dash-icon-3.svg"
            label="Billing Summary"
            isActive={router.pathname === '/employer/billing/billingsummary'}
            isCollapsed={isSidebarCollapsed}
          />
        </ul>
      </div>
    </div>
  );
}

interface SidebarLinkProps {
  href: string;
  iconDefault: string;
  iconActive: string;
  label: string;
  isActive: boolean;
  isCollapsed: boolean;
}

function SidebarLink({href, iconDefault, iconActive, label, isActive, isCollapsed}: SidebarLinkProps) {
  return (
    <li className={`${isActive ? 'active' : ''} ${isCollapsed && styles.sidebar_list}`}>
      <Link href={href}>
        <img src={iconDefault} alt={`${label} icon`} className="icon-a" />
        <img src={iconActive} alt={`${label} icon hover`} className="icon-hover" />
        {!isCollapsed && label}
      </Link>
    </li>
  );
}
