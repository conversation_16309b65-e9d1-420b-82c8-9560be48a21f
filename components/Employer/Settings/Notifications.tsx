import React, {useState, useEffect, useContext} from 'react';
import {getSettings, createUserSettings} from '../../../lib/frontendapi';
import Link from 'next/link';
import {Switch, message} from 'antd';
import AuthContext from '@/Context/AuthContext';

export default function Notifications() {
  const {user} = useContext(AuthContext);
  const [settings, setSettings] = useState<any>([]);

  const userSettings = () => {
    if (user) {
      const data = {
        user_id: user?.id,
      };
      getSettings(data)
        .then(res => {
          if (res.status == true) {
            setSettings(res.data);
          }
        })
        .catch(err => {
          console.log(err);
        });
    }
  };

  useEffect(() => {
    userSettings();
  }, [user]);

  const submitSettings = (value: boolean, name: string) => {
    const data = {
      id: user?.id,
      [name]: value === true ? 1 : 0,
    };
    createUserSettings(data).then(res => {
      if (res.success) message.success('Saved.');
      else message.error('Failed!');
      userSettings();
    });
  };

  return (
    <>
      <div className="dash-right">
        <h1>Settings </h1>
        <div className="row ">
          <div className="col-sm-12">
            <ul className="list-loc m-m-0 mt-4">
              <li>
                <Link href="/employer/settings">Account</Link>
              </li>
              <li>
                <Link href="/employer/settings/changepassword">Password </Link>
              </li>
              <li className="active">
                <Link href="/employer/settings/notifications">Notification </Link>
              </li>
            </ul>
          </div>
        </div>
        <div className="data-management m-p-10 p-0">
          <div className="work-experience-fieild m-p-10">
            <div className="row">
              <div className="col-sm-12">
                <form className="form-experience-fieild">
                  <div className="row">
                    <div className="col-sm-10 col-8">
                      <p className="f-22 c-2C2C2C w-500 mb-1">Account Settings:</p>
                      <p className="f-16 c-747474">Receive account-related notifications and updates via email.</p>
                    </div>
                    <div className="col-sm-2 col-4 text-right">
                      <Switch
                        onClick={value => submitSettings(value, 'account_access')}
                        checked={settings?.account_access === 1 ? true : false}
                      />
                    </div>
                  </div>
                  <div className="row mt-4">
                    <div className="col-sm-10 col-8">
                      <p className="f-22 c-2C2C2C w-500 mb-1">Newsletter Preferences:</p>
                      <p className="f-16 c-747474">
                        Subscribe to our newsletter to receive the latest updates, news, and promotions.
                      </p>
                    </div>
                    <div className="col-sm-2 col-4 text-right">
                      <Switch
                        onClick={value => submitSettings(value, 'newsletter_access')}
                        checked={settings?.newsletter_access === 1 ? true : false}
                      />
                    </div>
                  </div>
                  <div className="row mt-4">
                    <div className="col-sm-10 col-8">
                      <p className="f-22 c-2C2C2C w-500 mb-1">Recommendations:</p>
                      <p className="f-16 c-747474">
                        Allow us to provide you with personalized recommendations based on your usage.
                      </p>
                    </div>
                    <div className="col-sm-2 col-4 text-right">
                      <Switch
                        onClick={value => submitSettings(value, 'recommendations_access')}
                        checked={settings?.recommendations_access === 1 ? true : false}
                      />
                    </div>
                  </div>
                  <div className="row mt-4">
                    <div className="col-sm-10 col-8">
                      <p className="f-22 c-2C2C2C w-500 mb-1">Announcements:</p>
                      <p className="f-16 c-747474">
                        Stay informed about important announcements, new features, and improvements.
                      </p>
                    </div>
                    <div className="col-sm-2 col-4 text-right">
                      <Switch
                        onClick={value => submitSettings(value, 'announcements_access')}
                        checked={settings?.announcements_access === 1 ? true : false}
                      />
                    </div>
                  </div>
                  <div className="row mt-3">
                    <div className="col-sm-10 col-8">
                      <p className="f-22 c-2C2C2C w-500 mb-1">Message from Candidates:</p>
                      <p className="f-16 c-747474">
                        Opt-in to receive messages from recruiters about potential job opportunities.
                      </p>
                    </div>
                    <div className="col-sm-2 col-4 text-right">
                      <Switch
                        onClick={value => submitSettings(value, 'message_from_candidate_access')}
                        checked={settings?.message_from_candidate_access === 1 ? true : false}
                      />
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
