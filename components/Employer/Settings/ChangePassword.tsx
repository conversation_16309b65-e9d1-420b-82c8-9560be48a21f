import React, {useContext, useState} from 'react';
import Link from 'next/link';
import {Button, Col, Form, Input, Row, notification} from 'antd';
const {useForm} = Form;
import AuthContext from '@/Context/AuthContext';
import {changepassword} from '@/lib/frontendapi';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '@/lib/ErrorHandler';
import PasswordStrengthBar from 'react-password-strength-bar';

export default function ChangePassword() {
  const {user} = useContext(AuthContext);
  const [password, setPassword] = useState('');
  const [passwordScore, setPasswordScore] = useState<number>(0);
  const [form] = Form.useForm();

  const submitForm = (data: any) => {
    if (passwordScore < 4) {
      notification.info({
        message: 'Your password strength could be stronger.',
        description: `For a more secure account, consider using a longer password with a mix of uppercase and lowercase letters, numbers, and special characters.`,
      });
      return;
    }

    const password_data = {
      ...data,
      user_id: user?.id,
    };
    changepassword(password_data)
      .then(res => {
        if (res.status == true) notification.success({message: res.message});
        else notification.error({message: res.message});
        form.resetFields();
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  };

  return (
    <>
      <div className="dash-right">
        <h1>Settings </h1>
        <div className="row ">
          <div className="col-sm-12">
            <ul className="list-loc m-m-0 mt-4">
              <li>
                <Link href="/employer/settings">Account</Link>
              </li>
              <li className="active">
                <Link href="/employer/settings/changepassword">Password </Link>
              </li>
              <li>
                <Link href="/employer/settings/notifications">Notification </Link>
              </li>
            </ul>
          </div>
        </div>
        <div className="data-management m-p-10 p-0">
          <div className="work-experience-fieild m-p-10">
            <p className="f-12 c-2C2C2C m-center w-700">RESET PASSWORD</p>
            <div className="row">
              <Form layout="vertical" form={form} size="large" scrollToFirstError onFinish={submitForm}>
                <Row>
                  <Col md={10}>
                    <Form.Item name={'currentPassword'} label={'Current Password'} rules={[{required: true}]}>
                      <Input.Password placeholder="Current Password" />
                    </Form.Item>
                  </Col>
                  <Col md={24}></Col>
                  <Col md={10}>
                    <Form.Item
                      name={'newPassword'}
                      label={'New Password'}
                      rules={[
                        {
                          required: true,
                          message: 'Please input your password!',
                        },
                      ]}
                      hasFeedback>
                      <Input.Password placeholder="New Password" onChange={(e: any) => setPassword(e.target.value)} />
                    </Form.Item>
                    <PasswordStrengthBar password={password} onChangeScore={score => setPasswordScore(score)} />
                  </Col>
                  <Col md={24}></Col>
                  <Col md={10}>
                    <Form.Item
                      name={'retypePassword'}
                      label={'Re-type Password'}
                      dependencies={['newPassword']}
                      hasFeedback
                      rules={[
                        {
                          required: true,
                          message: 'Please confirm your password!',
                        },
                        ({getFieldValue}) => ({
                          validator(_, value) {
                            if (!value || getFieldValue('newPassword') === value) {
                              return Promise.resolve();
                            }
                            return Promise.reject(new Error('The new password that you entered do not match!'));
                          },
                        }),
                      ]}>
                      <Input.Password placeholder="Re-type Password" />
                    </Form.Item>
                  </Col>
                  <Col md={24} className="d-flex gap-2 justify-content-end">
                    <Form.Item>
                      <Button htmlType="reset" type="default">
                        Cancel
                      </Button>
                    </Form.Item>
                    <Form.Item>
                      <Button htmlType="submit" type="primary">
                        Save
                      </Button>
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
