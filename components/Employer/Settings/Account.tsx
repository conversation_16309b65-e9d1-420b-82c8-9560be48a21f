import React, { useContext } from 'react';
import { updateUserDetails } from '../../../lib/frontendapi';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import UserProfileImage from '@/components/Common/UserProfileImage';
import AuthContext from '@/Context/AuthContext';
import { Button, Col, Form, Input, Row, Switch, message, notification } from 'antd';
import <PERSON><PERSON><PERSON><PERSON>and<PERSON> from '@/lib/ErrorHandler';

export default function Account() {
  const { user } = useContext(AuthContext);
  const { data: session }: any = useSession();

  const submitForm = (data: any) => {
    updateUserDetails(user?.id, data)
      .then(res => {
        if (res.status) message.success('Saved');
        // else
        // notification.error({message: res.message});
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  };

  return (
    <>
      <div className="dash-right">
        <h1>Settings </h1>
        <div className="row ">
          <div className="col-sm-12">
            <ul className="list-loc m-m-0 mt-4">
              <li className="active">
                <Link href="/employer/settings">Account</Link>
              </li>
              {session ? (
                ''
              ) : (
                <li>
                  <Link href="/employer/settings/changepassword">Password </Link>
                </li>
              )}
              <li>
                <Link href="/employer/settings/notifications">Notification </Link>
              </li>
            </ul>
          </div>
        </div>
        <div className="data-management m-p-10 p-0">
          <div className="work-experience-fieild m-p-10">
            <div className="row align-items-left">
              <div className="col-lg-12 col-md-3 col-12">
                <div className="mb-4 m-auto z-vel">
                  <p className="f-12 c-2C2C2C m-center">Profile Picture</p>
                  <UserProfileImage
                    user={user}
                    showUploadButton={true}
                    className="mxw-unset" />
                </div>
              </div>
            </div>
            <Form layout="vertical" size="large" onFinish={submitForm}>
              <Row>
                <Col md={10}>
                  <Form.Item
                    name={'email'}
                    initialValue={user?.email}
                    label={'Email ID'}
                    rules={[{ required: true, type: 'email' }]}>
                    <Input />
                  </Form.Item>
                </Col>
                <Col md={24}></Col>
                <Col md={10}>
                  <Form.Item
                    name={'name'}
                    initialValue={user?.name}
                    label={'Name'}
                    rules={[{ required: true, type: 'string' }]}>
                    <Input />
                  </Form.Item>
                </Col>
                <Col md={24}>
                  <div className="row">
                    <div className="col-sm-10 col-8">
                      <p className="f-22 c-2C2C2C w-500 mb-1">Two-Factor Authentication (2FA)</p>
                      <p className="f-16 c-747474">
                        2FA adds an extra layer of protection, making it more challenging for hackers to compromise user
                        accounts.
                      </p>
                    </div>
                    <div className="col-sm-2 col-4 text-right">
                      <Form.Item name={'is2FA'} valuePropName="checked">
                        <Switch value={user?.is2FA} />
                      </Form.Item>
                    </div>
                  </div>
                </Col>
                <Col md={24} className="text-end">
                  <Form.Item>
                    <Button htmlType="submit" type="primary">
                      Save
                    </Button>
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </div>
        </div>
      </div>
    </>
  );
}
