import React, { useState, useEffect } from "react";
import { getUserAllPaymentDetails, getStripePlanDetails, getCurrentUserPaymentDetails, getSingleUserDetails, updateUserCardDetails, getLastPaymentDetails } from '../../../lib/frontendapi';
import PopupModal from '../../../components/Common/PopupModal';
import { getCurrentUserData } from "../../../lib/session";
import Pagination from "../../../components/Common/Pagination";
import { paginate } from "../../../helpers/paginate";
import moment from "moment";
import SuccessToast from "../../Common/showSuccessTostrMessage";
import ErrorToast from "../../Common/showErrorTostrMessage";
import Image from "next/image";

export default function BillingHistory() {
  const [paymentTransaction, setPaymentTransaction] = useState([]);
  const [stripePlanId, setStripePlanId] = useState('');
  const [stripePlanDetails, setStripePlanDetails] = useState([]);
  const [userData, SetUserData]: any = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPaymentTransaction, setTotalPaymentTransaction] = useState([]);
  const [modalConfirmUpdateCardDetails, setModalConfirmUpdateCardDetails] = useState(false);
  const [cardNumber, setCardNumber] = useState('');
  const [cvc, setCvc] = useState('');
  const [cardError, setCardError] = useState('');
  const [cvcError, setCvcError] = useState('');
  const [expiryMonth, setExpiryMonth] = useState('');
  const [expiryYear, setExpiryYear] = useState('');
  const [expiry, setExpiry] = useState('');
  const [expiryDateError, setExpiryDateError] = useState('');
  const [expiryMonthError, setExpiryMonthError] = useState('');
  const [lastPaymentData, setLastPaymentData]: any = useState([]);
  const [filterStartDate, setFilterStartDate] = useState('');
  const [filterEndDate, setFilterEndDate] = useState('');

  const [filterStartDateError, setFilterStartDateError] = useState('');
  const [filterEndDateError, setFilterEndDateError] = useState('');

  const [showPopupunsave, setShowPopupunsave] = useState(false);
  const [showPopupunsave1, setShowPopupunsave1] = useState(false);
  const [showmessage, setShowmessage] = useState('');

  const pageSize = 5;

  const onPageChange = (page: any) => {
    const current_user_data: any = getCurrentUserData();
    setCurrentPage(page);
    const data = {
      filterStartDate: '',
      filterEndDate: ''
    }
    getUserAllPaymentDetails(current_user_data.id, data)
      .then(res => {
        if (res.status == true) {
          setTotalPaymentTransaction(res.data);
          const paginatedPosts = paginate(res.data, page, pageSize);
          setPaymentTransaction(paginatedPosts);
        } else {
          setPaymentTransaction([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  useEffect(() => {
    const current_user_data: any = getCurrentUserData();
    const data = {
      filterStartDate: '',
      filterEndDate: ''
    }
    getUserAllPaymentDetails(current_user_data.id, data)
      .then(res => {
        if (res.status == true) {
          setTotalPaymentTransaction(res.data);
          const paginatedPosts = paginate(res.data, currentPage, pageSize);
          setPaymentTransaction(paginatedPosts);
        } else {
          setPaymentTransaction([]);
        }
      })
      .catch(err => {
        setShowmessage(err);
        setShowPopupunsave1(true);
        setTimeout(() => {
          setShowPopupunsave1(false)
        }, 10000)
      })
  }, []);
  useEffect(() => {
    const current_user_data: any = getCurrentUserData();
    getUserAllPaymentDetails(current_user_data.id)
      .then(res => {
        if (res.status == true) {
          setTotalPaymentTransaction(res.data);
          const paginatedPosts = paginate(res.data, currentPage, pageSize);
          setPaymentTransaction(paginatedPosts);
        } else {
          setPaymentTransaction([]);
        }
      })
      .catch(err => {
        setShowmessage(err);
        setShowPopupunsave1(true);
        setTimeout(() => {
          setShowPopupunsave1(false)
        }, 10000)
      });
    getCurrentUserPaymentDetails(current_user_data.id)
      .then(res => {
        if (res.status == true) {
          refreshStripePlanDetails(res.data.stripe_plan_id);
        } else {
        }
      })
      .catch(err => {
        console.log(err);
      });
    getSingleUserDetails(current_user_data.id)
      .then(res => {
        if (res.status === true) {
          SetUserData(res.user);
          setCardNumber(res.user.card_number);
          setExpiryMonth(res.user.card_exp_month);
          setExpiryYear(res.user.card_exp_year);
          setCvc(res.user.card_cvv);
        } else {
          SetUserData([]);
          setCardNumber('');
          setExpiryMonth('');
          setExpiryYear('');
          setCvc('');
        }
      })
      .catch(err => {
        console.log(err);
      });
    const payment_data = {
      user_id: current_user_data.id
    }
    getLastPaymentDetails(payment_data)
      .then(res => {
        if (res.status === true) {
          setLastPaymentData(res.data);
        } else {
          setLastPaymentData([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  }, []);
  const refreshStripePlanDetails = (stripe_plan_id: any) => {
    const data = {
      stripe_plan_id: stripe_plan_id
    }
    getStripePlanDetails(data)
      .then(res => {
        if (res.status == true) {
          setStripePlanDetails(res.data);
        } else {
          setStripePlanDetails([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  }
  const modalConfirmUpdateCardDetailsPopupOpen = () => {
    setModalConfirmUpdateCardDetails(true);
  }
  const modalConfirmUpdateCardDetailsPopupClose = () => {
    setModalConfirmUpdateCardDetails(false);
  }
  const handleCardNumberChange = (event: any) => {
    setCardNumber(event.target.value);
  };
  const handleExpiryMonthChange = (event: any) => {
    const value = event.target.value;
    setExpiryMonth(value);
    if (/^((0[1-9])|(1[0-2]))$/.test(value)) {
      setExpiryMonthError('');
    } else {
      setExpiryMonthError('Invalid expiry month');
    }
  };
  const handleExpiryYearChange = (event: any) => {
    const value = event.target.value;
    setExpiryYear(value);
    if (/^\d{4}$/.test(value)) {
      setExpiryDateError('');
    } else {
      setExpiryDateError('Invalid expiry date');
    }
  };
  const handleCvcChange = (event: any) => {
    const value = event.target.value;
    setCvc(value);
    if (/^\d{3}$/.test(value)) {
      setCvcError('');
    } else {
      setCvcError('Invalid CVC');
    }
  };
  const handleSubmit = (event: any) => {
    const current_user_data: any = getCurrentUserData();
    event.preventDefault();
    if (!cardNumber) {
      setCardError('Please enter your card number');
    } else if (!/^\d{16}$/.test(cardNumber)) {
      setCardError('Invalid card number');
    } else {
      setCardError('');
    }
    if (!expiryMonth || !expiryYear) {
      setExpiryDateError('Please enter your card expiry date');
      setExpiryMonthError('Please enter your card expiry month');
    } else if (!/^((0[1-9])|(1[0-2]))$/.test(expiryMonth)) {
      setExpiryMonthError('Invalid expiry month');
      setExpiryDateError('');
    } else if (!/^\d{4}$/.test(expiryYear)) {
      setExpiryMonthError('');
      setExpiryDateError('Invalid expiry year');
    } else {
      setExpiryMonthError('');
      setExpiryDateError('');
    }
    if (!cvc) {
      setCvcError('Please enter your card security code (CVC)');
    } else if (!/^\d{3}$/.test(cvc)) {
      setCvcError('Invalid CVC');
    } else {
      setCvcError('');
    }
    if (cardNumber && cvc && expiryMonth && expiryYear && !cardError && !cvcError && !expiryDateError && !expiryMonthError) {
      // visa
      let card_type = "";
      var re = new RegExp("^4");
      if (cardNumber.match(re) != null) {
        card_type = "Visa";
      }
      // Mastercard
      // Updated for Mastercard 2017 BINs expansion
      if (/^(5[1-5][0-9]{14}|2(22[1-9][0-9]{12}|2[3-9][0-9]{13}|[3-6][0-9]{14}|7[0-1][0-9]{13}|720[0-9]{12}))$/.test(cardNumber)) {
        card_type = "Mastercard";
      }
      // AMEX
      re = new RegExp("^3[47]");
      if (cardNumber.match(re) != null) {
        card_type = "AMEX";
      }
      // Discover
      re = new RegExp("^(6011|622(12[6-9]|1[3-9][0-9]|[2-8][0-9]{2}|9[0-1][0-9]|92[0-5]|64[4-9])|65)");
      if (cardNumber.match(re) != null) {
        card_type = "Discover";
      }
      // Diners
      re = new RegExp("^36");
      if (cardNumber.match(re) != null) {
        card_type = "Diners";
      }
      // Diners - Carte Blanche
      re = new RegExp("^30[0-5]");
      if (cardNumber.match(re) != null) {
        card_type = "Diners - Carte Blanche";
      }
      // JCB
      re = new RegExp("^35(2[89]|[3-8][0-9])");
      if (cardNumber.match(re) != null) {
        card_type = "JCB";
      }
      // Visa Electron
      re = new RegExp("^(4026|417500|4508|4844|491(3|7))");
      if (cardNumber.match(re) != null) {
        card_type = "Visa Electron";
      }

      const data = {
        card_number: cardNumber,
        card_exp_month: expiryMonth,
        card_exp_year: expiryYear,
        card_cvv: cvc,
        card_type: card_type
      }
      updateUserCardDetails(current_user_data.id, data)
        .then(res => {
          if (res.status == true) {
            setShowmessage(res.message);
            setShowPopupunsave(true);
            setTimeout(() => {
              setShowPopupunsave(false)
            }, 1000)

          } else {
            setShowmessage(res.message);
            setShowPopupunsave1(true);
            setTimeout(() => {
              setShowPopupunsave1(false)
            }, 1000)
          }
        })
        .catch(err => {
          console.log(err);
        });
    }
  }
  function addZeroes(num: any) {
    const dec = num.split('.')[1]
    const len = dec && dec.length > 2 ? dec.length : 2
    return Number(num).toFixed(len)
  }
  const handleSubmitFilter = (event: any) => {
    event.preventDefault();
    const current_user_data: any = getCurrentUserData();
    if (filterStartDate > filterEndDate) {
      setFilterEndDateError('please select end date more than of start date.');
    } else if (filterStartDate == '') {
      setFilterStartDateError('please select start date');
    } else if (filterEndDate == '') {
      setFilterEndDateError('please select end date');
    } else {
      setFilterStartDateError('');
      setFilterEndDateError('');
      const data = {
        filterStartDate: filterStartDate,
        filterEndDate: filterEndDate
      }
      getUserAllPaymentDetails(current_user_data.id, data)
        .then(res => {
          if (res.status == true) {
            setTotalPaymentTransaction(res.data);
            const paginatedPosts = paginate(res.data, currentPage, pageSize);
            setPaymentTransaction(paginatedPosts);
          } else {
            setPaymentTransaction([]);
          }
        })
        .catch(err => {
          setShowmessage(err);
          setShowPopupunsave1(true);
          setTimeout(() => {
            setShowPopupunsave1(false)
          }, 10000)
        });
    }
  }
  return (
    <>
      <div className='dash-right'>
        <div className="row">
          <h1 className='f-45 c-191919 w-500'>Billing History</h1>
          <div className='row'>
            <div className='col-lg-7'>
              <div className='card-box p-4'>
                <p className='f-18 c-2C2C2C w-600 mb-2'>Payment Due</p>
                <p className='f-16 c-4D4D4D w-400'>Your payment method will be charged by the due date.</p>
                <div className='row mt-5'>
                  <div className='col-sm-9'>
                    <p className='c-0055BA w-500 f-26 mb-2'>{lastPaymentData.plan_name}</p>
                    <p className='f-18 c-4D4D4D w-400  mt-3'>{lastPaymentData.expire_at ? moment(lastPaymentData.expire_at).format("MM/DD/YYYY") : ''}</p>
                  </div>
                  <div className='col-sm-3'>
                    {/* <p className='f-22 c-4D4D4D w-500 mt-3 '>AED 255.<small className='f-16 w-600'>00</small></p> */}
                    {/* <p className='f-22 c-4D4D4D w-500 mt-3 '>AED {lastPaymentData.amount ? addZeroes(new Intl.NumberFormat('ar-AE').format(lastPaymentData.amount/100)) : ''}</p> */}
                    <p className='f-22 c-4D4D4D w-500 mt-3 '><span style={{ "textTransform": "uppercase" }}>{lastPaymentData.currency}</span> {lastPaymentData.amount}</p>
                  </div>
                </div>
              </div>
            </div>
            <div className='col-lg-5'>
              <div className='card-box  p-4  '>
                <div className='row'>
                  <div className='col-sm-9'>
                    <p className='f-18 c-2C2C2C w-600 mb-2'>Payment Method</p>
                    <p className='f-16 c-4D4D4D w-400'>
                      {userData.card_type == 'Visa'
                        ?
                        <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/visa-2.png'} alt="visa-2" className='w-60' />
                        :
                        ''
                      }
                      &nbsp; {userData.card_type} ending in {userData.card_number ? userData.card_number.substr(-4) : ''}
                    </p>
                  </div>
                  <div className='col-sm-3 text-right'>
                    <p><a href="#" className='c-0070F5 f-12 w-700' onClick={modalConfirmUpdateCardDetailsPopupOpen}>Edit</a></p>
                  </div>
                </div>
                <div className='row mt-5'>
                  <div className='col-sm-5'>
                    <p className='f-18 c-2C2C2C w-600 mb-2'>Currency</p>
                    <p className='f-16 c-4D4D4D w-400'>AED</p>
                  </div>
                  <div className='col-sm-7 text-right m-text-left m-top-sp'>
                    <p className='f-18 c-2C2C2C w-600 mb-2'>Invoice Recipient</p>
                    <p className='f-16 c-4D4D4D w-400'>{userData.email}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className='row'>
            <div className='col-sm-12'>
              <div className='card-box p-4 h-410'>
                <div className='row'>
                  <div className='col-sm-6'>
                    <p className='f-22 c-191919 w-500'>Transaction History</p>
                    <ul className="list-loc m-0 mt-3">
                      <li className="active" ><a href="#" className='c-0070F5 f-12 p-0 pb-2' >Tab</a></li>
                    </ul>
                  </div>
                  <div className='col-sm-6'>
                    <form onSubmit={handleSubmitFilter}>
                      <div className='row'>
                        <div className='col-sm-4'>
                          <div className='form_field_sec'>
                            <input type='date' className='date-viza w-100 b-r-8 p-2' onChange={(e: any) => setFilterStartDate(e.target.value)} />
                            <label className='f-12 c-2C2C2C w-400 mar-b-1'>From</label><br />
                          </div>
                          {filterStartDateError && <span className="error text-danger">{filterStartDateError}</span>}
                        </div>
                        <div className='col-sm-4'>
                          <div className='form_field_sec'>
                            <input type='date' className='date-viza w-100 b-r-8 p-2' onChange={(e: any) => setFilterEndDate(e.target.value)} />
                            <label className='f-12 c-2C2C2C w-400 mar-b-1'>To</label><br />
                          </div>
                          {filterEndDateError && <span className="error text-danger">{filterEndDateError}</span>}
                        </div>
                        <div className='col-sm-4'>
                          <button className="btn-a primary-size-16 b-4 btn-bg-0055BA b-r-8 mar-top4 mt-4" type="submit">Update</button>
                        </div>
                      </div>
                    </form>
                  </div>
                  <div className='pay-due'>
                    <table>
                      <thead>
                        <tr>
                          <th scope="col" >Due Date</th>
                          <th scope="col" className='w-60-par'>Activity</th>
                          <th scope="col" className='text-right'>Amount <i className="fa-solid fa-circle-info c-0055BA"></i></th>
                          <th scope="col" className='text-right'>Balance <i className="fa-solid fa-circle-info c-0055BA"></i></th>
                        </tr>
                      </thead>
                      <tbody>
                        {paymentTransaction.length > 0
                          ?
                          paymentTransaction.map((payment_tansaction: any, index: any) => {
                            return (
                              // <tr key={index}>
                              //   <td data-label="Account">{moment.unix(payment_tansaction.current_period_start).format("MM/DD/YYYY")}</td>
                              //   <td data-label="Due Date">Your {payment_tansaction.plan_type == 'Year' ? 'Yearly' : 'Monthly' } Plan Payment Successfull!</td>
                              //   <td data-label="Amount" className='text-right f-16 '>AED {addZeroes(new Intl.NumberFormat('ar-AE').format(payment_tansaction.amount/100))}  </td>
                              //   <td data-label="Amount" className='text-right f-16 '>AED {addZeroes(new Intl.NumberFormat('ar-AE').format(payment_tansaction.amount/100))} </td>
                              // </tr>
                              <tr key={index}>
                                <td data-label="Account">{payment_tansaction.purchase_at ? moment(payment_tansaction.purchase_at).format("MM/DD/YYYY") : ''}</td>
                                <td data-label="Due Date">Your {payment_tansaction.plan_type} Plan Payment Successfull!</td>
                                <td data-label="Amount" className='text-right f-16 '><span style={{ "textTransform": "uppercase" }}>{lastPaymentData.currency}</span> {payment_tansaction.amount}  </td>
                                <td data-label="Amount" className='text-right f-16 '><span style={{ "textTransform": "uppercase" }}>{lastPaymentData.currency}</span> {payment_tansaction.amount} </td>
                              </tr>
                            )
                          })

                          :
                          <tr><td colSpan={6} style={{ "textAlign": "center" }}>No Any Transactions Found</td></tr>
                        }
                      </tbody>
                    </table>
                    <Pagination
                      items={totalPaymentTransaction.length}
                      currentPage={currentPage}
                      pageSize={pageSize}
                      onPageChange={onPageChange}
                    />
                    {/* <nav aria-label="Page navigation example">
                      <ul className="pagination justify-content-center mt-4">
                        <li className="page-item active"><a className="page-link" href="#">1</a></li>
                        <li className="page-item"><a className="page-link" href="#">2</a></li>
                        <li className="page-item"><a className="page-link" href="#">3</a></li>
                        <li className="page-item"><a className="page-link" href="#">4</a></li>
                        <li className="page-item">
                          <a className="page-link" href="#"><i className="fa-solid fa-angle-right"></i></a>
                        </li>
                      </ul>
                    </nav> */}
                    <PopupModal show={modalConfirmUpdateCardDetails} handleClose={modalConfirmUpdateCardDetailsPopupClose} customclass={' modal-dialog-centered   modal-sm body-sp-0'} closebtnclass={'close-x  bg-0055BA border-design'} closebtnicon={'icon'}>
                      <div className="popup-body">
                        <form className="pb-2 stripe_form" onSubmit={handleSubmit}>
                          <div className="row">
                            <div className="col-12 mb-3">
                              <div className='form_field_sec'>
                                <input type="text" className="form-control" value={cardNumber} onChange={handleCardNumberChange} placeholder='Enter Card Number' maxLength={16} />
                                <label>Card Number*</label>
                              </div>
                              {cardError && <span className="error text-danger">{cardError}</span>}
                            </div>
                            <div className="col-6 mb-3">
                              <div className='form_field_sec'>
                                <input type="text" className="form-control" id="expiryMonth" value={expiryMonth} onChange={handleExpiryMonthChange} placeholder='MM' maxLength={2} />
                                <label>Card Expire Month*</label>
                              </div>
                              {expiryMonthError && <span className="error text-danger">{expiryMonthError}</span>}
                            </div>
                            <div className="col-6 mb-3">
                              <div className='form_field_sec'>
                                <input type="text" className="form-control" id="expiryYear" value={expiryYear} onChange={handleExpiryYearChange} placeholder='YYYY' maxLength={4} />
                                <label>Card Expire Year*</label>
                              </div>
                              {expiryDateError && <span className="error text-danger">{expiryDateError}</span>}
                            </div>
                            <div className="col mb-4">
                              <div className='form_field_sec'>
                                <input type="text" className="form-control" value={cvc} onChange={handleCvcChange} placeholder='ex. 311' maxLength={3} />
                                <label>Card CVV*</label>
                              </div>
                              {cvcError && <span className="error text-danger">{cvcError}</span>}
                            </div>
                            <div className='col-md-12 mb-3 text-right'>
                              <button className="btn signup mobile-w-100 btn-submit stripe_form_btn m-0" type="submit" style={{ "width": "100%" }}>
                                Update
                              </button>
                            </div>
                          </div>
                        </form>
                      </div>
                    </PopupModal>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {showPopupunsave &&
          <SuccessToast message={showmessage} />
        }
        {showPopupunsave1 &&
          <ErrorToast message={showmessage} />
        }
      </div>
    </>
  )
}
