import React, { useState, useEffect, useContext } from 'react';
import PopupModal from '../../../components/Common/PopupModal';
import { getAllCandidateSearch, getAllSectors, getAllCountries, getAllCandidateFilter, UpdateSaveCandidateFilterData, DeleteCandidateFilter, getAllSkills, checkAndUpdateResumesViewed } from '../../../lib/frontendapi';
import Pagination from "../../../components/Common/Pagination";
import { paginate } from "../../../helpers/paginate";
import swal from "sweetalert";
import ModalForm from '@/components/Common/ModalForm';
import { Col, Form, Input, Row, Select, notification } from 'antd';
import AuthContext from '@/Context/AuthContext';
import Image from 'next/image';

interface Job {
  monthly_fixed_salary_currency: string;
  monthly_fixed_salary_max: string;
}

interface Country {
  id?: number;
  country_name?: string;
  status?: string;
}

interface Sector {
  id?: number;
  sector_name?: string;
}

interface Skill {
  id?: number;
  skills?: string;
}


const experienceRanges = [
  { id: 0, label: 'Choose Experience' },
  { id: 1, label: 'Fresher' },
  { id: 2, label: '0-1' },
  { id: 3, label: '2-3' },
  { id: 4, label: '3-5' },
  { id: 5, label: '5-7' },
  { id: 6, label: '7-10' },
  { id: 7, label: '10-15' },
  { id: 8, label: '15-20' },
  { id: 9, label: '20+' },
];

export default function Candidates() {
  const { user } = useContext(AuthContext)
  const [modalConfirm4, setModalConfirm4] = useState(false);
  const [modalConfirm5, setModalConfirm5] = useState(false);
  const [sectors, setSectors] = useState<Sector[]>([]);
  const [skill, setskill] = useState<Skill[]>([]);
  const [selectedJobType, setSelectedJobType] = useState<any[]>([]);
  const [selectedSectors, setSelectedSectors] = useState<any[]>([]);
  const [selectdSkill, setSelecteSkiill] = useState<any[]>([]);

  const [jobstatus, setJobStatus] = useState("");
  const [searchByKeyword, setSearchByKeyword] = useState<string>("");

  const [selectedlocation, setSelectedLocation] = useState<any[]>([]);

  const [Country, setCountry] = useState<Country[]>([]);

  const [currentPage, setCurrentPage] = useState(1);
  const [totalCandidates, setTotalCandidates] = useState<Job[]>([]);

  const [candidates, setCandidate] = useState<Job[]>([]);

  const [experienceRange, setExperienceRange] = useState(0);
  const [experience, setExperience] = useState(experienceRanges[0].label);
  const [selectedCurrency, setSelectedCurrency] = useState('');
  const [maxSalary, setMaxSalary] = useState<number>(100000);
  const [selectedSalary, setSelectedSalary] = useState<number>(0);

  const [filterCandidateData, setFilterCandidate] = useState([]);
  const [filtercandidateId, setFilterCandidateId] = useState('1');
  const [filtercandidatesectionname, setFilterCandidateSectionName] = useState("");

  const [filterjobtype, setFilterJobsType] = useState("");

  const [filterjobIndex, setFilterJobIndex] = useState(0);

  const [profile_status, setProfileStatus] = useState("");

  const [companycvcount, setCompanyCvCount] = useState(0);

  const [searchskillKeywords, setSearchskillKeywords] = useState('');
  const [searchcountryKeywords, setSearchCountryKeywords] = useState('');
  const [searchSectorKeywords, setsearchSectorKeywords] = useState('');

  const [count, setCount] = useState(1);


  const pageSize = 10;
  const onPageChange = (page: any) => {

    setCurrentPage(page);

    const data = {
      searchByKeyword: searchByKeyword,
      jobstatus: jobstatus,
      profile_status: profile_status,
      location: selectedlocation.join(','),
      skill: selectdSkill.join(','),
      job_type: selectedJobType.join(','),
      sector: selectedSectors.join(','),
      experience: experienceRange != 0 ? experience : null,
      currency: selectedSalary > 0 ? selectedCurrency : null,
      minsalary: selectedSalary > 0 ? selectedSalary : null,
      maxSalary: maxSalary,
      user_id: user?.id,
      company_id: user?.company_id,
      filter_id: filtercandidateId,
      section_name: filtercandidatesectionname
    };

    getAllCandidateSearch(data)
      .then((res) => {
        if (res.status === true) {
          setTotalCandidates(res.data);
          const paginatedPosts = paginate(res.data, page, pageSize);
          setCandidate(paginatedPosts);


        } else {
          setCandidate([]);
        }
      })
      .catch((err) => {
        console.log(err);
      });

  };

  const modalConfirmOpen4 = () => {
    setModalConfirm4(true);
  }
  const modalConfirmClose4 = () => {
    setModalConfirm4(false);
  }

  const modalConfirmClose5 = () => {
    setModalConfirm5(false);
  }

  useEffect(() => {
    getAllSectorsData();
    getAllSkillsData();
    getAllCountriesData();
    if (user?.id) {
      getAllCandidateFilterData(user?.id);
    }
  }, [user]);


  const getAllCountriesData = async () => {
    try {
      const res = await getAllCountries();
      if (res) {
        setCountry(res);
      } else {
        setCountry([]);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const getAllSectorsData = async () => {
    try {
      const res = await getAllSectors();
      if (res.success == true) {
        setSectors(res.sectors);
      } else {
        setSectors([]);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const getAllSkillsData = async () => {
    try {
      const res = await getAllSkills();
      if (res.success == true) {
        setskill(res.data);
      } else {
        setskill([]);
      }
    } catch (err) {
      console.log(err);
    }
  };


  const getAllCandidateFilterData = async (id: any, searchByKeyword: any = null) => {
    try {
      const res = await getAllCandidateFilter(id, searchByKeyword);
      if (res.status == true) {
        setFilterCandidate(res.data);
        if (!filterjobtype) {
          setFilterCandidateId(res.data[0].id);
          setFilterCandidateSectionName(res.data[0].section_name);
          handleJobFilterNav(res.data[0]);
        }
      } else {
        setFilterCandidate([]);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const imagePath = (data: any) => {
    const DEMO_USER_IMAGE = `${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-1.png`
    // let newUrl = url.replace('/storage/', '');
    // console.log("data", process.env.NEXT_PUBLIC_IMAGE_URL + "/" + newUrl);
    // return process.env.NEXT_PUBLIC_IMAGE_URL + "/" + data;
    return data ? data : DEMO_USER_IMAGE;


    // if (data) {
    //   let url = data;
    //   let newUrl = url.replace('/storage/', '');
    //   console.log("newwwwwwwww", newUrl);

    //   return newUrl;
    // }
  }

  const handleChangeSearchBySector = (e: any, sectorId: any) => {
    if (e.target.checked) {
      setSelectedSectors((prevSectors: any[]) => [...prevSectors, sectorId]);
    } else {
      const updatedSectors = selectedSectors.filter((id) => id !== sectorId);
      setSelectedSectors(updatedSectors);
    }
  };

  const handleChangeJobType = (e: any) => {
    const jobType = e.target.value;

    setSelectedJobType((prevJobTypes: any) => {
      if (e.target.checked) {
        return [...prevJobTypes, jobType];
      } else {
        return prevJobTypes.filter((type: any) => type !== jobType);
      }
    });
  };

  const handleChangeSearchskill = (e: any, skillId: number) => {
    if (e.target.checked) {
      setSelecteSkiill((prevSkills: any[]) => [...prevSkills, skillId]);

    } else {
      const updatedSkills = selectdSkill.filter((id) => id !== skillId);
      setSelecteSkiill(updatedSkills);
    }
  };

  const handleChangeSearchlocation = (e: any, countryid: number) => {
    if (e.target.checked) {
      setSelectedLocation((prevSkills: any[]) => [...prevSkills, countryid]);

    } else {
      const updatedlocation = selectedlocation.filter((id) => id !== countryid);
      setSelectedLocation(updatedlocation);
    }
  };

  const handleSubmit = () => {
    const data = {
      searchByKeyword: searchByKeyword,
      jobstatus: jobstatus,
      profile_status: profile_status,
      location: selectedlocation.join(','),
      skill: selectdSkill.join(','),
      job_type: selectedJobType.join(','),
      sector: selectedSectors.join(','),
      experience: experienceRange != 0 ? experience : null,
      currency: selectedSalary > 0 ? selectedCurrency : null,
      minsalary: selectedSalary > 0 ? selectedSalary : null,
      maxSalary: maxSalary,
      user_id: user?.id,
      company_id: user?.company_id,
      filter_id: filtercandidateId,
      section_name: filtercandidatesectionname
    };

    getAllCandidateSearch(data)
      .then((res) => {
        if (res.status === true) {
          setTotalCandidates(res.data);
          const paginatedPosts = paginate(res.data, 1, pageSize);
          setCandidate(paginatedPosts);
          handleJobfilterdata(data);

        } else {
          setCandidate([]);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const handleJobfilterdata = (data: any, searchByKeyword: null | string = null) => {
    UpdateSaveCandidateFilterData(data)
      .then((res) => {
        if (res.status == true) {
          getAllCandidateFilterData(user?.id, searchByKeyword);
          setFilterCandidateId(res.id);
          setModalConfirm5(false);
          setModalConfirm4(false);
        } else {
          setCandidate([]);
        }
      })
      .catch((err) => {
        console.log(err);
      });

  }

  const handleExperienceChange = (event: any) => {
    const selectedRange = Number(event.target.value);
    setExperienceRange(selectedRange);

    // Find the corresponding experience label based on the selected range
    const selectedExperience = experienceRanges.find((range) => range.id === selectedRange);
    if (selectedExperience) {
      setExperience(selectedExperience.label);

    }
  };


  const handleCurrencyChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    if (event.target.value != '') {
      setSelectedCurrency(event.target.value);
    } else {
      setSelectedSalary(0)
      setSelectedCurrency('')
    }

  };

  const handleSalaryChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (selectedCurrency) {
      setSelectedSalary(Number(event.target.value));
    } else {
      notification.error({ message: 'Please choose a currency first' })
    }
  };

  const handleJobFilterEdit = (id: any, name: any, index: any) => {
    setFilterCandidateId(id);
    setFilterCandidateSectionName(name ?? '');
    setFilterJobsType("edit")
    setFilterJobIndex(index);
    setModalConfirm5(true);
  };

  const handleSubmitCandidatefilter = (e: any) => {
    e.preventDefault();
    const data = {
      filter_id: filterjobtype == 'edit' ? filtercandidateId : '',
      section_name: filtercandidatesectionname,
      user_id: user?.id,
      company_id: user?.company_id,
      profile_status: profile_status,
      job_status: '',
      // searchByKeyword: searchByKeyword,
      currency: '',
      salary: '',
      experience: '',
      country_id: '',
      skills: '',
      sector: '',
      job_type: ''
    };

    UpdateSaveCandidateFilterData(data)
      .then((res) => {
        if (res.status == true) {

          getAllCandidateFilterData(user?.id);
          setFilterCandidateId(res.id);
          setModalConfirm5(false);
          handleJobFilterNav(res.data);
        } else {
          setCandidate([]);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const handleJobFilterNav = (candidate: any) => {

    setFilterCandidateId(candidate.id);
    setFilterCandidateSectionName(candidate.section_name);

    if (candidate.country_id) {
      setSelectedLocation(candidate.country_id.split(','));
    } else {
      setSelectedLocation([]);
    }

    if (candidate.job_status) {
      setJobStatus(candidate.job_status);
    } else {
      setJobStatus("");
    }

    if (candidate.profile_status) {
      setProfileStatus(candidate.profile_status);
    } else {
      setProfileStatus("");
    }

    if (candidate.currency) {
      setSelectedCurrency(candidate.currency);
      setSelectedSalary(candidate.salary > 0 ? candidate.salary : 0);
    } else {
      setSelectedCurrency('');
      setSelectedSalary(0);
    }

    if (candidate.experience) {

      if (candidate.experience == "Fresher") {
        setExperienceRange(1)
      }
      if (candidate.experience == "0-1") {
        setExperienceRange(2)
      }
      if (candidate.experience == "2-3") {
        setExperienceRange(3)
      }
      if (candidate.experience == "3-5") {
        setExperienceRange(4)
      }
      if (candidate.experience == "5-7") {
        setExperienceRange(4)
      }
      if (candidate.experience == "7-10") {
        setExperienceRange(6)
      }
      if (candidate.experience == "10-15") {
        setExperienceRange(7)
      }
      if (candidate.experience == "15-20") {
        setExperienceRange(8)
      }

      if (candidate.experience == "20+") {
        setExperienceRange(9)
      }

      setExperience(candidate.experience)

    } else {

      setExperience("Choose Experience")
      setExperienceRange(0)
    }

    if (candidate.skills) {
      setSelecteSkiill(candidate.skills.split(','));
    } else {
      setSelecteSkiill([]);
    }

    if (candidate.sector) {
      setSelectedSectors(candidate.sector.split(','));
    } else {
      setSelectedSectors([]);
    }

    if (candidate.job_type) {
      setSelectedJobType(candidate.job_type.split(','));
    } else {
      setSelectedJobType([]);
    }

    const data = {
      searchByKeyword: candidate?.search_by_keyword ? candidate.search_by_keyword : null,
      jobstatus: candidate.job_status ? candidate.job_status : null,
      profile_status: candidate.profile_status ? candidate.profile_status : null,
      location: candidate.country_id ? candidate.country_id : null,
      skill: candidate.skills ? candidate.skills : null,
      job_type: candidate.job_type ? candidate.job_type : null,
      sector: candidate.sector ? candidate.sector : null,
      currency: candidate.currency ? candidate.currency : null,
      minsalary: candidate.salary ? candidate.salary : null,
      experience: candidate.experience ? candidate.experience : null,
      user_id: user?.id,
      company_id: user?.company_id,
      filter_id: candidate.id,

    };

    getAllCandidateSearch(data)
      .then((res) => {
        if (res.status === true) {
          setTotalCandidates(res.data);
          const paginatedPosts = paginate(res.data, 1, pageSize);
          setCandidate(paginatedPosts);
          modalConfirmClose4()
        } else {
          setCandidate([]);
        }
      })
      .catch((err) => {
        console.log(err);
      });

  };

  const handleDeleteCandidateFilter = (e: any) => {
    e.preventDefault();
    swal({
      title: "Are you sure?",
      text: "You want to delete the Candidate filter Section",
      icon: "warning",
      dangerMode: true,
      buttons: ["Cancel", "Yes, I am sure!"],
    }).then((willDelete) => {
      if (willDelete) {
        const data = {
          id: filtercandidateId
        }
        DeleteCandidateFilter(data)
          .then((res) => {
            if (res.status === true) {
              notification.error({ message: res.message })
              getAllCandidateFilterDataDelete(user?.id);


              setModalConfirm5(false);
            } else {
              console.log("Deletion failed");
            }
          })

      } else {
        console.log("Deletion cancelled");
      }
    });
  };

  const getAllCandidateFilterDataDelete = async (id: any) => {
    try {
      const res = await getAllCandidateFilter(id);
      if (res.status == true) {
        setFilterCandidate(res.data);
        setFilterCandidateId(res.data[0].id);
        setFilterCandidateSectionName(res.data[0].section_name);
        handleJobFilterNav(res.data[0]);


      } else {
        setFilterCandidate([]);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const handleClick = (type: any, id: any) => {

    let updatedSkill = [];
    let updatedSector = [];
    let updatedJobType = [];
    let updatedCountry = [];
    let tempSearchByKeyword: string | null = searchByKeyword;

    if (type == "skill") {
      updatedSkill = selectdSkill.filter((skillId) => skillId != id);
      setSelecteSkiill(updatedSkill)
    }

    if (type == "sector") {
      updatedSector = selectedSectors.filter((SectorId) => SectorId != id);
      setSelectedSectors(updatedSector)
    }

    if (type == "jobtype") {
      updatedJobType = selectedJobType.filter((jobtype) => jobtype != id);
      setSelectedJobType(updatedJobType)
    }

    if (type == "country") {
      updatedCountry = selectedlocation.filter((CountryID) => CountryID != id);
      setSelectedSectors(updatedCountry)
    }

    if (type == 'job_status') {
      setJobStatus("");
    }

    if (type == 'salary') {
      setSelectedCurrency("");
      setSelectedSalary(0);
    }

    if (type == 'experience') {

      setExperienceRange(0);
    }
    if (type == "searchByKeyword") {
      setSearchByKeyword("");
      tempSearchByKeyword = null
    }
    const data = {
      searchByKeyword: type === "searchByKeyword" ? "" : tempSearchByKeyword,
      jobstatus: type == 'job_status' ? '' : jobstatus,
      location: type == "country" ? updatedCountry.join(",") : selectedlocation.join(","),
      skill: type == "skill" ? updatedSkill.join(",") : selectdSkill.join(","),
      job_type: type == "jobtype" ? updatedJobType.join(",") : selectedJobType.join(","),
      sector: type == "sector" ? updatedSector.join(",") : selectedSectors.join(","),
      experience: type == 'experience' ? '' : experienceRange != 0 ? experience : '',
      currency: type == 'salary' ? '' : selectedCurrency,
      minsalary: type == 'salary' ? '' : selectedSalary > 0 ? selectedSalary : '',
      maxSalary: maxSalary,
      user_id: user?.id,
      company_id: user?.company_id,
      filter_id: filtercandidateId,
      section_name: filtercandidatesectionname
    };

    getAllCandidateSearch(data)
      .then((res) => {
        if (res.status === true) {
          setTotalCandidates(res.data);
          const paginatedPosts = paginate(res.data, currentPage, pageSize);
          setCandidate(paginatedPosts);
          handleJobfilterdata(data);
          modalConfirmClose4()
        } else {
          setCandidate([]);
        }
      })
      .catch((err) => {
        console.log(err);
      });

  };

  const handleClickDownloadResumeValidation = (e: any, resume_path: any) => {
    if (resume_path == null || resume_path == '') {
      notification.info({ message: 'Candidate has not yet uploaded the resume.' })
    }
  }

  const handleViewCandidateProfile = (id: any, slug: string) => {

    const data = {
      user_id: user?.id,
      user_role: user?.role,
      company_id: user?.company_id,
      applicant_id: id,
    };

    checkAndUpdateResumesViewed(data)
      .then((res) => {
        if (res.status === true) {
          window.open(`/candidate-profile/${slug}`, '_blank')
          getAllCandidateFilterData(user?.id)
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const handleCvCountErrorMsg = () => {
    let msg;
    if (user?.membership) {
      msg = 'Hi,memebership plan has been expired. Please upgrade your plan to see more candidates';
    } else {
      msg = 'Hi, you have consumed all CV count. Please upgrade your plan to see more candidates';
    }
    notification.info({ message: msg })

  };


  const onSearchskill = (e: React.ChangeEvent<HTMLInputElement>) => {
    const keywords = e.target.value;
    setSearchskillKeywords(keywords);

    if (keywords) {
      const filteredSkills = skill.filter((skill: any) =>
        skill.skills.toLowerCase().includes(keywords.toLowerCase())
      );
      setskill(filteredSkills);
    } else {
      getAllSkillsData()
    }
  };

  const onSearchlocation = (e: React.ChangeEvent<HTMLInputElement>) => {
    const keywords = e.target.value;
    setSearchCountryKeywords(keywords);

    if (keywords) {
      const filteredLocation = Country.filter((Country: any) =>
        Country.country_name.toLowerCase().includes(keywords.toLowerCase())
      );
      setCountry(filteredLocation);
    } else {
      getAllCountriesData()
    }
  };

  const onSearchSector = (e: React.ChangeEvent<HTMLInputElement>) => {
    const keywords = e.target.value;
    setsearchSectorKeywords(keywords);

    if (keywords) {
      const filteredLocation = sectors.filter((sector: any) =>
        sector.sector_name.toLowerCase().includes(keywords.toLowerCase())
      );
      setSectors(filteredLocation);
    } else {
      getAllSectorsData();
    }
  };

  const handleClickAddFilterSection = (e: any) => {
    setCount(count + 1);
    setFilterJobsType("add");
    const addSectionName = "Custom List " + count;
    const data = {
      filter_id: '',
      section_name: addSectionName,
      user_id: user?.id,
      company_id: user?.company_id,
      profile_status: profile_status,
      job_status: '',
      currency: '',
      salary: '',
      experience: '',
      country_id: '',
      skills: '',
      sector: '',
      job_type: ''
    };

    UpdateSaveCandidateFilterData(data)
      .then((res) => {
        if (res.status == true) {
          getAllCandidateFilterData(user?.id);
          setFilterCandidateId(res.id);
          //setModalConfirm5(false);
          handleJobFilterNav(res.data);
        } else {
          setCandidate([]);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  }
  console.log(filterCandidateData.filter((candidate_filter: any) => filtercandidateId == candidate_filter.id), "filterCandidae")
  return (
    <>
      <div className="dash-right">
        <h1>
          Resume Search
        </h1>
        <div className="data-management m-p-10">
          <div className="tab-filter mt-4">
            <button>ADD</button>
            <nav>
              <div className="nav nav-tabs" id="nav-tab" role="tablist">
                {filterCandidateData.map((candidate: any, index: any) => (
                  <button
                    key={index}
                    className={`nav-link ${filtercandidateId === candidate.id ? 'active' : ''}${index == 0 ? '' : 'mx-1'}`}
                    type="button"
                    style={{ color: filtercandidateId === candidate.id ? '#0d6efd' : 'gray' }}
                    onClick={() => {
                      handleJobFilterNav(candidate);
                      setFilterJobsType("edit");
                    }}
                  >
                    {candidate.section_name ? candidate.section_name : 'All Resumes'} <i className="fa-solid fa-pencil" onClick={() => handleJobFilterEdit(candidate.id, candidate.section_name, index)}></i>
                  </button>
                ))}
                {/* <button
                  className="nav-link"
                  onClick={() => { setModalConfirm5(true); setFilterJobsType("add"); setFilterCandidateSectionName("") }}
                >+</button> */}
                <button
                  className="nav-link"
                  onClick={(e: any) => handleClickAddFilterSection(e)}
                >+</button>
              </div>
            </nav>
            <div className="tab-content" id="nav-tabContent">
              <div
                className="tab-pane fade show active"
                id="nav-home"
                role="tabpanel"
                aria-labelledby="nav-home-tab"
              >
                <div className="filter">
                  <div className="filter-sp">


                    {filterCandidateData.filter((candidate_filter: any) => filtercandidateId == candidate_filter.id).map((candidate_filter: any, index: any) => (
                      <div key={index} className="ASd">
                        {
                          candidate_filter?.search_by_keyword && (
                            <p className="cat d-inline-block">
                              {
                                candidate_filter.search_by_keyword
                              }
                              <i className="fa-solid fa-xmark" onClick={() => handleClick("searchByKeyword", "")}></i>
                            </p>
                          )
                        }
                        {candidate_filter.job_status && (
                          <p className="cat d-inline-block">
                            {candidate_filter.job_status == 'ready_to_interview'
                              ? 'Ready to Interview'
                              : candidate_filter.job_status == 'open_to_offer'
                                ? 'Open to Offer'
                                : candidate_filter.job_status == 'not_looking'
                                  ? 'Not Looking'
                                  : ''
                            }<i className="fa-solid fa-xmark" onClick={() => handleClick("job_status", "")}></i>
                          </p>
                        )}

                        {candidate_filter.profile_status && (
                          <p className="cat d-inline-block mx-2">
                            {candidate_filter.profile_status}<i className="fa-solid fa-xmark" onClick={() => handleClick("profile_status", "")}></i>
                          </p>
                        )}

                        {candidate_filter.salary && (
                          <p className="cat d-inline-block mx-2">
                            0 - {candidate_filter.salary} {candidate_filter.currency}<i className="fa-solid fa-xmark" onClick={() => handleClick("salary", "")}></i>
                          </p>
                        )}

                        {candidate_filter.experience && (
                          <p className="cat d-inline-block mx-2">
                            {candidate_filter.experience} {candidate_filter.experience !== 'fresher' && (candidate_filter.experience != '0-1' ? 'years' : 'year')}
                            <i className="fa-solid fa-xmark" onClick={() => handleClick("experience", "")}></i>
                          </p>
                        )}

                        {candidate_filter.country_id && (
                          <>
                            {candidate_filter.country_id.split(",").map((CountryId: any, index: any) => {
                              const countryObj = Country.find((s) => s.id == Number(CountryId) && s.status == 'active');
                              return (
                                <p className="cat d-inline-block mx-2" key={index}>
                                  {countryObj ? <span>{countryObj.country_name}</span> : null}
                                  <i className="fa-solid fa-xmark" onClick={() => handleClick("country", CountryId)}></i>
                                </p>
                              );
                            })}
                          </>
                        )}


                        {candidate_filter.skills && (
                          <>
                            {candidate_filter.skills.split(",").map((skillId: any, index: any) => {
                              const skillObj = skill.find((s) => s.id == Number(skillId));
                              return (
                                <p className="cat d-inline-block mx-2" key={index}>
                                  {skillObj ? <span>{skillObj.skills}</span> : null}
                                  <i className="fa-solid fa-xmark" onClick={() => handleClick("skill", skillId)}></i>
                                </p>
                              );
                            })}
                          </>
                        )}

                        {candidate_filter.sector && (
                          <>
                            {candidate_filter.sector.split(",").map((sectorId: any, index: any) => {
                              const sector = sectors.find((s) => s.id == Number(sectorId));
                              return (
                                <p className="cat d-inline-block mx-2" key={index}>
                                  {sector ? <span>{sector.sector_name}</span> : null}
                                  <i className="fa-solid fa-xmark" onClick={() => handleClick("sector", sectorId)}></i>
                                </p>
                              );
                            })}
                          </>
                        )}

                        {candidate_filter.job_type && (

                          <>
                            {candidate_filter.job_type.split(",").map((jobtype: any, index: any) => {
                              return (
                                <p className="cat d-inline-block mx-2" key={index}>
                                  {jobtype ? <span>{jobtype}</span> : null}
                                  <i className="fa-solid fa-xmark" onClick={() => handleClick("jobtype", jobtype)}></i>
                                </p>
                              );
                            })}
                          </>

                        )}

                      </div>

                    ))}

                  </div>

                  <div className="filter-bottom" style={{ cursor: 'pointer' }}>
                    <p onClick={() => {
                      modalConfirmOpen4();
                    }}>
                      <i className="fa-solid fa-angles-down"></i> Filter
                    </p>
                    <ModalForm
                      open={modalConfirm4}
                      onCancel={modalConfirmClose4}
                      title='Filter By'
                    >
                      <div className="popup-body filter-pop text-left">
                        <Form layout='vertical' size='large'>
                          <Row gutter={10}>
                            <Col md={12}>
                              <Form.Item>
                                <label> Position Or Keyword</label>
                                <Input onChange={(e) => setSearchByKeyword(e.target.value)} placeholder="Enter position or keyword" />
                              </Form.Item>
                            </Col>
                            <Col md={12}>
                              <Form.Item>
                                <label>Job Search Status</label>
                                <Select
                                  onChange={(value) => setJobStatus(value)}
                                  placeholder='Job Search Status'
                                  options={[
                                    { value: '', label: 'Choose job status' },
                                    { value: 'ready_to_interview', label: 'Ready To Interview' },
                                    { value: 'open_to_offer', label: 'Open To Offer' },
                                    { value: 'not_looking', label: 'Read To Interview' },
                                  ]}
                                />
                              </Form.Item>
                            </Col>
                            <Col md={12}>
                              <Form.Item>
                                <label>Profile Seen Status</label>
                                <Select
                                  onChange={(value) => setProfileStatus(value)}
                                  placeholder={'Choose job status'}
                                  options={[
                                    { value: '', label: 'All' },
                                    { value: 'seen', label: 'Seen' },
                                    { value: 'unseed', label: 'Unseen' },
                                  ]}
                                />
                              </Form.Item>
                            </Col>
                          </Row>
                        </Form>

                        <div className="row">
                          <div className="col-sm-6">
                            <div className="salary-box h-2 mt-4">

                              <select className="choose-currency border-gray mb-4" value={selectedCurrency} onChange={handleCurrencyChange}>
                                <option value="">Choose Currency</option>
                                {Country.map((country: any, index: any) => {
                                  if (country.currency !== null) {
                                    if (country.status == 'active') {
                                      return (
                                        <option key={index} value={country.currency}>
                                          {country.currency}
                                        </option>
                                      );
                                    }
                                  }
                                  return null; // If currency is null, don't render the option
                                })}
                              </select>
                              <p className="f-16 w-600 c-2C2C2C">Salary</p>

                              <p className="f-12 c-2C2C2C">
                                {`${selectedCurrency} 0 - ${selectedCurrency} ${maxSalary}`}
                              </p>

                              {selectedSalary > 0 && (
                                <p className="f-12 c-2C2C2C">
                                  {selectedSalary} {selectedCurrency}
                                </p>
                              )}

                              <input
                                type="range"
                                className="form-range w-75"
                                id="customRange1"
                                min="0"
                                max={maxSalary.toString()}
                                value={selectedSalary.toString()}
                                onChange={handleSalaryChange}
                              />

                            </div>
                          </div>

                          <div className="col-sm-6">
                            <div className="salary-box h-2 mt-4">
                              <p className="f-16 w-600 c-2C2C2C pb-2">Experience</p>
                              <p className="f-12 c-2C2C2C">{experience} {(experienceRange !== 0 && experienceRange !== 1) && (experienceRange == 2 ? 'year' : 'years')} </p>
                              <input
                                type="range"
                                className="form-range w-75"
                                id="customRange1"
                                min="0"
                                max="9"
                                step="1"
                                value={experienceRange}
                                onChange={handleExperienceChange}
                              />
                            </div>
                          </div>
                        </div>

                        <div className="row">

                          <div className="col-sm-6">
                            <div className="salary-box mt-3">
                              <p className="f-16 w-600 c-2C2C2C pb-2">Location</p>
                              <div className="form-in position-relative input-bababa ">
                                <i className="fa-solid fa-magnifying-glass  glass-search pad-sp"></i>
                                <input
                                  type="text"
                                  placeholder="Search Locations"
                                  className="medium-input left-sp mt-2 mb-2 c-999999"
                                  value={searchcountryKeywords}
                                  onChange={(e) => onSearchlocation(e)}
                                />
                              </div>
                              <div className="form-in" id='scrolldata'>
                                {Country.length > 0 ? (
                                  Country.map((country: any, index: any) => {
                                    if (country.status == 'active') {
                                      return (
                                        <div className="form-master-field dflex" key={index}>
                                          <input
                                            type="checkbox"
                                            placeholder="Placeholder"
                                            className="master-fields checkbox border-0 mb-0"
                                            onChange={(e) => {
                                              handleChangeSearchlocation(e, country.id.toString());
                                            }}
                                            checked={selectedlocation.includes(country.id.toString())} // Set the 'checked' attribute based on the 'selectedSkill' array
                                          />
                                          <label className="check-label c-4D4D4D mb-0">{country.country_name}</label>
                                        </div>
                                      );
                                    }
                                  })
                                ) : null}
                              </div>


                            </div>
                          </div>

                          <div className="col-sm-6">
                            <div className="salary-box mt-3">
                              <p className="f-16 w-600 c-2C2C2C pb-2">Skills</p>
                              <div className="form-in position-relative input-bababa ">
                                <i className="fa-solid fa-magnifying-glass  glass-search pad-sp"></i>
                                <input
                                  type="text"
                                  placeholder="Search Skills"
                                  className="medium-input left-sp mt-2 mb-2 c-999999"
                                  value={searchskillKeywords}
                                  onChange={(e) => onSearchskill(e)}
                                />
                              </div>
                              <div className="form-in" id='scrolldata'>
                                {skill.length > 0 ? (
                                  skill.map((skills: any, index: any) => {

                                    return (
                                      <div className="form-master-field dflex" key={index}>
                                        <input
                                          type="checkbox"
                                          placeholder="Placeholder"
                                          className="master-fields checkbox border-0 mb-0"
                                          onChange={(e) => {
                                            handleChangeSearchskill(e, skills.id.toString());
                                          }}
                                          checked={selectdSkill.includes(skills.id.toString())} // Set the 'checked' attribute based on the 'selectedSkill' array
                                        />
                                        <label className="check-label c-4D4D4D mb-0">{skills.skills}</label>
                                      </div>
                                    );
                                  })
                                ) : null}
                              </div>


                            </div>
                          </div>
                          <div className="col-sm-6">
                            <div className="salary-box mt-4 h-100">
                              <p className="f-16 w-600 c-2C2C2C">Sector</p>
                              <div className="form-in position-relative input-bababa ">
                                <i className="fa-solid fa-magnifying-glass  glass-search pad-sp"></i>
                                <input
                                  type="text"
                                  placeholder="Search Locations"
                                  className="medium-input left-sp mt-2 mb-2 c-999999"
                                  value={searchSectorKeywords}
                                  onChange={(e) => onSearchSector(e)}
                                />
                              </div>

                              <div className="form-in" id='scrolldata'>
                                {sectors.length > 0
                                  ? sectors
                                    .map((sectors_data: any, index: any) => {
                                      return (
                                        <div
                                          className="form-master-field dflex"
                                          key={index}
                                        >
                                          <input
                                            type="checkbox"
                                            placeholder="Placeholder"
                                            className="master-fields checkbox border-0 mb-0"
                                            onChange={(e) => {
                                              handleChangeSearchBySector(e, sectors_data.id.toString()
                                              );
                                            }}
                                            checked={selectedSectors.includes(sectors_data.id.toString())}
                                          />
                                          <label className="check-label c-4D4D4D mb-0">
                                            {sectors_data.sector_name}
                                          </label>
                                        </div>
                                      );
                                    })
                                  : null}
                              </div>

                            </div>
                          </div>

                          <div className="col-sm-6">
                            <div className="salary-box mt-4 h-100">
                              <p className="f-16 w-600 c-2C2C2C">Job Type</p>

                              <div className="form-master-field dflex ">
                                <input
                                  type="checkbox"
                                  placeholder="Placeholder"
                                  className="master-fields checkbox border-0 mb-0"
                                  value="fulltime"
                                  onChange={handleChangeJobType}
                                  checked={selectedJobType.includes("fulltime")}
                                />
                                <label className="check-label c-4D4D4D mb-0">
                                  Full-Time
                                </label>
                              </div>

                              <div className="form-master-field dflex ">
                                <input
                                  type="checkbox"
                                  placeholder="Placeholder"
                                  className="master-fields checkbox border-0 mb-0"
                                  value="parttime"
                                  onChange={handleChangeJobType}
                                  checked={selectedJobType.includes("parttime")}
                                />
                                <label className="check-label c-4D4D4D mb-0">
                                  Part-time
                                </label>
                              </div>

                              <div className="form-master-field dflex ">
                                <input
                                  type="checkbox"
                                  placeholder="Placeholder"
                                  className="master-fields checkbox border-0 mb-0"
                                  value="contract"
                                  onChange={handleChangeJobType}
                                  checked={selectedJobType.includes("contract")}
                                />
                                <label className="check-label c-4D4D4D mb-0">
                                  Contract
                                </label>
                              </div>

                              <div className="form-master-field dflex ">
                                <input
                                  type="checkbox"
                                  placeholder="Placeholder"
                                  className="master-fields checkbox border-0 mb-0"
                                  value="freelance"
                                  onChange={handleChangeJobType}
                                  checked={selectedJobType.includes("freelance")}
                                />
                                <label className="check-label c-4D4D4D mb-0">
                                  FreeLancer
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="text-center mt-5">
                          <button
                            className="btn-a primary-size-16 btn-bg-0055BA"
                            onClick={() => {
                              handleSubmit();
                              setCurrentPage(1);
                            }}
                          >
                            View Results
                          </button>
                        </div>
                      </div>
                    </ModalForm>
                  </div>
                </div>

                <PopupModal
                  show={modalConfirm5}
                  handleClose={modalConfirmClose5}
                  customclass={"header-remove body-sp-0 "}
                  closebtnicon={"icon"}
                >
                  <div className="col-sm-2 text-right">
                    <button
                      type="button"
                      className="close-b-des close-x close-none bg-0055BA border-design"
                      data-bs-dismiss="modal"
                      aria-label="Close"
                      onClick={() => { setModalConfirm5(false); }}
                    >
                      <i className="fa-solid fa-xmark"></i>
                    </button>
                  </div>
                  <form onSubmit={handleSubmitCandidatefilter} className="common_form_error" id="menu_form">
                    <div className="popup-body">
                      <p className="f-12 c-2C2C2C">{filterjobtype == 'edit' ? 'Edit Search Name' : 'Saved Search Name'}</p>
                      <input
                        type="text"
                        placeholder="Search Name"
                        className="big-input mb-0"
                        onChange={(e) => setFilterCandidateSectionName(e.target.value)} value={filtercandidatesectionname}
                        required
                        maxLength={50}
                      ></input>
                      <p className='f-12 c-474D66 text-right mt-2'>50</p>

                      <div className="text-right mt-3">
                        {filterjobtype === 'edit' && filterjobIndex != 0 && (
                          <button className="cancel" onClick={handleDeleteCandidateFilter}>Delete</button>
                        )}

                        <button type='submit' className="save" >{filterjobtype == 'edit' ? 'Update' : 'Done'}</button>
                      </div>
                    </div>
                  </form>
                </PopupModal>
                {candidates.length > 0 ? (
                  candidates.map((candidates: any, index: any) => {
                    const skillIdsArray = candidates.skills ? candidates.skills.split(',') : [];

                    return (
                      <div
                        className=" "
                        key={index}
                      >
                        <div className='filter filter-sp m-center mt-4' key={index}>
                          <div className='row tab-w-col-12'>
                            <div className='col w-150-col pr-0' id='candidate_image'>
                              {/* src={candidates.profile?.source} */}

                              {candidates.profile
                                ?
                                <img src={imagePath(candidates.profile?.source)} alt={candidates.profile?.name} className='same-w-150' />
                                :
                                <img src={process.env.NEXT_PUBLIC_BASE_URL + '/images/user-p.png'} alt=" user-p" className=' same-w-150 h-140' />
                              }


                              <p className='text-start mt-2'>

                                {candidates.applicant_id && (<span className="w-100 badge bg-success float-start">Seen by {candidates.viewed_user_id == user?.id ? 'me' : candidates.viewed_user_name.split(' ')[0]}</span>)}
                              </p>

                            </div>
                            <div className='col m-center sp-m-3'>
                              <p className='p-18 blue-text mt-2'>
                                {candidates.name} ({candidates.current_position})
                              </p>
                              <p className='p-16 black-text line-height-22'>{/*candidates.candidate_current_position@*/}<a target="_blank" href={'/companies/' + candidates.company_slug}>{candidates.company_name}</a></p>
                              <ul className='full-time'>

                                <li><i className="fa-solid fa-business-time"></i> {candidates.job_status === 'ready_to_interview' ? 'Ready to Interview' : candidates.job_status === 'open_to_offer' ? 'Open to Offer' : 'Not Looking'}  </li>

                                <li><i className="fa-solid fa-location-dot"></i> {candidates.country_name}</li>


                              </ul>

                              <ul className='full-time'>
                                {candidates.industries_name && (
                                  <li><i className="fa fa-industry" aria-hidden="true"></i> {candidates.industries_name}</li>
                                )}
                                {candidates.sector_name && (
                                  <li><i className="fas fa-industry"></i> {candidates.sector_name}</li>
                                )}

                              </ul>

                              <ul className='full-time'>
                                {
                                  candidates.skills && (
                                    <li key={index}>
                                      <i className="fas fa-award me-2"></i>
                                      {skillIdsArray.map((skillId: any, index: any) => {
                                        const matchedSkill = skill.find((s) => s.id === parseInt(skillId, 10));

                                        if (matchedSkill) {
                                          return (
                                            <span key={index}>
                                              {matchedSkill.skills}
                                            </span>
                                          );
                                        } else {
                                          // Handle the case if a skill with the given ID doesn't exist in the skill array
                                          return null;
                                        }
                                      }).reduce((prev: any, curr: any) => (prev === null ? [curr] : [...prev, ', ', curr]), null)}
                                    </li>


                                  )}


                              </ul>

                              <ul className='full-time'>
                                {candidates.job_type && (
                                  <li>
                                    <i className="fa fa-tasks me-2"></i>
                                    {candidates.job_type.split(',').map((word: any) => word.trim().charAt(0).toUpperCase() + word.trim().slice(1)).join(', ')}
                                  </li>
                                )}


                              </ul>

                            </div>
                            <div className='col max-268-col text-center m-center'>


                              {user?.membership === true && (companycvcount != 0 || candidates.applicant_id) ? (
                                <a
                                  target="_blank"
                                  href={'/candidate-profile/' + candidates.slug}
                                  className="btn-a primary-size-16 btn-bg-0055BA w-100 pr-0 pl-0"
                                  type="button"
                                  id="candidate_slug"
                                  onClick={() => handleViewCandidateProfile(candidates.id, candidates.slug)}
                                >
                                  <i className="fa-solid fa-arrow-up-right-from-square"></i> View Candidate Profile
                                </a>
                              ) : (
                                <a
                                  target="_blank"
                                  className="btn-a primary-size-16 btn-bg-0055BA w-100 pr-0 pl-0"
                                  type="button"
                                  id="candidate_slug"
                                  onClick={() => handleViewCandidateProfile(candidates.id, candidates.slug)}

                                >
                                  <i className="fa-solid fa-arrow-up-right-from-square"></i> View Candidate Profile
                                </a>
                              )}

                              {user?.membership === true && (companycvcount != 0 || candidates.applicant_id) ? (
                                <a
                                  target="_blank"
                                  href={'/employer/messages/inbox/' + candidates.id}
                                  className="btn-a primary-size-16 btn-success mt-2 w-100"
                                  type="button"
                                >
                                  <i className="fa-regular fa-message mx-2"></i>Send Message
                                </a>
                              ) : (
                                <a
                                  target="_blank"
                                  onClick={handleCvCountErrorMsg}
                                  className="btn-a primary-size-16 btn-success mt-2 w-100"
                                  type="button"
                                >
                                  <i className="fa-regular fa-message mx-2"></i>Send Message
                                </a>
                              )}

                              {candidates.resume_pdf_path ? (
                                user?.membership === true && (companycvcount != 0 || candidates.applicant_id) ? (
                                  <a
                                    target="_blank"
                                    href={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/employee/resume/' + candidates.resume_pdf_path}
                                  >
                                    <button className="download mt-2 w-100">
                                      <i className="fa-solid fa-download"></i> Download Resume
                                    </button>
                                  </a>
                                ) : (
                                  <p>
                                    <button className="download mt-2 w-100" onClick={handleCvCountErrorMsg}>
                                      <i className="fa-solid fa-download"></i> Download Resume
                                    </button>
                                  </p>
                                )
                              ) : (
                                <button
                                  className="download mt-2 w-100"
                                  onClick={(e: any) =>
                                    handleClickDownloadResumeValidation(e, candidates.resume_pdf_path)
                                  }
                                >
                                  <i className="fa-solid fa-download"></i> Download Resume
                                </button>
                              )}
                            </div>
                          </div>

                        </div>

                      </div>
                    );
                  })
                ) : (
                  <div className="filter filter-sp m-center mt-4">
                    <div className="row">
                      <p>No candidates founds</p>
                    </div>
                  </div>
                )}
                <Pagination
                  items={totalCandidates.length}
                  currentPage={currentPage}
                  pageSize={pageSize}
                  onPageChange={onPageChange}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
