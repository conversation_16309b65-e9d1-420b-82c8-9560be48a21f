import React from 'react';
import Image from 'next/image';

export default function NotificationsBlank() {
    return (
        <>
            <div className="dash-right">
                <h1>My <span className='span-color'> Notifications</span></h1>

                <div className='row '>
                    <div className='col-sm-6'>
                    </div>
                    <div className='col-sm-6 text-right'>
                        <ul className="blue-text-line  text-right">
                            <li>
                                <a href="#">Notification Settings</a>
                            </li>
                        </ul>
                    </div>
                </div>

                <div className='data-management m-p-10'>
                    <div className='work-experience-fieild m-p-10'>
                        <div className='row'>
                            <div className='col-sm-12'>
                                <p className='f-22 m-center'>Inbox</p>
                            </div>
                        </div>

                        <div className='text-center'>
                            <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blank-5.png'} alt="blank-5" />
                            <p className='f-22 c-BABABA mb-2'>No New Notifications</p>
                            <p className='f-18 c-BABABA w-400'>Check this section for job updates, and<br /> general notifications. </p>
                        </div>

                    </div>

                </div>
            </div>
        </>
    )
}
