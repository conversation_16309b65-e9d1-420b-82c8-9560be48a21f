import React, { useState, useEffect, useContext } from 'react';
import 'react-toastify/dist/ReactToastify.css';
import moment from 'moment';

import Pagination from '../../../components/Common/Pagination';
import { getNotifications, updateNotificationStatus } from '../../../lib/frontendapi';
import { paginate } from '../../../helpers/paginate';
import AuthContext from '@/Context/AuthContext';
import Image from 'next/image';

export default function Notifications() {
  const { user } = useContext(AuthContext);
  const [notifications, setNotifications] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalNotifications, setTotalNotifications] = useState([]);
  const pageSize = 10;

  const onPageChange = (page: any) => {
    setCurrentPage(page);
    const data = {
      notify_to: user?.id,
    };
    getNotifications(data)
      .then(res => {
        if (res.status == true) {
          setTotalNotifications(res);
          const paginatedPosts = paginate(res, page, pageSize);
          setNotifications(paginatedPosts);
        } else {
          setNotifications([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  useEffect(() => {
    const data = {
      notify_to: user?.id,
    };
    getNotifications(data)
      .then(res => {
        setTotalNotifications(res);
        const paginatedPosts = paginate(res, currentPage, pageSize);
        setNotifications(paginatedPosts);
      })
      .catch(err => {
        console.log(err);
      });
  }, []);

  useEffect(() => {
    const timer = setTimeout(() => {
      updateNotificationStatus(user?.id)
        .then(response => { })
        .catch(error => { });
    }, 3000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <>
      <div className="dash-right">
        <h1>
          My <span className="span-color"> Notifications</span>
        </h1>
        <div className="data-management  mb-3 m-p-10">
          <div className="work-experience-fieild m-p-10">
            {notifications.length > 0 ? (
              notifications.slice(0, 5).map((notification_data: any, index: any) => {
                const created_at = moment.utc(notification_data.created_at);
                const currentTime = moment();
                const yesterday = moment().subtract(1, 'day');
                let timeFormatted;

                if (created_at.isSame(currentTime, 'day')) {
                  timeFormatted = created_at.local().format('hh:mm A');
                } else if (created_at.isSame(yesterday, 'day')) {
                  timeFormatted = 'Yesterday';
                } else {
                  timeFormatted = created_at.local().format('MMMM D');
                }
                return (
                  <div className="box-text-img bg-CFE5FF mb-2" key={index}>
                    <div className="avatar">
                      {notification_data.profile_image ? (
                        <img
                          src={`${process.env.NEXT_PUBLIC_IMAGE_URL}images/userprofileImg/${notification_data.profile_image}`}
                          alt="Avatars-notification_data"
                          className="w-40 m-none"
                        />
                      ) : (
                        <small
                          title={notification_data.name}
                          className="text-uppercase w-75 notfication_name_two m-none">
                          {notification_data.name?.split(' ').length === 1
                            ? notification_data.name.substring(0, 2)
                            : notification_data.name
                              ?.split(' ')
                              .map((word: any, index: any) => (index === 0 || index === 1 ? word[0] : ''))
                              .join('')}
                        </small>
                      )}
                    </div>
                    <div className="col-lg-9 col-md-8">
                      <div className="text-align-center m-2">
                        <span
                          className={`${notification_data.is_read === 0 ? 'notification_font_weight' : 'normal'}`}
                          dangerouslySetInnerHTML={{
                            __html: notification_data.notification,
                          }}></span>
                      </div>
                    </div>
                    <div className="col-sm-2 text-right mt-2">
                      <p className="f-12 c-999999  ">
                        {/* {moment.utc(notification_data.created_at).local().startOf('seconds').fromNow()} */}
                        {timeFormatted}
                      </p>
                    </div>
                  </div>
                );
              })
            ) : (
              <>
                <div className="row">
                  <div className="col-sm-12">
                    <p className="f-22 m-center">Inbox</p>
                  </div>
                </div>
                <div className="text-center">
                  <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blank-5.png'} alt="blank-5" />
                  <p className="f-22 c-BABABA mb-2">No New Notifications</p>
                  <p className="f-18 c-BABABA w-400">
                    Check this section for job updates, and
                    <br /> general notifications.{' '}
                  </p>
                </div>
              </>
            )}

            <Pagination
              items={totalNotifications.length}
              currentPage={currentPage}
              pageSize={pageSize}
              onPageChange={onPageChange}
            />
          </div>
        </div>
      </div>
    </>
  );
}
