import React, { useState, useEffect, useContext } from 'react';
import moment from 'moment';
import Link from 'next/link';
import { Tooltip } from 'antd';
import {
  getCurrentUserAllJobs,
  getCompanyFollowers,
  updateFirstTimePopupStatus,
  getCurrentUserDetails,
  getFutureInterviewSchedule,
  getNotifications,
  getTotalMessageUnReadCount,
  getCurrentUserPaymentDetails,
} from '@/lib/frontendapi';
import PopupModal from '../../../components/Common/PopupModal';
import PlanPopup from '../../../components/Common/PlanPopup';
import AuthContext from '@/Context/AuthContext';
import Image from 'next/image';

export default function Dashboard() {
  const { user, refreshUserData } = useContext(AuthContext);
  const [companyFollowersCount, setCompanyFollowersCount] = useState('');
  const [jobs, setJobs] = useState([]);
  const [futureInterviewsSchedule, setFutureInterviewSchedule] = useState([]);
  const [welcomeModal, setWelcomeModal] = useState(false);
  const [upgradePlanModal, setUpgradePlanModal] = useState(false);
  const [totalNotifications, setTotalNotifications] = useState(0);
  const [totalUnReadMessageCount, setTotalUnReadMessageCount] = useState(0);
  const [membershipStatus, setMemberShipStatus] = useState('');
  const [membershipPlan, setMemberShipPlan] = useState('');
  const [featuredJobCount, setFeaturedJobCount] = useState(0);

  const closeWelcomeModel = () => {
    setWelcomeModal(false);
  };

  const updateFirstTimePopup = () => {
    updateFirstTimePopupStatus(user?.id)
      .then(res => {
        if (res.status == true) {
          setWelcomeModal(false);
        } else {
          setWelcomeModal(true);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  useEffect(() => {
    if (!user) {
      return;
    }

    // If user exists but company data is missing, refresh user data
    // if (user.role === 'employer' && !user.company && refreshUserData) {
    //   refreshUserData();
    // }

    const data = {
      company_id: user.company_id,
    };

    getCurrentUserAllJobs(data)
      .then(res => {
        if (res.status == true) {
          setJobs(res.data);
        } else {
          setJobs([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
    getCompanyFollowers(user.company_id)
      .then((res) => {
        if (res.status == true) {
          setCompanyFollowersCount(res.companies_followers.length);
        } else {
          setCompanyFollowersCount("");
        }
      })
      .catch((err) => {
        console.log(err);
      });
    getCurrentUserDetails(user.id)
      .then(res => {
        if (res.status == true) {
          setMemberShipPlan(res.plan);
          setMemberShipStatus(res.membership.toString());
          if (res.user.first_login == '0') {
            setTimeout(() => {
              setWelcomeModal(true);
            }, 2000);
          }
        }
      })
      .catch(err => {
        console.log(err);
      });

    const jobData = {
      user_id: user?.id,
      company_id: user?.company_id,
    };

    const noti_data = {
      notify_to: user.id,
    };
    getNotifications(noti_data)
      .then(res => {
        if (res.status == true) {
          setTotalNotifications(res.data.length);
        } else {
          setTotalNotifications(0);
        }
      })
      .catch(err => {
        console.log(err);
      });
    getFutureInterviewSchedule(user.id)
      .then(res => {
        if (res.status == true) {
          setFutureInterviewSchedule(res.interviews);
        } else {
          setFutureInterviewSchedule([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
    const message_data = {
      sender_id: user?.id,
    };
    getTotalMessageUnReadCount(message_data)
      .then(res => {
        if (res.status == true) {
          setTotalUnReadMessageCount(res.total_unread_message_count);
        } else {
          setTotalUnReadMessageCount(0);
        }
      })
      .catch(err => {
        console.log(err);
      });
    getCurrentUserPaymentDetails(user.id)
      .then(res => {
        if (res.status == true) {
        }
      })
      .catch(err => {
        console.log(err);
      });
  }, [user]);

  const openUpgradePlanModal = () => {
    setUpgradePlanModal(true);
  };

  return (
    <>
      <div className="dash-right">
        <h1>
          Welcome <span className="span-color">{user?.name}</span>
        </h1>
        <div className="row mt-4">
          <div className="col-lg-3 col-md-6 col-6">
            <div className="dash-card d-c-1">
              <div className="row">
                <div className="col-4">
                  <h5 className="dash-card-h5">{jobs?.length || 0}</h5>
                </div>
                <div className="col-8">
                  <div className="text-right">
                    <h6>Active Jobs</h6>
                    <Link href="/employer/jobs">
                      <p className="f-12">View All</p>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-lg-3 col-md-6 col-6">
            <div className="dash-card d-c-2">
              <div className="row">
                <div className="col-4">
                  <h5 className="dash-card-h5">{companyFollowersCount}</h5>
                </div>
                <div className="col-8">
                  <div className="text-right">
                    <h6>Followers</h6>
                    <Link href="/employer/company/followers">
                      <p className="f-12">View All</p>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-lg-3 col-md-6 col-6">
            <div className="dash-card d-c-4">
              <div className="row">
                <div className="col-4">
                  <h5 className="dash-card-h5">{totalUnReadMessageCount}</h5>
                </div>
                <div className="col-8">
                  <div className="text-right">
                    <h6>Messages</h6>
                    <Link href="/employer/messages">
                      <p className="f-12">View All</p>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-lg-3 col-md-6 col-6">
            <div className="dash-card d-c-3">
              <div className="row">
                <div className="col-4">
                  <h5 className="dash-card-h5">{totalNotifications}</h5>
                </div>
                <div className="col-8">
                  <div className="text-right">
                    <h6>Notifications</h6>
                    <Link href="/employer/notifications">
                      <p className="f-12">View All</p>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <p className="over mt-4 mb-4">Recent Job Posts</p>
        <div className="row">
          <div className="col-lg-8 col-md-12">
            {jobs.length > 0 ? (
              jobs.slice(0, 3).map((jobs_data: any, index: any) => {
                return jobs_data.job_status == 'active' ? (
                  <div className="filter filter-sp m-center border-r-16 mb-3" key={index}>
                    <div className="row">
                      <div className="col-sm-9">
                        <p className="p-18">
                          <a target="_blank" href={'/job/' + jobs_data.job_slug}>
                            {jobs_data.job_title}
                          </a>
                        </p>
                        <p className="p-16 mt-1">
                          <a target="_blank" href={'/companies/' + jobs_data.company_slug}>
                            {jobs_data.company_name}
                          </a>
                        </p>

                        {jobs_data.is_featured == '1' ? (
                          <button className="pro">
                            Featured{' '}
                            <img
                              src={process.env.NEXT_PUBLIC_BASE_URL + 'images/pro.png'}
                              alt=" pro"
                              className="w-25 "
                            />
                          </button>
                        ) : (
                          ''
                        )}
                        <ul className="full-time f-12-list">
                          <li>
                            <i className="fa-solid fa-business-time"></i>{' '}
                            {jobs_data.job_type === 'parttime'
                              ? 'Part-Time'
                              : jobs_data.job_type === 'fulltime'
                                ? 'Full-Time'
                                : jobs_data.job_type === 'contract'
                                  ? 'Contract'
                                  : jobs_data.job_type === 'freelance'
                                    ? 'Freelance'
                                    : ' '}{' '}
                          </li>
                          <li>
                            <i className="fa-solid fa-location-dot"></i> {jobs_data.country_name}
                          </li>
                          <li>Created: {moment(jobs_data.created_at).format('LL')}</li>
                          <li>Deadline: {moment(jobs_data.deadline).format('LL')}</li>
                        </ul>
                        <p className="f-18 w-600 mt-2 c-4D4D4D">
                          Job Insights
                          <Tooltip
                            color={'#EBF4FF'}
                            placement="bottom"
                            title={
                              <span style={{ color: '#4D4D4D' }}>
                                how this feature works in a <br></br>friendly tone & concise manner.
                              </span>
                            }>
                            <span
                              className="custom-tooltip-container"
                              style={{ position: 'relative', display: 'inline-block' }}>
                              <i className="fa-solid fa-circle-info c-D9D9D9" style={{ margin: '0px 5px' }}></i>
                            </span>
                          </Tooltip>
                        </p>
                        <div className="row mt-3 mb-3">
                          <div className="col-lg-3 col-md-4 col-4">
                            <div className="right-border">
                              <p className="f-12 c-4D4D4D m-sp-0">Applicants</p>
                              <h3 className="f-37 c-2C2C2C">{jobs_data.jobs_applicants}</h3>
                            </div>
                          </div>
                          <div className="col-lg-4 col-md-4  col-4">
                            <div className="right-border">
                              <p className="f-12 c-4D4D4D m-sp-0">Impressions</p>
                              <h3 className="f-37 c-2C2C2C">
                                {jobs_data.jobViewCountslastweek > jobs_data.jobViewCountslasttwoweek ? (
                                  <i className="fa-solid fa-arrow-up-long c-3D9F79 up-icon"></i>
                                ) : (
                                  <i className="fa-solid fa-arrow-up-long c-3D9F79 up-icon"></i>
                                )}{' '}
                                {parseInt(jobs_data.jobsImpressionpercentage) + '%'}
                              </h3>
                            </div>
                          </div>
                          <div className="col-lg-3 col-md-4 col-4">
                            <div className="">
                              <p className="f-12 c-4D4D4D m-sp-0">Shortlisted</p>
                              <h3 className="f-37 c-2C2C2C">{jobs_data.shortlisted_jobs_count}</h3>
                            </div>
                          </div>
                          <small className="mt-1">
                            Posted by : {user?.id == jobs_data.job_created_by_id ? 'Me' : jobs_data.job_created_name}
                          </small>
                        </div>
                      </div>
                      <div className="col-sm-3 text-right">
                        <div className="dropdown w-400-list ">
                          <button
                            className="dropdown-toggle btn-a primary-size-16 btn-bg-0055BA "
                            type="button"
                            id="dropdownMenuButton1"
                            data-bs-toggle="dropdown"
                            aria-expanded="false">
                            <i className="fa-solid fa-circle-chevron-down sp-right"></i> Actions
                          </button>
                          <ul
                            className="dropdown-menu view-right  m-text-center m-w-60"
                            aria-labelledby="dropdownMenuButton1">
                            <li>
                              <Link
                                className="dropdown-item item-2"
                                target="_blank"
                                href={'/job/' + jobs_data.job_slug}>
                                View public job post
                              </Link>
                            </li>
                            <li>
                              <Link className="dropdown-item item-2" href="/employer/applicants">
                                View all applicants
                              </Link>
                            </li>
                            <li>
                              <Link className="dropdown-item item-2" href="/employer/jobs">
                                Manage job post
                              </Link>
                            </li>
                            <li>
                              {/* do this later  */}
                              {user?.plan == 3 && user?.membership == false ? (
                                <Link className="dropdown-item item-2" href="#">
                                  Post as featured ({25 - featuredJobCount + '/25'})
                                </Link>
                              ) : user?.plan == 2 && user?.membership == false ? (
                                <Link className="dropdown-item item-2" href="#">
                                  Post as featured ({15 - featuredJobCount + '/15'})
                                </Link>
                              ) : user?.plan == 1 ? (
                                <Link className="dropdown-item item-2" href="#">
                                  Post as featured ({0 - featuredJobCount + '/0'})
                                </Link>
                              ) : (
                                <Link className="dropdown-item item-2" href="#">
                                  Post as featured ({0 - featuredJobCount + '/0'})
                                </Link>
                              )}
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  ''
                );
              })
            ) : (
              <div className="filter filter-sp m-center border-r-16 p-5">
                <div className="row">
                  <div className="col-sm-12">
                    <p className="p-18 text-center">Recent Jobs Post Not Found!</p>
                  </div>
                </div>
              </div>
            )}
            <center>
              <p className="explore-jobs mt-4">
                {' '}
                <Link href="/employer/jobs">View All Job Posts</Link>{' '}
              </p>
            </center>
          </div>
          {membershipPlan == '1' || membershipStatus == 'true' ? (
            <>
              <div className="col-lg-4 col-md-12">
                <div className="profile-checklist">
                  <div className="row">
                    <div className="col-12">
                      <h5>Boost your hiring process</h5>
                      <h6>
                        Try <b>Pro+</b> and unlock!
                      </h6>
                    </div>
                  </div>
                  <ul className="Verify">
                    <li>
                      <i className={`fa-regular fa-circle-check`}></i> Unlimited Cv Search.
                    </li>
                    <li className="dis">
                      <i className={`fa-regular fa-circle-check`}></i> Free unlimited User access.
                    </li>
                    <li className="dis">
                      <i className={`fa-regular fa-circle-check`}></i> 25 Job Posting.
                    </li>
                    <li className="dis">
                      <i className={`fa-regular fa-circle-check`}></i> Unlimited Job Share on Linkedin.
                    </li>
                    <li className="dis">
                      <i className={`fa-regular fa-circle-check`}></i>Relationship Management.
                    </li>
                  </ul>
                  <button className="signup-cards white-btn f-22" onClick={openUpgradePlanModal}>
                    <i className="fa-solid fa-bolt"></i>Upgrade Now
                  </button>
                </div>
              </div>
              <PlanPopup show={upgradePlanModal} setModalConfirm={(bool: any) => setUpgradePlanModal(bool)} />
            </>
          ) : (
            <div className="col-lg-4 col-md-12">
              <div className="blue-box">
                <div className="row">
                  <div className="col-10">
                    <p className="f-26 c-fff w-700">Interviews</p>
                    <p className="f-22  c-fff">Coming up...</p>
                  </div>
                  <div className="col-2 text-center">
                    <Tooltip
                      color={'#EBF4FF'}
                      placement="bottom"
                      title={
                        <span style={{ color: '#4D4D4D' }}>
                          how this feature works in a <br></br>friendly tone & concise manner.
                        </span>
                      }>
                      <span
                        className="custom-tooltip-container"
                        style={{ position: 'relative', display: 'inline-block' }}>
                        <i className="fa-solid fa-circle-info c-D9D9D9"></i>
                      </span>
                    </Tooltip>
                  </div>
                </div>
                {futureInterviewsSchedule.length > 0
                  ? futureInterviewsSchedule.slice(0, 2).map((interviews_data: any, index: any) => {
                    return (
                      <div key={index}>
                        <div className="bg-D9D9D9 work-senior p-2 text-center mt-2 mb-3">
                          <p className="f-12 c-2C2C2C w-700 mb-sp c-0070F5">
                            {moment(interviews_data.interview_schedule_date).format('MMMM')}
                          </p>
                          <h3 className="f-54 c-191919 mt-3 mb-3 c-0070F5">
                            {moment(interviews_data.interview_schedule_date).format('D')}
                          </h3>
                          <p className="f-12 c-2C2C2C w-700  mb-sp c-0070F5">
                            {moment(interviews_data.interview_schedule_date).format('dddd')}
                          </p>
                        </div>
                        <p className="f-18 c-fff w-600 ">{interviews_data.job_title}</p>
                        <p className="f-16 w-600 c-fff">{interviews_data.applicants_name}</p>
                        <ul className="full-time f-12-list">
                          <li>
                            <i className="fa-regular fa-clock"></i>{' '}
                            {moment(interviews_data.interview_schedule_from_time, 'hh:mm A').format('hh:mm A')} -{' '}
                            {moment(interviews_data.interview_schedule_to_time, 'hh:mm A').format('hh:mm A')}
                          </li>
                        </ul>
                      </div>
                    );
                  })
                  : ''}
                <p className="mb-0 mt-4">
                  <Link href="/employer/messages/interviews" className="f-16 w-700 c-fff">
                    {' '}
                    VIEW All <i className="fa-solid fa-arrow-right"></i>
                  </Link>
                </p>
              </div>
            </div>
          )}
        </div>
        <PopupModal
          show={welcomeModal}
          handleClose={closeWelcomeModel}
          customclass={'add_company_signup_popup modal-lg big-size body-sp-0 b-r-30'}
          closebtnclass={'d-none close-x mt-2'}
          closebtnicon={'icon'}>
          <div className="popup-modal">
            <div className="row">
              <div className="col-sm-4 popup-right border-left popup-left-2"></div>
              <div className="col-sm-8">
                <div className="popup-left-text p-25 text-left " style={{paddingTop: '100px' , paddingBottom: '67px' }}>
                  <h3>
                    Welcome <span className="span-color"> to The Talent Point! </span>
                  </h3>
                  <br/>
                  <p className="f-22 c-2C2C2C w-500">Your journey to hiring great talent starts now.</p>
                  <br/>
                  <p className="f-24 c-4D4D4D">
                    You've just unlocked <span style={{ fontWeight: 700 }}>free</span> access to a thriving talent marketplace. Post your first job today and discover how easy hiring can be.
                  </p>
                  <br/>
                  <p className="f-24 c-4D4D4D" style={{ fontWeight: 600 }}>
                    Want more visibility and faster hires? Upgrade to Talent Point Pro anytime!
                  </p>
                  <br/>
                  <div className="mt-4">
                    <Link href="/employer/jobs">
                      <button className="btn login me-3" onClick={updateFirstTimePopup}>
                        Post a Job Now
                      </button>
                    </Link>
                    <span
                      className="f-16 c-0055BA"
                      style={{
                        textDecoration: 'underline',
                        cursor: 'pointer',
                        fontWeight: 600,
                        marginLeft: '15px'
                      }}
                      onClick={() => {
                        updateFirstTimePopup();
                        setWelcomeModal(false);
                        openUpgradePlanModal();
                      }}
                    >
                      Learn About PRO
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </PopupModal>
      </div>
    </>
  );
}
