import React, {useState, useEffect, useContext} from 'react';
import moment from 'moment';
import swal from 'sweetalert';
import Link from 'next/link';
import {paginate} from '@/helpers/paginate';
import {deleteJob, getAllIndustries} from '@/lib/adminapi';
import MobileViewEmployerJobsListing from '@/components/Common/MobileViewEmployerJobsListing';
import Pagination from '../../../components/Common/Pagination';
import constantVariable from '../../../lib/constant.config.js';
import PopupModal from '../../../components/Common/PopupModal';
import ModalForm from '@/components/Common/ModalForm';
import CreateJobForm from '@/components/Jobs/CreateJobForm';
import AuthContext from '@/Context/AuthContext';
import {getCurrentUserAllJobs} from '@/lib/frontendapi';
import Image from 'next/image';

interface SwalOptions {
  title?: string;
  text?: string;
  icon?: string;
  dangerMode?: boolean;
  buttons?: string[];
  confirmButtonColor?: string;
}

interface JobViewUser {
  email: string;
  name: string;
  slug: string;
}
export default function Jobs() {
  const {user} = useContext(AuthContext);
  const [jobId, setJobId] = useState('');
  const [modalConfirmPostJob, setModalConfirmPostJob] = useState(false);
  const [modalConfirmJobSubmitConfirmationPopup, setModalConfirmJobSubmitConfirmationPopup] = useState(false);
  const [modalConfirmJobUpdateConfirmationPopup, setModalConfirmJobUpdateConfirmationPopup] = useState(false);
  const [jobs, setJobs] = useState([]);
  const [latestSavedJobsId, setLatestSavedJobsId] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalJobs, setTotalJobs] = useState([]);
  const [allIndustries, setAllIndustries] = useState([]);
  const [membershipPlan, setMemberShipPlan] = useState('');
  const pageSize = constantVariable.paginationn_per_page_records;
  const [membershipStatus, setMemberShipStatus] = useState('');
  const [showMobileView, setShowMobileView] = useState(false);
  const [reload, setReload] = useState(false);
  const [editJobData, setEditJobData]: any = useState([]);

  useEffect(() => {
    if (user) getUsersJob();
    let details = navigator.userAgent;
    let regexp = /android|iphone|kindle|ipad/i;
    let isMobileDevice = regexp.test(details);
    if (isMobileDevice) {
      setShowMobileView(true);
    } else {
      setShowMobileView(false);
    }
  }, [currentPage, pageSize, reload, user]);

  const getUsersJob = () => {
    const data = {
      company_id: user?.company_id || user?.company?.id,
      job_sort: '',
    };
    getCurrentUserAllJobs(data)
      .then(res => {
        if (res.status == true) {
          setTotalJobs(res.data);
          const paginatedPosts = paginate(res.data, currentPage, pageSize);
          setJobs(paginatedPosts);
        } else {
          setJobs([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  const modalConfirmOpenPostJob = () => {
    editJob([]);
    getAllIndustries()
      .then(res => {
        if (res.success == true) {
          setAllIndustries(res.data);
        } else {
          setAllIndustries([]);
        }
      })
      .catch(err => {
        console.log(err);
      });

    setModalConfirmPostJob(true);
  };
  const modalConfirmClosePostJob = () => {
    setModalConfirmPostJob(false);
  };

  const modalConfirmCloseJobSubmitConfirmationPopup = () => {
    setModalConfirmJobSubmitConfirmationPopup(false);
    window.location.reload();
  };

  const modalConfirmCloseJobUpdateConfirmationPopup = () => {
    setModalConfirmJobUpdateConfirmationPopup(false);

    const data = {
      company_id: user?.company_id,
      job_sort: '',
    };
    getCurrentUserAllJobs(data)
      .then(res => {
        if (res.status == true) {
          setTotalJobs(res.data);
          const paginatedPosts = paginate(res.data, currentPage, pageSize);
          setJobs(paginatedPosts);
        } else {
          setJobs([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  const onPageChange = (page: any) => {
    setCurrentPage(page);

    const data = {
      company_id: user?.company_id,
      job_sort: '',
    };
    getCurrentUserAllJobs(data)
      .then(res => {
        if (res.status == true) {
          setTotalJobs(res.data);
          const paginatedPosts = paginate(res.data, page, pageSize);
          setJobs(paginatedPosts);
        } else {
          setJobs([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  const [modalConfirm7, setModalConfirm7] = useState(false);

  const modalConfirmClose7 = () => {
    setModalConfirm7(false);
  };

  const [jobviewuser, setJobViewUser] = useState<JobViewUser[]>([]);

  const [currentPage1, setCurrentPage1] = useState(1);

  const onPageChange1 = (page: number) => {
    setCurrentPage1(page);
  };
  const pageSize1 = 10;

  const paginatedData = paginate(jobviewuser, currentPage1, pageSize1);

  const deletejob = (id: any) => {
    const options: Partial<SwalOptions> = {
      title: 'Are you sure?',
      text: 'You want to delete the job',
      icon: 'warning',
      dangerMode: true,
      buttons: ['Cancel', 'Yes, I am sure!'],
      confirmButtonColor: '#062B60',
    };
    swal(options).then(willDelete => {
      if (willDelete) {
        deleteJob(id)
          .then(res => {
            if (res.status == true) {
              swal('Your Job has been deleted!', {
                icon: 'success',
              });
              const data = {
                company_id: user?.company_id,
                job_sort: '',
              };
              getCurrentUserAllJobs(data)
                .then(res => {
                  if (res.status == true) {
                    setTotalJobs(res.data);
                    const paginatedPosts = paginate(res.data, currentPage, pageSize);
                    setJobs(paginatedPosts);
                  } else {
                    setJobs([]);
                  }
                })
                .catch(err => {
                  console.log(err);
                });
              $('#data_' + id).hide();
            } else {
            }
          })
          .catch(err => {});
      } else {
      }
    });
  };

  const editJob = (job: any) => {
    setEditJobData(job);
    setModalConfirmPostJob(true);
  };

  const handleChangeJobSorting = (e: any) => {
    const data = {
      company_id: user?.company_id,
      job_sort: e.target.value,
    };
    getCurrentUserAllJobs(data)
      .then(res => {
        if (res.status == true) {
          setTotalJobs(res.data);
          const paginatedPosts = paginate(res.data, currentPage, pageSize);
          setJobs(paginatedPosts);
        } else {
          setJobs([]);
        }
      })
      .catch(err => {});
  };

  return (
    <>
      <div className="dash-right">
        <h1>Jobs</h1>
        <div className="row mt-4">
          <div className="col-sm-4 col-6">
            <div className="sort-d-flex mt-2">
              <p className="sort-by">Sort By:</p>
              <select className="all-recent" onChange={handleChangeJobSorting}>
                <option value=""> All</option>
                <option value="active">Active</option>
                <option value="expired">Expired</option>
              </select>
            </div>
          </div>
          <div className="col-sm-8 col-6 text-right mb-3 btn-font-short">
            <ul className="blue-text-line text-right m-center mobole-add-sp">
              {membershipStatus == 'false' ? (
                membershipPlan == '1' ||
                (membershipPlan == '2' && totalJobs.length >= 25) ||
                (membershipPlan == '3' && totalJobs.length >= 35) ? (
                  <li>
                    <button className="btn-a primary-size-16 btn-bg-0055BA" onClick={modalConfirmOpenPostJob}>
                      <i className="fa-solid fa-plus"></i> &nbsp; Post A New Job
                    </button>
                  </li>
                ) : (
                  <li>
                    <button className="btn-a primary-size-16 btn-bg-0055BA" onClick={modalConfirmOpenPostJob}>
                      <i className="fa-solid fa-plus"></i> &nbsp; Post A New Job
                    </button>
                  </li>
                )
              ) : (
                <li>
                  <button className="btn-a primary-size-16 btn-bg-0055BA" onClick={modalConfirmOpenPostJob}>
                    <i className="fa-solid fa-plus"></i> &nbsp; Post A New Job
                  </button>
                </li>
              )}
            </ul>
          </div>
        </div>
        <ModalForm
          open={modalConfirmPostJob}
          title={editJobData.job_title ? `Edit ${editJobData.job_title}` : 'Post a Job'}
          description={'Enter your basic job details information & get started right away.'}
          onCancel={modalConfirmClosePostJob}>
          <CreateJobForm
            onCompleted={() => {
              setModalConfirmPostJob(false);
              getUsersJob();
              setReload(!reload);
            }}
            job={editJobData}
          />
        </ModalForm>
        <ModalForm
          open={modalConfirmJobSubmitConfirmationPopup}
          onCancel={modalConfirmCloseJobSubmitConfirmationPopup}
          title={'Job posted'}>
          <div className="popup-body pt-0">
            <div className="text-center sp-50 pt-0 pb-3">
              <div className="pog-r w-120 m-0auto">
                <img
                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blue-round.png'}
                  alt="blue-round"
                  className="w-120 fa-spin"
                />
                <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/check-i.png'} alt="check-i" className="check-i" />
              </div>
              <h2 className="f-31">
                Job <span className="span-color">Posted</span>
              </h2>
              <p className="f-18 w-600">
                Your job post is live
                <Link href={'/job/' + latestSavedJobsId} className="c-0070F5">
                  {' '}
                  View{' '}
                </Link>
                it here. Go to
                <Link href={'/employer/jobs'} className="c-0070F5">
                  {' '}
                  Jobs{' '}
                </Link>
                for more insights.
              </p>
            </div>
          </div>
        </ModalForm>
        <ModalForm
          title={'Job <span className="span-color">Updated</span>'}
          open={modalConfirmJobUpdateConfirmationPopup}
          onCancel={modalConfirmCloseJobUpdateConfirmationPopup}>
          <div className="popup-body">
            <div className="text-center sp-50 pt-0">
              <div className="pog-r w-120 m-0auto">
                <img
                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blue-round.png'}
                  alt="blue-round"
                  className="w-120 fa-spin"
                />
                <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/check-i.png'} alt="check-i" className="check-i" />
              </div>
              <h2 className="f-31">
                Job <span className="span-color">Updated</span>
              </h2>
              <p className="f-18 w-600">
                Your job post is live
                <Link href={'/job/' + jobId} className="c-0070F5">
                  {' '}
                  View{' '}
                </Link>
                it here. Go to
                <Link href={'/employer/jobs'} className="c-0070F5">
                  {' '}
                  Jobs{' '}
                </Link>
                for more insights.
              </p>
            </div>
          </div>
        </ModalForm>
        {showMobileView == true ? (
          <MobileViewEmployerJobsListing jobsdata={jobs} />
        ) : (
          <div className="table-part mt-4">
            <table className="rwd-table">
              <tbody>
                <tr>
                  <th>
                    JOB TITLE<i className="fa-solid fa-align-left"></i>
                  </th>
                  <th className="w-18">
                    IMPRESSIONS <i className="fa-solid fa-align-left"></i>
                  </th>
                  <th>
                    POSTED ON <i className="fa-solid fa-align-left"></i>
                  </th>
                  <th>
                    ALL RESPONSES <i className="fa-solid fa-align-left"></i>
                  </th>
                  <th>STATUS</th>
                  <th>MANAGE</th>
                </tr>
                {jobs.length > 0 ? (
                  jobs.map((jobs_data: any, index: any) => {
                    return (
                      <tr id={`data_${jobs_data.id}`} key={index}>
                        <td data-th="JOB TITLE">
                          <p className="c-0070F5 f-18 w-600">
                            <a target="_blank" href={'/job/' + jobs_data.job_slug}>
                              {jobs_data.job_title}
                            </a>
                          </p>
                          <p className="c-999999 f-16 ">{jobs_data.country_name}</p>
                          {jobs_data.is_featured == '1' ? (
                            <button className="pro mt-3 mb-3">
                              Featured
                              <img
                                src={process.env.NEXT_PUBLIC_BASE_URL + 'images/pro.png'}
                                alt="Avatars-2"
                                className="w-25"
                              />
                            </button>
                          ) : (
                            ''
                          )}
                          <p className="f-16 c-4D4D4D">
                            <b>{jobs_data.jobs_applicants}</b> Applicants <b>{jobs_data.shortlisted_jobs_count}</b>{' '}
                            Shortlisted
                            <b>
                              <br />
                              {jobs_data.rejected_jobs_count}
                            </b>{' '}
                            Rejected <br />
                          </p>
                          <small className="mt-1">
                            Posted by : {user?.id == jobs_data.job_created_by_id ? 'Me' : jobs_data.job_created_name}
                          </small>
                        </td>
                        <td data-th="IMPRESSIONS">
                          <div className="row">
                            <div className="col-6">
                              <p className="f-22 c-2C2C2C w-500">{jobs_data.totaljobimpression}</p>
                            </div>
                            <div className="col-6">
                              <p className="f-16 c-3D9F79 w-600">
                                {parseFloat(jobs_data.jobsImpressionpercentage).toFixed(2) + '%'}
                              </p>
                            </div>
                          </div>
                        </td>
                        <td data-th="POSTED ON">
                          <p className="location">{moment(jobs_data.created_at).format('LL')}</p>
                        </td>
                        <td data-th="ALL RESPONSES">
                          <a className="team-m" href={`/employer/applicants`}>
                            {jobs_data.jobs_applicants}{' '}
                            <span className="f-12">
                              <span style={{cursor: 'pointer'}}>(View)</span>
                            </span>
                          </a>
                        </td>
                        <td data-th="STATUS">
                          {jobs_data.job_status == 'active' && (
                            <button className="btn-app bg-3D9F79-app">{jobs_data.job_status}</button>
                          )}
                          {jobs_data.job_status == 'closed' && (
                            <button className="btn-app bg-bababa-app">{jobs_data.job_status}</button>
                          )}
                          {jobs_data.job_status == 'expired' && (
                            <button className="btn-app bg-D04E4F-app">{jobs_data.job_status}</button>
                          )}
                        </td>
                        <td data-th="MANAGE">
                          <span style={{cursor: 'pointer'}} onClick={() => editJob(jobs_data)}>
                            <i className="fa-solid fa-pencil edit-pencil"></i>
                          </span>
                          <a href="#" onClick={() => deletejob(jobs_data.id)}>
                            <i className="fa-regular fa-trash-can del-trash"></i>
                          </a>
                        </td>
                      </tr>
                    );
                  })
                ) : (
                  <tr>
                    <td colSpan={6} className="work-experience-fieild m-p-10 text-center">
                      <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blank-1.jpg'} alt="blank-1" className="" />
                      <p className="f-22 c-BABABA mb-1">You don't seem to have any active jobs.</p>
                      <p className="f-18">
                        <Link href="/employer/jobs" className="c-0070F5">
                          Post A Job
                        </Link>
                      </p>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
            <Pagination
              items={totalJobs.length}
              currentPage={currentPage}
              pageSize={pageSize}
              onPageChange={onPageChange}
              activePage={currentPage}
            />
          </div>
        )}
        <ModalForm title={'User Views'} open={modalConfirm7} onCancel={modalConfirmClose7}>
          <section className="container mt-4 mb-4">
            <div className="work-experience-field">
              <div className="row">
                {paginatedData.length > 0 ? (
                  paginatedData.map((jobviewuser: any, index: any) => (
                    <div className="col-lg-12 col-md-9" key={index}>
                      <div className="right-text-edit mb-3">
                        <div className="row mobile-column-reverse">
                          <div className="col-sm-9">
                            {jobviewuser.role == 'admin' ? (
                              ''
                            ) : jobviewuser.role !== 'staff' ? (
                              <a href={`/candidate-profile/${jobviewuser.slug}`}>
                                <h6>{jobviewuser.name}</h6>
                                <p>
                                  <strong>{jobviewuser.email}</strong>
                                </p>
                              </a>
                            ) : (
                              <>
                                <h6>{jobviewuser.name}</h6>
                                <p>
                                  <strong>{jobviewuser.email}</strong>
                                </p>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-center mt-3">No User Views</p>
                )}
                <div className="pagination-wrapper mt-4">
                  <div className="pagination-wrapper">
                    <Pagination
                      items={jobviewuser}
                      currentPage={currentPage1}
                      pageSize={pageSize1}
                      onPageChange={onPageChange1}
                      activePage={currentPage1}
                    />
                  </div>
                </div>
              </div>
            </div>
          </section>
        </ModalForm>
      </div>
    </>
  );
}
