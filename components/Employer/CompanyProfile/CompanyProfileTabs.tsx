import React from 'react';
import Link from 'next/link';
import {Company} from '@/lib/types';

interface CompanyProfileTabsProps {
  currentTab?: string;
  company: Company;
}

const CompanyProfileTabs = ({currentTab, company}: CompanyProfileTabsProps) => {
  return (
    <div className="row m-column-reverse">
      <div className="col-sm-7">
        <ul className={'list-loc m-m-0 mt-4 over-tab ' + (currentTab === 'overview' ? 'blue-active' : '')}>
          <li className={currentTab === 'overview' ? 'active' : ''}>
            <Link href="/employer/company">Overview</Link>
          </li>
          <li className={currentTab === 'profile' ? 'active' : ''}>
            <Link href="/employer/company/profile">
              Profile <i className="fa-solid fa-circle circle-round"></i>
            </Link>
          </li>
          <li className={currentTab === 'followers' ? 'active' : ''}>
            <Link href="/employer/company/followers">Followers</Link>
          </li>
          <li className={currentTab === 'insights' ? 'active' : ''}>
            <Link href="/employer/company/insights">Insights</Link>
          </li>
        </ul>
      </div>
      <div className="col-sm-5">
        <ul className="blue-text-line mt-4 text-right">
          <li>
            <Link target="_blank" href={'/companies/' + company?.id}>
              View Public Profile
            </Link>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default CompanyProfileTabs;
