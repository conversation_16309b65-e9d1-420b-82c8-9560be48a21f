import React, {useState, useEffect, useContext} from 'react';
import {
  getAllCountries,
  getSingleCompanyDetails,
  updateCompanyDetails,
  updateCompanyProfileSocialLinks,
  addTeamMembers,
  getUserAllTeamMembers,
  getSingleTeamMember,
  deleteTeamMember,
  updateTeamMember,
  getAllSectors,
} from '../../../lib/frontendapi';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import swal from 'sweetalert';
import {HtmlEditor} from '../../Common/HtmlEditor';
import CompanyProfileTabs from '@/components/Employer/CompanyProfile/CompanyProfileTabs';
import {Company} from '@/lib/types';
import AuthContext from '@/Context/AuthContext';
import UserProfileImage from '@/components/Common/UserProfileImage';
import {Form, Input, Select, message, notification} from 'antd';
import ErrorHandler from '@/lib/ErrorHandler';
import {textPasteRule} from '@tiptap/core';
import {ButtonUi} from '@/ui/Button';

export default function Profile() {
  const [Country, setCountry] = useState([]);
  const [companyDescription, setCompanyDescription] = useState('');
  const [addCompanyLogo, setAddCompanyLogo] = useState('');
  const [uploadError, setUploadError] = useState('');
  const [inputFields, setInputFields] = useState([
    {
      team_member_email: '',
    },
  ]);
  const [userAllTeamMembers, setUserAllTeamMembers] = useState([]);
  const [editTeamMember, setEditTeamMember] = useState(0);
  const [editTeamMemberList, setEditTeamMemberList]: any = useState([]);

  const [editTeamMemberName, setEditTeamMemberName] = useState('');
  const [editTeamMemberEmail, setEditTeamMemberEmail] = useState('');
  const [sectorData, setSectorData] = useState([]);
  const {Option} = Select;
  const [isLoading, setIsLoading] = useState(false);

  const {user} = useContext(AuthContext);
  const [currentCompany, setCurrentCompany]: any = useState<Company>();
  const [Loading, setLoading] = useState(true);
  const [linkedinUrl, setLinkedinUrl] = useState(currentCompany?.linkedin_link ?? null);
  const [twitterUrl, setTwitterUrl] = useState(currentCompany?.twitter_link ?? null);
  const [instagramUrl, setInstagramUrl] = useState(currentCompany?.instagram_link ?? null);
  const [facebookUrl, setFacebookUrl] = useState(currentCompany?.facebook_link ?? null);

  useEffect(() => {
    setLoading(true);
    if (user?.id) {
      const promises = [
        getSingleCompanyDetails(user?.id),
        getAllCountries(),
        getAllSectors(),
        getUserAllTeamMembers(user?.id),
      ];

      Promise.all(promises)
        .then(([companyDetailsRes, countriesRes, sectorsRes, teamMembersRes]) => {
          if (companyDetailsRes.status === true) {
            setCurrentCompany(companyDetailsRes.data);
            setCompanyDescription(companyDetailsRes.data.company_description);
            console.log(companyDetailsRes);
          }

          if (countriesRes) {
            setCountry(countriesRes);
          } else {
            setCountry([]);
          }

          if (teamMembersRes.status === true) {
            setUserAllTeamMembers(teamMembersRes.data);
          } else {
            setUserAllTeamMembers([]);
          }

          if (sectorsRes.success === true) {
            setSectorData(sectorsRes.sectors);
          } else {
            setSectorData([]);
          }
        })
        .catch(err => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [user]);

  const submitForm = (data: any) => {
    const formData = {
      ...data,
      edit_company_description: companyDescription,
      edit_company_logo: addCompanyLogo[0],
    };

    updateCompanyDetails(user?.company_id, formData)
      .then(res => {
        if (res.status == true) {
          setCurrentCompany(res.data);
          message.success('Saved');
        } else {
          message.error('Failed');
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  };
  const SubmitSocialLinks = (data: any) => {
    console.log(data);
    updateCompanyProfileSocialLinks(user?.company_id, data)
      .then(res => {
        if (res.status == true) {
          message.success('Saved');
        } else {
          message.error('Failed');
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  };

  const addInputField = () => {
    setInputFields([
      ...inputFields,
      {
        team_member_email: '',
      },
    ]);
  };

  const removeInputFields = (index: any) => {
    const rows = [...inputFields];
    rows.splice(index, 1);
    setInputFields(rows);
  };

  const handleChange = (index: any, evnt: any) => {
    const {name, value} = evnt.target;
    const list: any = [...inputFields];
    list[index][name] = value;
    setInputFields(list);
  };

  const submitTeamMembersForm = (event: any) => {
    event.preventDefault();
    setIsLoading(true);
    const user_data = {
      company_id: user?.company_id,
      team_members_email: inputFields,
      created_by_id: user?.id,
      role: 'staff',
      available_resume_count: 0,
    };

    addTeamMembers(user_data)
      .then(res => {
        if (res.status == true) {
          notification.success({message: res.message});
        } else {
          notification.error({message: res.message});
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleImageChange = (event: any) => {
    if (event.target.files && event.target.files[0]) {
      if (event.target.files[0].type.includes('image')) {
        setAddCompanyLogo(event.target.files);
        setUploadError('');
      } else {
        setUploadError('Please upload an image file (JPEG,JPG,PNG).');
      }
    }
  };

  const handleEditTeamMemberClick = (index: number) => {
    if (editTeamMember === index) {
      setEditTeamMember(0);
    } else {
      setEditTeamMember(index);
    }

    getSingleTeamMember(index)
      .then(res => {
        setEditTeamMemberList(res.data);
        setEditTeamMemberName(res.data.name);
        setEditTeamMemberEmail(res.data.email);
      })
      .catch(err => {
        console.log(err);
      });
  };

  const handleTeamMemberDelete = (e: any, id: any) => {
    e.preventDefault();
    swal({
      title: 'Are you sure?',
      text: 'You want to delete your Team Member',
      icon: 'warning',
      dangerMode: true,
      buttons: ['Cancel', 'Yes, I am sure!'],
    }).then(willDelete => {
      if (willDelete) {
        deleteTeamMember(id)
          .then(res => {
            if (res.status === true) {
              swal('Your Team Member has been deleted!', {
                icon: 'success',
              });
              getUserAllTeamMembers(user?.id).then(res => {
                setUserAllTeamMembers(res.data);
              });
            } else {
              notification.error({message: res.message});
            }
          })
          .catch(err => {
            ErrorHandler.showNotification(err);
          });
      }
    });
  };
  const updateTeamMemberForm = (event: any, id: any) => {
    event.preventDefault();
    const data = {
      team_member_name: editTeamMemberName,
    };
    updateTeamMember(id, data)
      .then(res => {
        if (res.status) {
          notification.success({message: res.message});
          getUserAllTeamMembers(user?.id).then(res => {
            setUserAllTeamMembers(res.data);
            console.log(res.data);
          });
          setEditTeamMember(0);
        } else {
          notification.error({message: res.message});
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  const handleCvCountErrorMsg = () => {
    let msg;
    if (user?.membership) {
      msg = 'Hi,memebership plan has been expired. Please upgrade your plan to see more candidates';
    } else {
      msg = 'Hi, you have consumed all CV count. Please upgrade your plan to see more candidates';
    }

    notification.info({message: msg});
  };

  return (
    !Loading && (
      <>
        <div className="dash-right company_profile_page_section">
          <h1>
            My <span className="span-color">Profile</span>
          </h1>
          <CompanyProfileTabs company={currentCompany} currentTab={'profile'} />
          <div className="m-p-10 mt-2">
            <div className="work-experience-fieild m-p-10 mb-3 rounded">
              <div className="row">
                <div className="col-lg-3 col-md-3">
                  <div className="left-text-fieild">
                    <h3 className=" m-center">About</h3>
                    <p className="c-747474  m-center">Tell us about yourself</p>
                  </div>
                </div>
                <div className="col-lg-9 col-md-9">
                  <Form size="large" layout="vertical" onFinish={submitForm}>
                    <div className="row align-items-left">
                      <div className="col-lg-12 col-md-3 col-12">
                        <div className="mb-4 m-auto z-vel">
                          <p className="f-12 c-2C2C2C m-center">Profile Picture</p>
                          <UserProfileImage user={user} showUploadButton={true} className="mxw-unset" />
                        </div>
                      </div>
                    </div>
                    <div className="row">
                      <div className="col-md-6">
                        <Form.Item
                          name="edit_company_name"
                          label="Company Name"
                          rules={[{required: true, message: 'Company Name is required.'}]}
                          initialValue={currentCompany?.company_name}>
                          <Input />
                        </Form.Item>
                      </div>
                      <div className="col-md-6">
                        <Form.Item
                          name="edit_designation"
                          label="Your Designation"
                          rules={[{required: true, message: 'Your Designation is required.'}]}
                          initialValue={currentCompany?.designation}>
                          <Input />
                        </Form.Item>
                      </div>
                    </div>

                    <div className="row">
                      <div className="col-sm-6">
                        <Form.Item
                          name="edit_company_email"
                          label="Email ID"
                          initialValue={currentCompany?.company_email}
                          rules={[
                            {
                              type: 'email',
                              message: 'The input is not valid E-mail!',
                            },
                            {
                              required: true,
                              message: 'Please input your E-mail!',
                            },
                          ]}
                          required>
                          <Input placeholder="email" />
                        </Form.Item>
                      </div>
                      <div className="col-sm-6">
                        <Form.Item
                          rules={[
                            {required: true, message: 'Contact Number is required.'},
                            {min: 10, message: 'Please Enter min length 10.'},
                            {max: 12, message: 'Please Enter max length 12.'},
                          ]}
                          name="edit_company_contact_no"
                          label="Contact Number"
                          initialValue={currentCompany?.company_contact_no}
                          required>
                          <PhoneInput country={'ae'} enableSearch />
                        </Form.Item>
                      </div>
                    </div>
                    <div className="row">
                      <div className="col-md-6">
                        <Form.Item
                          name="edit_company_website"
                          label="Website"
                          initialValue={currentCompany?.company_website}
                          rules={[{required: true, message: 'Website is required.'}]}>
                          <Input />
                        </Form.Item>
                      </div>
                      <div className="col-md-6">
                        <Form.Item
                          name="edit_company_location"
                          label="Location*"
                          initialValue={parseInt(currentCompany?.company_location)}
                          rules={[{required: true, message: 'Company Location is required.'}]}>
                          <Select>
                            {Country.length > 0 ? (
                              Country.map((CountryData: any) => (
                                <Option value={CountryData.id} key={CountryData.id}>
                                  {CountryData.country_name}
                                </Option>
                              ))
                            ) : (
                              <Option value={''}>Select Location</Option>
                            )}
                          </Select>
                        </Form.Item>
                      </div>
                    </div>

                    <div className="row">
                      <div className="col-md-6">
                        <Form.Item
                          name="edit_company_sector"
                          label="Sector*"
                          initialValue={parseInt(currentCompany?.company_sector)}
                          rules={[{required: true, message: 'Company Sector is required.'}]}>
                          <Select>
                            {sectorData.length > 0 ? (
                              sectorData.map((sector_data: any) => (
                                <Option value={sector_data.id} key={sector_data.id}>
                                  {sector_data.sector_name}
                                </Option>
                              ))
                            ) : (
                              <Option value="">No Data Found</Option>
                            )}
                          </Select>
                        </Form.Item>
                      </div>
                      <div className="col-md-6">
                        <Form.Item
                          name="edit_no_of_employees"
                          label="Number of Employees*"
                          initialValue={currentCompany?.no_of_employees}
                          rules={[{message: 'Company No Of Employees is required.'}]}>
                          <Select>
                            <Option value="0-50">0-50</Option>
                            <Option value="51-100">51-100</Option>
                            <Option value="101-200">101-200</Option>
                            <Option value="501-1000">501-1000</Option>
                            <Option value="1001-2000">1001-2000</Option>
                            <Option value="2001-5000">2001-5000</Option>
                            <Option value="5000+">5000+</Option>
                          </Select>
                        </Form.Item>
                      </div>
                    </div>
                    <Form.Item name="" label="About Our Company" initialValue={companyDescription}>
                      <HtmlEditor
                        value={companyDescription}
                        onChange={(name: any, value: any) => setCompanyDescription(value)}
                      />
                    </Form.Item>
                    <div className="d-flex justify-content-end text-right mt-3">
                      <Form.Item>
                        <button className="cancel">Cancel</button>
                      </Form.Item>
                      <Form.Item>
                        <button className="save" type="submit">
                          Save
                        </button>
                      </Form.Item>
                    </div>
                  </Form>
                </div>
              </div>
            </div>

            <div className="work-experience-fieild m-p-10 mb-3 rounded">
              <div className="row">
                <div className="col-lg-3 col-md-3">
                  <div className="left-text-fieild">
                    <h3>Social Links</h3>
                    <p className="c-747474">
                      Where can talent find you <br /> online?
                    </p>
                  </div>
                </div>
                <div className="col-lg-9 col-md-9">
                  <Form
                    layout="vertical"
                    size="large"
                    className="form-experience-fieild"
                    key={2}
                    onFinish={SubmitSocialLinks}>
                    <div className="left-text-fieild">
                      <h3>Additional Links</h3>
                    </div>
                    <Form.Item
                      name="edit_linkedin_link"
                      label="LinkedIn"
                      initialValue={currentCompany?.linkedin_link}
                      rules={[
                        {
                          type: 'url',
                          message: 'Please enter a valid URL for the LinkedIn link!',
                        },
                      ]}>
                      <Input
                        placeholder="https://www.linkedin.com"
                        prefix={<i className="fa-brands fa-linkedin right-line"></i>}
                        onChange={(e: any) => {
                          setLinkedinUrl(e.target.value || null);
                        }}
                      />
                    </Form.Item>

                    <Form.Item
                      name="edit_twitter_link"
                      label="Twitter"
                      initialValue={currentCompany?.twitter_link}
                      rules={[
                        {
                          type: 'url',
                          message: 'Please enter a valid URL for the Twitter link!',
                        },
                      ]}>
                      <Input
                        placeholder="https://twitter.com"
                        prefix={<i className="fa-brands fa-x-twitter right-line"></i>}
                        onChange={(e: any) => {
                          setTwitterUrl(e.target.value || null);
                        }}
                      />
                    </Form.Item>

                    <Form.Item
                      name="edit_instagram_link"
                      label="Instagram"
                      initialValue={currentCompany?.instagram_link || null}
                      rules={[
                        {
                          type: 'url',
                          message: 'Please enter a valid URL for the Instagram link!',
                        },
                      ]}>
                      <Input
                        placeholder="https://www.instagram.com"
                        prefix={<i className="fa-brands fa-instagram right-line"></i>}
                        onChange={(e: any) => {
                          setInstagramUrl(e.target.value || null);
                        }}
                      />
                    </Form.Item>
                    <Form.Item
                      name="edit_facebook_link"
                      label="Facebook"
                      initialValue={currentCompany?.facebook_link}
                      rules={[
                        {
                          type: 'url',
                          message: 'Please enter a valid URL for the Facebook link!',
                        },
                      ]}>
                      <Input
                        placeholder="https://facebook.com"
                        prefix={<i className="fa-brands fa-facebook-f right-line"></i>}
                        onChange={(e: any) => {
                          setFacebookUrl(e.target.value);
                        }}
                      />
                    </Form.Item>
                    <div className="d-flex justify-content-end text-right mt-3">
                      <Form.Item>
                        <button className="cancel">Cancel</button>
                      </Form.Item>
                      <Form.Item>
                        <ButtonUi
                          variant="contained"
                          color="primary"
                          className="save"
                          style={{
                            height: 47.2,
                            padding: '0 25px',
                          }}
                          type="submit"
                          disabled={
                            currentCompany?.linkedin_link === linkedinUrl &&
                            currentCompany?.twitter_link === twitterUrl &&
                            currentCompany?.instagram_link === instagramUrl &&
                            currentCompany?.facebook_link === facebookUrl
                          }
                          onClick={() => SubmitSocialLinks(currentCompany)}>
                          Save
                        </ButtonUi>
                      </Form.Item>
                    </div>
                  </Form>
                </div>
              </div>
            </div>

            <div className="work-experience-fieild m-p-10 mb-3 rounded">
              <div className="row">
                <div className="col-lg-3 col-md-3">
                  <div className="left-text-fieild">
                    <h3>Team Members</h3>
                    <p className="c-747474">
                      Add your team members <br /> to your company profile
                    </p>
                  </div>
                </div>
                <div className="col-lg-9 col-md-9">
                  {userAllTeamMembers.map((user_team_members: any, index: any) => (
                    <div className="right-text-edit mt-1" key={index}>
                      <div className="row mobile-column-reverse">
                        <div className="col-sm-9">
                          <h6>{user_team_members.name}</h6>
                          <p>
                            <strong>{user_team_members.email}</strong>
                          </p>
                        </div>
                        <div className="col-sm-3 text-right">
                          <div className="edit-pi">
                            <i
                              className="fa-solid fa-square-pen"
                              onClick={() => handleEditTeamMemberClick(user_team_members.id)}></i>
                          </div>
                        </div>
                      </div>
                      {editTeamMember === user_team_members.id && (
                        <form
                          className="form-experience-fieild"
                          onSubmit={e => updateTeamMemberForm(e, editTeamMemberList.id)}>
                          <input type="hidden" name="id" value={editTeamMemberList.id} />
                          <div className="form_field_sec">
                            <input
                              type="text"
                              placeholder="Enter Team Member Name"
                              name="team_member_name"
                              onChange={(e: any) => setEditTeamMemberName(e.target.value)}
                              className="fild-des"
                              value={editTeamMemberName}
                            />
                            <label>Team Member Name</label>
                          </div>
                          <div className="form_field_sec">
                            <input
                              type="email"
                              name="team_member_email"
                              placeholder="Enter Team Member Email"
                              value={editTeamMemberEmail}
                              className="fild-des"
                              readOnly
                            />
                            <label>Team Member Email</label>
                          </div>
                          <div className="text-right mt-3">
                            <a className="rmewp" onClick={e => handleTeamMemberDelete(e, editTeamMemberList.id)}>
                              Remove Team Member
                            </a>
                            <a className="cancel" onClick={() => setEditTeamMember(0)}>
                              Cancel
                            </a>
                            <button className="save">Save</button>
                          </div>
                        </form>
                      )}
                    </div>
                  ))}
                  <form className="form-experience-fieild" onSubmit={submitTeamMembersForm}>
                    <Form layout="vertical">
                      <Form.Item name="edit_designation" className="m-0" label="Add Team Member">
                        {inputFields.map((data: any, index) => {
                          const {team_member_email} = data;
                          return (
                            <div className="row" key={index}>
                              <div className="col-sm-10">
                                <div className="form-group">
                                  <input
                                    type="email"
                                    placeholder="<EMAIL>"
                                    className="fild-des mt-0 mb-2"
                                    onChange={evnt => handleChange(index, evnt)}
                                    value={team_member_email}
                                    name={'team_member_email'}
                                    pattern="^([a-zA-Z0-9_\-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([a-zA-Z0-9\-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$"
                                  />
                                </div>
                              </div>
                              <div className="col-sm-2">
                                {inputFields.length !== 1 ? (
                                  <button
                                    className="btn btn-outline-danger danger-sp mt-1 "
                                    onClick={removeInputFields}>
                                    <i className="fa-solid fa-xmark"></i>
                                  </button>
                                ) : (
                                  ''
                                )}
                              </div>
                            </div>
                          );
                        })}
                      </Form.Item>
                    </Form>

                    {!user?.membership ? (
                      <p className="f-16-form add-member">
                        <a role="button" onClick={addInputField}>
                          <i className="fa-solid fa-plus"></i> Add Team Member
                        </a>
                      </p>
                    ) : (
                      <p className="f-16-form add-member" onClick={handleCvCountErrorMsg} role="button">
                        <i className="fa-solid fa-plus"> </i> Add Team Member
                      </p>
                    )}

                    <div className="text-right mt-3">
                      <a className="cancel">Cancel</a>
                      <button className="save" type="submit" disabled={isLoading}>
                        {' '}
                        {isLoading ? 'Please wait...' : 'Save'}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    )
  );
}
