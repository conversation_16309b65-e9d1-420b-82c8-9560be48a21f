import React, { useState, useEffect, useContext } from 'react';
import { getCompanyFollowers, getSingleCompanyDetails } from '../../../lib/frontendapi';
import moment from 'moment';
import Link from 'next/link';
import Pagination from '../../../components/Common/Pagination';
import { paginate } from '../../../helpers/paginate';
import constantVariable from '../../../lib/constant.config.js';
import AuthContext from '@/Context/AuthContext';
import Image from 'next/image';

export default function Followers() {
  const { user } = useContext(AuthContext);
  const [companyFollowers, setCompanyFollowers] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCompanyFollowers, setTotalCompanyFollowers] = useState([]);
  const pageSize = constantVariable.paginationn_per_page_records;
  const [company_slug, setCompanySlug] = useState('');

  const onPageChange = (page: any) => {
    setCurrentPage(page);
    getCompanyFollowers(user?.company_id)
      .then(res => {
        if (res.status == true) {
          setTotalCompanyFollowers(res.companies_followers);
          const paginatedPosts = paginate(res.companies_followers, page, pageSize);
          setCompanyFollowers(paginatedPosts);
        } else {
          setCompanyFollowers([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  useEffect(() => {
    getCompanyFollowers(user?.company_id)
      .then(res => {
        if (res.status == true) {
          setTotalCompanyFollowers(res.companies_followers);
          const paginatedPosts = paginate(res.companies_followers, currentPage, pageSize);
          setCompanyFollowers(paginatedPosts);
        } else {
          setCompanyFollowers([]);
        }
      })
      .catch(err => {
        console.log(err);
      });

    getSingleCompanyDetails(user?.id)
      .then(res => {
        if (res.status == true) {
          setCompanySlug(res.data.company_slug);
        } else {
          setCompanySlug('');
        }
      })
      .catch(err => {
        console.log(err);
      });
  }, [user]);

  return (
    <>
      <div className="dash-right">
        <h1>
          My <span className="span-color">Profile</span>
        </h1>
        <div className="row m-column-reverse">
          <div className="col-sm-7">
            <ul className="list-loc m-m-0 mt-4  over-tab">
              <li>
                <Link href="/employer/company">Overview</Link>
              </li>
              <li>
                <Link href="/employer/company/profile">
                  Profile <i className="fa-solid fa-circle circle-round"></i>
                </Link>
              </li>
              <li className="active">
                <Link href="/employer/company/followers">Followers</Link>
              </li>
              <li>
                <Link href="/employer/company/insights">Insights</Link>
              </li>
            </ul>
          </div>
          <div className="col-sm-5">
            <ul className="blue-text-line mt-4 text-right">
              <li>
                <Link target="_blank" href={'/companies/' + company_slug}>
                  View Public Profile
                </Link>
              </li>
            </ul>
          </div>
        </div>
        <div className="data-management m-p-10">
          <div className="row">
            <div className="col-lg-12">
              <div className="left-text-fieild">
                <h3 className=" m-center">Your Followers</h3>
              </div>
            </div>
          </div>
          <div className="table-part mt-1">
            <table className="rwd-table">
              <tbody>
                <tr>
                  <th>PROFILE</th>
                  <th>
                    <div className="text-right">TIME</div>{' '}
                  </th>
                </tr>
                {companyFollowers.length > 0 ? (
                  companyFollowers.map((company_followers: any, index: any) => {
                    return (
                      <tr key={index}>
                        <td data-th="PROFILE">
                          <div className="row">
                            <div className="col pr-0">
                              <img
                                src={
                                  company_followers.profile?.source ||
                                  process.env.NEXT_PUBLIC_BASE_URL + 'images/Avatars-1.png'
                                }
                                alt="Avatars-1"
                                className="w-48 round-img-200"
                              />
                            </div>
                            <div className="col-sm-11 col-9 pl-0 name-candi">
                              <p className="c-n mb-0">
                                <Link href={'/candidate-profile/' + company_followers.candidate_profile_slug}>
                                  {company_followers.candidate_name}
                                </Link>
                              </p>
                              <p className="f-16 w-600 c-2C2C2C">
                                {company_followers.candidate_current_position}{' '}
                                {company_followers.currently_work_here == '1'
                                  ? '@' + company_followers.candidate_currently_company
                                  : ''}
                              </p>
                            </div>
                          </div>
                        </td>
                        <td data-th="TIME">
                          <div className="text-right right-time">
                            <p className="f-16 w-600 c-999999">
                              {moment(company_followers.created_at).format('HH:mm A')}
                            </p>
                          </div>
                        </td>
                      </tr>
                    );
                  })
                ) : (
                  <tr>
                    <td data-th="TIME" colSpan={2} className="f-16 w-600 c-999999 text-center">
                      No followers found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
            <Pagination
              items={totalCompanyFollowers.length}
              currentPage={currentPage}
              pageSize={pageSize}
              onPageChange={onPageChange}
            />
          </div>
        </div>
      </div>
    </>
  );
}
