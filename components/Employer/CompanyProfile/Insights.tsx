import React, { useState, useEffect, useContext } from 'react';
import { getCurrentUserData } from '../../../lib/session';
import Link from 'next/link';
import {
  getCompanyFirstInsightsChart,
  getCompanySecondInsightsChart,
  getSingleEmployerCompanyDetails,
} from '../../../lib/frontendapi';
import {
  Chart as ChartJS,
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  PointElement,
  LineElement,
} from 'chart.js';
import { Pie, Bar } from 'react-chartjs-2';
import e from 'express';
import AuthContext from '@/Context/AuthContext';
import PlanPopup from '../../../components/Common/PlanPopup';
ChartJS.register(ArcElement, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, PointElement, LineElement);
import Image from 'next/image';
export default function Insights() {
  const [companyFollowers, setCompanyFollowers] = useState([]);
  const [companyFollowersCount, setCompanyFollowersCount] = useState(0);
  const [companyProfileAllUsersViewsCount, setCompanyProfileAllUsersViewsCount] = useState(0);
  const [companyActiveJobs, setCompanyActiveJobs] = useState(0);
  const [totalCompanyJobApplicationsCount, setTotalCompanyJobApplicationsCount] = useState(0);
  const [totalApplicationsCount, setTotalApplicationsCount] = useState(0);
  const [totalCompanyFirstChartShortlistedCount, setTotalCompanyFirstChartShortlistedCount] = useState(0);
  const [totalCompanyFirstChartRejectedCount, setTotalCompanyFirstChartRejectedCount] = useState(0);
  const [totalCompanySecondChartShortlistedCount, setTotalCompanySecondChartShortlistedCount] = useState([]);
  const [totalCompanySecondChartRejectedCount, setTotalCompanySecondChartRejectedCount] = useState([]);
  const [totalCompanySecondChartLables, setTotalCompanySecondChartLables] = useState([]);
  const [totalCompanySecondSectionApplicantsCount, setTotalCompanySecondSectionApplicantsCount] = useState(0);
  const [totalCompanySecondSectionShortlistedCount, setTotalCompanySecondSectionShortlistedCount] = useState(0);
  const [totalCompanySecondSectionRejectedCount, setTotalCompanySecondSectionRejectedCount] = useState(0);
  const [timeType, setTimeType] = useState('');
  const [companyProfileImpressionPercentage, setCompanyProfileImpressionPercentage] = useState('');
  const [jobsImpressionPercentage, setJobsImpressionPercentage] = useState('');
  const [companyProfileViewlastWeekCount, setCompanyProfileViewlastWeekCount] = useState('');
  const [companyProfileViewlastTwoWeekCount, setCompanyProfileViewlastTwoWeekCount] = useState('');
  const [jobslastWeekCount, setJobslastWeekCount] = useState('');
  const [jobslastTwoWeekCount, setJobslastTwoWeekCount] = useState('');
  const [companyFollowersImpressionPercentage, setCompanyFollowersImpressionPercentage] = useState('');
  const [companyFollowerslastWeekCount, setCompanyFollowerslastWeekCount] = useState('');
  const [companyFollowerslastTwoWeekCount, setCompanyFollowerslastTwoWeekCount] = useState('');
  const [applicantsImpressionPercentage, setApplicantsImpressionPercentage] = useState('');
  const [applicantslastWeekCount, setApplicantslastWeekCount] = useState('');
  const [applicantslastTwoWeekCount, setApplicantslastTwoWeekCount] = useState('');

  const [applicationsImpressionPercentage, setApplicationsImpressionPercentage] = useState('');
  const [applicationsLastTwoWeekCount, setApplicationsLastTwoWeekCount] = useState('');
  const [applicationsLastWeekCount, setApplicationsLastWeekCount] = useState('');
  const [applicationsShortlistedImpressionPercentage, setApplicationsShortlistedImpressionPercentage] = useState('');
  const [applicationsShortlistedLastTwoWeekCount, setApplicationsShortlistedLastTwoWeekCount] = useState('');
  const [applicationsShortlistedLastWeekCount, setApplicationsShortlistedLastWeekCount] = useState('');
  const [applicationsRejectedImpressionPercentage, setApplicationsRejectedImpressionPercentage] = useState('');
  const [applicationsRejectedLastTwoWeekCount, setApplicationsRejectedLastTwoWeekCount] = useState('');
  const [applicationsRejectedLastWeekCount, setApplicationsRejectedLastWeekCount] = useState('');
  const [company_slug, setCompanySlug] = useState('');
  const { user } = useContext(AuthContext);
  const [modalConfirm, setModalConfirm] = useState(false);

  useEffect(() => {
    const current_user_data: any = getCurrentUserData();
    const datas = {
      user_id: current_user_data.id,
      company_id: current_user_data.company_id,
      first_section_time_type: '',
    };
    // commented for future use

    if (user?.company_id, user?.id) {
      const data = {
        company_id: user?.company_id,
        user_id: user?.id,
      }
      getCompanyFirstInsightsChart(data)
        .then(res => {
          if (res.status == true) {
            setTotalCompanyFirstChartShortlistedCount(res.totalShortlistedApplicants);
            setTotalCompanyFirstChartRejectedCount(res.totalRejectedApplicants);
            setTotalApplicationsCount(res.totalApplicants);
            setCompanyActiveJobs(res.active_jobs_count);
            setCompanyProfileAllUsersViewsCount(res.company_profile_views_count);
            setCompanyFollowersCount(res.company_followers_count);
            setCompanyFollowers(res.company_followers);
            setCompanyProfileImpressionPercentage(res.company_profileView_Impression_percentage);
            setJobsImpressionPercentage(res.jobs_Impression_percentage);
            setCompanyProfileViewlastWeekCount(res.company_profileView_Counts_lastweek);
            setCompanyProfileViewlastTwoWeekCount(res.company_profileView_Counts_lasttwoweek);
            setJobslastWeekCount(res.jobs_lastweek);
            setJobslastTwoWeekCount(res.jobs_lasttwoweek);
            setCompanyFollowersImpressionPercentage(res.company_followers_ImpressionPercentage);
            setCompanyFollowerslastWeekCount(res.company_followers_lastweek);
            setCompanyFollowerslastTwoWeekCount(res.company_followers_lasttwoweek);
            setApplicantsImpressionPercentage(res.applicants_ImpressionPercentage);
            setApplicantslastWeekCount(res.applicants_lastweek);
            setApplicantslastTwoWeekCount(res.applicants_lasttwoweek);
          } else {
            setTotalCompanyFirstChartShortlistedCount(0);
            setTotalCompanyFirstChartRejectedCount(0);
            setTotalApplicationsCount(0);
            setCompanyActiveJobs(0);
            setCompanyProfileAllUsersViewsCount(0);
            setCompanyFollowersCount(0);
            setCompanyFollowers([]);
            setCompanyProfileImpressionPercentage('');
            setJobsImpressionPercentage('');
            setCompanyProfileViewlastWeekCount('');
            setCompanyProfileViewlastTwoWeekCount('');
            setJobslastWeekCount('');
            setJobslastTwoWeekCount('');
            setCompanyFollowersImpressionPercentage('');
            setCompanyFollowerslastWeekCount('');
            setCompanyFollowerslastTwoWeekCount('');
            setApplicantsImpressionPercentage('');
            setApplicantslastWeekCount('');
            setApplicantslastTwoWeekCount('');
          }
        })
        .catch(err => {
          console.log(err);
        });
    }

    // getCompanyFirstInsightsChart(datas)
    //   .then(res => {
    //     if (res.status == true) {
    //       setTotalCompanyFirstChartShortlistedCount(res.totalShortlistedApplicants);
    //       setTotalCompanyFirstChartRejectedCount(res.totalRejectedApplicants);
    //       setTotalApplicationsCount(res.totalApplicants);
    //       setCompanyActiveJobs(res.active_jobs_count);
    //       setCompanyProfileAllUsersViewsCount(res.company_profile_views_count);
    //       setCompanyFollowersCount(res.company_followers_count);
    //       setCompanyFollowers(res.company_followers);
    //       setCompanyProfileImpressionPercentage(res.company_profileView_Impression_percentage);
    //       setJobsImpressionPercentage(res.jobs_Impression_percentage);
    //       setCompanyProfileViewlastWeekCount(res.company_profileView_Counts_lastweek);
    //       setCompanyProfileViewlastTwoWeekCount(res.company_profileView_Counts_lasttwoweek);
    //       setJobslastWeekCount(res.jobs_lastweek);
    //       setJobslastTwoWeekCount(res.jobs_lasttwoweek);
    //       setCompanyFollowersImpressionPercentage(res.company_followers_ImpressionPercentage);
    //       setCompanyFollowerslastWeekCount(res.company_followers_lastweek);
    //       setCompanyFollowerslastTwoWeekCount(res.company_followers_lasttwoweek);
    //       setApplicantsImpressionPercentage(res.applicants_ImpressionPercentage);
    //       setApplicantslastWeekCount(res.applicants_lastweek);
    //       setApplicantslastTwoWeekCount(res.applicants_lasttwoweek);
    //     } else {
    //       setTotalCompanyFirstChartShortlistedCount(0);
    //       setTotalCompanyFirstChartRejectedCount(0);
    //       setTotalApplicationsCount(0);
    //       setCompanyActiveJobs(0);
    //       setCompanyProfileAllUsersViewsCount(0);
    //       setCompanyFollowersCount(0);
    //       setCompanyFollowers([]);
    //       setCompanyProfileImpressionPercentage('');
    //       setJobsImpressionPercentage('');
    //       setCompanyProfileViewlastWeekCount('');
    //       setCompanyProfileViewlastTwoWeekCount('');
    //       setJobslastWeekCount('');
    //       setJobslastTwoWeekCount('');
    //       setCompanyFollowersImpressionPercentage('');
    //       setCompanyFollowerslastWeekCount('');
    //       setCompanyFollowerslastTwoWeekCount('');
    //       setApplicantsImpressionPercentage('');
    //       setApplicantslastWeekCount('');
    //       setApplicantslastTwoWeekCount('');
    //     }
    //   })
    //   .catch(err => {
    //     console.log(err);
    //   });

    const second_chart_datas = {
      company_id: current_user_data.company_id,
      user_id: current_user_data.id,
      time_type: '',
    };
    getCompanySecondInsightsChart(second_chart_datas)
      .then(res => {
        if (res.status == true) {
          setTotalCompanySecondSectionApplicantsCount(res.totalApplicants);
          setTotalCompanySecondSectionShortlistedCount(res.totalShortlistedApplicants);
          setTotalCompanySecondSectionRejectedCount(res.totalRejectedApplicants);
          setTotalCompanySecondChartLables(res.months_labels_data);
          setTotalCompanySecondChartShortlistedCount(res.shortlisted_applicants_counts_data);
          setTotalCompanySecondChartRejectedCount(res.rejected_applicants_counts_data);

          setApplicationsImpressionPercentage(res.applicants_ImpressionPercentage);
          setApplicationsLastTwoWeekCount(res.applicants_lasttwoweek);
          setApplicationsLastWeekCount(res.applicants_lastweek);
          setApplicationsShortlistedImpressionPercentage(res.applicants_shortlisted_ImpressionPercentage);
          setApplicationsShortlistedLastTwoWeekCount(res.applicants_shortlisted_lasttwoweek);
          setApplicationsShortlistedLastWeekCount(res.applicants_shortlisted_lastweek);
          setApplicationsRejectedImpressionPercentage(res.applicants_rejected_ImpressionPercentage);
          setApplicationsRejectedLastTwoWeekCount(res.applicants_rejected_lasttwoweek);
          setApplicationsRejectedLastWeekCount(res.applicants_rejected_lastweek);
        } else {
          setTotalCompanySecondSectionApplicantsCount(0);
          setTotalCompanySecondSectionShortlistedCount(0);
          setTotalCompanySecondSectionRejectedCount(0);
          setTotalCompanySecondChartLables([]);
          setTotalCompanySecondChartShortlistedCount([]);
          setTotalCompanySecondChartRejectedCount([]);
          setApplicationsImpressionPercentage('');
          setApplicationsLastTwoWeekCount('');
          setApplicationsLastWeekCount('');
          setApplicationsShortlistedImpressionPercentage('');
          setApplicationsShortlistedLastTwoWeekCount('');
          setApplicationsShortlistedLastWeekCount('');
          setApplicationsRejectedImpressionPercentage('');
          setApplicationsRejectedLastTwoWeekCount('');
          setApplicationsRejectedLastWeekCount('');
        }
      })
      .catch(err => {
        console.log(err);
      });
    getSingleEmployerCompanyDetails(user?.company_id)
      .then(res => {
        if (res) {
          setCompanySlug(res.company_slug);
        } else {
          setCompanySlug('');
        }
      })
      .catch(err => {
        console.log(err);
      });
  }, [user]);
  const submitTimeAccordingData = (e: any) => {
    e.preventDefault();
    const current_user_data: any = getCurrentUserData();
    const datas = {
      company_id: user?.company_id,
      user_id: user?.id,
      time_type: timeType,
    };
    getCompanySecondInsightsChart(datas)
      .then(res => {
        if (res.status == true) {
          setTotalCompanySecondSectionApplicantsCount(res.totalApplicants);
          setTotalCompanySecondSectionShortlistedCount(res.totalShortlistedApplicants);
          setTotalCompanySecondSectionRejectedCount(res.totalRejectedApplicants);
          setTotalCompanySecondChartLables(res.months_labels_data);
          setTotalCompanySecondChartShortlistedCount(res.shortlisted_applicants_counts_data);
          setTotalCompanySecondChartRejectedCount(res.rejected_applicants_counts_data);
          setApplicationsImpressionPercentage(res.applicants_ImpressionPercentage);
          setApplicationsLastTwoWeekCount(res.applicants_lasttwoweek);
          setApplicationsLastWeekCount(res.applicants_lastweek);
          setApplicationsShortlistedImpressionPercentage(res.applicants_shortlisted_ImpressionPercentage);
          setApplicationsShortlistedLastTwoWeekCount(res.applicants_shortlisted_lasttwoweek);
          setApplicationsShortlistedLastWeekCount(res.applicants_shortlisted_lastweek);
          setApplicationsRejectedImpressionPercentage(res.applicants_rejected_ImpressionPercentage);
          setApplicationsRejectedLastTwoWeekCount(res.applicants_rejected_lasttwoweek);
          setApplicationsRejectedLastWeekCount(res.applicants_rejected_lastweek);
        } else {
          setTotalCompanySecondSectionApplicantsCount(0);
          setTotalCompanySecondSectionShortlistedCount(0);
          setTotalCompanySecondSectionRejectedCount(0);
          setTotalCompanySecondChartLables([]);
          setTotalCompanySecondChartShortlistedCount([]);
          setTotalCompanySecondChartRejectedCount([]);
          setApplicationsImpressionPercentage('');
          setApplicationsLastTwoWeekCount('');
          setApplicationsLastWeekCount('');
          setApplicationsShortlistedImpressionPercentage('');
          setApplicationsShortlistedLastTwoWeekCount('');
          setApplicationsShortlistedLastWeekCount('');
          setApplicationsRejectedImpressionPercentage('');
          setApplicationsRejectedLastTwoWeekCount('');
          setApplicationsRejectedLastWeekCount('');
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  const handleChangeFirstChartFilter = (e: any) => {
    const data = {
      company_id: user?.company_id,
      user_id: user?.id,
      first_section_time_type: e.target.value,
    };
    getCompanyFirstInsightsChart(data)
      .then(res => {
        if (res.status == true) {
          setTotalCompanyFirstChartShortlistedCount(res.totalShortlistedApplicants);
          setTotalCompanyFirstChartRejectedCount(res.totalRejectedApplicants);
          setTotalApplicationsCount(res.totalApplicants);
          setCompanyActiveJobs(res.active_jobs_count);
          setCompanyProfileAllUsersViewsCount(res.company_profile_views_count);
          setCompanyFollowersCount(res.company_followers_count);
          setCompanyFollowers(res.company_followers);
          setCompanyProfileImpressionPercentage(res.company_profileView_Impression_percentage);
          setJobsImpressionPercentage(res.jobs_Impression_percentage);
          setCompanyProfileViewlastWeekCount(res.company_profileView_Counts_lastweek);
          setCompanyProfileViewlastTwoWeekCount(res.company_profileView_Counts_lasttwoweek);
          setJobslastWeekCount(res.jobs_lastweek);
          setJobslastTwoWeekCount(res.jobs_lasttwoweek);
          setCompanyFollowersImpressionPercentage(res.company_followers_ImpressionPercentage);
          setCompanyFollowerslastWeekCount(res.company_followers_lastweek);
          setCompanyFollowerslastTwoWeekCount(res.company_followers_lasttwoweek);
          setApplicantsImpressionPercentage(res.applicants_ImpressionPercentage);
          setApplicantslastWeekCount(res.applicants_lastweek);
          setApplicantslastTwoWeekCount(res.applicants_lasttwoweek);
        } else {
          setTotalCompanyFirstChartShortlistedCount(0);
          setTotalCompanyFirstChartRejectedCount(0);
          setTotalApplicationsCount(0);
          setCompanyActiveJobs(0);
          setCompanyProfileAllUsersViewsCount(0);
          setCompanyFollowersCount(0);
          setCompanyFollowers([]);
          setCompanyProfileImpressionPercentage('');
          setJobsImpressionPercentage('');
          setCompanyProfileViewlastWeekCount('');
          setCompanyProfileViewlastTwoWeekCount('');
          setJobslastWeekCount('');
          setJobslastTwoWeekCount('');
          setCompanyFollowersImpressionPercentage('');
          setCompanyFollowerslastWeekCount('');
          setCompanyFollowerslastTwoWeekCount('');
          setApplicantsImpressionPercentage('');
          setApplicantslastWeekCount('');
          setApplicantslastTwoWeekCount('');
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  const data: any = {
    labels: ['Shortlisted', 'Rejected'],
    datasets: [
      {
        label: 'Count',
        data: [totalCompanyFirstChartShortlistedCount, totalCompanyFirstChartRejectedCount],
        backgroundColor: ['#0eb1d2', '#ff8d74'],
        // borderColor: [
        //     'rgba(255, 99, 132, 1)',
        //     'rgba(54, 162, 235, 1)',
        //     'rgba(255, 206, 86, 1)',
        //     'rgba(75, 192, 192, 1)',
        //     'rgba(153, 102, 255, 1)',
        //     'rgba(255, 159, 64, 1)',
        // ],
        // borderWidth: 1,
      },
    ],
  };
  const options = {
    plugins: {
      title: {
        display: true,
        //text: 'Chart.js Bar Chart - Stacked',
      },
    },
    responsive: true,
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    scales: {
      x: {
        stacked: true,
      },
      y: {
        stacked: true,
      },
    },
  };
  //const labels = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
  const labels = totalCompanySecondChartLables;
  const bar_data = {
    labels,
    datasets: [
      {
        label: 'Shortlisted',
        data: totalCompanySecondChartShortlistedCount,
        //data: [65, 59, 80, 81, 56, 55, 40, 34, 24, 29, 20, 31],
        backgroundColor: '#003d85',
        stack: 'Stack 0',
      },
      {
        label: 'Rejected',
        data: totalCompanySecondChartRejectedCount,
        //data: [65, 59, 80, 81, 56, 55, 40, 34, 24, 29, 20, 31],
        backgroundColor: '#5ca6ff',
        stack: 'Stack 0',
      },
    ],
  };
  const modalConfirmOpen = () => {
    setModalConfirm(true);
  };
  console.log(totalApplicationsCount)
  return (
    <>
      <div className="dash-right">
        <h1>
          Company <span className="span-color">Profile</span>
        </h1>
        <div className="row m-column-reverse">
          <div className="col-sm-7">
            <ul className="list-loc m-m-0 mt-4  over-tab">
              <li>
                <Link href="/employer/company">Overview</Link>
              </li>
              <li>
                <Link href="/employer/company/profile">
                  Profile <i className="fa-solid fa-circle circle-round"></i>
                </Link>
              </li>
              <li>
                <Link href="/employer/company/followers">Followers</Link>
              </li>
              <li className="active">
                <Link href="/employer/company/insights">Insights</Link>
              </li>
            </ul>
          </div>
          <div className="col-sm-5">
            <ul className="blue-text-line mt-4 text-right">
              <li>
                <Link target="_blank" href={'/companies/' + company_slug}>
                  View Public Profile
                </Link>
              </li>
            </ul>
          </div>
        </div>
        <div className="data-management bg-fff  m-p-10">
          <div className="row">
            <div className="col-lg-9">
              <div className="left-text-fieild">
                <h3 className=" m-center">Profile Performance</h3>
              </div>
            </div>
            <div className="col-lg-3 text-right">
              <select className="last-fields" onChange={handleChangeFirstChartFilter}>
                <option value="12_months">12 Months</option>
                <option value="6_months">6 Months</option>
                <option value="30_days">30 days</option>
                <option value="7_days">Last 7 days</option>
              </select>
            </div>
          </div>
          <div className="table-part  mt-1">
            <div className="row mt-2">
              <div className="col-lg-3 col-md-6">
                <div className="dash-card b-fff">
                  <p className="f-12 c-747474 w-700">Profile views</p>
                  <div className="row">
                    <div className="col-7">
                      <p className="f-22 c-2C2C2C mb-0">{companyProfileAllUsersViewsCount}</p>
                    </div>
                    <div className="col-5">
                      <div className="text-right">
                        {companyProfileViewlastWeekCount > companyProfileViewlastTwoWeekCount ? (
                          <p className="f-16 c-3D9F79 w-600">
                            <i className="fa-solid fa-caret-up"></i>
                            {companyProfileImpressionPercentage}
                          </p>
                        ) : (
                          <p className="f-16 c-FD7373 w-600">
                            <i className="fa-solid fa-caret-down"></i>
                            {companyProfileImpressionPercentage}
                          </p>
                        )}
                        {/* <p className='f-16 c-3D9F79 w-600'><i className="fa-solid fa-caret-up"></i> {companyProfileImpressionPercentage}</p> */}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6">
                <div className="dash-card b-fff">
                  <p className="f-12 c-747474 w-700">Followers</p>
                  <div className="row">
                    <div className="col-7">
                      <p className="f-22 c-2C2C2C mb-0">{companyFollowersCount}</p>
                    </div>
                    <div className="col-5">
                      <div className="text-right">
                        {companyFollowerslastWeekCount > companyFollowerslastTwoWeekCount ? (
                          <p className="f-16 c-3D9F79 w-600">
                            <i className="fa-solid fa-caret-up"></i>
                            {companyFollowersImpressionPercentage}
                          </p>
                        ) : (
                          <p className="f-16 c-FD7373 w-600">
                            <i className="fa-solid fa-caret-down"></i>
                            {companyFollowersImpressionPercentage}
                          </p>
                        )}
                        {/* <p className='f-16 c-FD7373 w-600'><i className="fa-solid fa-caret-down"></i> 14%</p> */}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6">
                <div className="dash-card b-fff">
                  <p className="f-12 c-747474 w-700">Active job posts</p>
                  <div className="row">
                    <div className="col-7">
                      <p className="f-22 c-2C2C2C mb-0">{companyActiveJobs}</p>
                    </div>
                    <div className="col-5">
                      <div className="text-right">
                        {jobslastWeekCount > jobslastTwoWeekCount ? (
                          <p className="f-16 c-3D9F79 w-600">
                            <i className="fa-solid fa-caret-up"></i>
                            {jobsImpressionPercentage}
                          </p>
                        ) : (
                          <p className="f-16 c-FD7373 w-600">
                            <i className="fa-solid fa-caret-down"></i>
                            {jobsImpressionPercentage}
                          </p>
                        )}
                        {/* <p className='f-16 c-3D9F79 w-600'><i className="fa-solid fa-caret-up"></i> {jobsImpressionPercentage}</p> */}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6">
                <div className="dash-card b-fff">
                  <p className="f-12 c-747474 w-700">Total Applicants</p>
                  <div className="row">
                    <div className="col-7">
                      <p className="f-22 c-2C2C2C mb-0">{totalApplicationsCount}</p>
                    </div>
                    <div className="col-5">
                      <div className="text-right">
                        {applicantslastWeekCount > applicantslastTwoWeekCount ? (
                          <p className="f-16 c-3D9F79 w-600">
                            <i className="fa-solid fa-caret-up"></i>
                            {applicantsImpressionPercentage}
                          </p>
                        ) : (
                          <p className="f-16 c-FD7373 w-600">
                            <i className="fa-solid fa-caret-down"></i>
                            {applicantsImpressionPercentage}
                          </p>
                        )}
                        {/* <p className='f-16 c-3D9F79 w-600'><i className="fa-solid fa-caret-up"></i> 14%</p> */}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {user?.plan != 1 && user?.plan != null || user?.membership == true
            ?
            <>
              <div className="row p-2">
                <div className="col-sm-8 mt-5">
                  <div className="chart_section" style={{ border: '1px solid #ccc', padding: '15px', borderRadius: '10px' }}>
                    <div className="row pb-5" style={{ padding: '15px' }}>
                      <div className="col-sm-6">
                        <p className="f-16 c-747474 w-700">TOTAL APPLICANTS</p>
                        <h4 className="big-short">
                          <big>{totalApplicationsCount}</big>
                        </h4>
                        <p className="f-12 c-747474">You have an acceptance rating of 70%</p>
                      </div>
                      <div className="col-sm-6">
                        <Pie data={data} />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="col-sm-4 mt-5">
                  <div
                    className="followers_section"
                    style={{ border: '1px solid #ccc', padding: '15px', borderRadius: '10px' }}>
                    <p className="f-16 c-000 w-700">Recent Followers</p>
                    <p className="f-12 c-747474">Lorem Ipsum is simply dummy</p>
                    <ul className="mb-3">
                      {companyFollowers.length > 0 ? (
                        companyFollowers.slice(0, 4).map((company_followers: any, index: any) => {
                          return (
                            <li key={index} style={{ listStyle: 'none' }}>
                              <div className="row">
                                <div className="col-sm-3 pr-0">
                                  {company_followers.profile_image ? (
                                    <img
                                      src={
                                        process.env.NEXT_PUBLIC_IMAGE_URL +
                                        '/images/userprofileImg/' +
                                        company_followers.profile_image
                                      }
                                      alt={company_followers.profile_image}
                                      className="w-40"
                                    />
                                  ) : (
                                    <img
                                      src={'/images/Avatars-4.png'}
                                      alt="Avatars-4"
                                      className="w-40"
                                    />
                                  )}
                                </div>
                                <div className="col-sm-9 pl-0">
                                  <p className="f-16 c-4D4D4D w-600">
                                    <Link href={'/candidate-profile/' + company_followers.candidate_profile_slug}>
                                      {' '}
                                      {company_followers.candidate_name}
                                    </Link>
                                  </p>
                                  <p className="f-14 c-747474">
                                    <Link href={'mailto:' + company_followers.candidate_email}>
                                      {company_followers.candidate_email}
                                    </Link>
                                  </p>
                                </div>
                              </div>
                            </li>
                          );
                        })
                      ) : (
                        <li style={{ listStyle: 'none' }}>No Followers found</li>
                      )}
                    </ul>
                    <p className="f-14 c-747474">
                      <Link href="/employer/company/followers">
                        SEE ALL FOLLOWERS <i className="fa-solid fa-greater-than"></i>{' '}
                      </Link>
                    </p>
                  </div>
                </div>
              </div>
              <div className="row p-2">
                <div className="col-sm-12">
                  <div className="chart_section" style={{ border: '1px solid #ccc', padding: '15px', borderRadius: '10px' }}>
                    <p className="f-22 c-00000 w-700 mb-1">Hiring Metrics</p>
                    <p className="f-14 c-747474">A deep dive into your hiring activity.</p>
                    <hr />
                    <div className="row">
                      <div className="col-sm-9">
                        <ul style={{ display: 'flex' }}>
                          <li style={{ padding: '0px 30px 10px 5px', borderLeft: '2px solid #ccc', listStyle: 'none' }}>
                            <p className="mb-0">Total Candidates</p>
                            <p className="mb-0">
                              <span className="f-14 c-00000 w-700" style={{ padding: '5px' }}>
                                {totalCompanySecondSectionApplicantsCount}
                              </span>
                              {applicantslastWeekCount > applicantslastTwoWeekCount ? (
                                <span className="f-12 c-3D9F79" style={{ padding: '5px' }}>
                                  <i className="fa-solid fa-caret-up"></i>
                                  {applicationsImpressionPercentage}
                                </span>
                              ) : (
                                <span className="f-12 c-FD7373" style={{ padding: '5px' }}>
                                  <i className="fa-solid fa-caret-down"></i>
                                  {applicationsImpressionPercentage}
                                </span>
                              )}
                            </p>
                          </li>
                          <li style={{ padding: '0px 30px 10px 5px', borderLeft: '2px solid #ccc', listStyle: 'none' }}>
                            <p className="mb-0">Shortlisted</p>
                            <p className="mb-0">
                              <span className="f-14 c-00000 w-700" style={{ padding: '5px' }}>
                                {totalCompanySecondSectionShortlistedCount}
                              </span>
                              {applicationsShortlistedLastWeekCount > applicationsShortlistedLastTwoWeekCount ? (
                                <span className="f-12 c-3D9F79" style={{ padding: '5px' }}>
                                  <i className="fa-solid fa-caret-up"></i>
                                  {applicationsShortlistedImpressionPercentage}
                                </span>
                              ) : (
                                <span className="f-12 c-FD7373" style={{ padding: '5px' }}>
                                  <i className="fa-solid fa-caret-down"></i>
                                  {applicationsShortlistedImpressionPercentage}
                                </span>
                              )}
                            </p>
                          </li>
                          <li style={{ padding: '0px 30px 10px 5px', borderLeft: '2px solid #ccc', listStyle: 'none' }}>
                            <p className="mb-0">Rejected</p>
                            <p className="mb-0">
                              <span className="f-14 c-00000 w-700" style={{ padding: '5px' }}>
                                {totalCompanySecondSectionRejectedCount}
                              </span>
                              {applicationsRejectedLastWeekCount > applicationsRejectedLastTwoWeekCount ? (
                                <span className="f-12 c-3D9F79" style={{ padding: '5px' }}>
                                  <i className="fa-solid fa-caret-up"></i>
                                  {applicationsRejectedImpressionPercentage}
                                </span>
                              ) : (
                                <span className="f-12 c-FD7373" style={{ padding: '5px' }}>
                                  <i className="fa-solid fa-caret-down"></i>
                                  {applicationsRejectedImpressionPercentage}
                                </span>
                              )}
                            </p>
                          </li>
                        </ul>
                      </div>
                      <div className="col-sm-3">
                        <form className="form-experience-fieild" onSubmit={submitTimeAccordingData}>
                          <div className="row">
                            <div className="col-sm-6">
                              <select
                                className="selectMonth"
                                style={{ padding: '8px 10px 8px 0px', borderRadius: '4px' }}
                                onChange={(e: any) => setTimeType(e.target.value)}>
                                <option value="monthly">Monthly</option>
                                <option value="yearly">Yearly</option>
                                <option value="weekly">Weekly</option>
                              </select>
                            </div>
                            <div className="col-sm-6">
                              <button className="btn w-100 f-16 w-400" type="submit" style={{ border: '1px solid #737373' }}>
                                <i className="fa-sharp fa-light fa-filter-list"></i> Filter
                              </button>
                            </div>
                          </div>
                        </form>
                      </div>
                    </div>
                    <Bar options={options} data={bar_data} />
                  </div>
                </div>
              </div>
              {/* <img src={process.env.NEXT_PUBLIC_BASE_URL+'images/graf-3.png'} alt="graf-3" className='w-100 mt-4 mb-4'  />  */}
              <form className="form-experience-fieild">
                <div className="text-right mt-3">
                  <button className="cancel">Cancel</button>
                  <button className="save">Save</button>
                </div>
              </form>
            </>
            :
            <>
              <div style={{ backgroundImage: "url('/images/insights-blur-without-text.png')", backgroundSize: "cover", backgroundPosition: "center", padding: "345px 0px 505px 0px", textAlign: "center" }}>
                <p style={{ textAlign: "center", fontSize: "26px", color: "#0055BA", fontWeight: "bold", textShadow: "1px 1px #ccc" }}>Unlock the true potentail of our platform & step up your hiring game.</p>
                <button className="btn f-16" style={{ backgroundColor: "#0070F5", color: "#fff", fontSize: "18px", fontWeight: "600" }} onClick={modalConfirmOpen}>Upgrade Now</button>
              </div>
              <PlanPopup show={modalConfirm} setModalConfirm={(bool: any) => setModalConfirm(bool)} />
            </>
          }
        </div>
      </div>
    </>
  );
}
