import React, {useState, useEffect, useContext} from 'react';
import swal from 'sweetalert';
import Link from 'next/link';
import {
  getCompanyProfileAllUserViewsCount,
  getCompanyFollowers,
  getCompanyActiveJobs,
  getTotalCompanyJobApplicationsCount,
  AddUpdateStaffMember,
  getAllStaffMembers,
  DeleteStaff,
  getAllMemeberShipDetails,
  getSingleCompanyDetails,
  getSingleUserDetails,
} from '@/lib/frontendapi';
import {Tooltip as AntdTooltip, notification} from 'antd';
import PlanPopup from '../../../components/Common/PlanPopup';
import {Company} from '@/lib/types';
import CompanyProfileTabs from '@/components/Employer/CompanyProfile/CompanyProfileTabs';
import AuthContext from '@/Context/AuthContext';
import Image from 'next/image';
import ErrorHandler from '@/lib/ErrorHandler';
import ModalForm from '@/components/Common/ModalForm';
import moment from 'moment';

export default function Overview() {
  const {user} = useContext(AuthContext);
  const [companyProfileAllUsersViewsCount, setCompanyProfileAllUsersViewsCount] = useState('');
  const [companyProfileViewImpressionPercentage, setcompanyProfileViewsImpressionPercentage] = useState('');
  const [companyProfileViewLastTwoWeekCount, setCompanyProfileViewLastTwoWeekCount] = useState('');
  const [companyProfileViewLastWeekCount, setCompanyProfileViewLastWeekCount] = useState('');

  const [companyFollowersImpressionPercentage, setCompanyFollowersImpressionPercentage] = useState('');
  const [companyFollowersLastTwoWeekCount, setCompanyFollowersLastTwoWeekCount] = useState('');
  const [companyFollowersLastWeekCount, setCompanyFollowersLastWeekCount] = useState('');

  const [companyActiveJobsImpressionPercentage, setCompanyActiveJobsImpressionPercentage] = useState('');
  const [companyActiveJobsLastTwoWeekCount, setCompanyActiveJobsLastTwoWeekCount] = useState('');
  const [companyActiveJobsLastWeekCount, setCompanyActiveJobsLastWeekCount] = useState('');
  const [companyFollowers, setCompanyFollowers] = useState([]);
  const [companyActiveJobs, setCompanyActiveJobs] = useState([]);
  const [currentCompany, setCurrentCompany]: any = useState<Company>();
  const [totalCompanyJobApplicationsCount, setTotalCompanyJobApplicationsCount] = useState('');

  const [companyJobApplicationsImpressionPercentage, setCompanyJobApplicationsImpressionPercentage] = useState('');
  const [companyJobApplicationsLastTwoWeekCount, setCompanyJobApplicationsLastTwoWeekCount] = useState('');
  const [companyJobApplicationsLastWeekCount, setCompanyJobApplicationsLastWeekCount] = useState('');

  const [allstaffmember, setAllStaffMember] = useState([]);

  const [staffname, setStaffName] = useState('');
  const [staffemail, setStaffEmail] = useState('');
  const [staffcvcount, setStaffCvCount] = useState('');
  const [staffstatus, setStaffStatus] = useState('');
  const [staffid, setStaffId] = useState('');

  const [isLoading, setIsLoading] = useState(false);

  const [purchasedate, setMemberPurchaseDate] = useState('');
  const [expiredate, setMemberExpireDate] = useState('');

  const [featuredJobCount, setFeaturedJobCount] = useState(0);
  const [membershipStatus, setMembershipStatus] = useState('');
  const [membershipPlan, setMembershipPlan] = useState('');

  const Tooltip = () => {
    return (
      <AntdTooltip
        color={'#EBF4FF'}
        placement="bottom"
        title={
          <span className="text-dark">
            Give users more information<br></br>about how this feature works in a<br></br>friendly tone & concise
            manner.
          </span>
        }>
        <span className="custom-tooltip-container" style={{position: 'relative', display: 'inline-block'}}>
          <i className="fa-solid fa-circle-info c-D9D9D9"></i>
        </span>
      </AntdTooltip>
    );
  };

  const [currentStaffCvCount, setCurrentStaffCvCount] = useState('');

  const [modalConfirmStaff, setModalConfirmStaff] = useState(false);

  const modalConfirmCloseStaff = () => {
    setModalConfirmStaff(false);
  };

  useEffect(() => {
    getSingleCompanyDetails(user?.id)
      .then(res => {
        if (res.status == true) {
          setCurrentCompany(res.data);
        } else {
          setCurrentCompany([]);
        }
      })
      .catch(e => {
        console.error(e);
      });

    getSingleUserDetails(user?.id).then(res => {
      setMembershipStatus(res.membership);
      setMembershipPlan(res.plan);
    });
  }, [user]);

  useEffect(() => {
    if (user?.company_id) {
      const data = {
        company_id: user?.company_id,
      };
      getCompanyProfileAllUserViewsCount(data)
        .then(res => {
          if (res.status == true) {
            setCompanyProfileAllUsersViewsCount(res.company_profile_views_count);
            setcompanyProfileViewsImpressionPercentage(res.company_profileView_Impression_percentage);
            setCompanyProfileViewLastTwoWeekCount(res.company_profileView_Counts_lasttwoweek);
            setCompanyProfileViewLastWeekCount(res.company_profileView_Counts_lastweek);
          } else {
            setCompanyProfileAllUsersViewsCount('');
            setcompanyProfileViewsImpressionPercentage('');
            setCompanyProfileViewLastTwoWeekCount('');
            setCompanyProfileViewLastWeekCount('');
          }
        })
        .catch(err => {
          console.log(err);
        });
      getCompanyFollowers(user?.company_id)
        .then(res => {
          if (res.status == true) {
            setCompanyFollowers(res.companies_followers);
            setCompanyFollowersImpressionPercentage(res.company_followers_ImpressionPercentage);
            setCompanyFollowersLastTwoWeekCount(res.company_followers_lasttwoweek);
            setCompanyFollowersLastWeekCount(res.company_followers_lastweek);
          } else {
            setCompanyFollowers([]);
            setCompanyFollowersImpressionPercentage('');
            setCompanyFollowersLastTwoWeekCount('');
            setCompanyFollowersLastWeekCount('');
          }
        })
        .catch(err => {
          console.log(err);
        });
      getCompanyActiveJobs(data)
        .then(res => {
          if (res.status == true) {
            setCompanyActiveJobs(res.data);
            setCompanyActiveJobsImpressionPercentage(res.jobs_Impression_percentage);
            setCompanyActiveJobsLastTwoWeekCount(res.jobs_lasttwoweek);
            setCompanyActiveJobsLastWeekCount(res.jobs_lastweek);
            setFeaturedJobCount(res.featuredjobscount);
          } else {
            setCompanyActiveJobs([]);
            setCompanyActiveJobsImpressionPercentage('');
            setCompanyActiveJobsLastTwoWeekCount('');
            setCompanyActiveJobsLastWeekCount('');
            setFeaturedJobCount(0);
          }
        })
        .catch(err => {
          console.log(err);
        });
    }

    if (user?.id && user?.company_id) {
      const applications_data = {
        company_id: user?.company_id,
        user_id: user?.id,
      };
      getTotalCompanyJobApplicationsCount(applications_data)
        .then(res => {
          if (res.status == true) {
            setTotalCompanyJobApplicationsCount(res.data);
            setCompanyJobApplicationsImpressionPercentage(res.applicants_ImpressionPercentage);
            setCompanyJobApplicationsLastTwoWeekCount(res.applicantsLasttwoweek);
            setCompanyJobApplicationsLastWeekCount(res.applicantsLastweek);
          } else {
            setTotalCompanyJobApplicationsCount('');
            setCompanyJobApplicationsImpressionPercentage('');
            setCompanyJobApplicationsLastTwoWeekCount('');
            setCompanyJobApplicationsLastWeekCount('');
          }
        })
        .catch(err => {
          console.log(err);
        });
    }
    fetchStaffMemeber(user?.id);
    fetchMemeberShipDetails(user?.id);
  }, [user]);

  const [modalConfirm, setModalConfirm] = useState(false);
  const modalConfirmOpen = () => {
    setModalConfirm(true);
  };

  const fetchMemeberShipDetails = async (id: any) => {
    try {
      const res = await getAllMemeberShipDetails(id);
      if (res.status == true) {
        setMemberPurchaseDate(res.membership.purchase_at);
        setMemberExpireDate(res.membership.expire_at);
      } else {
        setMemberPurchaseDate('');
        setMemberExpireDate('');
      }
    } catch (error) {
      console.error(error);
    }
  };

  const fetchStaffMemeber = async (id: any) => {
    try {
      const response = await getAllStaffMembers(id);
      setAllStaffMember(response.data);
    } catch (error) {
      console.error(error);
    }
  };

  const handleStaffSubmit = (e: any) => {
    e.preventDefault();
    let company_cv_count;
    let updatecount;

    if (staffid) {
      updatecount = Number(currentStaffCvCount) - Number(staffcvcount);
      company_cv_count = Number(updatecount) + Number(currentCompany.available_resume_count);
    } else {
      company_cv_count =
        staffcvcount < currentCompany.available_resume_count
          ? Number(currentCompany.available_resume_count) - Number(staffcvcount)
          : Number(staffcvcount) - Number(currentCompany.available_resume_count);
    }

    const data = {
      id: staffid,
      name: staffname,
      email: staffemail,
      company_id: user?.company_id,
      role: 'staff',
      available_resume_count: staffcvcount,
      created_by_id: user?.id,
      company_cv_count: company_cv_count,
      status: staffstatus,
    };

    if (staffcvcount > currentCompany.available_resume_count && !staffid) {
      if (currentCompany.available_resume_count == 0) {
        notification.info({message: 'You cannot add a team member because your CV count is not available.'});
      } else {
        notification.info({
          message: 'Cv count value must be less than  and equal to ' + (currentCompany.available_resume_count ?? 0),
        });
      }
    } else {
      if (Number(staffcvcount) > Number(currentStaffCvCount) + Number(currentCompany.available_resume_count)) {
        notification.info({
          message:
            'Cv count value must be less than and equal to ' +
            (Number(currentStaffCvCount) + Number(currentCompany.available_resume_count)),
        });
      } else {
        setIsLoading(true); // Corrected line
        AddUpdateStaffMember(data)
          .then(res => {
            if (res.status == true) {
              notification.success({message: res.message});
              fetchStaffMemeber(user?.id);
              setTimeout(() => {
                setModalConfirmStaff(false);
              }, 3000);
            } else {
              const errors = res.message;
              let errorMessage = '';
              for (const error in errors) {
                errorMessage += errors[error];
              }
              notification.info({message: res.message});
            }
          })
          .catch(err => {
            console.log(err);
          })
          .finally(() => {
            setIsLoading(false);
          });
      }
    }
  };

  const resetForm = () => {
    setStaffName('');
    setStaffEmail('');
    setStaffCvCount('');
    setStaffId('');
    setCurrentStaffCvCount('');
    setStaffStatus('');
  };

  const handleStaffEdit = (staff: any) => {
    setStaffName(staff.name);
    setStaffEmail(staff.email);
    setStaffCvCount(staff.available_resume_count);
    setStaffId(staff.id);
    setCurrentStaffCvCount(staff.available_resume_count);
    setStaffStatus(staff.status);
    setModalConfirmStaff(true);
  };

  const handleStaffDelete = (staff: any) => {
    swal({
      title: 'Are you sure?',
      text: 'You want to delete the staff',
      icon: 'warning',
      dangerMode: true,
      buttons: ['Cancel', 'Yes, I am sure!'],
    }).then(willDelete => {
      if (willDelete) {
        const data = {
          id: staff.id,
          company_id: user?.company_id,
          company_cv_count: Number(currentCompany.available_resume_count) + Number(staff.available_resume_count),
        };

        DeleteStaff(data)
          .then(res => {
            if (res.status === true) {
              notification.success({message: res.message});
              fetchStaffMemeber(user?.id);
            } else {
              console.log('Deletion failed');
            }
          })
          .catch(err => {
            ErrorHandler.showNotification(err);
          });
      } else {
      }
    });
  };

  const handleCvCountErrorMsg = () => {
    let msg;
    if (membershipStatus == 'true') {
      msg = 'Hi, memebership plan has been expired. Please upgrade your plan to see more candidates';
    } else {
      msg = 'Hi, you have consumed all CV count. Please upgrade your plan to see more candidates';
    }

    notification.info({message: msg});
  };

  const styleValue =
    membershipPlan === '3' && membershipStatus === 'false'
      ? 100 - featuredJobCount * 4
      : membershipPlan === '2' && membershipStatus === 'false'
        ? 100 - featuredJobCount * 6.66
        : 0;

  if (!currentCompany) {
    return null;
  }

  return (
    <>
      <div className="dash-right">
        <h1>
          Company <span className="span-color">Profile</span>
        </h1>
        <CompanyProfileTabs company={currentCompany} currentTab={'overview'} />
        <div className="overview-highlights-container">
          <div className="row">
            <div className="col-sm-8 col-7">
              <p className="f-22 c-fff ">Profile Highlights {Tooltip()}</p>
            </div>
            <div className="col-sm-4 col-5 text-right m-text-left">
              <Link href="/employer/company/insights">
                <p className="16 c-fff">
                  EXPAND &nbsp;&nbsp;
                  <i className="fa-solid fa-up-right-and-down-left-from-center c-D9D9D9"></i>
                </p>
              </Link>
            </div>
          </div>
          <div className="row">
            <div className="col-lg-3 col-md-6">
              <div className="dash-card b-fff">
                <p className="f-12 c-747474 w-700 text-upper">Profile views</p>
                <div className="row">
                  <div className="col-7">
                    <p className="f-22 c-2C2C2C mb-0">{companyProfileAllUsersViewsCount}</p>
                  </div>
                  <div className="col-5">
                    <div className="text-right">
                      {companyProfileViewLastWeekCount > companyProfileViewLastTwoWeekCount ? (
                        <p className="f-16 c-3D9F79 w-600">
                          <i className="fa-solid fa-caret-up"></i> {companyProfileViewImpressionPercentage}
                        </p>
                      ) : (
                        <p className="f-16 c-FD7373 w-600">
                          <i className="fa-solid fa-caret-down"> </i> {companyProfileViewImpressionPercentage}
                        </p>
                      )}
                      {/* <p className='f-16 c-3D9F79 w-600'><i className="fa-solid fa-caret-up"></i> 14%</p> */}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-3 col-md-6">
              <div className="dash-card b-fff">
                <p className="f-12 c-747474 w-700 text-upper">Followers</p>
                <div className="row">
                  <div className="col-7">
                    <p className="f-22 c-2C2C2C mb-0">{companyFollowers.length}</p>
                  </div>
                  <div className="col-5">
                    <div className="text-right">
                      {companyFollowersLastWeekCount > companyFollowersLastTwoWeekCount ? (
                        <p className="f-16 c-3D9F79 w-600">
                          <i className="fa-solid fa-caret-up"></i> {companyFollowersImpressionPercentage}
                        </p>
                      ) : (
                        <p className="f-16 c-FD7373 w-600">
                          <i className="fa-solid fa-caret-down"></i> {companyFollowersImpressionPercentage}
                        </p>
                      )}
                      {/* <p className='f-16 c-FD7373 w-600'><i className="fa-solid fa-caret-down"></i> 14%</p> */}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-3 col-md-6">
              <div className="dash-card b-fff">
                <p className="f-12 c-747474 w-700 text-upper">Active job posts</p>
                <div className="row">
                  <div className="col-7">
                    <p className="f-22 c-2C2C2C mb-0">{companyActiveJobs.length}</p>
                  </div>
                  <div className="col-5">
                    <div className="text-right">
                      {companyActiveJobsLastWeekCount > companyActiveJobsLastTwoWeekCount ? (
                        <p className="f-16 c-3D9F79 w-600">
                          <i className="fa-solid fa-caret-up"></i> {companyActiveJobsImpressionPercentage}
                        </p>
                      ) : (
                        <p className="f-16 c-FD7373 w-600">
                          <i className="fa-solid fa-caret-down"></i> {companyActiveJobsImpressionPercentage}
                        </p>
                      )}
                      {/* <p className='f-16 c-3D9F79 w-600'><i className="fa-solid fa-caret-up"></i> 14%</p> */}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-3 col-md-6">
              <div className="dash-card b-fff">
                <p className="f-12 c-747474 w-700 text-upper">Total Applicants</p>
                <div className="row">
                  <div className="col-7">
                    <p className="f-22 c-2C2C2C mb-0">{totalCompanyJobApplicationsCount}</p>
                  </div>
                  <div className="col-5">
                    <div className="text-right">
                      {companyJobApplicationsLastWeekCount > companyJobApplicationsLastTwoWeekCount ? (
                        <p className="f-16 c-3D9F79 w-600">
                          <i className="fa-solid fa-caret-up"></i> {companyJobApplicationsImpressionPercentage}
                        </p>
                      ) : (
                        <p className="f-16 c-FD7373 w-600">
                          <i className="fa-solid fa-caret-down"></i> {companyJobApplicationsImpressionPercentage}
                        </p>
                      )}
                      {/* <p className='f-16 c-3D9F79 w-600'><i className="fa-solid fa-caret-up"></i> 14%</p> */}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {membershipStatus == 'false' && membershipPlan != '1' ? (
          <div className="company-profile-block">
            <div className="row ">
              <div className="col-sm-3 pr-0">
                <p className="f-22 c-191919 mb-1">Featured Job Posts {Tooltip()}</p>
                <p className="f-16 c-747474 w-600">A glimpse into featured job posts count</p>
              </div>
              <div className="col-sm-8 pr-0 pl-sp">
                <div className="row">
                  <div className="col-sm-12">
                    <div className="progress">
                      <div
                        className="progress-bar progress-w"
                        style={{width: `${styleValue}%`}}
                        role="progressbar"
                        aria-valuenow={featuredJobCount}
                        aria-valuemin={0}
                        aria-valuemax={100}></div>
                    </div>
                  </div>
                  <div className="col-sm-8">
                    <h4 className="big-short mt-5 ">
                      {membershipPlan === '3' && membershipStatus === 'false' ? (
                        <big>{35 - featuredJobCount}</big>
                      ) : membershipPlan === '2' && membershipStatus === 'false' ? (
                        <big>{25 - featuredJobCount} </big>
                      ) : membershipPlan === '1' ? (
                        <big>0 </big>
                      ) : (
                        <big>0 </big>
                      )}
                      featured job posts
                    </h4>

                    <p className="f-12 c-747474 m-sp-0">
                      Available from{' '}
                      {new Date(purchasedate).toLocaleString('en-US', {
                        day: 'numeric',
                        month: 'short',
                        year: 'numeric',
                      })}{' '}
                      -{' '}
                      {new Date(expiredate).toLocaleString('en-US', {
                        day: 'numeric',
                        month: 'short',
                        year: 'numeric',
                      })}
                    </p>
                  </div>
                  <div className="col-sm-4 text-right m-center">
                    <Link href="/employer/billing/billinghistory">
                      <button className="download mt-4 w-100">Billing History</button>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="company-profile-block">
            <div className="row ">
              <div className="col-sm-3 pr-0">
                <p className="f-22 c-191919 mb-1">Featured Job Posts {Tooltip()}</p>
                <p className="f-16 c-747474 w-600">A glimpse into featured job posts count</p>
              </div>
              <div className="col-sm-8 pr-0 pl-sp">
                <div className="row">
                  <div className="col-sm-8">
                    <h4 className="big-short mt-1 ">
                      {membershipPlan === '3' && membershipStatus === 'false' ? (
                        <big>{35 - featuredJobCount}</big>
                      ) : membershipPlan === '2' && membershipStatus === 'false' ? (
                        <big>{25 - featuredJobCount} </big>
                      ) : membershipPlan === '1' ? (
                        <big>0 </big>
                      ) : (
                        <big>0 </big>
                      )}
                      featured job posts
                    </h4>
                    <p className="f-12 c-747474 m-sp-0">
                      Available from{' '}
                      {new Date(purchasedate).toLocaleString('en-US', {
                        day: 'numeric',
                        month: 'short',
                        year: 'numeric',
                      })}{' '}
                      -{' '}
                      {new Date(expiredate).toLocaleString('en-US', {
                        day: 'numeric',
                        month: 'short',
                        year: 'numeric',
                      })}
                    </p>
                  </div>
                  <div className="col-sm-4 text-right m-center">
                    <button
                      className="btn-a primary-size-16 btn-bg-0055BA  tab-add-sp mt-3 w-100"
                      onClick={modalConfirmOpen}>
                      <i className="fa-solid fa-bolt"></i> Upgrade
                    </button>
                  </div>
                  <PlanPopup show={modalConfirm} setModalConfirm={(bool: any) => setModalConfirm(bool)} />
                </div>
              </div>
            </div>
          </div>
        )}
        <div className="company-profile-block">
          <div className="row">
            <div className="col-sm-3 pr-0">
              <p className="f-22 c-191919 mb-1">Team CV View {Tooltip()}</p>
              <p className="f-16 c-747474 w-600">Manage your teams CV viewing breakdown</p>
            </div>
            <div className="col-sm-4 pr-0 pl-sp">
              <h4 className="big-short mt-1 ">
                <big>{currentCompany.available_resume_count ? currentCompany.available_resume_count : 0}</big> CV Views
                Available
              </h4>
              <p className="f-12 c-747474 m-sp-0">
                Available from{' '}
                {new Date(purchasedate).toLocaleString('en-US', {
                  day: 'numeric',
                  month: 'short',
                  year: 'numeric',
                })}{' '}
                -{' '}
                {new Date(expiredate).toLocaleString('en-US', {
                  day: 'numeric',
                  month: 'short',
                  year: 'numeric',
                })}
              </p>
            </div>
            <div className="col-sm-5 ">
              <p className="f-18 w-600 c-2C2C2C mb-4">Team Members ({allstaffmember.length})</p>
              <div className="team_memebr">
                {allstaffmember.length > 0 ? (
                  allstaffmember.map((staff: any, index: any) => {
                    return (
                      <div className="row mt-1" key={index}>
                        <div className="col-7">
                          <p className="f-16 c-4D4D4D ">{staff.name}</p>
                        </div>
                        <div className="col-5 pt-2">
                          <p className="f-16 w-700 c-4D4D4D  ">
                            {staff.available_resume_count} CV’s{' '}
                            <i
                              className="fa-solid fa-pencil pencil-12"
                              onClick={() => handleStaffEdit(staff)}
                              role="button"></i>
                            <i
                              className="fa fa-trash pencil-12 mx-2"
                              onClick={() => handleStaffDelete(staff)}
                              role="button"></i>
                          </p>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <p>No Team Member Found</p>
                )}
              </div>
              {membershipStatus === 'false' ? (
                <p
                  className="add mt-2"
                  onClick={() => {
                    setModalConfirmStaff(true);
                    resetForm();
                  }}
                  role="button">
                  <i className="fa-solid fa-plus"> </i> Add Team Member
                </p>
              ) : (
                <p className="add mt-2" onClick={handleCvCountErrorMsg} role="button">
                  <i className="fa-solid fa-plus"> </i> Add Team Member
                </p>
              )}
            </div>
          </div>
        </div>
        <div className="company-profile-block">
          <div className="row">
            <div className="col-sm-3 pr-0">
              <p className="f-22 c-191919 mb-1 m-text-center">
                What candidates will <br /> see
              </p>
              <p className="f-16 c-747474 w-600 m-text-center mb-3">
                A glimpse into what talent <br /> will look at on your profile
              </p>
            </div>
            <div className="col-sm-7 ">
              <div className="row pb-4 g-4 align-items-center">
                <div className="col-lg-2  col-md-3 pr-0">
                  <div className="dash-profile-img m-auto over-h-none">
                    <img
                      src={user?.company?.logo?.source || '/images/logo-img.png'}
                      alt="logo-img"
                      className="logo-filter"
                      width={50}
                      height={50}
                      layout="responsive"
                    />
                  </div>
                </div>
                <div className="col-lg-10 col-md-9 ">
                  <div className="col-sm-12 col-12 m-center">
                    <h4 className="em-name f-32 mb-2 c-0055BA">{currentCompany.company_name}</h4>
                    <p className="f-16 c-0070F5">
                      <i className="fa-solid fa-globe"></i>{' '}
                      <a href={currentCompany.company_website} target="_blank">
                        {currentCompany.company_website}
                      </a>
                    </p>
                    <ul className="skills mt-1 mb-1">
                      {currentCompany.country_name ? (
                        <li style={{paddingRight: '10px'}}>
                          <p className="f-12 c-999999">
                            <i className="fa-solid fa-location-dot"></i> {currentCompany.country_name}
                          </p>
                        </li>
                      ) : (
                        ''
                      )}
                      {currentCompany.company_email ? (
                        <li style={{paddingRight: '10px'}}>
                          <p className="f-12 c-999999">
                            <i className="fa-regular fa-envelope"></i> {currentCompany.company_email}
                          </p>
                        </li>
                      ) : (
                        ''
                      )}
                      {currentCompany.company_contact_no ? (
                        <li style={{paddingRight: '10px'}}>
                          <p className="f-12 c-999999">
                            <i className="fa-solid fa-phone-volume"></i> +{currentCompany.company_contact_no}
                          </p>
                        </li>
                      ) : (
                        ''
                      )}
                    </ul>
                  </div>
                </div>
              </div>
              <div className="row mb-3">
                <p className="f-14 c-000">About Our Company</p>
                <p
                  className="f-16 w-400 c-4D4D4D open-sans description-des"
                  dangerouslySetInnerHTML={{
                    __html: currentCompany.company_description,
                  }}></p>
              </div>
              <div className="row mb-3">
                <p className="f-14 c-000 mb-2">Sector</p>
                <p className="f-16 w-400 c-4D4D4D">{currentCompany.sector_name}</p>
              </div>
              <div className="row">
                <p className="f-14 c-000 mb-2">Company Size</p>
                <p className="f-16 w-400 c-4D4D4D">{currentCompany.no_of_employees}+ employees</p>
              </div>
            </div>
            <div className="col-sm-2">
              <div className="text-right link-right-icons">
                <p className="mt-2">
                  {currentCompany.twitter_link && (
                    <a href={currentCompany.twitter_link} target="_blank">
                      <i className="fa-brands fa-x-twitter"></i>
                    </a>
                  )}
                  {currentCompany.facebook_link && (
                    <a href={currentCompany.facebook_link} target="_blank">
                      <i className="fa-brands fa-facebook-f"></i>
                    </a>
                  )}
                  {currentCompany.instagram_link && (
                    <a href={currentCompany.instagram_link} target="_blank">
                      <i className="fa-brands fa-instagram"></i>
                    </a>
                  )}
                  {currentCompany.linkedin_link && (
                    <a href={currentCompany.linkedin_link} target="_blank">
                      <i className="fa-brands fa-linkedin"></i>
                    </a>
                  )}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <ModalForm
        open={modalConfirmStaff}
        onCancel={modalConfirmCloseStaff}
        title={staffid ? 'Edit Team Member' : 'Add Team Member'}>
        <div className="head-box">
          <div className="row">
            <div className="col-sm-10"></div>
            <div className="col-sm-2 text-right">
              <button
                type="button"
                className="close-b-des close-x  bg-0055BA border-design"
                data-bs-dismiss="modal"
                aria-label="Close">
                <i className="fa-solid fa-xmark"></i>
              </button>
            </div>
          </div>
        </div>
        <div className="popup-body">
          <form className="form-experience-fieild" onSubmit={(e: any) => handleStaffSubmit(e)}>
            <div className="form_field_sec">
              <input
                type="text"
                placeholder="Enter name"
                className="big-input"
                value={staffname}
                onChange={e => setStaffName(e.target.value)}
                required
              />
              <label>Name</label>
            </div>
            <div className="form_field_sec">
              <input
                type="email"
                placeholder="Enter email"
                className="big-input"
                value={staffemail}
                onChange={e => setStaffEmail(e.target.value)}
                required
                disabled={!!staffid}
              />
              <label>Email</label>
            </div>
            <label>Status</label>
            <div className="form_field_sec">
              <select className="big-select" onChange={e => setStaffStatus(e.target.value)} value={staffstatus}>
                <option value="">Staff Status</option>
                <option value="active">Active</option>
                <option value="deactive">Deactive</option>
              </select>
            </div>

            <label>Cv Count</label>
            <input
              type="number"
              placeholder="Enter cv count"
              className="big-input mb-4"
              value={staffcvcount}
              onChange={e => {
                const enteredValue = e.target.value;
                // Ensure the entered value is a non-negative number
                const nonNegativeValue = Math.max(0, parseInt(enteredValue));
                setStaffCvCount(nonNegativeValue.toString());
              }}
              required
            />
            <small style={{color: '#dc3545'}}>
              Note : {currentCompany.available_resume_count} cv count are available
            </small>

            <div className="text-right mt-3">
              <button className="save" type="submit" disabled={isLoading}>
                {isLoading ? 'Please wait...' : 'Submit'}
              </button>
            </div>
          </form>
        </div>
      </ModalForm>
    </>
  );
}
