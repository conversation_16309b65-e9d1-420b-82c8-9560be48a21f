import NiceModal, {useModal} from '@ebay/nice-modal-react';
import {ModalWrapper} from '..';
import Dayjs from 'dayjs';
import styles from './style.module.css';
import {useFormik} from 'formik';
import {ButtonUi} from '@/ui/Button';
import {InputUi} from '@/components/Common/Input';
import {Radio, Checkbox} from 'antd';
import {DropdownUi} from '@/components/Common/DropdownUi';
import {useWorkflowNodes} from '@/hooks/useAddWorkflowItem';
import {FrequencyData, NodesData} from '@/modules/admin/automation/types';
import {DatePickerUi} from '@/components/Common/DatePicker';

interface AddFrequencyModalProps {
  closeModal: () => void;
  selectedLogic?: 'AND' | 'OR';
  nodeId?: string;
}

const periodOptions = [
  {
    label: 'Minutes(s)',
    value: 'minutes',
  },
  {
    label: 'Hours(s)',
    value: 'hours',
  },
  {
    label: 'Days(s)',
    value: 'days',
  },
  {
    label: 'Weeks(s)',
    value: 'weeks',
  },
];

const timeOptions = [
  {
    label: 'AM',
    value: 'AM',
  },
  {
    label: 'PM',
    value: 'PM',
  },
];

export const FrequencyModal = NiceModal.create(({closeModal, selectedLogic, nodeId}: AddFrequencyModalProps) => {
  const {visible, remove} = useModal();
  const {nodes, updateNodes, addNode} = useWorkflowNodes();

  const currentNode = nodes?.find(el => el.id === nodeId) as unknown as NodesData;

  const {values, setFieldValue, handleSubmit} = useFormik({
    initialValues: {
      every: (currentNode?.data as FrequencyData)?.frequency?.every ?? {
        periodValue: '',
        periodType: '',
      },
      time: (currentNode?.data as FrequencyData)?.frequency?.time ?? {
        timeValue: '',
        timeType: '',
      },
      startTime: (currentNode?.data as FrequencyData)?.frequency?.startTime ?? {
        dateTime: {
          date: '',
          time: '',
        },
        delayTime: {
          delayValue: '',
          delayType: '',
        },
        option: '',
      },
      oneTime: (currentNode?.data as FrequencyData)?.frequency?.oneTime ?? false,
    },
    onSubmit: values => {
      if (currentNode) {
        updateNodes([
          {
            ...currentNode,
            data: {
              ...currentNode.data,
              frequency: {
                ...values,
                startTime: {
                  option: values.startTime.option,
                  dateTime: {
                    date: values.startTime.dateTime.date,
                    time: values.startTime.dateTime.time,
                  },
                  delayTime: {
                    delayValue: values.startTime.delayTime.delayValue,
                    delayType: values.startTime.delayTime.delayType,
                  },
                },
              },
            },
          },
        ]);
        closeModal?.();
        remove();
        return;
      }
      selectedLogic
        ? addNode(
            'frequency',
            {
              frequency: values,
              type: 'frequency',
              selectedLogic,
            },
            selectedLogic,
          )
        : addNode('frequency', {
            frequency: values,
            type: 'frequency',
          });
      closeModal?.();
      remove();
    },
  });

  const disabled = () => {
    // If oneTime is selected, we only need to validate the startTime option
    if (values.oneTime) {
      return (
        !values.startTime.option ||
        (values.startTime.option === 'specific' && (!values.startTime.dateTime.date || !values.startTime.dateTime.time)) ||
        (values.startTime.option === 'delay' &&
          (!values.startTime.delayTime.delayValue || !values.startTime.delayTime.delayType))
      );
    }

    // Otherwise, validate all fields as before
    return (
      !values.every.periodValue ||
      !values.every.periodType ||
      !values.time.timeValue ||
      !values.time.timeType ||
      !values.startTime.option ||
      (values.startTime.option === 'specific' && (!values.startTime.dateTime.date || !values.startTime.dateTime.time)) ||
      (values.startTime.option === 'delay' &&
        (!values.startTime.delayTime.delayValue || !values.startTime.delayTime.delayType))
    );
  };

  return (
    <ModalWrapper
      open={visible}
      onCancel={remove}
      headerTitle="Frequency"
      closeIcon={null}
      headerDescription="Select the frequency for your workflow"
      headerContent>
      <form className={styles.modalBody} onSubmit={handleSubmit}>
        <div className={styles.oneTimeOption}>
          <Checkbox
            checked={values.oneTime}
            onChange={(e) => setFieldValue('oneTime', e.target.checked)}
          >
            Just Once (One-time event)
          </Checkbox>
        </div>

        <div className={styles.frequencyTopContainer} style={{ opacity: values.oneTime ? 0.5 : 1, pointerEvents: values.oneTime ? 'none' : 'auto' }}>
          <div className={styles.everyContainer}>
            <h5>Every</h5>
            <div className={styles.fieldGroup}>
              <InputUi
                placeholder="Value"
                value={values.every.periodValue}
                onChange={e => setFieldValue('every.periodValue', e.target.value)}
                bottomLabel={false}
                style={{
                  width: '480px',
                }}
                type="number"
              />
              <DropdownUi
                placeholder="Period"
                onChange={value => setFieldValue('every.periodType', value)}
                options={periodOptions}
                value={periodOptions.find(option => option.value === values.every.periodType)}
                style={{
                  marginBottom: '0',
                  width: '100%',
                }}
              />
            </div>
          </div>
          <div className={styles.timeContainer}>
            <h5>At</h5>
            <div className={styles.fieldGroup}>
              <InputUi
                placeholder="HH:mm"
                value={values.time.timeValue}
                onChange={e => setFieldValue('time.timeValue', e.target.value)}
                bottomLabel={false}
                style={{
                  width: '480px',
                }}
              />
              <DropdownUi
                placeholder="AM/PM"
                value={timeOptions.find(option => option.value === values.time.timeType)}
                onChange={value => setFieldValue('time.timeType', value)}
                options={timeOptions}
                style={{
                  marginBottom: '0',
                  width: '100%',
                }}
              />
            </div>
          </div>
        </div>
        <Radio.Group
          onChange={e => setFieldValue('startTime.option', e.target.value)}
          value={values.startTime.option}
          className={styles.radioGroup}>
          <Radio value="specific">
            <div className={styles.startContainer}>
              Start time
              <DatePickerUi
                style={{
                  height: '40px',
                }}
                bottomLabel={false}
                type="date"
                placeholder="Date"
                value={values.startTime.dateTime.date ? Dayjs(values.startTime.dateTime.date) : null}
                onChange={(date, dateString) => setFieldValue('startTime.dateTime.date', date.format('YYYY-MM-DD'))}
                format={'DD-MM-YYYY'}
              />
              <InputUi
                placeholder="HH:mm"
                onChange={e => setFieldValue('startTime.dateTime.time', e.target.value)}
                bottomLabel={false}
                style={{
                  width: '80px',
                  height: '40px',
                }}
                value={values.startTime.dateTime.time}
              />
            </div>
          </Radio>
          <Radio value="immediate">Immediately</Radio>
          <Radio value="delay">
            <div className={styles.delayContainer}>
              with a delay of at least
              <InputUi
                placeholder="Value"
                onChange={e => setFieldValue('startTime.delayTime.delayValue', e.target.value)}
                bottomLabel={false}
                style={{
                  width: '80px',
                  height: '40px',
                }}
                value={values.startTime.delayTime.delayValue}
                type="number"
              />
              <DropdownUi
                placeholder="Minutes(s)"
                onChange={value => setFieldValue('startTime.delayTime.delayType', value)}
                options={periodOptions}
                style={{
                  marginBottom: '0',
                  width: '160px',
                  height: '40px',
                }}
                value={periodOptions.find(option => option.value === values.startTime.delayTime.delayType)}
              />
            </div>
          </Radio>
        </Radio.Group>
        <div className={styles.buttonContainer}>
          <ButtonUi variant="outlined" color="primary" onClick={remove}>
            Cancel
          </ButtonUi>
          <ButtonUi
            variant="contained"
            color="primary"
            type="submit"
            disabled={disabled()}
            onClick={e => {
              e.preventDefault();
              handleSubmit();
            }}>
            Save & Update
          </ButtonUi>
        </div>
      </form>
    </ModalWrapper>
  );
});
