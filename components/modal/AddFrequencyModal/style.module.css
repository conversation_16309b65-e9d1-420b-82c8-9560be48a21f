.modalBody {
  padding: 24px;
  display: flex;
  flex-direction: column;
}
.frequencyTopContainer {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.fieldGroup {
  display: flex;
  gap: 8px;
  align-items: center;
}
.everyContainer,
.timeContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.everyContainer h5,
.timeContainer h5 {
  color: #191919;
  font-size: 22px;
  font-weight: 500;
  line-height: 120%;
}
.radioGroup {
  display: flex;
  flex-direction: column;
  padding-top: 24px;
  gap: 16px;
}
.startContainer,
.delayContainer {
  display: flex;
  gap: 8px;
  align-items: center;
}
.buttonContainer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
}
