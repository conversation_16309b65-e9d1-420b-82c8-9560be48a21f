import {Modal} from 'antd';
import {ModalProps} from 'antd/lib';
import styles from './style.module.css';
import Image from 'next/image';

interface ModalWrapperProps extends ModalProps {
  children: React.ReactNode;
  headerContent?: boolean;
  size?: 'medium' | 'large' | 'small' | 'fullScreen';
  headerTitle?: string;
  headerDescription?: string;
}

export const ModalWrapper = ({
  headerContent = false,
  size = 'medium',
  headerTitle,
  headerDescription,
  ...props
}: ModalWrapperProps) => {
  return (
    <Modal
      className={`${styles.model_dialog} ${styles[size]}`}
      classNames={{
        content: `${styles.model} ${styles[size]}`,
        body: styles.body,
        ...props.classNames,
      }}
      cancelButtonProps={{
        style: {
          display: 'none',
        },
      }}
      closeIcon={
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path
            d="M13.0601 12L17.4801 7.57996C17.5538 7.5113 17.6129 7.4285 17.6539 7.3365C17.6949 7.2445 17.7169 7.14518 17.7187 7.04448C17.7205 6.94378 17.702 6.84375 17.6642 6.75036C17.6265 6.65697 17.5704 6.57214 17.4992 6.50092C17.4279 6.4297 17.3431 6.37356 17.2497 6.33584C17.1563 6.29811 17.0563 6.27959 16.9556 6.28137C16.8549 6.28314 16.7556 6.30519 16.6636 6.34618C16.5716 6.38717 16.4888 6.44627 16.4201 6.51996L12.0001 10.94L7.58012 6.51996C7.43795 6.38748 7.2499 6.31535 7.0556 6.31878C6.8613 6.32221 6.67591 6.40092 6.5385 6.53834C6.40109 6.67575 6.32237 6.86113 6.31895 7.05544C6.31552 7.24974 6.38764 7.43778 6.52012 7.57996L10.9401 12L6.52012 16.42C6.37967 16.5606 6.30078 16.7512 6.30078 16.95C6.30078 17.1487 6.37967 17.3393 6.52012 17.48C6.66075 17.6204 6.85137 17.6993 7.05012 17.6993C7.24887 17.6993 7.4395 17.6204 7.58012 17.48L12.0001 13.06L16.4201 17.48C16.5607 17.6204 16.7514 17.6993 16.9501 17.6993C17.1489 17.6993 17.3395 17.6204 17.4801 17.48C17.6206 17.3393 17.6995 17.1487 17.6995 16.95C17.6995 16.7512 17.6206 16.5606 17.4801 16.42L13.0601 12Z"
            fill="#2C2C2C"
          />
        </svg>
      }
      okButtonProps={{
        style: {
          display: 'none',
        },
      }}
      {...props}>
      {headerContent && (
        <div className={styles?.modal_header}>
          <h4>{headerTitle ?? 'Account Information'}</h4>
          <p>{headerDescription ?? "Review the company's account status information."}</p>
          <div className={styles.icon_container}>
            <img
              src={'/icons/employer/close_frame.svg'}
              alt="close"
              height={32}
              width={32}
              onClick={(e: any) => props?.onCancel?.(e)}
            />
          </div>
        </div>
      )}
      {props?.children}
    </Modal>
  );
};
