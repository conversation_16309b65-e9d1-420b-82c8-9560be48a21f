// DataPointComponent.js
import React from 'react';
import styles from './style.module.css';
import {DataPointType} from '@/modules/admin/automation/query/useGetDataPoints';
import {InputUi} from '@/components/Common/Input';
import {DropdownUi} from '@/components/Common/DropdownUi';
import {DatePickerUi} from '@/components/Common/DatePicker';
import dayjs from 'dayjs';
import {useGetAllSkills} from '@/modules/common/query/useGetAllSkills';
import {Skill} from '@/lib/types';
import {useGetCountries} from '@/modules/common/query/useGetCountries';
import {operators} from '@/utils/getAllOperatorOptions';
import {Slider} from 'antd/lib';

interface DataPointComponentProps {
  label: DataPointType;
  value?: string;
  setStoredValue: (value: string) => void;
  operator: operators;
  setStoredLabel: (label: string) => void;
  setToLabel?: (label: string) => void;
  setToValue?: (value: string) => void;
  setFromLabel?: (label: string) => void;
  setFromValue?: (value: string) => void;
  fromValue?: string;
  toValue?: string;
  componenetLabel?: string;
}

const accountType = [
  {label: 'Free', value: 1},
  {label: 'Premium Plan for 12 -month', value: 2},
  {
    label: 'Premium Plan for 18 -month',
    value: 3,
  },
];
const experienceData = [
  {label: 'Entry Level', value: 'entry_level'},
  {label: 'Mid Level', value: 'mid_level'},
  {label: 'Senior Level', value: 'senior_level'},
];

const jobTypeData = [
  {label: 'Full Time', value: 'full time'},
  {label: 'Part Time', value: 'part time'},
  {label: 'Internship', value: 'internship'},
];

const activityStatus = [
  {
    label: 'Active',
    value: 'active',
  },
  {
    label: 'Deactive',
    value: 'deactive',
  },
];

const jobStatus = [
  {
    label: 'Active',
    value: 'active',
  },
  {
    label: 'Expired',
    value: 'expired',
  },
];

const DataPointComponent = ({
  label,
  value,
  setStoredValue,
  operator,
  setStoredLabel,
  fromValue,
  toValue,
  setFromLabel,
  setFromValue,
  setToLabel,
  setToValue,
  componenetLabel,
}: DataPointComponentProps) => {
  const {data: skills} = useGetAllSkills();
  const {data: countries} = useGetCountries();

  const renderInputField = () => {
    switch (label) {
      case 'name':
      case 'contact-number':
      case 'social-linkedin':
      case 'current-job-role':
      case 'job-title':
      case 'application-status':
        return (
          <InputUi
            type="text"
            value={value}
            onChange={e => {
              setStoredValue(e.target.value);
              setStoredLabel(e.target.value);
            }}
            style={{width: 387}}
            placeholder="Enter Value"
          />
        );
      case 'company-size':
        return (
          <InputUi
            type="number"
            value={value}
            onChange={e => {
              setStoredValue(e.target.value);
              setStoredLabel(e.target.value);
            }}
            style={{width: 387}}
            placeholder="Enter Value"
          />
        );

      case 'notification-read':
        return (
          <InputUi
            type="number"
            value={value}
            onChange={e => {
              setStoredValue(e.target.value);
              setStoredLabel(e.target.value);
            }}
            placeholder="0,1"
            style={{width: 387}}
          />
        );

      case 'languages-known':
        return (
          <InputUi
            type="text"
            value={value}
            onChange={e => {
              setStoredValue(e.target.value);
              setStoredLabel(e.target.value);
            }}
            style={{width: 387}}
            placeholder="Hindi,English etc."
          />
        );
      case 'profile-image':
      case 'resume':
      case 'job-saved':
      case 'job-viewed':
        return;
      case 'location':
      case 'job-location':
      case 'company-location':
      case 'current-job-location':
        return (
          <DropdownUi
            options={countries?.map(country => ({label: country.country_name, value: country.id}))}
            value={value || null}
            onChange={value => {
              setStoredValue(value);
              setStoredLabel(countries?.find(country => country.id === value)?.country_name || '');
            }}
            style={{width: 387}}
            placeholder="Select value"
          />
        );
      case 'skills':
        return (
          <DropdownUi
            options={skills?.map((skill: Skill) => ({label: skill.skills, value: skill.id}))}
            onChange={value => {
              setStoredValue(value);
              setStoredLabel(
                skills
                  ?.filter(skill => value === skill.id)
                  .map(skill => skill.skills)
                  .join(', ') || '',
              );
            }}
            style={{width: 387}}
            placeholder="Select value"
          />
        );
      case 'profile-completion-percentage':
        if (operator === 'BETWEEN') {
          return (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: 10,
              }}>
              <InputUi
                type="number"
                value={fromValue}
                onChange={e => {
                  setFromValue?.(e.target.value);
                  setFromLabel?.(e.target.value + '%');
                }}
                style={{width: 190}}
                placeholder="From"
              />
              <InputUi
                type="number"
                value={toValue}
                onChange={e => {
                  setToValue?.(e.target.value);
                  setToLabel?.(e.target.value + '%');
                }}
                style={{width: 190}}
                placeholder="To"
              />
            </div>
          );
        } else {
          return (
            <InputUi
              type="number"
              value={value}
              onChange={e => {
                setStoredValue(e.target.value);
                setStoredLabel(e.target.value + '%');
              }}
              style={{width: 387}}
              placeholder="Enter Value"
            />
          );
        }
      case 'experience-level':
        return (
          <DropdownUi
            options={experienceData}
            value={value || null}
            onChange={value => {
              setStoredValue(value);
              setStoredLabel(experienceData.find(option => option.value === value)?.label || '');
            }}
            style={{width: 387}}
            placeholder="Select value"
          />
        );
      case 'expected-salary-range':
        const MAX_SALARY = 70000;
        return (
          <div>
            <p>
              {fromValue || 0} - {toValue || MAX_SALARY}
            </p>
            <Slider
              range
              value={fromValue && toValue ? [parseInt(fromValue), parseInt(toValue)] : [0, MAX_SALARY]}
              onChange={value => {
                setFromValue?.(value[0].toString());
                setToValue?.(value[1]?.toString());
                setFromLabel?.(value[0].toString());
                setToLabel?.(value[1]?.toString());
              }}
              defaultValue={[0, MAX_SALARY]}
              style={{width: 387}}
              max={MAX_SALARY}
            />
          </div>
        );
      case 'last-login-date':
      case 'last-application-date':
      case 'registered-date':
      case 'job-deadline':
        if (operator === 'BETWEEN') {
          return (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: 10,
              }}>
              <DatePickerUi
                value={fromValue ? dayjs(fromValue) : null}
                onChange={date => {
                  setFromValue?.(date ? date.format('YYYY-MM-DD hh:mm:ss') : '');
                  setFromLabel?.(date ? date.format('DD-MM-YYYY') : '');
                }}
                style={{width: 190}}
                placeholder="From"
                format={'DD-MM-YYYY'}
                type="date"
              />
              <DatePickerUi
                value={toValue ? dayjs(toValue) : null}
                onChange={date => {
                  setToValue?.(date ? date.format('YYYY-MM-DD hh:mm:ss') : '');
                  setToLabel?.(date ? date.format('DD-MM-YYYY') : '');
                }}
                style={{width: 190}}
                placeholder="To"
                format={'DD-MM-YYYY'}
                type="date"
              />
            </div>
          );
        }
        return (
          <DatePickerUi
            value={value ? dayjs(value) : null}
            onChange={date => {
              setStoredValue(date ? date.format('YYYY-MM-DD hh:mm:ss') : '');
              setStoredLabel(date ? date.format('DD-MM-YYYY') : '');
            }}
            style={{width: 387}}
            placeholder="Select date"
            format={'DD-MM-YYYY'}
            type="date"
          />
        );
      case 'activity-status':
        return (
          <DropdownUi
            options={activityStatus}
            value={value || null}
            onChange={value => {
              setStoredValue(value);
              setStoredLabel(activityStatus.find(option => option.value === value)?.label || '');
            }}
            style={{width: 387}}
            placeholder="Select Activity Status"
          />
        );
      case 'job-type':
        return (
          <DropdownUi
            options={jobTypeData}
            value={value || null}
            onChange={value => {
              setStoredValue(value);
              setStoredLabel(jobTypeData.find(option => option.value === value)?.label || '');
            }}
            style={{width: 387}}
            placeholder="Select Job Type"
          />
        );
      case 'salary-range-max':
        if (operator === 'BETWEEN') {
          return (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: 10,
              }}>
              <InputUi
                type="number"
                value={fromValue}
                onChange={e => {
                  setFromValue?.(e.target.value);
                  setFromLabel?.(e.target.value);
                }}
                style={{width: 190}}
                placeholder="From"
              />
              <InputUi
                type="number"
                value={toValue}
                onChange={e => {
                  setToValue?.(e.target.value);
                  setToLabel?.(e.target.value);
                }}
                style={{width: 190}}
                placeholder="To"
              />
            </div>
          );
        } else {
          return (
            <InputUi
              type="number"
              value={value}
              onChange={e => {
                setStoredValue(e.target.value);
                setStoredLabel(e.target.value);
              }}
              style={{width: 387}}
              placeholder="Enter Value"
            />
          );
        }
      case 'experience-level-required':
        return (
          <InputUi
            type="number"
            value={value}
            onChange={e => {
              setStoredValue(e.target.value);
              setStoredLabel(e.target.value);
            }}
            style={{width: 387}}
            placeholder="Enter Experience Level"
          />
        );
      case 'account-type':
        return (
          <DropdownUi
            options={accountType}
            value={value || null}
            onChange={value => {
              setStoredValue(value);
              setStoredLabel(accountType.find(option => option.value === value)?.label || '');
            }}
            style={{width: 387}}
            placeholder="Select Account Type"
          />
        );
      case 'job-status':
        return (
          <DropdownUi
            options={jobStatus}
            value={value || null}
            onChange={value => {
              setStoredValue(value);
              setStoredLabel(jobStatus.find(option => option.value === value)?.label || '');
            }}
            style={{width: 387}}
            placeholder="Select Job Status"
          />
        );

      default:
        if (operator === 'IS NULL' || operator === 'IS NOT NULL') {
          return null;
        } else if (operator === 'BETWEEN') {
          return (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: 10,
              }}>
              <InputUi
                type="number"
                value={fromValue}
                onChange={e => {
                  setFromValue?.(e.target.value);
                  setFromLabel?.(e.target.value);
                }}
                style={{width: 190}}
                placeholder="From"
              />
              <InputUi
                type="number"
                value={toValue}
                onChange={e => {
                  setToValue?.(e.target.value);
                  setToLabel?.(e.target.value);
                }}
                style={{width: 190}}
                placeholder="To"
              />
            </div>
          );
        }
        return (
          <InputUi
            value={value}
            onChange={e => {
              setStoredValue(e.target.value);
              setStoredLabel(e.target.value);
            }}
            style={{width: 387}}
            placeholder="Enter Value"
          />
        );
    }
  };

  return (
    <>
      {label === 'job-saved' || label === 'job-viewed' || label === 'resume' || label === 'profile-image' ? null : (
        <h5>{componenetLabel}</h5>
      )}
      <div className={styles.dataPointComponent}>
        <div className={styles.input}>{renderInputField()}</div>
      </div>
    </>
  );
};

export default DataPointComponent;
