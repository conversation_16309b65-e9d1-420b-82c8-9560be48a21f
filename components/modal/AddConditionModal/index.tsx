import NiceModal, {useModal} from '@ebay/nice-modal-react';
import {ModalWrapper} from '..';
import styles from './style.module.css';
import {useEffect, useMemo} from 'react';
import {useFormik} from 'formik';
import {ButtonUi} from '@/ui/Button';
import {DropdownUi} from '@/components/Common/DropdownUi';
import {useWorkflowNodes} from '@/hooks/useAddWorkflowItem';
import {DataPointTableData, DataPointType, useGetDataPoints} from '@/modules/admin/automation/query/useGetDataPoints';
import DataPointComponent from './BecomeComponents';
import {operators} from '@/utils/getAllOperatorOptions';
import {dataPointsOperators} from './getDataPointOperator';
import {useGetDataPointOptions} from '@/hooks/useGetDataTypeOptions';

interface AddConditionModalProps {
  closeModal: () => void;
  selectedLogic?: 'AND' | 'OR';
  id?: string;
  nodeId?: string;
}

export const AddConditionModal = NiceModal.create(({closeModal, selectedLogic, nodeId}: AddConditionModalProps) => {
  const {data} = useGetDataPoints({
    search: '',
    per_page: 10,
  });

  const {data: options} = useGetDataPointOptions();

  const dataPoints: DataPointTableData[] =
    useMemo(() => {
      return data?.pages?.map(page => page).flat();
    }, [data]) ?? [];

  const WhenOptions = options?.dataPoints;
  const {nodes, updateNodes, addNode, edges} = useWorkflowNodes();

  const {visible, remove} = useModal();

  const currentNode = nodes?.find(el => el.id === nodeId) as any;

  const {values, setFieldValue, handleSubmit} = useFormik({
    initialValues: {
      when: currentNode?.data?.condition?.when ?? {
        label: '',
        value: '',
      },
      operator: currentNode?.data?.condition?.operator ?? {
        label: '',
        value: '',
      },
      becomes: currentNode?.data?.condition?.becomes ?? {
        label: '',
        value: '',
      },
      to: currentNode?.data?.condition?.to ?? {
        label: '',
        value: '',
      },
      from: currentNode?.data?.condition?.from ?? {
        label: '',
        value: '',
      },
    },
    enableReinitialize: true,
    onSubmit: values => {
      if (currentNode) {
        updateNodes([
          {
            ...currentNode,
            data: {
              ...currentNode.data,
              condition: values,
            },
          },
        ]);

        closeModal?.();
        remove();
        return;
      }
      selectedLogic
        ? addNode?.(
            'condition',
            {
              label: `WHEN ${values.when.label} BECOMES ${values.becomes.label}`,
              condition: values,
              selectedLogic,
              type: 'condition',
            },
            selectedLogic,
          )
        : addNode?.('condition', {
            label: `WHEN ${values.when.label} BECOMES ${values.becomes.label}`,
            condition: values,
            type: 'condition',
          });

      closeModal?.();
      remove();
    },
  });

  console.log(edges, 'TEMP edges');

  const operators = useMemo(() => {
    return dataPointsOperators({
      label: values.when.label as DataPointType,
      setOptions: value => setFieldValue('operator.value', value),
    });
  }, [setFieldValue, values.when.label]);

  useEffect(() => {
    if (operators?.length === 1) {
      setFieldValue('operator.value', operators[0].value);
      setFieldValue('operator.label', operators[0].label);
    }
  }, [values.operator]);

  console.log(values.operator, 'values.operator');

  return (
    <ModalWrapper
      open={visible}
      onCancel={remove}
      headerTitle="Add a Condition"
      closeIcon={<></>}
      headerDescription="Specify the conditions for this workflow"
      headerContent>
      <form className={styles.modal_body} onSubmit={handleSubmit}>
        <div className={styles.when_container}>
          <h5>When</h5>
          <DropdownUi
            options={WhenOptions}
            value={WhenOptions?.find(option => option.value.toString() === values.when.value.toString())}
            onChange={value => {
              setFieldValue('when.value', value);
              setFieldValue('when.label', WhenOptions?.find(option => option.value === value)?.label);
              setFieldValue('operator', {
                label: '',
                value: '',
              });
              setFieldValue('becomes', {
                label: '',
                value: '',
              });
              setFieldValue('from', {
                label: '',
                value: '',
              });
              setFieldValue('to', {
                label: '',
                value: '',
              });
            }}
            style={{width: 387}}
            placeholder="Select"
            showSearch
          />
        </div>

        <div className={styles.operator_container}>
          <h5>Operator</h5>
          <DropdownUi
            options={operators}
            value={values.operator.value || undefined}
            style={{width: 387}}
            onChange={value => {
              setFieldValue('operator.value', value);
              setFieldValue('operator.label', operators?.find(op => op.value === value)?.label);
              setFieldValue('becomes', {
                label: '',
                value: '',
              });
              setFieldValue('from', {
                label: '',
                value: '',
              });
              setFieldValue('to', {
                label: '',
                value: '',
              });
            }}
            placeholder="Select Operator"
          />
        </div>
        {values.operator.value === 'IS NULL' || values.operator.value === 'IS NOT NULL' ? null : (
          <div className={styles.becomes_container}>
            <DataPointComponent
              label={values.when.label as DataPointType}
              value={values.becomes.value}
              setStoredValue={value => setFieldValue('becomes.value', value)}
              operator={values.operator.value as operators}
              setStoredLabel={label => {
                setFieldValue('becomes.label', label);
              }}
              setFromLabel={label => {
                setFieldValue('from.label', label);
              }}
              setFromValue={value => {
                setFieldValue('from.value', value);
              }}
              setToLabel={label => {
                setFieldValue('to.label', label);
              }}
              setToValue={value => {
                setFieldValue('to.value', value);
              }}
              fromValue={values?.from?.value}
              toValue={values?.to?.value}
            />
          </div>
        )}
        <div className={styles.button_container}>
          <ButtonUi variant="outlined" color="primary" onClick={remove}>
            Cancel
          </ButtonUi>
          <ButtonUi
            variant="contained"
            color="primary"
            type="submit"
            disabled={
              !values.when.value ||
              !values.operator.value ||
              ((values.operator === 'IS NULL' || values.operator === 'IS NOT NULL') && false)
            }
            onClick={e => {
              e.preventDefault();
              handleSubmit();
            }}>
            Save & update
          </ButtonUi>
        </div>
      </form>
    </ModalWrapper>
  );
});
