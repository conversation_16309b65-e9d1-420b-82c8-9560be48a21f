import {DataPointType} from '@/modules/admin/automation/query/useGetDataPoints';
import {getAllOperatorOptions} from '@/utils/getAllOperatorOptions';

interface DataPointOperatorProps {
  label: DataPointType;
  setOptions: (value: string) => void;
}

export const dataPointsOperators = ({label, setOptions}: DataPointOperatorProps) => {
  const operators = getAllOperatorOptions();

  switch (label) {
    case 'resume':
    case 'job-saved':
    case 'job-viewed':
    case 'profile-image':
      return operators?.filter(op => op.value === 'IS NULL' || op.value === 'IS NOT NULL');
    case 'profile-completion-percentage':
      return operators?.filter(
        op => op.value === '<' || op.value === '=' || op.value === '>' || op.value === 'BETWEEN',
      );
    case 'skills':
      return operators?.filter(op => op.value === 'LIKE');
    case 'name':
    case 'contact-number':
    case 'location':
    case 'job-location':
    case 'notification-read':
    case 'account-type':
    case 'subscription-expiry':
    case 'industry-type':
    case 'social-linkedin':
    case 'company-size':
    case 'company-location':
    case 'jobs-posted':
      return operators?.filter(op => op.value === '=');
    case 'experience-level':
    case 'current-job-role':
    case 'languages-known':
    case 'application-status':
    case 'activity-status':
    case 'job-title':
    case 'job-status':
    case 'jobs-expired':
      return operators?.filter(op => op.value === '=' || op.value === 'LIKE');
    case 'expected-salary-range':
      return operators?.filter(op => op.value === 'BETWEEN');
    case 'last-login-date':
    case 'last-application-date':
      return operators?.filter(op => op.value === 'BETWEEN' || op.value === '=');
    case 'registered-date':
    case 'salary-range-max':
      return operators?.filter(
        op => op.value === '=' || op.value === '<' || op.value === '>' || op.value === 'BETWEEN',
      );
    case 'current-job-location':
    case 'job-type':
      return operators?.filter(op => op.value === '=' || op.value === 'IS NULL' || op.value === 'IS NOT NULL');
    case 'experience-level-required':
      return operators?.filter(op => op.value === '=' || op.value === '>' || op.value === '<');
    default:
      return operators;
  }
};
