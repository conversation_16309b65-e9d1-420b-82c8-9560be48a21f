import NiceModal, {useModal} from '@ebay/nice-modal-react';
import {ModalWrapper} from '..';
import styles from './style.module.css';
import {Checkbox} from 'antd/lib';
import {ButtonUi} from '@/ui/Button';
import {CustomOptionDropdown} from '@/components/Common/CustomOptionDropdown';
import {useGetCountries} from '@/modules/common/query/useGetCountries';
import {useState} from 'react';
import {useFormik} from 'formik';
import {ContactExportType, useExportContacts} from '@/modules/admin/automation/mutation/useExportContact';
import {notification} from 'antd';

export const ExportContactListModal = NiceModal.create(() => {
  const {remove, visible} = useModal();
  const [selectedExportType, setSelectedExportType] = useState<ContactExportType | null>(null);
  const [selectedLocation, setSelectedLocation] = useState<string>('');
  const {data: countries} = useGetCountries();
  const [isOpen, setIsOpen] = useState(false);
  const {mutate: exportContact} = useExportContacts();

  const {values, setFieldValue, handleSubmit} = useFormik({
    initialValues: {
      exportType: '',
      location: '',
    },
    onSubmit: values => {
      exportContact(
        {
          contact_type: values.exportType as ContactExportType,
          location: selectedLocation === 'all' ? null : parseInt(values.location),
        },
        {
          onSuccess: data => {
            if (data?.message) {
              notification.error({
                message: data.message,
              });
              return;
            }
            var blob = new Blob([data], {type: 'text/csv'});
            var url = URL.createObjectURL(blob);

            var pom = document.createElement('a');
            pom.href = url;
            pom.setAttribute('download', 'contacts.csv');
            pom.click();
          },
        },
      );
    },
  });

  const handleExportTypeChange = (type: ContactExportType | null) => {
    setSelectedExportType(type);
    setFieldValue('exportType', type);
  };

  const handleLocationSelect = (selected: {label: string; value: string}) => setFieldValue('location', selected?.value);

  const isDisabled = !selectedExportType || !selectedLocation || (selectedLocation === 'select' && !values.location);

  return (
    <ModalWrapper
      open={visible}
      onCancel={remove}
      headerContent
      headerTitle="Export List"
      headerDescription="Export all the contacts listed"
      closeIcon={false}
      classNames={{
        content: styles.export_modal_content,
      }}>
      <form onSubmit={handleSubmit} className={styles.export_modal_container}>
        <div className={styles.export_type}>
          <h4>Export your contacts by:</h4>
          <div className={styles.export_type_checkbox}>
            <Checkbox
              checked={selectedExportType === 'all'}
              onChange={e => handleExportTypeChange(e.target.checked ? 'all' : null)}>
              All
            </Checkbox>
            <Checkbox
              checked={selectedExportType === 'candidates'}
              onChange={e => handleExportTypeChange(e.target.checked ? 'candidates' : null)}>
              Candidates only
            </Checkbox>
            <Checkbox
              checked={selectedExportType === 'employers'}
              onChange={e => handleExportTypeChange(e.target.checked ? 'employers' : null)}>
              Employers only
            </Checkbox>
          </div>
        </div>
        <div className={styles.export_location}>
          <h5>Location</h5>
          <div className={styles.export_location_checkbox}>
            <Checkbox
              checked={selectedLocation === 'all'}
              onChange={e => setSelectedLocation(e.target.checked ? 'all' : '')}>
              All
            </Checkbox>
            <div className={styles.checkbox_container}>
              <Checkbox
                checked={selectedLocation === 'select'}
                onChange={e => setSelectedLocation(e.target.checked ? 'select' : '')}>
                Select
              </Checkbox>
              <CustomOptionDropdown
                items={
                  countries?.map(country => {
                    return {label: country.country_name, value: country.id};
                  }) || []
                }
                labelExtractor={(item: any) => item.label}
                valueExtractor={(item: any) => item.value}
                placeholder="Select location"
                onSelect={handleLocationSelect}
                className={styles.dropdown_container}
                open={isOpen}
                setOpen={setIsOpen}
              />
            </div>
          </div>
        </div>
        <div className={styles.export_text}>Exported files are in CSV format</div>
        <div className={styles.button_container}>
          <ButtonUi
            variant="contained"
            color="primary"
            fullWidth
            disabled={isDisabled}
            className={styles.export_button}
            rounded={false}
            onClick={() => selectedExportType && selectedLocation && handleSubmit()}>
            Export
          </ButtonUi>
        </div>
      </form>
    </ModalWrapper>
  );
});
