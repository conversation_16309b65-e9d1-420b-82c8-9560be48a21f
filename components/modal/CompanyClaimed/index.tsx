import NiceModal, {antdModal, useModal} from '@ebay/nice-modal-react';
import {ModalWrapper} from '..';
import styles from './style.module.css';

export const CompanyClaimModal = NiceModal.create(() => {
  const {hide, visible} = useModal();
  return (
    <ModalWrapper
      open={visible}
      onCancel={hide}
      classNames={{
        content: styles.company_claimed_modal,
      }}>
      <h6 className={styles.company_claimed_title}>Profile claimed by the company</h6>
      <div className={styles.divider}></div>
      <p className={styles.company_claimed_content}>
        A representative from this business has claimed this profile. However, Talent Point does not verify the accuracy
        of the information. Not all details on this profile have been provided or verified by the business.
      </p>
    </ModalWrapper>
  );
});
