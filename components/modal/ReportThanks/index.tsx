import NiceModal, {antdModal, useModal} from '@ebay/nice-modal-react';
import {ModalWrapper} from '..';
import styles from './style.module.css';
import {Button} from 'antd';

export const ReportThanks = NiceModal.create(() => {
  const modal = useModal();
  return (
    <ModalWrapper {...antdModal(modal)} closeIcon={null}>
      <div className={styles.report_container}>
        <h4>Thank you for your report!</h4>
        <p>
          We appreciate you taking the time to flag <br /> this review. Our team will review it <br />
          according to our <span>community guidelines</span> <br /> and take appropriate action.
        </p>
        <div className={styles.divider}></div>
        <Button onClick={modal.hide} type="primary">
          Done
        </Button>
      </div>
    </ModalWrapper>
  );
});
