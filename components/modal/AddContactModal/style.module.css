.modal_body {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.dropdown {
  width: 387px;
}
.button_container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
}
.selected_contact {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 8px;
}
.send_email_to h5,
.segment_container h5 {
  color: #191919;
  font-size: 22px;
  font-weight: 500;
  line-height: 120%;
  padding-bottom: 16px;
}
.segment_container {
  padding-top: 24px;
}
.segment_checkbox {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 12px 0;
}
.condition_segment_select {
  display: flex;
  gap: 8px;
  align-items: center;
}
.condition_value {
  padding-top: 16px;
  display: flex;
  flex-direction: column;
}
.add_condition_button {
  padding-top: 16px;
}
.condition_section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
