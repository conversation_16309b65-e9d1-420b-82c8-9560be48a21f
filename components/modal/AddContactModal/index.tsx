import NiceModal, {useModal} from '@ebay/nice-modal-react';
import {ModalWrapper} from '..';
import styles from './style.module.css';
import {useState} from 'react';
import {useFormik} from 'formik';
import {ButtonUi} from '@/ui/Button';
import {Checkbox} from 'antd';
import {useGetCountries} from '@/modules/common/query/useGetCountries';
import Image from 'next/image';
import {DropdownUi} from '@/components/Common/DropdownUi';
import {useWorkflowNodes} from '@/hooks/useAddWorkflowItem';
import {ContactCondition} from '@/modules/admin/automation/types';
import {useGetDataPointOptions} from '@/hooks/useGetDataTypeOptions';
import DataPointComponent from '../AddConditionModal/BecomeComponents';
import {ContactType, useGetContactList} from '@/modules/admin/automation/query/useGetContactLists';

interface AddContactModalProps {
  closeModal: () => void;
  selectedLogic?: 'AND' | 'OR';
  nodeId?: string;
}

const contactOptions = [
  {
    label: 'Candidate',
    value: '1',
  },
  {
    label: 'Employer',
    value: '2',
  },
];

const conditionOption = [
  {
    label: 'Include',
    value: '=',
  },
  {
    label: 'Exclude',
    value: '!=',
  },
];

const segmentKeyOptions = [
  {
    label: 'Location',
    value: 'location',
  },
];

export const AddContactModal = NiceModal.create(({closeModal, selectedLogic, nodeId}: AddContactModalProps) => {
  const {data} = useGetCountries();
  const {visible, remove} = useModal();
  const [showSendEmailTo, setShowSendEmailTo] = useState(false);
  const {addNode, updateNodes, nodes} = useWorkflowNodes();
  const currentNode = nodes?.find(el => el.id === nodeId) as any;
  const {data: options} = useGetDataPointOptions();

  const WhenOptions = options?.dataPoints ?? [];

  const {values, setFieldValue, handleSubmit} = useFormik({
    initialValues: {
      sendEmailTo: currentNode?.data?.contact?.sendEmailTo ?? {
        label: '',
        value: '',
      },
      applySegment: currentNode?.data?.contact?.applySegment ?? false,
      condition: currentNode?.data?.contact?.condition ?? [
        {
          conditionType: {
            label: '',
            value: '',
          },
          conditionKey: {
            label: '',
            value: '',
          },
          conditionValue: {
            label: '',
            value: '',
          },
        },
      ],
    },
    onSubmit: values => {
      if (currentNode) {
        updateNodes([
          {
            ...currentNode,
            data: {
              ...currentNode.data,
              contact: values,
            },
          },
        ]);

        closeModal?.();
        remove();
        return;
      }

      selectedLogic
        ? addNode(
            'contact',
            {
              label: `Send email to ${values.sendEmailTo.label}`,
              contact: values,
              type: 'contact',
              selectedLogic: selectedLogic,
            },
            selectedLogic,
          )
        : addNode('contact', {
            label: `Send email to ${values.sendEmailTo.label}`,
            contact: values,
            type: 'contact',
          });

      closeModal?.();
      remove();
    },
  });

  const handleAddCondition = () => {
    setFieldValue('condition', [
      ...values.condition,
      {
        conditionType: {
          label: '',
          value: '',
        },
        conditionKey: {
          label: '',
          value: '',
        },
        conditionValue: {
          label: '',
          value: '',
        },
      },
    ]);
  };

  const {data: contacts} = useGetContactList({
    type: values.sendEmailTo.value as ContactType,
    name: '',
    location: '',
    per_page: 10,
  });

  return (
    <ModalWrapper
      open={visible}
      onCancel={remove}
      headerTitle="Select Contacts"
      closeIcon={<></>}
      headerDescription="Select contact lists related to your workflow"
      headerContent>
      <form className={styles.modal_body} onSubmit={handleSubmit}>
        <div className={styles.email_segment_content}>
          <div className={styles.send_email_to}>
            <h5>Send email to:</h5>
            <div className={styles.selected_contact}>
              <span>Contacts in the list:</span>
              <div>
                <DropdownUi
                  options={contactOptions}
                  value={contactOptions.find(option => option.value === values.sendEmailTo.value)}
                  onChange={value => {
                    setFieldValue('sendEmailTo.value', value);
                    setFieldValue('sendEmailTo.label', contactOptions.find(option => option.value === value)?.label);
                    setShowSendEmailTo(!showSendEmailTo);
                  }}
                  style={{width: 387, marginBottom: 0}}
                  placeholder="Send Email To"
                />
              </div>
            </div>
          </div>

          <div className={styles.segment_container}>
            <h5>Segment</h5>
            <div className={styles.segment_checkbox}>
              <Checkbox
                checked={values.applySegment}
                onChange={e => {
                  setFieldValue('applySegment', e.target.checked);
                }}>
                Apply a segment to filter contacts for this workflow
              </Checkbox>
            </div>
            <div className={styles.condition_section}>
              {values.applySegment &&
                (values.condition as ContactCondition[]).map((condition, index) => (
                  <div key={index} className={styles.condition_container}>
                    <div className={styles.condition_segment_select}>
                      <div className={styles.condition_type}>
                        <DropdownUi
                          options={conditionOption}
                          value={conditionOption.find(
                            option => option.value === values.condition[index].conditionType.value,
                          )}
                          placeholder="Condition"
                          style={{width: 160}}
                          onChange={value => {
                            setFieldValue(`condition[${index}].conditionType.value`, value);
                            setFieldValue(
                              `condition[${index}].conditionType.label`,
                              conditionOption.find(option => option.value === value)?.label,
                            );
                          }}
                        />
                      </div>
                      <div className={styles.condition_key}>
                        <DropdownUi
                          options={WhenOptions}
                          value={WhenOptions?.find(
                            option => option.value === values.condition[index].conditionKey.value,
                          )}
                          placeholder="Select a segment"
                          style={{width: 504}}
                          onChange={value => {
                            setFieldValue(`condition[${index}].conditionKey.value`, value);
                            setFieldValue(
                              `condition[${index}].conditionKey.label`,
                              WhenOptions.find(option => option.value === value)?.label,
                            );
                          }}
                        />
                      </div>
                    </div>
                    <div className={styles.condition_value}>
                      <DataPointComponent
                        label={values.condition?.[index].conditionKey?.label}
                        value={values.condition?.[index].conditionValue?.value}
                        setStoredValue={value => setFieldValue(`condition.[${index}].conditionValue.value`, value)}
                        operator={values.condition?.[index].conditionType?.value}
                        setStoredLabel={label => setFieldValue(`condition.[${index}].conditionValue.label`, label)}
                        componenetLabel={values.condition[index].conditionKey.label}
                      />
                    </div>
                  </div>
                ))}
            </div>

            {values.applySegment && (
              <div className={styles.add_condition_button}>
                <ButtonUi color="primary" variant="text" onClick={handleAddCondition}>
                  <img height={24} width={24} alt="plus" src={'/icons/automation/plus_icon.svg'} /> Add conditions
                </ButtonUi>
              </div>
            )}
          </div>
        </div>
        <div className={styles.button_container}>
          <ButtonUi variant="outlined" color="primary" onClick={remove}>
            Cancel
          </ButtonUi>
          <ButtonUi
            variant="contained"
            color="primary"
            type="submit"
            onClick={e => {
              e.preventDefault();
              handleSubmit?.();
            }}>
            <button type="submit">Save & update</button>
          </ButtonUi>
        </div>
      </form>
    </ModalWrapper>
  );
});
