.model {
  border-radius: 8px;
  border: 1px solid var(--Text-Anti-Flash-White, #f2f2f2);
  background: var(--Text-<PERSON>, #fff);
  display: flex;
  width: 351px;
  padding: 0 !important;
  flex-direction: column;
  align-items: start;
}
.model_dialog {
  width: 351px !important;
  border-radius: 8px;
}
.body {
  width: 100%;
}
.modal_header {
  border-radius: 8px;
  background: #cfe5ff;
  padding: 24px 24px 12px 24px;
}

.icon_container {
  position: absolute;
  top: 0;
  right: 0;
  cursor: pointer;
}

.medium {
  width: 720px !important;
}

.fullScreen {
  width: calc(100vw - 30px) !important;
  height: calc(100vh - 30px) !important;
}
.large {
  width: 1000px !important;
}
