.testimonial_card {
  display: flex;
  padding: 32px;
  flex-direction: column;
  justify-content: space-between;
  gap: 24px;
  border-radius: 16px;
  background: #ebf4ff;
  width: 420px;
  height: 260px;
}

.quote_content p {
  color: #00377a;
  font-size: 18px;
  font-weight: 400;
  line-height: 160%;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow-wrap: break-word;
}
.person_info_container {
  display: flex;
  align-items: center;
  gap: 20px;
}
.person_image {
  width: 56px;
  height: 56px;
  border-radius: 8px;
  background:
    lightgray 50% / cover no-repeat,
    #fff;
}
.person_info {
  color: #00377a;
  font-size: 16px;
}
.person_info h5 {
  font-weight: 600;
  line-height: 140%;
}

.person_info p {
  font-weight: 400;
  line-height: 140%;
}
