import styles from './style.module.css';

export const TestimonialCard = (props: { quote: string; img: string; name: string; position: string }) => {
  const { quote, img, name, position } = props;

  return (
    <div className={styles.testimonial_card}>
      <div className={styles.quote_content}>
        <p>{quote}</p>
      </div>
      <div className={styles.person_info_container}>
        <div className={styles.person_image}>
          <img
            src={img}
            alt="Testimonial"
            width={56}
            height={56}
          />
        </div>
        <div className={styles.person_info}>
          <h5>{name}</h5>
          <p>{position} </p>
        </div>
      </div>
    </div>
  );
};
