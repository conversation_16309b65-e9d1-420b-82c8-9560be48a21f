.card {
  display: flex;
  padding: 24px 16px;
  flex-direction: column;
  gap: 32px;
  border-radius: 16px;
  background: #0070f5;
}

.card_content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.card_content h4 {
  color: #fff;
  font-size: 31px;
  font-weight: 700;
  line-height: 120%;
}
.card_content p {
  color: #fff;
  font-size: 22px;
  line-height: 120%;
  font-weight: 500;
}
.card_cta button {
  background-color: white;
  color: #191919;
  border-radius: 2px;
  padding: 21px 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 2px;
  font-size: 18px;
  font-weight: 500;
}
@media screen and (max-width: 991px) {
  .card_content h4 {
    font-size: 28px;
  }
  .card_content p {
    font-size: 19px;
  }
  .card_cta button {
    width: 100%;
  }
}
