import { Button } from 'antd';
import styled from './style.module.css';
import { useHandleHire } from '@/hooks/useHandleHireUser';
import Link from 'next/link';
import { useContext } from 'react';
import AuthContext from '@/Context/AuthContext';

export const LookingToHireCard = () => {
  const { user } = useContext(AuthContext)
  const { handleClickShowErrorMessage, uploadCVLink } = useHandleHire();

  return (
    <div className={styled.card}>
      <div className={styled.card_content}>
        <h4>Looking to hire? </h4>
        <p>Try our recruitment and job portal for free today!</p>
      </div>
      <div className={styled.card_cta}>
        <Link href={uploadCVLink} onClick={
          user?.role == "employee"
            ? (e) =>
              handleClickShowErrorMessage(
                e,
                "You need to register as a employer to hire candidates"
              )
            : undefined
        }> <Button>Get started</Button></Link>
      </div>
    </div>
  );
};
