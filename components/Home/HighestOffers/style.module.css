.highest-offer-section {
  padding: 80px 0px;
}

.find-jobs-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1 0 0;
}
.jobs-content-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.jobs-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.jobs-content p {
  color: #747474;
  font-size: 22px;
  font-weight: 500;
  line-height: 120%;
}

.jobs-content h4 {
  color: #151515;
  font-size: 54px;
  font-weight: 700;
  line-height: 120%;
}
.jobs-content h4 span {
  width: 320px;
}

.create-profile-btn {
  display: flex;
  padding: 22px 24px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 2px;
  background: #fdca40;
  color: #151515;
  font-size: 18px;
  font-weight: 500;
  line-height: 120%;
}
.arrow-buttons {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  cursor: pointer;
}
.image_slider {
  width: 400px;
}
.span_slider {
  max-width: 524px;
}
.jobs-title {
  display: flex;
  gap: 6px;
}
.jobs-image {
  height: 400px;
  width: 400px;
  position: relative;
}

@media screen and (max-width: 1400px) {
  .jobs-title {
    flex-direction: column;
  }
}

@media screen and (max-width: 991px) {
  .highest-offer-section {
    padding: 40px 12px;
  }
  .find-jobs-container {
    flex-direction: column;
    gap: 40px;
  }
  .explore-sector {
    font-size: 19px !important;
  }
  .jobs-title {
    font-size: 33px !important;
    flex-direction: column;
  }
  .create-profile-btn {
    width: 100%;
    padding: 21px 24px;
    font-size: 16px;
  }
  .image_slider {
    max-width: 320px;
  }
  .span_slider {
    max-width: 351px;
  }
  .jobs-image {
    height: 351px;
    width: 100%;
  }
  .arrow_button_container {
    display: flex;
    justify-content: center;
    width: 100%;
    padding-top: 24px;
  }
}
