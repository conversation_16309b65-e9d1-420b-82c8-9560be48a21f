// @ts-nocheck
import React, {useContext, useEffect, useRef, useState} from 'react';
import Image from 'next/image';
import styles from './style.module.css';
import {Button} from 'antd';
import useWindowDimensions from '@/helpers/useWindowDimensions';
import Slider from 'react-slick';
import AuthContext from '@/Context/AuthContext';
import {useRouter} from 'next/router';
import ts from 'typescript';

const carouselData = [
  {
    label: `<span style="color: #00418F;">Hospitality</span>`,
    image: '/images/home/<USER>',
  },
  {
    label: `<span style="color: #0B89A3;">IT & Telecom</span>`,
    image: '/images/home/<USER>',
  },
  {
    label: `<span style="color: #ff6442;">Reatail & WholeSale</span>`,
    image: '/images/home/<USER>',
  },
  {
    label: "<span style='color: #19AE8D;'>HealthCare</span>",
    image: '/images/home/<USER>',
  },
  {
    label: "<span style='color: #D49B02;'>Finance & Banking</span>",
    image: '/images/home/<USER>',
  },
];

export const HighestOffers = () => {
  const [nav1, setNav1] = useState<Slider>();
  const [nav2, setNav2] = useState<Slider>();
  const router = useRouter();
  const {user} = useContext(AuthContext);
  const [activeSlide, setActiveSlide] = useState(0);
  const windowDimension: any = useWindowDimensions();
  const isMobile = windowDimension.width < 768;

  const redirectToProfileCreation = () => {
    if (user?.id) {
      if (user?.role === 'superadmin' || user?.role === 'admin') {
        router.push('/admin/dashboard');
      } else if (user?.role === 'employee') {
        router.push('/employees/myprofile/profile');
      } else {
        router.push('/employer/companies/profile');
      }
    } else {
      router.push('/auth/signup');
    }
  };

  let sliderRef1: any = useRef(null);
  let sliderRef2: any = useRef(null);
  const setting = {
    dots: false,
    slidesToShow: 1,
    slideToScroll: 1,
    infinite: false,
    speed: 500,
    autoplaySpeed: 2000,
    autoplay: true,
    cssEase: 'linear',
    beforeChange: (current: number, next: number) => {
      setActiveSlide(next);
    },
  };

  const handleNext = () => {
    sliderRef1.slickNext();
  };

  const handlePrev = () => {
    sliderRef1.slickPrev();
  };

  useEffect(() => {
    setNav1(sliderRef1);
    setNav2(sliderRef2);
  }, []);

  if (activeSlide === carouselData.length - 1 && sliderRef1 && sliderRef2) {
    setTimeout(() => {
      sliderRef1?.slickGoTo?.(0);
    }, 2000);
  }

  const arrowButton = () => {
    return (
      <div className={styles['arrow-buttons']}>
        {activeSlide === 0 ? (
          <img
            src={'/images/home/<USER>'}
            alt="left-arrow"
            width={24}
            height={24}
            onClick={handlePrev}
          />
        ) : (
          <img
            src={'/images/home/<USER>'}
            alt="right-arrow"
            width={24}
            height={24}
            onClick={handlePrev}
            style={{
              rotate: '180deg',
            }}
          />
        )}
        {activeSlide === carouselData.length - 1 ? (
          <img
            src={'/images/home/<USER>'}
            alt="left-arrow"
            width={24}
            height={24}
            onClick={handleNext}
            style={{
              rotate: '180deg',
            }}
          />
        ) : (
          <img
            src={'/images/home/<USER>'}
            alt="right-arrow"
            width={24}
            height={24}
            onClick={handleNext}
          />
        )}
      </div>
    );
  };

  return (
    <div>
      <section className={`${styles['highest-offer-section']} container`}>
        <div className={styles['find-jobs-container']}>
          <div className={styles['jobs-content-container']}>
            <div className={styles['jobs-content']}>
              <p className={styles['explore-sectors']}>Explore Top-paying-sectors</p>
              <h4 className={styles['jobs-title']}>
                <span>Find Jobs in </span>
                <Slider
                  ref={slider => {
                    sliderRef1 = slider;
                  }}
                  asNavFor={nav2}
                  prevArrow={<></>}
                  nextArrow={<></>}
                  {...setting}
                  vertical={true}
                  verticalSwiping={true}
                  className={styles.span_slider}>
                  {carouselData.map((el, index) => {
                    return (
                      <span
                        key={index}
                        className={styles['job-label']}
                        dangerouslySetInnerHTML={{
                          __html: el.label,
                        }}
                      />
                    );
                  })}
                </Slider>
              </h4>
            </div>
            <div>
              <Button className={styles['create-profile-btn']} onClick={redirectToProfileCreation}>
                Create Your Profile Free
              </Button>
            </div>
            {!isMobile && arrowButton()}
          </div>
          <Slider
            ref={slider => {
              sliderRef2 = slider;
            }}
            asNavFor={nav1}
            {...setting}
            className={styles.image_slider}>
            {carouselData.map((el, index) => {
              return (
                <div className={styles['jobs-image']} key={index}>
                  <img src={el.image} alt="job-image" fill quality={100} />
                </div>
              );
            })}
          </Slider>
        </div>
        <div className={styles.arrow_button_container}>{isMobile && arrowButton()}</div>
      </section>
    </div>
  );
};
