// @ts-nocheck
import React, {useState, useEffect, useContext, useCallback} from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {getAllJobsWithId} from '@/lib/employeeapi';
import AuthContext from '@/Context/AuthContext';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '@/lib/ErrorHandler';
import {Button} from 'antd';
import styles from './style.module.css';
import {TopJobCard} from '../TopJobCard';
import Slider from 'react-slick';
export default function TopJobsOpenings({latestJob}: {latestJob: any}) {
  const {user} = useContext(AuthContext);

  const settings = {
    dots: false,
    slidesToShow: 3,
    slideToScroll: 3,
    infinite: false,
    speed: 500,
    nextArrow: <></>,
    prevArrow: <></>,
    responsive: [
      {
        breakpoint: 1400,
        settings: {
          slidesToShow: 2.5,
        },
      },
      {
        breakpoint: 1200,
        settings: {
          slidesToShow: 2,
        },
      },
      {
        breakpoint: 980,
        settings: {
          slidesToShow: 1.5,
        },
      },
      {
        breakpoint: 756,
        settings: {
          slidesToShow: 1,
        },
      },
      {
        breakpoint: 540,
        settings: {
          slidesToShow: 0.9,
          slideToScroll: 0.9,
        },
      },
      {
        breakpoint: 400,
        settings: {
          slidesToShow: 0.8,
          slideToScroll: 0.8,
        },
      },
    ],
  };

  return (
    <section className={`${styles['top-jobs']} `} id="top-job-opening-section">
      <div className="container">
        <h3 className="title-heading">
          Top<span className="span-color"> jobs openings</span>
        </h3>
        <h5>Discover exciting opportunities in your field!</h5>
        <div className={`row ${styles['testimonial-job-container']}`}>
          <Slider {...settings}>
            {latestJob?.map((jobs: any, index: number) => {
              return <TopJobCard jobs={jobs} key={index} />;
            })}
          </Slider>
        </div>
        <div className="text-center ">
          <Link href="/jobs-in-gulf" prefetch={false}>
            <Button className="btn-a border-primary-size-18 border-0055BA bg-ebf1f9" style={{height: 'auto'}}>
              Browse Jobs
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}
