// @ts-nocheck
import Image from 'next/image';
import styles from './style.module.css';
import {TestimonialCard} from '../TestimonialCard';
import {Carousel, Stack} from 'react-bootstrap';
import {useMemo, useRef, useState} from 'react';
import Slider from 'react-slick';
import useWindowDimensions from '@/helpers/useWindowDimensions';

export const Testimonial = ({testimonials}: {testimonials: any}) => {
  const testimonialData = (testimonials || []).map((item: any) => {
    return {
      quote: item?.review_comment,
      img:
        item?.user_profile_photo ??
        'https://static.vecteezy.com/system/resources/thumbnails/000/439/863/small/Basic_Ui__28186_29.jpg',
      name: item?.user_name,
      position: `${item?.position}, ${item?.company_name}`,
    };
  });

  let sliderRef: any = useRef(null);
  const [oldSlide, setOldSlide] = useState(0);
  const [activeSlide, setActiveSlide] = useState(0);
  const [activeSlide2, setActiveSlide2] = useState(0);
  const windowDimension: any = useWindowDimensions();

  const settings = {
    dots: false,
    slidesToShow: 3,
    slideToScroll: 1,
    infinite: false,
    speed: 500,
    beforeChange: (current: number, next: number) => {
      setOldSlide(current);
      setActiveSlide(next);
    },
    afterChange: (current: number) => {
      setActiveSlide2(current);
    },
    nextArrow: <></>,
    prevArrow: <></>,
    responsive: [
      {
        breakpoint: 1400,
        settings: {
          slidesToShow: 2.5,
        },
      },
      {
        breakpoint: 1200,
        settings: {
          slidesToShow: 2,
        },
      },
      {
        breakpoint: 980,
        settings: {
          slidesToShow: 1.5,
        },
      },
      {
        breakpoint: 756,
        settings: {
          slidesToShow: 1,
        },
      },
      {
        breakpoint: 540,
        settings: {
          slidesToShow: 0.9,
        },
      },
      {
        breakpoint: 400,
        settings: {
          slidesToShow: 0.8,
        },
      },
    ],
  };
  const noOfSlides = useMemo(() => {
    const items = settings?.responsive.map((item: any) => {
      return {
        breakpoint: item.breakpoint,
        slidesToShow: item.settings.slidesToShow,
        sideToScroll: item.settings.slidesToScroll,
      };
    });

    const slideToShow = items
      .filter((item: any) => item.breakpoint >= windowDimension.width)
      .sort((a: any, b: any) => a.breakpoint - b.breakpoint);

    if (!slideToShow.length) {
      return Math.ceil(testimonialData.length / settings.slidesToShow);
    }
    const noOfSlide = Math.ceil(testimonialData.length / slideToShow?.[0]?.slidesToShow);

    if (slideToShow?.[0]?.slidesToShow < 1) {
      return Math.ceil(testimonialData.length);
    }
    return Math.ceil(noOfSlide);
  }, [windowDimension.width]);

  const handleNext = () => {
    if (noOfSlides === Math.floor(activeSlide2)) return;
    sliderRef.slickNext();
  };

  const handlePrev = () => {
    if (activeSlide === 0) return;
    sliderRef.slickPrev();
  };

  return (
    <>
      <div className={styles.testimonial_top}>
        <div className={styles.testimonial_content}>
          <div className={styles.testimonial_quotes}>
            <div className={styles.quote_image}>
              <img src={'/images/home/<USER>'} alt="Quote" width={80} height={72} />
            </div>
            <span>{testimonialData?.[0]?.quote}</span>
          </div>
          <div className={styles.personal_info}>
            <h5>{testimonialData?.[0]?.name}</h5>
            <p>{testimonialData?.[0]?.position}</p>
          </div>
        </div>
        <div className={styles.testimonial_image}>
          <img src={'/images/home/<USER>'} alt="Testimonial" fill />
        </div>
      </div>
      <div className={`${styles.testimonial_container} container`}>
        <Slider ref={(slider: any) => (sliderRef = slider)} {...settings}>
          {testimonialData.map((testimonial: any, index: number) => {
            return <TestimonialCard key={index} {...testimonial} />;
          })}
        </Slider>
        <div className={`${styles['slide-icon-container']} `}>
          <div onClick={handlePrev} className={`${activeSlide === 0 && styles.inactive}`}>
            <img src={'/images/home/<USER>'} alt="Arrow Left" width={24} height={24} />
          </div>
          <div onClick={handleNext} className={`${noOfSlides === Math.ceil(activeSlide2) && styles.inactive}`}>
            <img src={'/images/home/<USER>'} alt="Arrow Right" width={24} height={24} />
          </div>
        </div>
      </div>
    </>
  );
};
