.testimonial_top {
  display: flex;
  gap: 80px;
  padding: 80px 120px 0 120px;
}
.testimonial_content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 32px;
  flex: 1 0 0;
}
.quote_image {
  position: absolute;
  left: -40px;
  top: -36px;
}
.testimonial_quotes {
  position: relative;
}
.testimonial_quotes span {
  color: #fff;
  font-size: 26px;
  font-weight: 500;
  line-height: 120%;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  max-width: 400px;
  overflow-wrap: break-word;
}

.personal_info {
  color: #ebf4ff;
  font-size: 18px;
}
.personal_info h5 {
  font-weight: 700;
  line-height: 160%;
}
.personal_info p {
  font-weight: 400;
  line-height: 160%;
}
.testimonial_image {
  position: relative;
  height: 400px;
  width: 320px;
}
.testimonial_image img {
  border-radius: 24px;
}
.testimonial_container {
  gap: 32px;
  width: 100%;
  position: relative;
  padding-bottom: 160px;
}
.testimonial_cards {
  display: flex;
  gap: 32px;
  align-items: center;
  position: relative;
}
.slide-icon-container {
  position: absolute;
  left: 50%;
  translate: -50%;
  display: flex;
  align-items: center;
  gap: 15px;
  padding-top: 48px;
}
.slide-icon-container div {
  border-radius: 50px;
  border: 1px solid #fdca40;
  display: flex;
  padding: 12px;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.slide-icon-container div.inactive {
  opacity: 0.5;
}

@media screen and (max-width: 991px) {
  .testimonial_top {
    flex-direction: column;
    gap: 40px;
    padding: 40px 20px 0 20px;
  }
  .testimonial_content {
    gap: 24px;
  }
  .quote_image {
    display: none;
  }
  .testimonial_container {
    padding-bottom: 120px;
  }
  .slide-icon-container {
    padding-top: 34px;
  }
  .testimonial_image {
    height: 340px;
    width: 272px;
  }
  .testimonial_quotes span {
    max-width: 320px;
  }
}
