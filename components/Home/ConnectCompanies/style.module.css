.company-connect-card {
  display: flex;
  padding: 0px 20px 0px 40px;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  position: absolute;
  bottom: -130px;
  border-radius: 16px;
  background: #00387a;
  box-shadow:
    0px 2px 5px 0px rgba(21, 21, 21, 0.12),
    0px 4px 8px -3px rgba(21, 21, 21, 0.15),
    0px 14px 20px -2px rgba(21, 21, 21, 0.16);
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
}
.company-connect-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.company-connect-content h4 {
  color: #fff;
  font-size: 31px;
  font-weight: 500;
  line-height: 120%;
}

.company-connect-content p {
  color: #fff;
  font-size: 22px;
  font-weight: 500;
  line-height: 120%;
}

.company-connect-cta {
  display: flex;
  align-items: center;
  gap: 8px;
  padding-top: 24px;
}

.company-connect-cta button {
  display: flex;
  width: 200px;
  padding: 20px 24px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 2px;

  font-size: 18px;
  font-weight: 500;
  line-height: 120%;
}
.company-connect-cta .cta-contain {
  background: #fdca40;
  color: #151515;
}

.company-connect-cta .cta-outline {
  border-radius: 2px;
  border: 2px solid #fdca40;
  background: rgba(253, 202, 64, 0.12);
  color: #fdca40;
}
.company-images {
  position: relative;
  height: 261px;
  width: 696px;
}

@media screen and (max-width: 991px) {
  .company-connect-card {
    flex-direction: column;
    bottom: -300px;
    justify-content: center;
    align-items: center;
    gap: 24px;
    padding: 24px 12px 0px 12px;
    z-index: 10;
    max-width: 351px;
  }
  .company-connect-content h4 {
    text-align: center;
    font-size: 28px;
  }
  .company-connect-content p {
    text-align: center;
    font-size: 19px;
  }
  .company-connect-cta {
    flex-direction: column;
  }
  .company-connect-cta button {
    width: 100%;
  }
  .company-images {
    height: 158px;
    width: 100%;
  }
  .company-images img {
    object-fit: fill;
  }
}
