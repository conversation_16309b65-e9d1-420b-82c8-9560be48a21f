import {Button, notification} from 'antd';
import Image from 'next/image';
import styles from './style.module.css';
import useWindowDimensions from '@/helpers/useWindowDimensions';
import Link from 'next/link';
import AuthContext from '@/Context/AuthContext';
import {useContext} from 'react';
import {useHandleHire} from '@/hooks/useHandleHireUser';

export const ConnectCompanies = () => {
  const windowDimension: any = useWindowDimensions();

  const isMobile = windowDimension?.width < 768;
  const {user} = useContext(AuthContext);

  const {handleClickShowErrorMessage, uploadCVLink} = useHandleHire();

  return (
    <div className={`${styles['company-connect-card']} container`}>
      <div>
        <div className={styles['company-connect-content']}>
          <h4>Connecting Companies & Top Talent.</h4>
          <p>We’re home to 1000+ job listings!</p>
        </div>
        <div className={styles['company-connect-cta']}>
          <Link href={'/jobs-in-gulf'}>
            <Button className={styles['cta-contain']}>Find your next job</Button>
          </Link>
          <Link
            href={uploadCVLink}
            onClick={
              user?.role == 'employee'
                ? e => handleClickShowErrorMessage(e, 'You need to register as a employer to hire candidates')
                : undefined
            }>
            <Button className={styles['cta-outline']}>Find your next hire</Button>
          </Link>
        </div>
      </div>
      <div className={styles['company-images']}>
        <img
          src={isMobile ? '/images/home/<USER>' : '/images/home/<USER>'}
          fill
          alt="Company Images"
        />
      </div>
    </div>
  );
};
