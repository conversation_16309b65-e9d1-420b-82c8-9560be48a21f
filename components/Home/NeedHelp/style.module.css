.card {
  border-radius: 16px;
  background: #fee39a;
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding: 24px 16px;
}

.card_content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.card_content h4 {
  color: #2c2c2c;
  font-size: 31px;
  font-weight: 700;
  line-height: 120%;
}

.card_content p {
  color: #4d4d4d;
  font-size: 22px;
  font-weight: 700;
  line-height: 120%;
}

.card_content span {
  font-weight: 500 !important;
}

.card_cta button {
  color: #191919;
  border-radius: 2px;
  padding: 21px 24px;
  background-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 2px;
  border: 2px solid #191919;
  font-size: 18px;
  font-weight: 500;
}

@media screen and (max-width: 991px) {
  .card_content h4 {
    font-size: 28px;
  }
  .card_content p {
    font-size: 19px;
  }
  .card_cta button {
    width: 100%;
  }
}
