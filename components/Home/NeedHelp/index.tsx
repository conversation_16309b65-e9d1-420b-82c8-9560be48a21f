import {Button, notification} from 'antd';
import styled from './style.module.css';
import {useContext} from 'react';
import AuthContext from '@/Context/AuthContext';
import {useRouter} from 'next/router';

export const NeedHelpCard = () => {
  const router = useRouter();
  const {user} = useContext(AuthContext);
  const uploadCVLink = () => {
    if (user?.id) {
      if (user?.role === 'employee') {
        router.push('/employees/resume/choose-design/');
      } else {
        notification.error({
          message: 'You need to login as candidate to upload CV',
        });
        return;
      }
    } else {
      return '/auth/login';
    }
  };
  return (
    <div className={styled.card}>
      <div className={styled.card_content}>
        <h4>Need help with your resume? </h4>
        <p>
          Let us make it for you — for free! <span>Build a professional, job winning CV in minutes.</span>
        </p>
      </div>
      <div className={styled.card_cta}>
        <Button
          onClick={() => {
            if (uploadCVLink) {
              uploadCVLink();
            }
          }}>
          Upload your CV
        </Button>
      </div>
    </div>
  );
};
