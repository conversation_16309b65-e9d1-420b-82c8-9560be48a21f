import {Job} from '@/lib/types';
import styles from './style.module.css';
import Link from 'next/link';
import Image from 'next/image';
import {useGetCityNameByCityId} from '@/hooks/useGetCityNameByCityId';
import moment from 'moment';

export const TopJobCard = ({jobs}: {jobs: Job}) => {
  const {cityName} = useGetCityNameByCityId(jobs?.country?.country_name || '', jobs?.job_city);

  const navigateUrl = `/job/${jobs.job_slug}`;

  return (
    <div className={styles['testimonial-job-card']}>
      <div className={styles['card-top-content']}>
        <div className={styles['job-content']}>
          <Link
            target="_blank"
            href={navigateUrl}
            title={jobs.job_title}
            prefetch={false}
            className={styles['job-title']}>
            <h4>{jobs.job_title}</h4>
          </Link>
          <h5 title={jobs?.company?.company_name}>{jobs?.company?.company_name}</h5>
          <ul className={styles['jab-list']}>
            {jobs?.country?.country_name && (
              <div className={styles['job-location']}>
                <img src={'/icons/location-dot.svg'} alt="" width={20} height={20} />
                <span className={styles['job-country-city']}>
                  {jobs.country?.country_name} - {cityName}
                </span>
              </div>
            )}
          </ul>
        </div>
        <div className={styles['company-logo']}>
          {jobs?.company?.logo ? (
            <img
              src={jobs.company.logo ? jobs.company.logo.thumbnail : '/images/logo-cir.png'}
              alt="Logo"
              width={52}
              height={56}
              priority={false}
              loading={'lazy'}
            />
          ) : (
            <span></span>
          )}
        </div>
      </div>
      <div className={styles['posting-time']}>Posted {moment(jobs.created_at).fromNow()}</div>
    </div>
  );
};
