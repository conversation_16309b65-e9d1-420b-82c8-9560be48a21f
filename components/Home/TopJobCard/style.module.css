.testimonial-job-card {
  display: flex;
  height: 200px;
  padding: 12px;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  gap: 8px;
  flex: 1 0 0;
  border-radius: 8px;
  border: 1px solid #f9f9f9;
  background: #fff;
  box-shadow:
    0px 1px 3px 0px rgba(21, 21, 21, 0.12),
    0px 2px 5px 0px rgba(21, 21, 21, 0.1),
    0px 4px 12px 0px rgba(21, 21, 21, 0.12);
  max-width: 416px;
  margin: 20px 0;
}
.job-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.job-content a {
  color: #2c2c2c;
  font-size: 22px;
  font-weight: 500;
  line-height: 120%;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.job-content h5 {
  color: #0055ba !important;
  font-size: 18px;
  font-weight: 600;
  line-height: 160%;
  text-align: start !important;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.card-top-content {
  display: flex;
  gap: 8px;
  align-items: start;
  justify-content: space-between;
  width: 100%;
}
.company-logo {
  display: flex;
  width: 72px;
  height: 72px;
  padding: 0px 7px 0px 6px;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  border: 1px solid var(--Grayscale-02, #eee);
  background: var(--Grayscale-White, #fff);
}
.company-logo span {
  width: 52px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.job-location {
  display: flex;
  align-items: center;
  gap: 4px;
}
.job-country-city {
  color: #999;
  font-size: 16px;
  font-weight: 400;
  line-height: 140%;
}
.posting-time {
  color: #999;
  font-size: 12px;
  font-style: normal;
  font-weight: 300;
  line-height: 140%;
}

@media screen and (max-width: 991px) {
  .testimonial-job-card {
    max-width: 320px;
  }
}
