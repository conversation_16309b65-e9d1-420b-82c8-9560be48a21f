import {Button} from 'antd';
import styles from './style.module.css';
import Image from 'next/image';
import useWindowDimensions from '@/helpers/useWindowDimensions';
import Link from 'next/link';

export const KnowYourWorthCard = () => {
  const windowDimension: any = useWindowDimensions();
  const isMobile = windowDimension.width < 768;
  return (
    <div className={styles.card}>
      <div className={styles.card_content}>
        <div>
          <h3 className={styles.card_title}>Know your worth</h3>
          <p className={styles.card_description}>
            Use our salary insights to discover what your skills and experience are worth in your local market.
          </p>
        </div>
        <div className={styles.card_button}>
          <Link href={'/salaries'}>
            <Button>Search for salaries</Button>
          </Link>
        </div>
      </div>
      <div className={styles.card_image_container}>
        <img
          src="/images/home/<USER>"
          alt="Know your worth"
          width={isMobile ? 343 : 502}
          height={isMobile ? 192 : 282}
          className={styles.card_image}
        />
      </div>
    </div>
  );
};
