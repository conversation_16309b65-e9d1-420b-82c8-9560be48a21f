.card {
  max-width: 620px;
  border-radius: 16px;
  background: #002a5c;
}

.card_content {
  padding: 24px 16px 0 16px;
  flex-direction: column;
  gap: 32px;
  display: flex;
}

.card_title {
  color: #fff;
  font-size: 37px;
  font-weight: 700;
  line-height: 120%;
  padding-bottom: 16px;
}

.card_description {
  color: #ebf4ff;
  font-size: 22px;
  font-weight: 500;
  line-height: 120%;
}
.card_button button {
  display: flex;
  padding: 22px 24px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 2px;
  background: #fff;
  color: #0055ba;
  font-size: 18px;
  font-weight: 500;
  line-height: 120%;
}
.card_image {
  object-fit: fill;
  border-radius: 16px;
}
.card_image_container {
  display: flex;
  justify-content: flex-end;
}

@media screen and (max-width: 991px) {
  .card_title {
    font-size: 33px;
  }
  .card_description {
    font-size: 19px;
  }
  .card_button {
    padding-bottom: 24px;
  }
  .card_button button {
    width: 100%;
  }
}
