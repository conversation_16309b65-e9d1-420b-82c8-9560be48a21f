import React, {useState, useEffect} from "react";
import { useForm } from "react-hook-form";
import { useSession } from "next-auth/react";
import PasswordStrengthBar from "react-password-strength-bar";
import { Base64 } from "js-base64";
import SuccessToast from "../Common/showSuccessTostrMessage";
import ErrorToast from "../Common/showErrorTostrMessage";

import "react-toastify/dist/ReactToastify.css";

export default function BuildTellUs() {
  const [contactNumber, setContactNumber] = useState("");
  const [sessionStatus, setSessionStatus] = useState("");
  const { data: session } = useSession();
  const {
    register,
    getValues,
    handleSubmit,
    formState: { errors },
  } = useForm();
  const [showPassword, setShowPassword] = useState(false);
  const [showRetypePassword, setShowRetypePassword] = useState(false);
  const [showPopupUnSave, setShowPopupUnSave] = useState(false);
  const [showPopupUnError, setShowPopupUnError] = useState(false);
  const [showMessage, setShowMessage] = useState([]);
  const [password, setPassword] = useState<string>();
  const [passwordScore, setPasswordScore] = useState<number>(0);

  useEffect(() => {
    if (session) {
      if (session["user"]) {
        setSessionStatus("ok");
      }
    }
  }, [session]);

  const submitForm = (data: any) => {
    let user_id = window.localStorage.getItem("temp_user_id");
    const user_data = {
      contact_number: data.contact_number,
      password: data.password,
      retype_password: data.retype_password,
    };
    let encode_string = Base64.encode(data.password);
    /*completeUserSignup(user_id, user_data)
      .then((res) => {
        if (res.status == true) {
          window.localStorage.setItem("temp_contact_no", data.contact_number);
          window.localStorage.setItem("temp_password", encode_string);
          setShowMessage(res.message);
          setShowPopupUnSave(true);
          setTimeout(() => {
            setShowPopupUnSave(false);
          }, 3000);
          window.location.href = "/addcompany";
        }
      })
      .catch((err) => {
        console.log(err);
      });*/
  };

  return (
    <>
      <section className="banner-part-home">
        <div className="container-fluid">
          <div className="row">
            <div className="col-lg-5 col-md-12 tab-none pl-0 back-img-form h-100vh">
              {/* <img src={process.env.NEXT_PUBLIC_BASE_URL+'images/form-img.png'} alt="form-img" className='w-100'   /> */}
            </div>
            <div className="col-lg-7 col-md-12 ">
              <div className="form-pages top-m-sp  ">
                <h2>Build your dream team with us!</h2>
                <h5 className="w-500">Tell us a bit about yourself</h5>
                <form
                  className="form-get mt-4 f-form"
                  onSubmit={handleSubmit(submitForm)}
                >
                  {sessionStatus == "ok" ? (
                    <>
                      <label>Your Contact Number *</label>
                      <input
                        type="number"
                        placeholder="123 – 456 – 7890"
                        className="big-input mb-4"
                        {...register("contact_number", {
                          required: true,
                          valueAsNumber: true,
                          minLength: 10,
                          maxLength: 12,
                        })}
                        onChange={(e: any) => setContactNumber(e.target.value)}
                      />
                      {errors.contact_number &&
                        errors.contact_number.type === "required" && (
                          <p
                            className="text-danger bottom-0"
                            style={{ textAlign: "left" }}
                          >
                            Contact Number is required.
                          </p>
                        )}
                      {errors.contact_number?.type === "minLength" && (
                        <p
                          className="text-danger bottom-0"
                          style={{ textAlign: "left" }}
                        >
                          Please Enter min length 10.
                        </p>
                      )}
                      {errors.contact_number?.type === "maxLength" && (
                        <p
                          className="text-danger bottom-0"
                          style={{ textAlign: "left" }}
                        >
                          Please Enter max length 12.
                        </p>
                      )}
                    </>
                  ) : (
                    <>
                      {/* <label>Your Contact Number *</label>
                        <input type="number" placeholder='123 – 456 – 7890' className='big-input mb-4' {...register('contact_number', { required: true, valueAsNumber: true, minLength: 10, maxLength: 12 })} onChange={(e:any) => setContactNumber(e.target.value)}/>
                        {errors.contact_number && errors.contact_number.type === 'required' && <p className="text-danger bottom-0" style={{"textAlign": "left"}}>Contact Number is required.</p>}
                        {errors.contact_number?.type === "minLength" && <p className="text-danger bottom-0" style={{"textAlign": "left"}}>Please Enter min length 10.</p> }
                        {errors.contact_number?.type === "maxLength" && <p className="text-danger bottom-0" style={{"textAlign": "left"}}>Please Enter max length 12.</p> } */}
                      <div className="form-in position-relative c-999999">
                        <div className="form_field_sec">
                          <input
                            type={showPassword ? "text" : "password"}
                            placeholder="Password"
                            className="big-input right-sp"
                            {...register("password", {
                              required: true,
                              minLength: 8,
                              onChange: (e) => {
                                setPassword(e.target.value);
                              }
                            })}
                          />
                          <label>Password*</label>
                        </div>
                        <PasswordStrengthBar password={password} onChangeScore={score => setPasswordScore} />
                        <label
                          onClick={() => setShowPassword(!showPassword)}
                          htmlFor="toggle"
                          style={{ width: "unset" }}
                        >
                          {showPassword ? (
                            <i
                              className="fa-regular fa-eye eye-icon"
                              role="button"
                            ></i>
                          ) : (
                            <i
                              className="fa-regular fa-eye-slash eye-icon"
                              role="button"
                            ></i>
                          )}
                        </label>
                        {errors.password &&
                          errors.password.type === "required" && (
                            <p
                              className="text-danger bottom-0"
                              style={{ textAlign: "left" }}
                            >
                              Password is required.
                            </p>
                          )}
                        {errors.password &&
                          errors.password.type === "pattern" && (
                            <p
                              className="text-danger"
                              style={{ textAlign: "left", marginTop: "10px" }}
                            >
                              Password must be strong: at least 8 characters, 1
                              uppercase, 1 lowercase, 1 digit, and 1 special
                              character.
                            </p>
                          )}
                      </div>
                      <div className="form-in position-relative c-999999">
                        <div className="form_field_sec">
                          <input
                            type={showRetypePassword ? "text" : "password"}
                            placeholder="'Re Password"
                            className="big-input right-sp"
                            {...register("retype_password", {
                              required: true,
                              minLength: 8,
                              validate: (value: string) =>
                                value === getValues("password"),
                            })}
                          />
                          <label>Re-Type Password*</label>
                        </div>

                        <label
                          onClick={() =>
                            setShowRetypePassword(!showRetypePassword)
                          }
                          htmlFor="toggle"
                          style={{ width: "unset" }}
                        >
                          {showRetypePassword ? (
                            <i
                              className="fa-regular fa-eye eye-icon"
                              role="button"
                            ></i>
                          ) : (
                            <i
                              className="fa-regular fa-eye-slash eye-icon"
                              role="button"
                            ></i>
                          )}
                        </label>
                        {errors.retype_password &&
                          errors.retype_password.type === "required" && (
                            <p
                              className="text-danger bottom-0"
                              style={{ textAlign: "left" }}
                            >
                              Retype Password is required.
                            </p>
                          )}
                        {errors.retype_password &&
                          errors.retype_password.type === "minLength" && (
                            <p
                              className="text-danger bottom-0"
                              style={{ textAlign: "left" }}
                            >
                              The Retype Password must be at least 8 Characters.
                            </p>
                          )}
                        {errors.retype_password &&
                          errors.retype_password.type === "validate" && (
                            <p
                              className="text-danger bottom-0"
                              style={{ textAlign: "left" }}
                            >
                              Passwords do not match
                            </p>
                          )}
                      </div>
                    </>
                  )}
                  <label className="check-label">
                    {" "}
                    <input type="checkbox" className="w-16" required /> By
                    clicking checkbox, you agree to our{" "}
                    <a href="#">Terms and Conditions</a> and{" "}
                    <a href="#">Privacy Policy</a>
                  </label>
                  <button
                    className="btn-a primary-size-16 b-0 btn-bg-0055BA w-100 mt-4"
                    type="submit"
                  >
                    Continue
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </section>

      {showPopupUnSave && <SuccessToast message={showMessage} />}
      {showPopupUnError && <ErrorToast message={showMessage} />}
    </>
  );
}
