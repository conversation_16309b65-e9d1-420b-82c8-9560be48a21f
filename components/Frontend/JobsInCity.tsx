import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { Button, Col, Empty, Flex, Form, Input, Modal, Pagination, Row, Select } from 'antd';
import axios from 'axios';
import { usePathname } from 'next/navigation';
import { useForm } from 'antd/lib/form/Form';
import { getAllCitiesByCountryName } from '@/lib/frontendapi';
import { City, Country, Job, PaginationMeta, User } from '@/lib/types';
import { getCountries, searchJobs } from '@/lib/ApiAdapter';
import ErrorHandler from '@/lib/ErrorHandler';
import SubscriptionForm from '../Common/SubscriptionForm';
import JobSearchFilters from '@/components/Jobs/JobSearchFilters';
// import { FunnelPlotOutlined } from '@ant-design/icons';
import JobListItem from '../Common/JobListItem';
import useWindowDimensions from '@/helpers/useWindowDimensions';
import 'react-phone-input-2/lib/style.css';
import 'react-toastify/dist/ReactToastify.css';
import Link from 'next/link';
import JobsOffersPageFaq from './Faq/faq';
import JobsOffersExapandSearch from './expandSearch/expandSearch';
import JsonLd from '../../components/JsonLd';
import Image from 'next/image';

interface JobOffersPageProps {
  jobInCityName: '';
  cityCountryName: '';
  ssrJobs: Job[];
  locationFaq: any;
  skillJobInCityName: string;
}

export default function JobInCity({ jobInCityName, cityCountryName, ssrJobs, locationFaq, skillJobInCityName }: JobOffersPageProps) {
  // const { user } = useContext(AuthContext);
  const splitText = jobInCityName.split("-in-");
  const cityName = splitText[splitText.length - 1];
  //const cityName = jobInCityName.replace('jobs-in-', '');
  const newCityName = cityName.replace(/-/g, ' ');
  const newCityArr = newCityName.split(' ');
  for (let i = 0; i < newCityArr.length; i++) {
    newCityArr[i] = newCityArr[i].charAt(0).toUpperCase() + newCityArr[i].slice(1);
  }
  const finalCityName = newCityArr.join(' ');

  const [jobs, setJobs] = useState<Job[]>(ssrJobs);
  const [cities, setCities] = useState<City[]>();
  const [faq, setFaq] = useState<[]>(locationFaq);
  const [paginationMeta, setPaginationMeta] = useState<PaginationMeta>();
  const [countries, setCountries] = useState<Country[]>();
  const [openJobFilters, setOpenJobFilters] = useState(false);
  const [form] = useForm();
  const router = useRouter();

  const windowDimensions: any = useWindowDimensions();
  const [savedJobs, setSavedJobs] = useState<number[]>();
  const [selectedCityName, setSelectedCityName] = useState('');

  const currentPage = usePathname();

  const GetLogo = () => {
    return (
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
        <mask id="mask0_5141_18910" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
          <rect width="16" height="16" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_5141_18910)">
          <path
            d="M6.66667 12V10.6667H9.33333V12H6.66667ZM4 8.66667V7.33333H12V8.66667H4ZM2 5.33333V4H14V5.33333H2Z"
            fill="#D9D9D9"
          />
        </g>
      </svg>
    );
  };

  function check(name: string = '') {
    const newCityName = name.replace(/-/g, ' ');
    const newCityArr = newCityName.split(" ");
    for (let i = 0; i < newCityArr.length; i++) {
      newCityArr[i] = newCityArr[i].charAt(0).toUpperCase() + newCityArr[i].slice(1);
    }
    return newCityArr.join(" ");
  }

  useEffect(() => {
    const cancelTokenSource = axios.CancelToken.source();
    getCountries(undefined, undefined, cancelTokenSource)
      .then(res => {
        setCountries(res);
      })
      .catch(error => { });

    return cancelTokenSource.cancel;
  }, []);

  useEffect(() => {
    const cancelTokenSource = axios.CancelToken.source();
    getAllCitiesByCountryName(cityCountryName)
      .then(res => {
        if (res.status == true) {
          setCities(res.data);
        } else {
          setCities([]);
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
    const data = {
      city: cityCountryName !== null ? finalCityName === 'Uae' ? '' : finalCityName : '',
      country: cityCountryName ? cityCountryName === 'Uae' ? 'United Arab Emirates' : cityCountryName : finalCityName === 'Uae' ? 'United Arab Emirates' : finalCityName,
      faqLocation: jobInCityName,
    };

    searchJobs(data, cancelTokenSource)
      .then(res => {
        if (res.favorites) {
          setSavedJobs(res.favorites);
        }
        setPaginationMeta(res.meta);
        setJobs(res.data);
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
    return cancelTokenSource.cancel;
  }, [router.query, finalCityName, cityCountryName]);

  const submitSearchForm = (values: any) => {
    const filteredOptions = Object.keys(values)
      .filter(function (k) {
        return values[k] != null;
      })
      .reduce(function (acc: any, k) {
        acc[k] = values[k];
        return acc;
      }, {});

    const filterQueryOptions = {
      country: filteredOptions.country,
      city: filteredOptions.city,
      page: values.page
    }
    router
      .push(
        {
          pathname: '/jobs-in-gulf',
          query: filterQueryOptions,
        },
        undefined,
        { shallow: false },
      )
      .then();
    return;
  };

  const handleLocationChange = (value: any) => {
    getAllCitiesByCountryName(value)
      .then(res => {
        if (res.status == true) {
          setCities(res.data);
        } else {
          setCities([]);
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  };
  const handleChangeCity = (value: any) => {
    setSelectedCityName(value);
  }

  const arr = [];
  const len = jobs.length;
  for (let i = 0; i < len; i++) {
    arr.push({
      "@type": "ListItem",
      "position": i,
      "url": process.env.NEXT_PUBLIC_BASE_URL + '/job/' + jobs[i].job_slug
    });
  }

  return (
    <>
      <section className="form-search-home">

        <div className="container max-search">
          {/* heading start */}
          <section className="top_hdng_outr">
            <div className="container">
              <div className="top_hdng">
                <h6>Helping a billion people to</h6>
                <h1>
                  Find the Right  <span> {check(jobInCityName)}</span>
                </h1>
                <h2>Explore 57,000 {check(jobInCityName)} from top employers</h2>
                {/* <h2>Search from 57,000 Job Listings in {finalCityName} from Top Employers</h2> */}
              </div>
            </div>
          </section>
          {/* heading end */}

          <div className="job-search-top">
            <Form form={form} onFinish={submitSearchForm} size={'large'} initialValues={router.query}>
              <Row gutter={[15, 15]}>
                <Col lg={9} xs={24}>
                  <div className={'field-container'}>
                    <i className="fa-solid fa-magnifying-glass"></i>
                    <Form.Item name={'keywords'} noStyle>
                      <Input placeholder={'Job Title, Keyword, Company or Phrase'} bordered={false} />
                    </Form.Item>
                  </div>
                </Col>
                <Col lg={6} xs={24}>
                  <div className={'field-container'}>
                    <i className="fa-solid fa-location-dot"></i>
                    <Form.Item name={'country'} noStyle initialValue={finalCityName === 'Uae' ? 'United Arab Emirates' : cityCountryName}>
                      <Select
                        allowClear
                        bordered={false}
                        style={{ width: '100%' }}
                        showSearch
                        optionFilterProp={'label'}
                        placeholder={'Select country'}
                        onChange={value => handleLocationChange(value)}
                        options={countries?.map((country: any) => {
                          return { value: country.country_name.toString(), label: country.country_name };
                        })}
                      //defaultValue={cityCountryName}
                      />
                    </Form.Item>
                  </div>
                </Col>
                <Col lg={6} xs={24}>
                  <div className={'field-container'}>
                    <i className="fa-solid fa-location-dot"></i>
                    <Form.Item name={'city'} noStyle initialValue={finalCityName === 'Uae' ? '' : finalCityName}>
                      <Select
                        style={{ width: '100%' }}
                        bordered={false}
                        showSearch
                        title={'Select a country to see cities'}
                        //disabled={!!cities}
                        onChange={value => handleChangeCity(value)}
                        placeholder={'Select city'}
                        options={cities?.map((city: any) => {
                          return { value: city.city_name.toString(), label: city.city_name };
                        })}
                      //defaultValue={finalCityName}
                      />
                    </Form.Item>
                  </div>
                </Col>
                <Col lg={3} xs={24}>
                  <Button htmlType={'submit'} type={'primary'} block>
                    Search
                  </Button>
                </Col>
              </Row>
            </Form>
          </div>
          {/* Trending Job */}
          {/* <TrendingJobs trendJobs={trendJobs} submitSearchForm={submitSearchForm} /> */}
          {/* Trending Job */}
        </div>
      </section>
      <div className="jobspage">
        <div className="container">
          <ul>
            <li>
              <Link href="/" prefetch={false}>Jobs</Link>
            </li>
          </ul>
        </div>
      </div>
      <section>
        <div className="container">
          <Flex justify={'space-between'} className="job_title_container">
            {/* <h2 className="job_title">{paginationMeta?.total} Jobs Found in MiddleEast</h2> */}
            <h2 className="job_subtitle">
              Displaying {paginationMeta?.from}-{paginationMeta?.to}
            </h2>
          </Flex>
          {windowDimensions.width < 991 && (
            <Button size={'small'} icon={<GetLogo />} onClick={() => setOpenJobFilters(true)}>
              Open filters
            </Button>
          )}
          <div className="row">
            <div className="col-lg-4 filter-sec">
              <div className="salary-insights">
                <p>What's your earning potential?</p>
                <h3>Find out what your skills are worth!</h3>
                <Link prefetch={false} href={"javascript:void(0)"}>
                  Try Salary Insights
                  <img src="images/arrow.svg" alt="" />
                </Link>
              </div>
              {windowDimensions.width > 991 && (
                <JobSearchFilters countries={countries} form={form} onFinish={submitSearchForm} />
              )}

              {/* Expand search */}
              {windowDimensions.width > 991 && (
                <JobsOffersExapandSearch />
              )}
              {/* Expand search */}
              <Modal open={openJobFilters} footer={false} title={'Filters'} onCancel={() => setOpenJobFilters(false)}>
                <JobSearchFilters countries={countries} form={form} onFinish={submitSearchForm} />
              </Modal>
            </div>
            <div className="col-lg-8">
              <div className={'jobs-results'}>
                {jobs?.map((job, index) => <JobListItem key={job?.id} job={job} index={index} jobInCityName={jobInCityName} jobPosting={false} />)}
                {jobs.length === 0 && (
                  <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description={'No jobs found with this search values, try using different values'}
                  />
                )}

              </div>
              {windowDimensions.width < 991 && (
                <JobsOffersExapandSearch />
              )}
              <div className="pagination-tab">
                {paginationMeta && (
                  <Pagination
                    onChange={page => {
                      submitSearchForm({ ...router.query, page });
                    }}
                    total={paginationMeta.total}
                    current={paginationMeta.current_page}
                  />
                )}
              </div>
              <JsonLd data={{
                "@context": "https://schema.org",
                "@type": "ItemList",
                "itemListElement": arr
              }} />
            </div>
          </div>
        </div>
      </section>
      {/* Faq */}
      {router.query.hasOwnProperty('keywords') || router.query.hasOwnProperty('country') ? <></> :
        <JobsOffersPageFaq
          submitSearchForm={submitSearchForm}
          countries={countries}
          jobs={jobs}
          locationFaq={faq}
          cityName={finalCityName}
          skillJobInCityName={jobInCityName}
        />}
      {/* Faq */}
      <SubscriptionForm />
    </>
  );
}
