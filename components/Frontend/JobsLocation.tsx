import React from 'react';
import Image from 'next/image';
import Link from 'next/link';

export default function JobsLocation({cityLocation, otherLocation}: {cityLocation: any; otherLocation: any}) {
  // const [cityLocation, setCityLocation] = useState([]);
  // const [otherLocation, setOtherLocation] = useState([]);
  // useEffect(() => {
  //   getCountriesCitiesLocations()
  //     .then(res => {
  //       if (res.status == true) {
  //         setCityLocation(res.data);
  //         setOtherLocation(res.other_location_data);
  //       } else {
  //         setCityLocation([]);
  //         setOtherLocation([]);
  //       }
  //     })
  //     .catch(err => {
  //       console.log(err);
  //     });
  // }, []);
  return (
    <>
      <section className="uae-new bg-dark-blue ">
        <div className="container">
          <div className="row">
            <div className="col-sm-9">
              <h1 aria-level={1}>
                Explore Jobs by <span className="span-highlight">Location</span>
              </h1>
            </div>
            <div className="col-sm-3 text-end">
              <img
                src={process.env.NEXT_PUBLIC_BASE_URL + 'images/jobLocationBannerImg.png'}
                alt="Banner Image for Job Loaction"
                className="glas m-none"
                width={175}
                height={90}
                priority
              />
            </div>
          </div>
        </div>
      </section>
      <section className="locations-new">
        <div className="container">
          <div className="jobs-by-location mt-5">
            <div className="row">
              {cityLocation.map((cityLocationData: any, index: number) => {
                const cityLocationTypeData: {country_name: string; cities: []} = cityLocationData;
                return (
                  <div className="col-sm-2" key={index}>
                    <h3 aria-level={3}>{cityLocationTypeData.country_name}</h3>
                    <ul className="uae-part">
                      {cityLocationTypeData.cities.map((citiesData, index) => {
                        const citiesTypeData: {city_name: string} = citiesData;
                        let city_slug = citiesTypeData.city_name.replace(/\s+/g, '-').toLowerCase();
                        return (
                          <li key={index}>
                            <Link prefetch={false} href={'/jobs-in-' + city_slug}>
                              {citiesTypeData.city_name}
                            </Link>
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                );
              })}
            </div>
          </div>
          <div className="jobs-by-location mt-5">
            <div className="row mb-3">
              <div className="col-sm-6 col-8">
                <h2 aria-level={2}>Other Locations</h2>
              </div>
            </div>
            <div className="row">
              <div className="col">
                <ul className="uae-part" style={{columnCount: '4'}}>
                  {otherLocation.map((otherLocationData: any, index: number) => {
                    const otherLocationTypeData: {city_name: string} = otherLocationData;
                    let city_slug = otherLocationTypeData.city_name.replace(/\s+/g, '-').toLowerCase();
                    return (
                      <li key={index}>
                        <Link prefetch={false} href={'/jobs-in-' + city_slug}>
                          {otherLocationTypeData.city_name}
                        </Link>
                      </li>
                    );
                  })}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
