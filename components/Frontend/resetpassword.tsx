import React, { useState } from 'react';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { resetPassword } from '../../lib/frontendapi'
import { useRouter } from 'next/router';
import SuccessToast from "../Common/showSuccessTostrMessage";
import ErrorToast from "../Common/showErrorTostrMessage";
import Image from 'next/image';

export default function ResetPassword() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [passwordConfirm, setPasswordConfirm] = useState('');
  const [message, setMessage] = useState('');
  const router = useRouter();
  const { token } = router.query;
  const [showPopupunsave, setShowPopupunsave] = useState(false);
  const [showPopupunerror, setShowPopupunerror] = useState(false);
  const [showmessage, setShowmessage] = useState('');

  const handleReset = async (e: any) => {
    e.preventDefault();

    if (!email || !password || !passwordConfirm) {
      setShowmessage('Please fill all fields');
      setShowPopupunerror(true);
      setTimeout(() => {
        setShowPopupunerror(false)
      }, 3000)
      // toast.error('Please fill all fields', {
      //   position: toast.POSITION.TOP_RIGHT,
      //   closeButton: true,
      //   hideProgressBar: false,
      //   style: {
      //     background: '#ffe6e6',
      //     color: '#d04e4f',
      //     maxWidth: "300px",
      //     padding: 0,
      //     margin: 0,
      //     fontSize: "15px",
      //     fontFamily: "var(--opensans-font)",
      //     paddingRight: "10px",
      //     paddingLeft: "10px",
      //   },
      //   progressStyle: {
      //     background: '#ffe6e6',
      //   },
      //   icon: () => <i className="fa-solid fa-circle-xmark" style={{ color: "#d04e4f", fontSize: "16px", }}></i>,
      // });
      return;
    }
    if (password !== passwordConfirm) {
      setShowmessage('Passwords do not match');
      setShowPopupunerror(true);
      setTimeout(() => {
        setShowPopupunerror(false)
      }, 3000)      // toast.error('Passwords do not match', {
      //   position: toast.POSITION.TOP_RIGHT,
      //   closeButton: true,
      //   hideProgressBar: false,
      //   style: {
      //     background: '#ffe6e6',
      //     color: '#d04e4f',
      //     maxWidth: "300px",
      //     padding: 0,
      //     margin: 0,
      //     fontSize: "15px",
      //     fontFamily: "var(--opensans-font)",
      //     paddingRight: "10px",
      //     paddingLeft: "10px",
      //   },
      //   progressStyle: {
      //     background: '#ffe6e6',
      //   },
      //   icon: () => <i className="fa-solid fa-circle-xmark" style={{ color: "#d04e4f", fontSize: "16px", }}></i>,
      // });
      return;
    }

    const user = {
      token: token,
      email: email,
      password: password,
      password_confirmation: passwordConfirm
    }
    // resetPassword(user);
    try {
      const response = await resetPassword(user);
      console.log(response)
      if (response.success === true) {
        setShowmessage('Password reset successful');
        setShowPopupunsave(true);
        setTimeout(() => {
          setShowPopupunsave(false)
        }, 3000)
        setTimeout(() => {
          router.push('/Login');
        }, 3000);
      } else {
        setShowmessage('Password reset failed');
        setShowPopupunerror(true);
        setTimeout(() => {
          setShowPopupunerror(false)
        }, 3000)
      }
    } catch (error) {
      setShowmessage('Password reset failed');
      setShowPopupunerror(true);
      setTimeout(() => {
        setShowPopupunerror(false)
      }, 3000)

    }
  };
  return (
    <>
      <section className='banner-part-home'>
        <div className='container-fluid'>
          <div className='row'>
            <div className='col-lg-5 col-md-12 tab-none pl-0'>
              <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/form-img.png'} alt="form-img" className='w-100' />
            </div>
            <div className='col-lg-7 col-md-12'>
              <div className='form-pages top-m-sp'>
                <h2>Forgot Your Password?</h2>
                <h5>Let’s get you connected.</h5>
                <form className='form-get mt-4'>
                  <div className="form_field_sec">
                    <input type="email" className="big-input" id="email" placeholder="<EMAIL>" value={email} onChange={(e) => setEmail(e.target.value)} />
                    <label>Email ID*</label>
                  </div>
                  {/* {errors.email && <span className="text-danger">{errors.email}</span>} */}
                  <button type="submit" className="btn-a primary-size-16 b-0 btn-bg-0055BA w-100">Reset Password</button>
                </form>
              </div>
            </div>
          </div>
        </div>
        <ToastContainer />
      </section>

      {showPopupunsave &&
        <SuccessToast message={showmessage} />
      }
      {showPopupunerror &&
        <ErrorToast message={showmessage} />
      }
    </>
  )
}
