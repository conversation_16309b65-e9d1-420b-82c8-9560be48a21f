import React, { useState, useEffect, useContext } from 'react';
import { useRouter } from 'next/router';
import { Button, Col, Empty, Flex, Form, Input, Modal, Pagination, Row, Select } from 'antd';
import axios from 'axios';
import useWindowDimensions from '@/helpers/useWindowDimensions';
import { useForm } from 'antd/lib/form/Form';
import AuthContext from '@/Context/AuthContext';
import { usePathname } from 'next/navigation'
import JsonLd from '../../components/JsonLd';

import {
  toggleFavoriteJob,
  removeFavoriteJob,
  applyJob,
  getusersavedjobs,
  getAllCitiesByCountryName,
  getSingleCityByName,
  getAllJobsSearch,
  getSingleSkillByName,
} from '@/lib/frontendapi';
import { City, Country, Job, PaginationMeta, User } from '@/lib/types';
import { getCountries, searchJobs } from '@/lib/ApiAdapter';

import JobsOffersPageFaq from './Faq/faq';

import JobItem from '@/components/Frontend/JobOffers/JobItem';
import ErrorHandler from '@/lib/ErrorHandler';
import SubscriptionForm from '../Common/SubscriptionForm';

import 'react-phone-input-2/lib/style.css';
import 'react-toastify/dist/ReactToastify.css';
import JobSearchFilters from '@/components/Jobs/JobInSkillSearchFilters';
import { FunnelPlotOutlined } from '@ant-design/icons';
import JobsOffersExapandSearch from './expandSearch/expandSearch';
import JobListItem from '../Common/JobListItem';
import Link from 'next/link';

function getWindowDimensions() {
  if (typeof window !== 'undefined') {
    const { innerWidth: width, innerHeight: height } = window;
    return {
      width,
      height,
    };
  } else {
    return {
      width: 800,
      height: 1000,
    };
  }
}

interface JobOffersPageProps {
  skillJobInCityName: '';
  cityCountryName: '';
  ssrJobs: Job[];
  locationFaq: any;
  keywordName: any;
  finalCityName: any;
}

export default function JobInSkills({
  skillJobInCityName,
  cityCountryName,
  ssrJobs,
  locationFaq,
  finalCityName,
  keywordName,
}: JobOffersPageProps) {

  const { user } = useContext(AuthContext);
  const skillName = skillJobInCityName.substring(0, skillJobInCityName.indexOf('-in-'));
  const newSkillName = skillName.replace(/-/g, ' ');
  const newSkillArr = newSkillName.split(' ');
  for (let i = 0; i < newSkillArr.length; i++) {
    newSkillArr[i] = newSkillArr[i].charAt(0).toUpperCase() + newSkillArr[i].slice(1);
  }
  const finalSkillName = newSkillArr.join(' ');

  const windowDimensions: any = useWindowDimensions();
  const [jobs, setJobs] = useState<Job[]>(ssrJobs);
  const [cities, setCities] = useState<City[]>();
  const [paginationMeta, setPaginationMeta] = useState<PaginationMeta>();
  const [countries, setCountries] = useState<Country[]>();
  const [faq, setFaq] = useState<[]>(locationFaq);
  const [openJobFilters, setOpenJobFilters] = useState(false);
  const [form] = useForm();
  const router = useRouter();
  const pathname = usePathname()

  console.log(pathname, "pathname", router)
  // const [windowDimensions, setWindowDimensions] = useState(getWindowDimensions());
  const [savedJobs, setSavedJobs] = useState<number[]>();
  const [jobTypeData, setJobType] = useState('');
  // useEffect(() => {
  //   function handleResize() {
  //     setWindowDimensions(getWindowDimensions());
  //   }

  //   window.addEventListener('resize', handleResize);
  //   return () => window.removeEventListener('resize', handleResize);
  // }, []);

  useEffect(() => {
    const cancelTokenSource = axios.CancelToken.source();
    getCountries(undefined, undefined, cancelTokenSource)
      .then(res => {
        setCountries(res);
      })
      .catch(error => { });

    return cancelTokenSource.cancel;
  }, []);




  useEffect(() => {
    const cancelTokenSource = axios.CancelToken.source();
    getAllCitiesByCountryName(cityCountryName)
      .then(res => {
        if (res) {
          setCities(res);
        } else {
          setCities([]);
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
    const cityData = finalCityName === 'Uae' ? 'United Arab Emirates' : finalCityName

    let jobType = ''

    if (keywordName === 'Part Time' || keywordName === 'Freelance' || keywordName === 'Contract' || keywordName === 'Full Time') {
      jobType = keywordName.replace(/\s/g, '').toLowerCase()
      setJobType(jobType)
    }

    const data = {
      country: cityCountryName
        ? cityCountryName === 'Uae'
          ? 'United Arab Emirates'
          : cityCountryName
        : cityData,
      city: cityCountryName !== null ? cityCountryName === finalCityName ? '' : finalCityName : '',
      //skill: finalSkillName,
      keywords: jobType === '' ? keywordName : '',
      faqLocation: skillJobInCityName,
      job_type: jobType
    };
    searchJobs(data, cancelTokenSource)
      .then(res => {
        if (res.favorites) {
          setSavedJobs(res.favorites);
        }
        setPaginationMeta(res.meta);
        setJobs(res.data);
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
    return cancelTokenSource.cancel;
  }, [router.query, finalCityName, finalSkillName, cityCountryName, user]);

  const submitSearchForm = (values: any) => {
    const filteredOptions = Object.keys(values)
      .filter(function (k) {
        return values[k] != null;
      })
      .reduce(function (acc: any, k) {
        acc[k] = values[k];
        return acc;
      }, {});
    router
      .push(
        {
          pathname: '/jobs-in-gulf',
          query: filteredOptions,
        },
        undefined,
        { shallow: false },
      )
      .then();
    return;
  };

  const handleLocationChange = (value: any) => {
    getAllCitiesByCountryName(value)
      .then(res => {
        if (res) {
          setCities(res);
        } else {
          setCities([]);
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  };

  const arr = [];
  const len = jobs.length;
  for (let i = 0; i < len; i++) {
    arr.push({
      "@type": "ListItem",
      "position": i,
      "url": process.env.NEXT_PUBLIC_BASE_URL + '/job/' + jobs[i].job_slug
    });
  }

  function check(name: string = '') {
    const newCityName = name.replace(/-/g, ' ');
    const newCityArr = newCityName.split(" ");
    for (let i = 0; i < newCityArr.length; i++) {
      newCityArr[i] = newCityArr[i].charAt(0).toUpperCase() + newCityArr[i].slice(1);
    }
    return newCityArr.join(" ");
  }

  return (
    <>
      <section className="form-search-home">
        <div className="container max-search">
          {/* heading start */}
          <section className="top_hdng_outr">
            <div className="container">
              <div className="top_hdng">
                <h6>Helping a billion people to</h6>
                <h1>
                  Find the Right <span> {check(skillJobInCityName)}</span>
                </h1>
                <h2>Explore 57,000 {check(skillJobInCityName)} from top employers</h2>
                {/* <h2>Search from 57,000 Job Listings in {finalCityName} from Top Employers</h2> */}
              </div>
            </div>
          </section>
          {/* heading end */}
          <div className="job-search-top">
            <Form form={form} onFinish={submitSearchForm} size={'large'} initialValues={router.query}>
              <Row gutter={[15, 15]}>
                <Col lg={9} xs={24}>
                  <div className={'field-container'}>
                    <i className="fa-solid fa-magnifying-glass"></i>
                    <Form.Item name={'keywords'} noStyle>
                      <Input placeholder={'Job Title, Keyword, Company or Phrase'} bordered={false} />
                    </Form.Item>
                  </div>
                </Col>
                <Col lg={6} xs={24}>
                  <div className={'field-container'}>
                    <i className="fa-solid fa-location-dot"></i>
                    <Form.Item name={'country'} noStyle>
                      <Select
                        allowClear
                        bordered={false}
                        style={{ width: '100%' }}
                        showSearch
                        optionFilterProp={'label'}
                        placeholder={'Select country'}
                        onChange={value => handleLocationChange(value)}
                        options={countries?.map((country: any) => {
                          return { value: country.country_name.toString(), label: country.country_name };
                        })}
                        //defaultValue={cityCountryName}
                        defaultValue={finalCityName === 'Uae' ? 'United Arab Emirates' : cityCountryName}

                      />
                    </Form.Item>
                  </div>
                </Col>
                <Col lg={6} xs={24}>
                  <div className={'field-container'}>
                    <i className="fa-solid fa-location-dot"></i>
                    <Form.Item name={'city'} noStyle>
                      <Select
                        style={{ width: '100%' }}
                        bordered={false}
                        showSearch
                        title={'Select a country to see cities'}
                        //disabled={!!cities}
                        placeholder={'Select city'}
                        // options={cities?.map((city: any) => {
                        //   return { value: city.city_name.toString(), label: city.city_name };
                        // })}
                        //defaultValue={finalCityName}
                        defaultValue={finalCityName === 'Uae' ? '' : finalCityName === cityCountryName ? '' : finalCityName}
                      />
                    </Form.Item>
                  </div>
                </Col>
                <Col lg={3} xs={24}>
                  <Button htmlType={'submit'} type={'primary'} block>
                    Search
                  </Button>
                </Col>
              </Row>
            </Form>
          </div>
        </div>
      </section>
      <div className="jobspage">
        <div className="container">
          <ul>
            <li>
              <Link href="/">Jobs</Link>
            </li>
          </ul>
        </div>
      </div>

      <section>
        <div className="container">
          <Flex justify={'space-between'} className="job_title_container">
            {/* <h2 style={{ fontSize: "24px" }}>{paginationMeta?.total} Jobs Found</h2> */}
            <h2 className="job_subtitle">
              Displaying {paginationMeta?.from}-{paginationMeta?.to}
            </h2>
          </Flex>
          {windowDimensions.width < 991 && (
            <Button size={'small'} icon={<FunnelPlotOutlined />} onClick={() => setOpenJobFilters(true)}>
              Open filters
            </Button>
          )}

          <div className="row">
            <div className="col-lg-4 filter-sec">
              <div className="salary-insights">
                <p>What's your earning potential?</p>
                <h3>Find out what your skills are worth!</h3>
                <a href="javascript:void(0)">
                  Try Salary Insights <img src="images/arrow.svg" alt="" />
                </a>
              </div>

              {windowDimensions.width > 991 && (
                <JobSearchFilters
                  countries={countries}
                  skillName={finalSkillName}
                  form={form}
                  onFinish={submitSearchForm}
                  jobTypeData={jobTypeData}
                />
              )}
              {/* Expand search */}
              {windowDimensions.width > 991 && <JobsOffersExapandSearch />}
              {/* Expand search */}
              <Modal open={openJobFilters} footer={false} title={'Filters'} onCancel={() => setOpenJobFilters(false)}>
                <JobSearchFilters
                  countries={countries}
                  skillName={finalSkillName}
                  form={form}
                  onFinish={submitSearchForm}
                  jobTypeData={jobTypeData}
                />
              </Modal>
            </div>
            <div className="col-lg-8">
              <div className={'jobs-results'}>
                {jobs?.map((job, index) => <JobListItem key={index} job={job} index={index} jobInCityName={skillJobInCityName} jobPosting={false} />)}
                {jobs?.length === 0 && (
                  <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description={'No jobs found with this search values, try using different values'}
                  />
                )}
                <JsonLd data={{
                  "@context": "https://schema.org",
                  "@type": "ItemList",
                  "itemListElement": arr
                }} />
              </div>
              {windowDimensions.width < 991 && <JobsOffersExapandSearch />}
              <div className="pagination-tab">
                {paginationMeta && (
                  <Pagination
                    onChange={page => {
                      submitSearchForm({ ...router.query, page });
                    }}
                    total={paginationMeta.total}
                    current={paginationMeta.current_page}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Faq */}
      {router.query.hasOwnProperty('keywords') || router.query.hasOwnProperty('country') ? (
        <></>
      ) : (
        <JobsOffersPageFaq
          submitSearchForm={submitSearchForm}
          countries={countries}
          jobs={jobs}
          locationFaq={faq}
          cityName={finalCityName}
          skillJobInCityName={skillJobInCityName}
        />
      )}
      {/* Faq */}
      <SubscriptionForm />
    </>
  );
}
