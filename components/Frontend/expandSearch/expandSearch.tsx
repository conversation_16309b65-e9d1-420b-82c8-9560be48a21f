import React, {useState, useEffect, useContext} from 'react';
import Link from 'next/link';
import Image from 'next/image';
import {useRouter} from 'next/router';

export default function JobsOffersExapandSearch() {
  const expandKeyWord = [
    {
      label: 'Software Engineer',
    },
    {
      label: 'Software Developer',
    },
    {
      label: 'Product Manager',
    },
    {
      label: 'QA Tester',
    },
    {
      label: 'Fresher',
    },
    {
      label: 'Part time',
    },
  ];
  const router = useRouter();

  return (
    <section className="expand_srch_sec">
      <div>
        <div className="expand_srch_hdng">
          <h6>Expand your search</h6>
        </div>
        <div className="expand_srch_list">
          <ul>
            {expandKeyWord.map((item, index) => {
              return (
                <li key={index} className={router.query.keywords === item.label ? 'active' : ''}>
                  <Link href={`?keywords=${item.label}`} prefetch={false}>
                    <span>
                      <img src="/images/search_line.png" alt="" width={16} height={16} />
                    </span>
                    {item.label}
                  </Link>
                </li>
              );
            })}
          </ul>
        </div>
      </div>
    </section>
  );
}
