import React, {useContext} from 'react';
import {useRouter} from 'next/router';
import SubscriptionForm from '../Common/SubscriptionForm';
import Link from 'next/link';
import Image from 'next/image';
import swal from 'sweetalert2';
import AuthContext from '@/Context/AuthContext';
import {Button, notification} from 'antd';

export default function HomeEmployer() {
  const router = useRouter();
  const {user, setMemberTempPlanInfo} = useContext(AuthContext);

  const handleClickSubmitPament = (e: any, amount: any, plan_id: any) => {
    if (plan_id == 1) {
      swal.fire({
        icon: 'info',
        title: 'Free Plan',
        text: 'Your free plan has been activated when you create an account and is valid for one day',
      });
    } else {
      setMemberTempPlanInfo(amount, plan_id);
      router.push('/payment');
    }
  };

  const handleError = (e: any, type: any) => {
    if (type == 'plan') {
      swal.fire({
        icon: 'info',
        title: 'Alert',
        text: 'Your have already bought the plan',
      });
    } else {
      swal.fire({
        icon: 'info',
        title: 'Alert!',
        text: 'This plan is avaibable for job seeker',
      });
    }
  };

  const handleApplyJob = (role: string, redirect = '/jobs-in-gulf') => {
    if (!user) {
      sessionStorage.setItem('signup.role', role);
      router.push('/auth/signup/step-1').then();
    } else router.push(redirect);
  };

  return (
    <>
      <section className="banner-part-home sp-80" id="pb-sp-0">
        <div className="container">
          <div className="row">
            <div className="col-lg-6 col-md-12">
              <div className="text-box-width-cover">
                <h1 className="mt-5 tab-m-0">
                  Attract <span className="span-color">Top Talent </span> & Elevate Your Hiring Game
                </h1>
                <p className="font-banner-26 mb-4">Discover Quality Talent on Our Platform Today!</p>
                <Button
                  onClick={() => handleApplyJob('employer')}
                  className="btn-a primary-size-22 btn-bg-0055BA mr-1 tab-w-100"
                  style={{height: 'auto'}}>
                  Sign Up Now
                </Button>
                <Button
                  onClick={() => handleApplyJob('employer', 'employer/jobs')}
                  className="btn-a border-primary-size-22 border-0055BA tab-w-100 tab-m-t-b"
                  style={{height: 'auto'}}>
                  Post A Job
                </Button>
              </div>
            </div>
            <div className="col-lg-6 col-md-12">
              <img
                src={process.env.NEXT_PUBLIC_BASE_URL + 'images/home/<USER>'}
                alt="Frame 17"
                width={821}
                height={650}
                layout="responsive"
              />
            </div>
          </div>
        </div>
      </section>

      <section className="sp-80 m-mins">
        <div className="container">
          <div className="pool-today">
            <div className="row">
              <div className="col-sm-7">
                <div className="updated-stay">
                  <h2>
                    Access Our Pool of <span className="span-yellow">5 Million Candidates</span> Today!
                  </h2>
                  <p className="f-22 c-fff">
                    Don't miss out on this opportunity - sign up today and take your hiring to the next level!
                  </p>
                  <Button
                    className="btn-a primary-size-22 btn-bg-fff"
                    onClick={() => handleApplyJob('employer', '/employer/candidates')}
                    style={{height: 'auto'}}>
                    Find Talent
                  </Button>
                </div>
              </div>
              <div className="col-sm-5"></div>
            </div>
          </div>
        </div>
      </section>

      <section className="say-wow m-pad-top-0 sp-80">
        <div className="container">
          <div className="row">
            <div className="col-sm-6">
              <img
                src={process.env.NEXT_PUBLIC_BASE_URL + 'images/dash-home-img.png'}
                alt="dash-home-img"
                className="mt-5 m-sp-0 "
                id="mar-top"
                width={534}
                height={421}
              />
            </div>
            <div className="col-sm-6 get-ready">
              <div className="get-wow">
                <h3 className="title-heading c-0055BA mobile-left">Get Ready to Say Wow:</h3>
                <p className="f-26 mobile-f-23">Simplifying Your Hiring Process</p>
                <p className="f-26 mobile-f-23">
                  At TheTalentpoint, we've designed a platform that streamlines your hiring process
                </p>
                <img
                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/home2-icon-1.png'}
                  alt="home2-icon-1"
                  className="mt-3 mb-2"
                  width={48}
                  height={48}
                />
                <p className="f-22 c-151515 mb-2 w-700">Stay Organised with Our All -in One Dashboard</p>
                <p className="f-18 c-292929 w-400">
                  Our all-in-one dashboard keeps everything organised. Track applicants, view resumes, and manage
                  interviews with ease.
                </p>
                <img
                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/home2-icon-2.png'}
                  alt="home2-icon-2"
                  className="mt-3 mb-2"
                  width={48}
                  height={48}
                />
                <p className="f-22 c-151515 mb-2 w-700">Filter and Sort Responses with ease</p>
                <p className="f-18 c-292929 w-400">
                  Quickly find the perfect fit with advanced filtering and sorting options. Refine your search based on
                  qualifications and experience.
                </p>
                <img
                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/home2-icon-3.png'}
                  alt="home2-icon-3"
                  className="mt-3 mb-2"
                  width={48}
                  height={48}
                />
                <p className="f-22 c-151515 mb-2 w-700">Never Miss a Qualified Candidate Again</p>
                <p className="f-18 c-292929 w-400">
                  This Portal highlights top talent based on your Search so you never miss a qualified candidate.
                </p>
                <p className="f-18 c-292929 w-400">
                  Join TheTalentpoint and simplify your hiring journey today. Say "Wow" to a brighter future for your
                  organisation.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
      <hr className="color-change" />
      <section className="say-wow sp-80">
        <div className="container">
          <div className="row">
            <div className="col-sm-6">
              <div className="get-wow">
                <h3 className="title-heading c-0055BA mobile-left">Scheduling Interviews Made Effortless:</h3>
                <p className="f-26 mobile-f-23">Streamline Your Hiring Process with Our Interview Scheduler</p>
                <img
                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/home2-icon-4.png'}
                  alt="home2-icon-1"
                  className="mt-3 mb-2"
                  width={40}
                  height={46}
                />
                <p className="f-22 c-151515 mb-2 w-700">Schedule Interviews with Ease</p>
                <p className="f-18 c-292929 w-400">
                  Say Goodbyes to phone calls . Choose time slots, invite candidates, and confirm appointments in just a
                  few clicks.
                </p>
                <img
                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/home2-icon-5.png'}
                  alt="home2-icon-5"
                  className="mt-3 mb-2"
                  width={39}
                  height={41}
                />
                <p className="f-22 c-151515 mb-2 w-700">Send Reminders & Notifications to Candidates</p>
                <p className="f-18 c-292929 w-400">
                  Ensure that candidates never miss an interview with our automated reminders and notifications
                </p>
                <img
                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/home2-icon-6.png'}
                  alt="home2-icon-6"
                  className="mt-3 mb-2"
                  width={39}
                  height={41}
                />
                <p className="f-22 c-151515 mb-2 w-700">Save Time in Your Hiring Process</p>
                <p className="f-18 c-292929 w-400 mb-5">
                  Spend less time on administrative tasks and more time focusing on the right candidate
                </p>
              </div>
            </div>

            <div className="col-sm-6">
              <img
                src={process.env.NEXT_PUBLIC_BASE_URL + 'images/dash-home-img2.png'}
                alt="dash-home-img2"
                className="mt-5 m-sp-0"
                id="mar-top-lg"
                width={462}
                height={360}
              />
            </div>
          </div>
        </div>
      </section>

      <section className="and-there bg-0055BA sp-80">
        <div className="container">
          <h3 className="title-heading c-fff mobile-left">And there’s more... </h3>
          <div className="row mt-4 mb-4">
            <div className="col-sm-6">
              <img
                src={process.env.NEXT_PUBLIC_BASE_URL + 'images/yellow-1.png'}
                alt="yellow-1"
                className="mt-1 mb-2 m-sp-0"
                width={56}
                height={57}
              />
              <h4 className="f-37 c-fff">Organize Candidates for Future Reference</h4>
              <p className="f-22 c-fff mb-1">
                Effortlessly categorize and store candidates for easy retrieval in the future. Streamline your talent
                pipeline management.
              </p>
            </div>
            <div className="col-sm-6">
              <img
                src={process.env.NEXT_PUBLIC_BASE_URL + 'images/yellow-2.png'}
                alt="yellow-2"
                className="mt-1 mb-2 m-sp-0"
                width={42}
                height={51}
              />
              <h4 className="f-37 c-fff">Instantly Get Matched To Top Quality Talent</h4>
              <p className="f-22 c-fff mb-1">
                instantly connects you with high-caliber talent that aligns with your requirements.
              </p>
            </div>
          </div>

          <div className="text-center mt-5">
            <Button
              className="btn-a primary-size-22 btn-bg-fff"
              onClick={() => handleApplyJob('employer', '/employer/candidates')}
              style={{height: 'auto'}}>
              Try Now
            </Button>
          </div>
        </div>
      </section>

      <section className="pricing-part ">
        <div className="container">
          <h2>
            Hire Smarter With Our <br />
            <span className="flexible_plan"> Flexible Pricing Plans</span>
          </h2>
          <h3 className="choose-plan">Choose a plan that’s right for you</h3>

          <div className="row mt-5">
            <div className="col-lg-4 col-md-6">
              <div className="plan-box set-height">
                <h4>Free</h4>
                <p>Ideal for small businesses or startups with limited hiring needs and a tight budget.</p>

                <h3>
                  <small>USD</small> 0 <sup>/ Month</sup>
                </h3>

                {user?.id ? (
                  user?.role == 'employer' ? (
                    user?.plan == 1 || user?.membership === true ? (
                      <Button
                        className="started-part mt-3 mb-3"
                        onClick={(e: any) => handleClickSubmitPament(e, '0', '1')}
                        style={{height: 'auto'}}>
                        Get Started Now
                      </Button>
                    ) : (
                      <Button
                        className="started-part mt-3 mb-3"
                        onClick={(e: any) => handleError(e, 'plan')}
                        style={{height: 'auto'}}>
                        Get Started Now
                      </Button>
                    )
                  ) : (
                    <Button
                      className="started-part mt-3 mb-3"
                      onClick={(e: any) => handleError(e, 'role')}
                      style={{height: 'auto'}}>
                      Get Started Now
                    </Button>
                  )
                ) : (
                  <Link href="/auth/login">
                    <Button className="started-part mt-3 mb-3" style={{height: 'auto'}}>
                      Get Started Now
                    </Button>
                  </Link>
                )}
                <ul className="check-close set-check-close">
                  <li>
                    <i className="fa-solid fa-check set-check"></i> Valid for 1 Day.
                  </li>
                  <li>
                    <i className="fa-solid fa-check set-check"></i> 30 CV Search.
                  </li>
                  <li>
                    <i className="fa-solid fa-check set-check"></i> 30 CV Views.
                  </li>
                  <li>
                    <i className="fa-solid fa-check set-check"></i> 30 CV Downdload.
                  </li>
                  <li className="dis">
                    <i className="fa-solid fa-xmark set-xmark"></i> Unlimited Cv Search.
                  </li>
                  <li className="dis">
                    <i className="fa-solid fa-xmark set-xmark"></i> Unlimited Cv Downloading and Emailing Per Month.
                  </li>
                  <li className="dis">
                    <i className="fa-solid fa-xmark set-xmark"></i> Free unlimited User access.
                  </li>
                  <li className="dis">
                    <i className="fa-solid fa-xmark set-xmark"></i> No Job Posting.
                  </li>
                  <li className="dis">
                    <i className="fa-solid fa-xmark set-xmark"></i> Email Notification of Matching CV.
                  </li>
                  <li className="dis">
                    <i className="fa-solid fa-xmark set-xmark"></i> Dedicated Relationship manager.
                  </li>
                  <li className="dis">
                    <i className="fa-solid fa-xmark set-xmark"></i> Mass Messaging to selected candidates.
                  </li>
                  <li className="dis">
                    <i className="fa-solid fa-xmark set-xmark"></i> Unlimited Job Share on Linkedin
                  </li>
                </ul>
              </div>
            </div>

            <div className="col-lg-4 col-md-6">
              <div className="plan-box blue-plan set-height">
                <h4>12 Month</h4>
                <p>Ideal for businesses with specific hiring needs and advanced search features.</p>

                <h3>
                  <small>USD</small> 2500 <sup>/12 Month</sup>
                </h3>

                {user?.id ? (
                  user?.role == 'employer' ? (
                    user?.plan != 1 || user?.membership !== true ? (
                      <Button
                        className="started-part mt-3 mb-3"
                        onClick={(e: any) => handleClickSubmitPament(e, '2500', '2')}
                        style={{height: 'auto'}}>
                        Get Started Now
                      </Button>
                    ) : (
                      <Button
                        className=" mt-3 mb-3"
                        onClick={(e: any) => handleError(e, 'plan')}
                        style={{height: 'auto'}}>
                        Get Started Now
                      </Button>
                    )
                  ) : (
                    <Button
                      className="started-part mt-3 mb-3"
                      onClick={(e: any) => handleError(e, 'role')}
                      style={{height: 'auto'}}>
                      Get Started Now
                    </Button>
                  )
                ) : (
                  <Link href="/auth/login">
                    <Button className="started-part mt-3 mb-3" style={{height: 'auto'}}>
                      Get Started Now
                    </Button>
                  </Link>
                )}

                <ul className="check-close set-check-close">
                  <li>
                    <i className="fa-solid fa-check set-check"></i> Unlimited Cv Search.
                  </li>
                  <li>
                    <i className="fa-solid fa-check set-check"></i> Unlimited Cv Downloading and Emailing Per Month.
                  </li>
                  <li>
                    <i className="fa-solid fa-check set-check"></i> 25 Job Posting.
                  </li>
                  <li>
                    <i className="fa-solid fa-check set-check"></i> Free unlimited User access.
                  </li>
                  <li>
                    <i className="fa-solid fa-check set-check"></i> Email Notification of Matching CV.
                  </li>
                  <li>
                    <i className="fa-solid fa-check set-check"></i> Dedicated Relationship manager.
                  </li>
                  <li>
                    <i className="fa-solid fa-check set-check"></i> Mass Messaging to selected candidates.
                  </li>
                  <li>
                    <i className="fa-solid fa-check set-check"></i> Unlimited Job Share on Linkedin.
                  </li>
                  <li className="dis">
                    <i className="fa-solid fa-xmark set-xmark"></i>35 Job Posting.
                  </li>
                </ul>
              </div>
            </div>

            <div className="col-lg-4 col-md-6">
              <div className="plan-box set-height">
                <h4>18 Months</h4>

                <p>Ideal for large organizations with extensive hiring needs and dedicated support.</p>

                <h3>
                  <small>USD</small> 3500 <sup>/18 Month</sup>
                </h3>

                {user?.id ? (
                  user?.role == 'employer' ? (
                    user?.plan == 1 || user?.membership == true ? (
                      <Button
                        className="started-part mt-3 mb-3"
                        onClick={(e: any) => handleClickSubmitPament(e, '3500', '3')}
                        style={{height: 'auto'}}>
                        Get Started Now
                      </Button>
                    ) : (
                      <Button
                        className="started-part mt-3 mb-3"
                        onClick={(e: any) => handleError(e, 'plan')}
                        style={{height: 'auto'}}>
                        Get Started Now
                      </Button>
                    )
                  ) : (
                    <Button
                      className="started-part mt-3 mb-3"
                      onClick={(e: any) => handleError(e, 'role')}
                      style={{height: 'auto'}}>
                      Get Started Now
                    </Button>
                  )
                ) : (
                  <Link href="/auth/login">
                    <Button className="started-part mt-3 mb-3" style={{height: 'auto'}}>
                      Get Started Now
                    </Button>
                  </Link>
                )}
                <ul className="check-close set-check-close">
                  <li>
                    <i className="fa-solid fa-check set-check"></i> Unlimited Cv Search.
                  </li>
                  <li>
                    <i className="fa-solid fa-check set-check"></i> Unlimited Cv Downloading and Emailing Per Month.
                  </li>
                  <li>
                    <i className="fa-solid fa-check set-check"></i> 35 Job Posting.
                  </li>
                  <li>
                    <i className="fa-solid fa-check set-check"></i> Free unlimited User access.
                  </li>
                  <li>
                    <i className="fa-solid fa-check set-check"></i> Email Notification of Matching CV.
                  </li>
                  <li>
                    <i className="fa-solid fa-check set-check"></i> Dedicated Relationship manager.
                  </li>
                  <li>
                    <i className="fa-solid fa-check set-check"></i> Mass Messaging to selected candidates.
                  </li>
                  <li>
                    <i className="fa-solid fa-check set-check"></i> Unlimited Job Share on Linkedin.
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>
      <SubscriptionForm />
    </>
  );
}
