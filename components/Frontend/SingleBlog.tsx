import React, {useContext, lazy, Suspense} from 'react';
import Link from 'next/link';
import {notification} from 'antd';
import {Blogs} from '@/lib/types';
import AuthContext from '@/Context/AuthContext';
import Head from 'next/head';
import {useRouter} from 'next/router';
import moment from 'moment';
import Image from 'next/image';
import ListingSection from '../Blog/ListingSection';
import BlogCardSection from '../Blog/BlogCardSection';

interface BlogPostProps {
  blogPost: Blogs;
  blog: Blogs[];
}
function BlogPost({blogPost, blog}: BlogPostProps) {
  const {user} = useContext(AuthContext);
  const router = useRouter();
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };

    return date.toLocaleDateString(undefined, options);
  };

  const handleClickShowErrorMessage = (e: any, message: string) => {
    e.preventDefault();
    notification.error({message: message});
  };
  let blogAuthorAvatar = process.env.NEXT_PUBLIC_BASE_URL + 'images/placeholder.jpg';
  let blogImage = process.env.NEXT_PUBLIC_IMAGE_URL + 'images/placeholder.jpg';
  if (blogPost?.author?.profile_image) {
    blogAuthorAvatar = process.env.NEXT_PUBLIC_IMAGE_URL + 'images/blogs/author/' + blogPost.author?.profile_image;
  }
  if (blogPost?.image) {
    blogImage = `${process.env.NEXT_PUBLIC_IMAGE_URL}images/blogs/${blogPost.image}`;
  }

  return (
    <>
      <Head>
        <title>{blogPost.name ? blogPost.name : ''}</title>
        <meta name="description" content={blogPost.meta_desc ? blogPost.meta_desc : ''} />
        {/* Open Graph tags */}
        <meta property="og:title" content={blogPost.name ? blogPost.name : ''} />
        <meta property="og:description" content={blogPost.meta_desc ? blogPost.meta_desc : ''} />
        <meta property="og:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.png`} />
        <meta property="og:url" content={new URL(router.asPath, process.env.NEXT_PUBLIC_BASE_URL).href} />
        <meta property="og:type" content="website" />

        {/* Twitter card */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:site" content="@theTalentPoint" />
        <meta name="twitter:title" content={blogPost.name ? blogPost.name : ''} />
        <meta name="twitter:description" content={blogPost.meta_desc ? blogPost.meta_desc : ''} />

        <meta property="twitter:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.webp`} />
      </Head>
      <div className="blog-top">
        <div className="container">
          <nav className={'breadcrumb'}>
            <ol vocab="https://schema.org/" typeof="BreadcrumbList">
              <li property="itemListElement" typeof="ListItem">
                <Link prefetch={false} property="item" typeof="WebPage" href="/blog">
                  <span property="name">Insights</span>
                </Link>
                <meta property="position" content="1" />
              </li>
              <li property="itemListElement" typeof="ListItem">
                <span property="name">{blogPost.name}</span>
                <meta property="position" content="2" />
              </li>
            </ol>
          </nav>

          <div className="blog-title">
            <h1 role="heading" aria-level={1}>
              {blogPost?.name}
            </h1>
          </div>
          <div className={'blog-author-card'}>
            <img height={48} width={48} src={blogAuthorAvatar} alt={blogPost?.author?.profile_name || 'Author'} />
            <div>
              <Link prefetch={false} href={`/author/${blogPost?.author?.slug}`}>
                <p className="name">{blogPost?.author?.name}</p>
              </Link>
              <small>
                {moment(formatDate(blogPost?.created_at || '')).format('MMMM D, YYYY')} ·{' '}
                {blogPost?.description ? Math.round(blogPost?.description?.split(' ').length / 200) : '0 '}
                min read
              </small>
            </div>
          </div>
        </div>
      </div>
      <div className="blog-main-outer">
        <ListingSection user={user} blogPost={blogPost} handleClickShowErrorMessage={handleClickShowErrorMessage} />
        <div className="blog-details">
          <BlogCardSection blog={blog} />
        </div>
      </div>
    </>
  );
}
export default BlogPost;
