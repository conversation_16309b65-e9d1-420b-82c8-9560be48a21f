import React, { useState, useEffect, useContext } from 'react';
import Link from 'next/link';
import { useSession, signIn } from 'next-auth/react';
import { Button, Checkbox, Form, Input, notification } from 'antd';
import { removeToken, removeStorageData } from '@/lib/session';
import AuthContext from '@/Context/AuthContext';
import { useRouter } from 'next/router';
import { getCsrfToken } from 'next-auth/react';
import <PERSON>rrorHandler from '@/lib/ErrorHandler';
import Image from 'next/image';
import axios from 'axios';
import Cookies from 'js-cookie';

const ROLE_ROUTE: Record<string, string> = {
  superadmin: '/admin/dashboard',
  admin:      '/admin/dashboard',
  employee:   '/employees/dashboard',
  employer:   '/employer/dashboard',
  staff:      '/staff/dashboard',
};

interface LoginProps {
  sessionToken?: string;
}

export default function Login({ sessionToken }: LoginProps) {
  const { user } = useContext(AuthContext);
  // console.log("loginuser", sessionToken);
  const { data: session }: any = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const { login } = useContext(AuthContext);
  const [hasSession, setHasSession] = useState<boolean>();

  const { registerSessionToken, refreshSignUpSession, setUser } = useContext(AuthContext);

  useEffect(() => {
    if (sessionToken) {
      registerSessionToken(sessionToken);
      refreshSignUpSession();
    }
  }, [sessionToken]);

  useEffect(() => {
    if (user?.contact_no || user?.role) {
      const signupMethod = sessionStorage.getItem('signup.method');
      sessionStorage.setItem('signup.role', String(user?.role));
      if (user) {
        if (!user.is2FA) {
          if (user?.role) {
            router.push(ROLE_ROUTE[user.role] ?? '/');
          } else {
            router.push('/auth/signup');
          }
        } else {
          if (signupMethod && (signupMethod === 'linkedin' || signupMethod === 'google')) {
            if (user?.role) {
              router.push(ROLE_ROUTE[user.role] ?? '/');
            } else {
              router.push('/auth/signup');
            }
          }
        }
      }
    }
    if (user?.role === null) {
      router.push('/auth/signup');
    }
  }, [user]);

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.size > 0) {
      const params_val: any = urlParams.get('social_session_data');
      if (params_val == 1) {
        notification.error({ message: 'Your account deleted from admin. please contact from administrator' });
      }
    }
    removeToken();
    removeStorageData();
  }, [session]);

  const submitForm = (data: any) => {
    setLoading(true);
    login(data.email, data.password) //added parameter for 2FA check
      // login(data.email, data.password)
      .then(res => {
        setLoading(false);
        if (res.user.is2FA == 1 && res.otp != null) {
          setUser(res?.user);
          console.log(res?.user?.id, "user id inside login api");
          Cookies.set("user_id", res?.user?.id);
          Cookies.set("user_role", res?.user?.role);
          router.push("/auth/otp").then();
        } else {
          if (user?.role == 'superadmin') {
            router.push('/admin/dashboard');
          } else if (user?.role == 'admin') {
            router.push('/admin/dashboard');
            // window.location.href = "/admin/dashboard"
          } else if (user?.role == 'employee') {
            router.push('/employees/dashboard');
          } else if (user?.role == 'employer') {
            router.push('/employer/dashboard');
          } else if (user?.role == 'staff') {
            router.push('/staff/dashboard');
          } else {
            router.push('/');
          }
          // router.push("/").then();
        }
        // router.push('/').then();
      })
      .catch(err => {
        setLoading(false);
        ErrorHandler.showNotification(err);
      });
  };

  return (
    <>
      <section className="banner-part-home">
        <div className="container-fluid">
          <div className="row">
            <div className="col-lg-5 col-md-12 tab-none pl-0 back-img-form" />
            <div className="col-lg-7 col-md-12">
              <div className="form-pages top-m-sp same-pad-up-down">
                <h2>Welcome Back!</h2>
                <h5 className="w-500">Let’s get you connected.</h5>
                <Form
                  className="form-get mt-4"
                  onFinish={submitForm}
                  size={'large'}
                  layout={'vertical'}
                  requiredMark={false}>
                  <Form.Item name={'email'} label={'Email ID'} rules={[{ type: 'email' }, { required: true }]}>
                    <Input placeholder={'Email'} size="large" />
                  </Form.Item>
                  <Form.Item name={'password'} label={'Password'} rules={[{ required: true }]}>
                    <Input.Password placeholder={'Password'} size="large" />
                  </Form.Item>
                  <p className="text-right">
                    <Link href="/auth/forgotpassword">Forgot Password?</Link>
                  </p>
                  <Form.Item name={'remember'}>
                    <Checkbox>Remember me</Checkbox>
                  </Form.Item>
                  <Button loading={loading} type={'primary'} htmlType={'submit'} block>
                    Log In
                  </Button>
                </Form>
                <div className="login_footer_section">
                  <div className="or">
                    <p>or</p>
                  </div>
                  <button className="google-g w-100 mb-4"
                    // onClick={() => signIn('google', { callbackUrl: '/join' })}
                    onClick={() => {
                      sessionStorage.setItem('signup.method', 'google')
                      signIn('google')
                    }}
                  >
                    <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/google-logo.png'} alt="google-logo" /> Continue
                    with Google
                  </button>
                  <button
                    className="linkedin-button w-100 mb-4"
                    onClick={() => {
                      sessionStorage.setItem('signup.method', 'linkedin')
                      signIn('linkedin', { callbackUrl: '/auth/login' })
                    }}>
                    <i className="fa-brands fa-linkedin" aria-label="LinkedIn"></i>
                    <span> Continue with LinkedIn</span>
                  </button>
                  <center>
                    <p className="f-12-747474">
                      New to The Talent Point?{' '}
                      <Link href="/auth/signup/[[...slug]]" className="w-600">
                        Create An Account
                      </Link>
                    </p>
                  </center>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}


