import React, { useContext } from 'react';
import { OtpMatch } from '../../lib/frontendapi';
import { useRouter } from 'next/router';
import AuthContext from '@/Context/AuthContext';
import { Form, Input, Button, notification } from 'antd';
import Cookies from 'js-cookie';
export default function TwofactorAuthenticate() {
  const router = useRouter();
  const { user, refreshUserData } = useContext(AuthContext);
  console.log(user, "this is user id");

  const onFinish = async (values: any) => {
    try {
      const UserID = Cookies.get("user_id");
      console.log(Cookies.get("user_id"), "cookie inside")
      const res = await OtpMatch({ user_id: UserID, otp: values.otp });

      if (res.status === true) {
        console.log("inside then")
        notification.success({ message: 'OTP matched successfully' });

        // Wait for user data to be refreshed
        await refreshUserData();

        const userRole = user?.role || Cookies.get("user_role")

        // Now check the updated user role
        if (userRole == 'superadmin' || userRole == 'admin') {
          window.location.assign('/admin/dashboard');
        } else if (userRole == 'employee') {
          window.location.assign('/employees/dashboard');
        } else if (userRole == 'employer') {
          window.location.assign('/employer/dashboard');
        } else if (userRole == 'staff') {
          window.location.assign('/staff/dashboard');
        } else {
          window.location.assign('/');
        }
      } else {
        notification.error({ message: 'OTP Not Matched' });
      }
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.message) {
        notification.error({ message: error.response.data.message });
      } else {
        notification.error({ message: 'OTP verification failed' });
      }
      console.error(error);
    }
  };

  return (
    <>
      <section className="banner-part-home">
        <div className="container-fluid">
          <div className="row">
            <div className="col-lg-5 col-md-12 tab-none pl-0 back-img-form h-90vh"></div>
            <div className="col-lg-7 col-md-12">
              <div className="form-pages top-m-sp">
                <h2>Two Factor Authenticates</h2>
                <h5>Mitigating Password Vulnerabilities.</h5>
                <Form className="form-get mt-4" onFinish={onFinish}>
                  <Form.Item
                    name="otp"
                    rules={[
                      { required: true, message: 'Please enter OTP' },
                      { pattern: /^[0-9]{4}$/, message: 'Enter a valid 4-digit OTP' },
                    ]}>
                    <Input type="number" className="" placeholder="Enter OTP" size="large" />
                  </Form.Item>

                  <Form.Item>
                    <Button
                      type="primary"
                      htmlType="submit"
                      className="btn-a primary-size-16 b-0 btn-bg-0055BA w-100"
                      style={{ height: 'auto' }}>
                      Submit
                    </Button>
                  </Form.Item>
                </Form>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
