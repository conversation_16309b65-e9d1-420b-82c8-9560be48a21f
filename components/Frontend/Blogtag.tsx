import React, { useState, useEffect } from 'react';
import Pagination from "../../components/Common/NewPagination";
import { paginate } from "../../helpers/paginate";
import Link from 'next/link';
import Image from 'next/image';
interface Blog {
    description: string;
    image: string;
    author_name: string;
    name: string;
    created_at: string;
    slug: string;
    tag: string;
}

const BlogTagPage = (props: any) => {
    console.log(props);

    const [blogs, setBlogs] = useState<Blog[]>([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [filteredBlogs, setFilteredBlogs] = useState<Blog[]>([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [, setTotalBlog] = useState<Blog[]>([]);
    const pageSize = 3;
    const [blogtag, setBlogTag] = useState('');




    useEffect(() => {
        if (props) {
            setBlogs(props.blogtag);
            setTotalBlog(props.blogtag);
            filterBlogs(props.blogtag, searchQuery);
            setBlogTag(props.blogtag[0].tag)

        }

    }, []);

    const filterBlogs = (blogs: Blog[], searchValue: string) => {
        const filteredBlogs = blogs.filter((blog: Blog) =>
            blog.name.toLowerCase().includes(searchValue.toLowerCase())
        );
        setFilteredBlogs(filteredBlogs);
        setCurrentPage(1); // Reset to the first page when applying a filter
    };

    const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
        const searchValue = e.target.value.toLowerCase();
        setSearchQuery(searchValue);
        filterBlogs(blogs, searchValue);
    };

    const onPageChange = (page: number) => {
        setCurrentPage(page);
    };

    const paginatedBlogs = paginate(filteredBlogs, currentPage, pageSize);


    const removeTags = (str: any) => {
        if (!str) {
            return '';
        }
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = str;
        return tempDiv.innerText;
    };
    return (
        <div>
            <section className='blog-section'>
                <div className='container text-center'>
                    <p className='f-16 w-600 c-2E9DFF'>Our Blog</p>
                    <h3 className='f-48 w-700 c-fff'>Insights &amp; Resources</h3>
                    <p className='c-fff f-24 w-700'>Learn about the latest visa updates &amp; guidelines around the world.</p>
                    <div className='row'>
                        <div className='col-sm-2'></div>
                        <div className='col-sm-8 text-center'>
                            <div className='search-part'>
                                <div className='row'>
                                    <div className='col-lg-12 col-md-9 col-12'>
                                        <div className='search-icon'>
                                            <input type='text' placeholder='Search blog here' className='search-input' value={searchQuery}
                                                onChange={handleSearch}
                                            />
                                            <i className='fas fa-search'></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <div className='container mt-5'>
                <h5 className='c-0C1622 f-32 w-600 mb-4'>{props.tag}</h5>
                <div className='col-sm-12'>
                    <div className='row'>
                        {paginatedBlogs.length > 0 ? (
                            paginatedBlogs.map((blog: any, index: any) => (
                                <div className='col-sm-4' key={index}>
                                    <div className='row blog-part'>
                                        <div className='col-sm-12'>
                                            {blog?.image ? (
                                                <img src={`${process.env.NEXT_PUBLIC_IMAGE_URL}images/blogs/${blog.image}`} alt={`${blog.name} - TalentPoint`} />
                                            ) : (
                                                <img src={process.env.NEXT_PUBLIC_IMAGE_URL + "images/placeholder.jpg"} alt={`${blog.name} - TalentPoint`} />
                                            )}
                                        </div>
                                        <div className='col-sm-12 mt-3'>
                                            <Link prefetch={false} href={`/blog/${blog.slug}`}>
                                                <p className='f-22 c-090914 w-700 mb-2'>{blog.name.length > 35 ? blog.name.slice(0, 35) + '...' : blog.name}</p>
                                            </Link>
                                            <p className='f-16 w-400 c-666666'>{removeTags(blog.description.length > 70 ? blog.description.slice(0, 70) + '...' : blog.description)}</p>

                                            <p className='mt-4 f-18 w-600'>
                                                <Link prefetch={false} href={`/blog/${blog.slug}`} className='c-04B477 decoration-line'>
                                                    Read Now <i className="fa-solid fa-arrow-up rotate-icon"></i>
                                                </Link>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            ))
                        ) : (
                            <p>No blogs found.</p>
                        )}
                    </div>
                </div>
                <div className="pagination-wrapper">
                    <div className="pagination-wrapper">
                        <Pagination
                            items={filteredBlogs}
                            currentPage={currentPage}
                            pageSize={pageSize}
                            onPageChange={onPageChange}
                            activePage={currentPage} // Add the activePage property here
                        />
                    </div>

                </div>
            </div>
        </div>
    )
};

export default React.memo(BlogTagPage);
