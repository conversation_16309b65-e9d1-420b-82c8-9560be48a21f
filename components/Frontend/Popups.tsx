import React, { useState } from 'react';
import PopupModal from '../../components/Common/PopupModal';
import Image from 'next/image';

export default function Popups() {
  const [modalConfirm1, setModalConfirm1] = useState(false);
  const [modalConfirm2, setModalConfirm2] = useState(false);
  const [modalConfirm3, setModalConfirm3] = useState(false);
  const [modalConfirm4, setModalConfirm4] = useState(false);
  const [modalConfirm5, setModalConfirm5] = useState(false);
  const [modalConfirm6, setModalConfirm6] = useState(false);
  const [modalConfirm7, setModalConfirm7] = useState(false);
  const [modalConfirm8, setModalConfirm8] = useState(false);
  const [modalConfirm9, setModalConfirm9] = useState(false);
  const [modalConfirm10, setModalConfirm10] = useState(false);
  const [modalConfirm11, setModalConfirm11] = useState(false);
  const [modalConfirm12, setModalConfirm12] = useState(false);
  const modalConfirmOpen1 = () => {
    setModalConfirm1(true);
  }
  const modalConfirmClose1 = () => {
    setModalConfirm1(false);
  }

  const modalConfirmOpen2 = () => {
    setModalConfirm2(true);
  }
  const modalConfirmClose2 = () => {
    setModalConfirm2(false);
  }


  const modalConfirmOpen3 = () => {
    setModalConfirm3(true);
  }
  const modalConfirmClose3 = () => {
    setModalConfirm3(false);
  }

  const modalConfirmOpen4 = () => {
    setModalConfirm4(true);
  }
  const modalConfirmClose4 = () => {
    setModalConfirm4(false);
  }

  const modalConfirmOpen5 = () => {
    setModalConfirm5(true);
  }
  const modalConfirmClose5 = () => {
    setModalConfirm5(false);
  }


  const modalConfirmOpen6 = () => {
    setModalConfirm6(true);
  }
  const modalConfirmClose6 = () => {
    setModalConfirm6(false);
  }


  const modalConfirmOpen7 = () => {
    setModalConfirm7(true);
  }
  const modalConfirmClose7 = () => {
    setModalConfirm7(false);
  }

  const modalConfirmOpen8 = () => {
    setModalConfirm8(true);
  }
  const modalConfirmClose8 = () => {
    setModalConfirm8(false);
  }


  const modalConfirmOpen9 = () => {
    setModalConfirm9(true);
  }
  const modalConfirmClose9 = () => {
    setModalConfirm9(false);
  }

  const modalConfirmOpen10 = () => {
    setModalConfirm10(true);
  }
  const modalConfirmClose10 = () => {
    setModalConfirm10(false);
  }

  const modalConfirmOpen11 = () => {
    setModalConfirm11(true);
  }
  const modalConfirmClose11 = () => {
    setModalConfirm11(false);
  }

  const modalConfirmOpen12 = () => {
    setModalConfirm12(true);
  }
  const modalConfirmClose12 = () => {
    setModalConfirm12(false);
  }


  return (
    <>
      <section className='banner-part-home sp-80'>
        <div className='container'>


          <button className="btn-a primary-size-18 btn-bg-0055BA" onClick={modalConfirmOpen1}>Modal 1</button>
          <PopupModal show={modalConfirm1} handleClose={modalConfirmClose1} customclass={'modal-lg big-size header-remove body-sp-0'}>
            <div className='popup-modal'>
              <div className='row'>
                <div className='col-sm-4 popup-right border-left'></div>
                <div className='col-sm-8'>
                  <div className="text-right">
                    <button type="button" className="close-x mt-2" data-bs-dismiss="modal" aria-label="Close">
                      <i className="fa-solid fa-xmark"></i>
                    </button>
                  </div>

                  <div className="popup-left-text sp-80 text-center">
                    <h3>Welcome  <span className="span-color"> to The Talent Point! </span></h3>
                    <p className='f-22 c-2C2C2C'>The gateway to finding your dream job</p>
                    <p className='f-18 c-4D4D4D'>Complete your profile to level up your job search and unlock the true potential of our platform.</p>
                    <button className="btn login  mt-4" type="submit">Get Started</button>
                  </div>
                </div>
              </div>
            </div>
          </PopupModal><br /><br />

          <button className="btn-a primary-size-18 btn-bg-0055BA" onClick={modalConfirmOpen2}>Modal 2</button>
          <PopupModal show={modalConfirm2} handleClose={modalConfirmClose2} customclass={'modal-lg  header-remove body-sp-0 '}>
            <div className='head-box'>
              <div className='row'>
                <div className='col-sm-10'>
                  <p className='f-26 mb-2 mt-2'> Apply to Company Name</p>
                  <p className='f-16'> Enter your basic information & get apply.</p>
                </div>
                <div className='col-sm-2 text-right'>
                  <button type="button" className="close-b-des close-x  bg-0055BA border-design" data-bs-dismiss="modal" aria-label="Close">
                    <i className="fa-solid fa-xmark"></i>
                  </button>
                </div>
              </div>

            </div>
            <div className='popup-body'>
              <div className='row '>
                <div className='col-sm-7 text-right'>
                  <p className='f-22 mt-2'>Upload your recent Resume/CV:</p>
                </div>
                <div className='col-sm-5'>
                  <div className="uploade-btn">
                    <input type="file" />
                    <button className="download ">
                      <i className="fa-solid fa-upload"></i> Upload Resume
                    </button>
                  </div>
                </div>
              </div>

              <form className='form-experience-fieild'>
                <label>Your Name*</label>
                <input type='text' placeholder='Alan Moore' className='fild-des' />

                <div className='row'>
                  <div className='col-sm-6'>
                    <label>Email ID*</label>
                    <input type='text' placeholder='<EMAIL>' className='fild-des' />
                  </div>
                  <div className='col-sm-6'>
                    <label>Contact Number*</label>
                    <input type='text' placeholder='(+971) 123 – 456 – 7890' className='fild-des' />
                  </div>
                </div>

                <div className='row'>
                  <div className='col-sm-6'>
                    <label>Date of Birth*</label>
                    <input type='date' placeholder='<EMAIL>' className='fild-des' />
                  </div>
                  <div className='col-sm-6'>
                    <label>Gender</label>
                    <select className="fild-des">
                      <option>Male</option>
                      <option>Female</option>
                    </select>
                  </div>
                </div>

                <label>Where are you currently based?*</label>
                <input type='text' placeholder='Dubai' className='fild-des' />

                <label>Description*</label>
                <textarea placeholder='Your description goes here...' className='fild-des'></textarea>
                {/* <p className='font-12 text-right words'>250 words</p> */}

                <div className='text-right mt-3'>
                  <button className='cancel'>Cancel</button>
                  <button className='save'>Save</button>
                </div>

              </form>


            </div>

          </PopupModal><br /><br />


          <button className="btn-a primary-size-18 btn-bg-0055BA" onClick={modalConfirmOpen3}>Modal 3</button>
          <PopupModal show={modalConfirm3} handleClose={modalConfirmClose3} customclass={'modal-lg  header-remove body-sp-0 '}>
            <div className='popup-body'>
              <div className='row'>
                <div className='col-sm-10'>
                </div>
                <div className='col-sm-2 text-right'>
                  <button type="button" className="close-x  bg-0055BA border-design" data-bs-dismiss="modal" aria-label="Close">
                    <i className="fa-solid fa-xmark"></i>
                  </button>
                </div>
              </div>

              <div className='text-center sp-50'>
                <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/check-blue.png'} alt="check-blue" className='w-120' />
                <h2 className='f-31'>Application <span className='span-color'>Submitted</span></h2>
                <p className='f-18'>Go to <a href="#" className='c-0070F5'> My Applications</a> to view your application status.</p>
              </div>
            </div>

          </PopupModal><br /><br />



          <button className="btn-a primary-size-18 btn-bg-0055BA" onClick={modalConfirmOpen4}>Modal 4</button>
          <PopupModal show={modalConfirm4} handleClose={modalConfirmClose4} customclass={'modal-lg  header-remove body-sp-0 '}>
            <div className='popup-body'>
              <div className='row'>
                <div className='col-sm-6'>
                  <p className='f-22 c-0055BA'>Filter By</p>
                </div>
                <div className='col-sm-6 text-right'>
                  <button type="button" className="close-x bg-none" data-bs-dismiss="modal" aria-label="Close">
                    <i className="fa-solid fa-xmark"></i>
                  </button>
                </div>
              </div>

              <div className='row'>
                <div className='col-sm-6'>
                  <div className="form-part mt-4">
                    <input type="text" placeholder="Job Title" className="medium-input" />
                  </div>
                </div>
                <div className='col-sm-6'>
                  <div className="form-part mt-4">
                    <input type="text" placeholder="Location" className="medium-input" />
                  </div>
                </div>
              </div>

              <div className='row'>
                <div className='col-sm-6'>
                  <div className='salary-box h-2 mt-4'>
                    <p className='f-16 w-600 c-2C2C2C'>Salary</p>
                    <p className='f-12 c-2C2C2C'>AED 0 - AED10,000</p>
                    <input type="range" className="form-range w-75" id="customRange1" />
                    <select className="choose-currency mt-4">
                      <option>Choose Currency</option>
                      <option>Choose Currency 1</option>
                      <option>Choose Currency 2</option>
                    </select>
                  </div>
                </div>
                <div className='col-sm-6'>
                  <div className='salary-box h-2 mt-4'>
                    <p className='f-16 w-600 c-2C2C2C'>Experience</p>
                    <p className='f-12 c-2C2C2C'>Intermediate</p>
                    <input type="range" className="form-range w-75" id="customRange1" />
                  </div>
                </div>
              </div>

              <div className='row'>
                <div className='col-sm-6'>
                  <div className='salary-box mt-4'>
                    <p className='f-16 w-600 c-2C2C2C'>Skills</p>
                    <div className="form-in position-relative input-bababa ">
                      <i className="fa-solid fa-magnifying-glass  glass-search pad-sp"></i>
                      <input type="text" placeholder="Search Skills" className="medium-input left-sp mt-2 mb-2 c-999999" />
                    </div>
                    <p className='f-12 w-700 c-999999'>POPULAR </p>

                    <div className="form-master-field dflex ">
                      <input type="checkbox" placeholder="Placeholder" className="master-fields checkbox border-0 mb-0" />
                      <label className="check-label c-4D4D4D mb-0">Python</label>
                    </div>

                    <div className="form-master-field dflex ">
                      <input type="checkbox" placeholder="Placeholder" className="master-fields checkbox border-0 mb-0" />
                      <label className="check-label c-4D4D4D mb-0">Ruby on Rails</label>
                    </div>

                    <div className="form-master-field dflex ">
                      <input type="checkbox" placeholder="Placeholder" className="master-fields checkbox border-0 mb-0" />
                      <label className="check-label c-4D4D4D mb-0">React.js</label>
                    </div>

                    <div className="form-master-field dflex ">
                      <input type="checkbox" placeholder="Placeholder" className="master-fields checkbox border-0 mb-0" />
                      <label className="check-label c-4D4D4D mb-0">Node.js</label>
                    </div>

                  </div>
                </div>
                <div className='col-sm-6'>
                  <div className='salary-box mt-4'>
                    <p className='f-16 w-600 c-2C2C2C'>Sector</p>
                    <div className="form-in position-relative input-bababa ">
                      <i className="fa-solid fa-magnifying-glass  glass-search pad-sp"></i>
                      <input type="text" placeholder="Search Sector" className="medium-input left-sp mt-2 mb-2 c-999999" />
                    </div>
                    <p className='f-12 w-700 c-999999'>POPULAR</p>

                    <div className="form-master-field dflex ">
                      <input type="checkbox" placeholder="Placeholder" className="master-fields checkbox border-0 mb-0" />
                      <label className="check-label c-4D4D4D mb-0">Healthcare</label>
                    </div>

                    <div className="form-master-field dflex ">
                      <input type="checkbox" placeholder="Placeholder" className="master-fields checkbox border-0 mb-0" />
                      <label className="check-label c-4D4D4D mb-0">E-commerce</label>
                    </div>

                    <div className="form-master-field dflex ">
                      <input type="checkbox" placeholder="Placeholder" className="master-fields checkbox border-0 mb-0" />
                      <label className="check-label c-4D4D4D mb-0">Education</label>
                    </div>

                    <div className="form-master-field dflex ">
                      <input type="checkbox" placeholder="Placeholder" className="master-fields checkbox border-0 mb-0" />
                      <label className="check-label c-4D4D4D mb-0">Software</label>
                    </div>

                  </div>
                </div>

                <div className='col-sm-6'>
                  <div className='salary-box mt-4'>
                    <p className='f-16 w-600 c-2C2C2C'>Job Type</p>

                    <div className="form-master-field dflex ">
                      <input type="checkbox" placeholder="Placeholder" className="master-fields checkbox border-0 mb-0" />
                      <label className="check-label c-4D4D4D mb-0">Full-Time</label>
                    </div>

                    <div className="form-master-field dflex ">
                      <input type="checkbox" placeholder="Placeholder" className="master-fields checkbox border-0 mb-0" />
                      <label className="check-label c-4D4D4D mb-0">Part-time</label>
                    </div>

                    <div className="form-master-field dflex ">
                      <input type="checkbox" placeholder="Placeholder" className="master-fields checkbox border-0 mb-0" />
                      <label className="check-label c-4D4D4D mb-0">Education</label>
                    </div>

                    <div className="form-master-field dflex ">
                      <input type="checkbox" placeholder="Placeholder" className="master-fields checkbox border-0 mb-0" />
                      <label className="check-label c-4D4D4D mb-0">Software</label>
                    </div>

                  </div>
                </div>
              </div>

              <div className='text-center mt-5'>
                <button className="btn-a primary-size-16 btn-bg-0055BA">View Results</button>
              </div>

            </div>

          </PopupModal><br /><br />


          <button className="btn-a primary-size-18 btn-bg-0055BA" onClick={modalConfirmOpen5}>Modal 5</button>
          <PopupModal show={modalConfirm5} handleClose={modalConfirmClose5} customclass={'   header-remove body-sp-0 '}>
            <div className='popup-body'>
              <p className='f-12 c-2C2C2C'>Saved Search Name</p>
              <input type="text" placeholder="Placeholder" className="big-input mb-0"></input>
              <p className='f-12 text-right c-474D66'>50</p>
              <div className="text-right mt-3">
                <button className="cancel">Delete</button>
                <button className="save">Done</button>
              </div>
            </div>

          </PopupModal><br /><br />


          <button className="btn-a primary-size-18 btn-bg-0055BA" onClick={modalConfirmOpen6}>Modal 6</button>
          <PopupModal show={modalConfirm6} handleClose={modalConfirmClose6} customclass={'modal-lg big-size header-remove body-sp-0'}>
            <div className='popup-modal'>
              <div className='row'>
                <div className='col-sm-4 popup-right border-left popup-left-2'></div>
                <div className='col-sm-8'>
                  <div className="text-right">
                    <button type="button" className="close-x mt-2" data-bs-dismiss="modal" aria-label="Close">
                      <i className="fa-solid fa-xmark"></i>
                    </button>
                  </div>

                  <div className="popup-left-text sp-80 text-center">
                    <h3>Welcome  <span className="span-color"> to The Talent Point! </span></h3>
                    <p className='f-22 c-2C2C2C w-500'>The gateway to finding your dream job</p>
                    <p className='f-18 c-4D4D4D'>Complete your profile to level up your job search and unlock the true potential of our platform.</p>
                    <button className="btn login  mt-4" type="submit">Get Started</button>
                  </div>
                </div>
              </div>
            </div>
          </PopupModal><br /><br />


          <button className="btn-a primary-size-18 btn-bg-0055BA" onClick={modalConfirmOpen7}>Modal 7</button>
          <PopupModal show={modalConfirm7} handleClose={modalConfirmClose7} customclass={'   header-remove body-sp-0 '}>
            <div className='popup-body'>
              <h5 className='f-26 c-0055BA w-700 text-center mb-4'>Upload Your Profile Picture</h5>
              <div className='upload-file'>
                <div className='file-up'>
                  <input type="file" multiple />
                  <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/cemra.png'} alt="cemra" className='cemra' />
                </div>
                <p className='upload-text'>Browse and chose the files you want to upload from your computer.</p>
                <p className='max-size'>Maximum upload size is 1MB</p>
              </div>
              <div className="modal-footer">
                <button type="button" className="cancel-btn" data-bs-dismiss="modal">Cancel</button>
                <button type="button" className="update-btn">Update</button>
              </div>
            </div>

          </PopupModal><br /><br />

          <button className="btn-a primary-size-18 btn-bg-0055BA" onClick={modalConfirmOpen8}>Modal 8</button>
          <PopupModal show={modalConfirm8} handleClose={modalConfirmClose8} customclass={'modal-lg  header-remove body-sp-0 '}>
            <div className='head-box'>
              <div className='row'>
                <div className='col-sm-10'>
                  <p className='f-26 mb-2 mt-2'> Post a Job</p>
                  <p className='f-16'> Enter basic job details and get started right away.</p>
                </div>
                <div className='col-sm-2 text-right'>
                  <button type="button" className="close-b-des close-x  bg-0055BA border-design" data-bs-dismiss="modal" aria-label="Close">
                    <i className="fa-solid fa-xmark"></i>
                  </button>
                </div>
              </div>

            </div>
            <div className='popup-body'>
              <div className='switch-w'>
                <label className="switch switch-sp ">
                  <input type="checkbox" />
                  <span className="slider round"></span>
                </label>
                <p className='f-16 c-747474 pog-r'>Post as Featured Job (n remaining) </p>
              </div>

              <form className='form-experience-fieild mt-4'>
                <label>Job Title*</label>
                <input type='text' placeholder='Title' className='fild-des' />

                <label>Description*</label>
                <textarea placeholder='Your description goes here...' className='fild-des'></textarea>

                <div className='row'>
                  <div className='col-sm-6'>
                    <label>Type of Position*</label>
                    <input type='text' placeholder='Full-time' className='fild-des' />
                  </div>
                  <div className='col-sm-6'>
                    <label>Job Country*</label>
                    <input type='text' placeholder='United Arab Emirates' className='fild-des' />
                  </div>
                </div>


                <div className='text-right mt-3'>
                  <button className='cancel'>Cancel</button>
                  <button className='save'>Save</button>
                </div>

              </form>


            </div>
          </PopupModal><br /><br />

          <button className="btn-a primary-size-18 btn-bg-0055BA" onClick={modalConfirmOpen9}>Modal 9</button>
          <PopupModal show={modalConfirm9} handleClose={modalConfirmClose9} customclass={' modal-lg  header-remove body-sp-0 '}>
            <div className='popup-body'>
              <div className='row'>
                <div className='col-sm-6'>
                  <p className='f-22 c-0055BA'>Filter By</p>
                </div>
                <div className='col-sm-6 text-right'>
                  <button type="button" className="close-x bg-none" data-bs-dismiss="modal" aria-label="Close">
                    <i className="fa-solid fa-xmark"></i>
                  </button>
                </div>
              </div>

              <div className="form-part mt-4">
                <input type="text" placeholder="Job Title" className="medium-input" />
              </div>

              <div className='row  mt-3'>
                <div className='col-sm-3 mt-4 mobile-m-0'>
                  <p className='f-16 w-600 c-2C2C2C  '>Job Search Status:</p>
                </div>
                <div className='col-sm-9'>
                  <div className="form-part form-experience-fieild">
                    <select className="fild-des">
                      <option>Ready to Interview</option>
                      <option>Ready to Interview 2</option>
                      <option>Ready to Interview 3</option>
                    </select>
                  </div>
                </div>
              </div>

              <div className='row'>
                <div className='col-sm-6'>
                  <div className='salary-box h-2 mt-4'>
                    <p className='f-16 w-600 c-2C2C2C'>Salary</p>
                    <p className='f-12 c-2C2C2C'>AED 0 - AED10,000</p>
                    <input type="range" className="form-range w-75" id="customRange1" />
                    <select className="choose-currency mt-4">
                      <option>Choose Currency</option>
                      <option>Choose Currency 1</option>
                      <option>Choose Currency 2</option>
                    </select>
                  </div>
                </div>
                <div className='col-sm-6'>
                  <div className='salary-box h-2 mt-4'>
                    <p className='f-16 w-600 c-2C2C2C'>Experience Required</p>
                    <p className='f-12 c-2C2C2C'>Intermediate</p>
                    <input type="range" className="form-range w-75" id="customRange1" />
                  </div>
                </div>
              </div>

              <div className='row'>
                <div className='col-sm-6'>
                  <div className='salary-box mt-4'>
                    <p className='f-16 w-600 c-2C2C2C'>Location</p>
                    <div className="form-in position-relative input-bababa ">
                      <i className="fa-solid fa-magnifying-glass  glass-search pad-sp"></i>
                      <input type="text" placeholder="Search by Country" className="medium-input left-sp mt-2 mb-2 c-999999" />
                    </div>
                    <p className='f-12 w-700 c-999999'>POPULAR </p>

                    <div className="form-master-field dflex ">
                      <input type="checkbox" className="master-fields checkbox border-0 mb-0" />
                      <label className="check-label c-4D4D4D mb-0">United Arab Emirates </label>
                    </div>

                    <div className="form-master-field dflex ">
                      <input type="checkbox" className="master-fields checkbox border-0 mb-0" />
                      <label className="check-label c-4D4D4D mb-0">India</label>
                    </div>

                    <div className="form-master-field dflex ">
                      <input type="checkbox" className="master-fields checkbox border-0 mb-0" />
                      <label className="check-label c-4D4D4D mb-0">Saudi Arabia</label>
                    </div>

                    <div className="form-master-field dflex ">
                      <input type="checkbox" className="master-fields checkbox border-0 mb-0" />
                      <label className="check-label c-4D4D4D mb-0">Pakistan</label>
                    </div>

                  </div>
                </div>
                <div className='col-sm-6'>
                  <div className='salary-box mt-4'>

                    <div className="form-master-field dflex ">
                      <input type="checkbox" className="master-fields checkbox border-0 mb-0" />
                      <label className="check-label  mb-0 w-600 f-16 c-2C2C2C">Shortlisted</label>
                    </div>

                    <div className="form-master-field dflex ">
                      <input type="checkbox" className="master-fields checkbox border-0 mb-0" />
                      <label className="check-label  mb-0 w-600 f-16 c-2C2C2C">Selected</label>
                    </div>

                    <div className="form-master-field dflex ">
                      <input type="checkbox" className="master-fields checkbox border-0 mb-0" />
                      <label className="check-label  mb-0 w-600 f-16 c-2C2C2C">Declined</label>
                    </div>


                  </div>
                </div>
              </div>

              <div className='text-right mt-5'>
                <button className="btn-a primary-size-16 btn-bg-0055BA">View Results</button>
              </div>

            </div>
          </PopupModal><br /><br />

          <button className="btn-a primary-size-18 btn-bg-0055BA" onClick={modalConfirmOpen10}>Modal 10</button>
          <PopupModal show={modalConfirm10} handleClose={modalConfirmClose10} customclass={'   modal-lg  header-remove body-sp-0'}>
            <div className='popup-body'>
              <div className='row'>
                <div className='col-sm-8'>
                  <i className="fa-solid fa-arrow-left f-20 c-0055BA"></i>
                  <p className='f-37 w-700 c-2C2C2C mb-2'>Candidate Name</p>
                  <p className='f-22  c-0055BA'>Current Role @Company Name</p>
                </div>
                <div className='col-sm-4 text-right'>
                  <select className="choose-currency mt-4 w-100 mb-3">
                    <option>Shortlisted </option>
                    <option>Shortlisted 1</option>
                    <option>Shortlisted 2</option>
                  </select>
                  <button className="btn-a primary-size-16 btn-bg-0055BA w-100">Button</button>
                </div>
              </div>
              <hr className='hr-line' />

              <div className='tab-popup'>
                <ul className="nav nav-pills mb-3" id="pills-tab" role="tablist">
                  <li className="nav-item" role="presentation">
                    <button className="nav-link active" id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#pills-home" type="button" role="tab" aria-controls="pills-home" aria-selected="true">Application</button>
                  </li>
                  <li className="nav-item" role="presentation">
                    <button className="nav-link" id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-profile" type="button" role="tab" aria-controls="pills-profile" aria-selected="false">Interview</button>
                  </li>

                </ul>
                <div className="tab-content" id="pills-tabContent">
                  <div className="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab">
                    <div className='row'>
                      <div className='col-sm-8'>
                        <ul className='skills pop-skils'>
                          <li>
                            <p className='f-16 c-999999 w-600'><i className="fa-regular fa-envelope"></i> <EMAIL></p>
                          </li>
                          <li>
                            <p className='f-16 c-999999 w-600'><i className="fa-solid fa-phone"></i> +971 501234567</p>
                          </li>
                          <li>
                            <p className='f-16 c-999999 w-600'><i className="fa-solid fa-briefcase"></i> Ready to Interview</p>
                          </li>
                          <li>
                            <p className='f-16 c-999999 w-600'><i className="fa-solid fa-location-dot"></i> Dubai, United Arab Emirates</p>
                          </li>
                          <li>
                            <p className='f-16 c-999999 w-600'><i className="fa-regular fa-user"></i> Age: 32</p>
                          </li>
                          <li>
                            <p className='f-16 c-999999 w-600'><i className="fa-regular fa-circle-user"></i> Male</p>
                          </li>
                          <li>
                            <p className='f-16 c-999999 w-600'><i className="fa-solid fa-graduation-cap"></i> Bachelor of Technology</p>
                          </li>
                        </ul>
                      </div>
                    </div>

                    <div className='mt-4'>
                      <p className='f-16 c-000'>About</p>
                      <p className='f-18 c-4D4D4D w-400  mb-4'>B. Tech from Georgia Institute of Technology, Coding wizard, working on an AI tool to assist developers, previously worked at Meta.</p>

                      <p className='f-16 c-000'>Work Experience</p>
                      <p className='f-22 w-700 c-2C2C2C mt-2 mb-2'>Software Engineer</p>
                      <p className='f-18 c-0055BA mb-2'>Meta</p>
                      <p className='f-16 c-999999'>12/2020 - 02/2023</p>

                      <p className='f-18 c-4D4D4D w-400'>Lorem ipsum dolor sit amet consectetur. Quis elementum mattis odio orci eu pellentesque odio velit. Egestas neque ipsum aliquet auctor dis pulvinar habitant. Id consectetur suspendisse scelerisque mattis mattis blandit nisl dolor sit. Faucibus enim ornare amet sit arcu consequat. </p>

                      <p className='f-22 w-700 c-2C2C2C mt-2 mb-2'>Junior Software Engineer</p>
                      <p className='f-18 c-0055BA mb-2'>Twitter</p>

                    </div>

                  </div>
                  <div className="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab">
                    <p className='f-18 c-747474 w-400 time-mon-fri'>Based on your schedule you are available from <strong>Monday</strong> to <strong>Friday</strong> <strong>between 09:00AM - 03:00PM</strong></p>

                    <p><a href="#" className='f-16 w-700 c-0070F5'>Edit Availability</a></p>
                    <form className='form-experience-fieild mt-4'>
                      <div className='row'>
                        <div className='col-sm-6'>
                          <label>From Time*</label>
                          <input type='text' placeholder='9:00 AM' className='fild-des' />
                        </div>
                        <div className='col-sm-6'>
                          <label>To Time*</label>
                          <input type='text' placeholder='3:00 PM' className='fild-des' />
                        </div>
                      </div>
                      <label>Zoom Link*</label>
                      <input type='text' placeholder='linke.linke.zooooom' className='fild-des' />


                    </form>

                  </div>
                </div>
              </div>

            </div>
          </PopupModal><br /><br />

          <button className="btn-a primary-size-18 btn-bg-0055BA" onClick={modalConfirmOpen11}>Modal 11</button>
          <PopupModal show={modalConfirm11} handleClose={modalConfirmClose11} customclass={'    modal-lg  header-remove body-sp-0'}>
            <div className='popup-body'>
              <p className='f-31 c-191919 text-left'>Message Candidate Name</p>
              <hr className="hr-line"></hr>

              <div className='form-experience-fieild'>
                <p className="f-12 c-2C2C2C">Your Message</p>
                <textarea placeholder='Your Message' className='fild-des'></textarea>
                <div className="text-right mt-3">
                  <div className='row'>
                    <div className='col-4'>
                      <button className="cancel  w-100">Cancel</button>
                    </div>
                    <div className='col-8'>
                      <button className="save w-100">Send</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </PopupModal><br /><br />

          <button className="btn-a primary-size-18 btn-bg-0055BA" onClick={modalConfirmOpen12}>Modal 12</button>
          <PopupModal show={modalConfirm12} handleClose={modalConfirmClose12} customclass={'   modal-md-size  header-remove body-sp-0 '}>
            <div className='popup-body'>
              <div className="row">
                <div className="col-sm-12 text-right">
                  <button type="button" className="close-x bg-none close-pog" data-bs-dismiss="modal" aria-label="Close"><i className="fa-solid fa-xmark"></i></button>
                </div>
              </div>
              <p className='f-31 c-191919 text-left mb-2'>Availability</p>
              <p className='f-22'>Mark the days and time you are available to interview.</p>
              <hr className="hr-line"></hr>
              <p className='f-16'>You may choose multiple days at once by clicking on them and mark if you are available.</p>

              <div className='row mt-2'>
                <div className='col-sm-7'>
                  <input type="date" className='big-input  ' />
                </div>
                <div className='col-sm-5'>
                  <p className='f-18 c-0055BA w-600'>Available?</p>
                  <label className="switch switch-sp mt-2">
                    <input type="checkbox" checked /> <span className="slider round"></span>
                  </label>

                  <p className='f-18 c-0055BA w-600 mt-4 mb-2'>From Time</p>
                  <input type="time" className='big-input' />


                  <p className='f-18 c-0055BA w-600 mt-4'>To Time</p>
                  <input type="time" className='big-input' />

                </div>
              </div>

              <center className='mt-3'>
                <button className="btn-a primary-size-18 btn-bg-0055BA">Update</button>
              </center>


            </div>
          </PopupModal><br /><br />



        </div>
      </section>



    </>
  )
}
