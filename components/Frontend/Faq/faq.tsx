import Link from 'next/link';
import React, {useEffect} from 'react';
import {FAQCard} from '../FaqCard';

interface JobOffersFaqProps {
  submitSearchForm: any;
  countries: any;
  jobs: any;
  locationFaq: any;
  cityName: string;
  skillJobInCityName: string;
}

function check(name: string = '') {
  const newCityName = name.replace(/-/g, ' ');
  const newCityArr = newCityName.split(' ');
  for (let i = 0; i < newCityArr.length; i++) {
    newCityArr[i] = newCityArr[i].charAt(0).toUpperCase() + newCityArr[i].slice(1);
  }
  return newCityArr.join(' ');
}

export default function JobsOffersPageFaq({
  submitSearchForm,
  countries,
  jobs,
  locationFaq,
  cityName,
  skillJobInCityName,
}: JobOffersFaqProps) {
  return (
    <div>
      <section className="faq_sec">
        <div className="container">
          <div className="faq_hdng">
            <h3>{check(skillJobInCityName)} FAQ</h3>
            <p>Your Job Search Questions Answered</p>
          </div>
          <div className="faq_area">
            <div className="accordion" id="accordionExample">
              {locationFaq?.map((res: any, index: number) => {
                return <FAQCard key={index} index={index.toString()} question={res.question} answer={res.answer} />;
              })}
            </div>
          </div>
        </div>
      </section>
      {/* more about location */}
      <section className="more_about_job_sec">
        <div className="container">
          <div className="more_about_job_top">
            <h3>More About {check(skillJobInCityName)}</h3>
            {locationFaq && <div dangerouslySetInnerHTML={{__html: locationFaq[0]?.more_about_location}} />}
          </div>
          <div className="more_about_job_bottom">
            <div className="more_about_job_col">
              <div className="more_about_job_innr">
                <h3>Jobs in other Locations</h3>
                <ul>
                  {countries?.map((country: any) => (
                    <li key={country.id}>
                      <Link
                        prefetch={false}
                        href="javascript:void(0)"
                        onClick={() => {
                          submitSearchForm({keywords: undefined, country: [country.country_name]});
                          window.scrollTo({top: 0, behavior: 'smooth'});
                        }}>
                        {country.country_name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            <div className="more_about_job_col">
              <div className="more_about_job_innr">
                <h3>Companies Hiring in {cityName}</h3>
                <ul>
                  {jobs
                    ?.filter(
                      (job: any, index: any, self: any) =>
                        index === self.findIndex((j: any) => j.company.company_name === job.company.company_name),
                    )
                    .map((job: any) => (
                      <li key={job.id}>
                        <Link prefetch={false} target="_blank" href={'/companies/' + job.company?.company_slug}>
                          {job.company.company_name}
                        </Link>
                      </li>
                    ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
