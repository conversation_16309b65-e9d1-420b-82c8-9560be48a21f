import React, {useState, useEffect} from 'react';
import Link from 'next/link';
import {getCountries} from '@/lib/frontendapi';
import <PERSON>rror<PERSON>andler from '@/lib/ErrorHandler';
import {Button} from 'antd';
import Image from 'next/image';

function FindJobsByLocation() {
  const [getCountriesData, setGetCountries] = useState([]);
  const [countryCount, setCountryCount] = useState([]);

  useEffect(() => {
    getCountries()
      .then(res => {
        if (res.status == true) {
          setGetCountries(res.data);
          setCountryCount(res.jobs);
        } else {
          setGetCountries([]);
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  }, []);

  return (
    <section className="find-bylocation" id="find-by-location-section">
      <div className="container">
        <h3 className="title-heading" aria-level={3}>
          Find jobs by <span className="span-color-2"> location</span>
        </h3>
        <h5 aria-level={5}>
          Find your <span className="span-color-2">dream job,</span> near you.
        </h5>
        <div className="d-flex-box mt-5 mb-4">
          <div className="box-flg-w-20">
            {getCountriesData?.length > 0 &&
              getCountriesData.map((country: any, index: number) => {
                if (country.status == 'active') {
                  return (
                    <div className="flg-part" key={index}>
                      <div className="row align-items-center">
                        <div className="col-4 pr-0">
                          <img
                            className="w-100"
                            src={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/country/' + country.flag}
                            // src={'/images/Avatars-4.webp'}
                            priority
                            alt="2"
                            width={100}
                            height={80}
                          />
                        </div>
                        <div className="col-8">
                          <p>{country.country_name}</p>
                          <p className="font-12">
                            {country.id in countryCount ? countryCount[country.id] + ' Jobs' : 'Jobs'}
                            <i className="fa-solid fa-chevron-right"></i>
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                }
              })}
          </div>
        </div>

        <div className="text-center mt-5">
          <Link href="/jobs-in-gulf" passHref prefetch={false}>
            <Button className="btn-a border-primary-size-22 border-fff" style={{height: 'auto'}}>
              Explore Jobs
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}
export default FindJobsByLocation;

// export async function getServerSideProps() {
//   try {
//     const res = await getCountries();
//     if (res.status === true) {
//       return {
//         props: {
//           countriesData: res.data,
//           countryCount: res.jobs,
//         },
//       };
//     } else {
//       return {
//         props: {
//           countriesData: [],
//           countryCount: [],
//         },
//       };
//     }
//   } catch (error) {
//     return {
//       props: {
//         countriesData: [],
//         countryCount: [],
//       },
//     };
//   }
// }
