.home-hero-section {
  background: #000e1f;
  border-radius: 0px 0px 80px 80px;
}
.home-hero-bg {
  background: url('/images/home/<USER>');
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
}
.home-hero-container {
  padding: 169px 72px 220px 72px;
  display: flex;
  flex-direction: column;
  gap: 32px;
}
.hero-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  text-align: center;
}
.hero-content h4 {
  color: #fff;
  text-align: center;
  font-size: 46px;
  font-weight: 700;
  line-height: 120%;
}

.hero-content span {
  color: #0070f5;
}
.hero-content p {
  color: #f9f9f9;
  text-align: center;
  font-size: 26px;
  font-weight: 500;
  line-height: 120%;
}

.job-search-top {
  position: relative;
  margin: 0 auto;
  max-width: 956px !important;
  border-radius: 72px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  background: rgba(255, 255, 255, 0.24);
  padding: 12px;

  .blue-background {
    backdrop-filter: blur(6px);
    z-index: -1;
  }

  .form-container {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  .form-input-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    width: 100%;
    z-index: 1;
  }

  .form-input-container-city {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
  }

  .field-container {
    display: flex;
    align-items: center;
    padding: 0 5px 0 15px;
    overflow: hidden;
    border: 1px solid #b6c8e2;
    border-radius: 40px;
    background: #ffffff;
    height: 48px;
  }
  .job-search-cta {
    background-color: #fdca40;
    color: #151515;
    border-radius: 32px;
    height: 47px;
    display: flex;
    width: 140px;
    padding: 14px 24px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    color: #151515;
    font-family: Archivo;
    font-size: 16px;
    font-weight: 500;
    line-height: 120%;
  }
}
.banner-container {
  display: flex;
  flex-direction: column;
  border-radius: 16px;
  display: flex;
  padding: 40px 72px;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  align-self: stretch;
}

.banner-title {
  color: #fff;
  font-size: 31px;
  font-weight: 700;
  line-height: 120%;
}
.banner-text {
  color: #fff;
  font-size: 22px;
  font-weight: 500;
  line-height: 120%;
}
.emiratis-image-container {
  position: absolute;
  right: 0;
  top: -35px;
}
.card-section {
  display: flex;
  padding: 80px 0;
  gap: 56px;
}
.card_section_right {
  display: flex;
  flex-direction: column;
  gap: 24px;
  justify-content: space-between;
  width: fit-content;
}
.testimonial_section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 48px;
  background: #003e8a;
}

@media screen and (max-width: 768px) {
  .home-hero-section {
    padding-bottom: 110px;
  }
  .home-hero-bg {
    background: url('/images/home/<USER>');
    background-repeat: no-repeat;
    background-position: center;
  }
  .job-search-top {
    padding: 8px;
    border-radius: 10px;
  }
  .form-input-container {
    grid-template-columns: repeat(1, 1fr) !important;
  }
  .form-container {
    flex-direction: column;
  }
  .field-container {
    width: 100%;
    grid-column: span 1 / span 1 !important;
  }
  .job-search-cta {
    width: 100% !important;
  }
  .home-hero-container {
    padding: 40px 12px 216px 12px;
    gap: 40px;
  }
  .form-submit-container {
    width: 100%;
  }
  .hero-content h4 {
    font-size: 32px;
    font-weight: 700;
    line-height: 110%;
  }
  .hero-content p {
    font-size: 20px;
    font-weight: 500;
    line-height: 120%;
  }
  .banner-title {
    font-size: 28px;
  }
  .banner-text {
    font-size: 19px;
  }
  .banner-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 40px 12px 24px 12px;
  }
  .emiratis-image-container {
    display: none;
  }
  .banner-container {
    padding: 40px 12px;
  }
  .card-section {
    flex-direction: column;
    padding: 40px 12px;
  }
}
