import React, {useContext} from 'react';
import Link from 'next/link';
import dynamic from 'next/dynamic';
// import TopBlogs from '../Common/TopBlogs';
const TopBlogs = dynamic(() => import('../Common/TopBlogs'));

// import SubscriptionForm from '../Common/SubscriptionForm';
const SubscriptionForm = dynamic(() => import('../Common/SubscriptionForm'));

// import HiringTalent from '../Common/HiringTalent';
const HiringTalent = dynamic(() => import('../Common/HiringTalent'));

import AuthContext from '@/Context/AuthContext';
import {Button, notification} from 'antd';
import Image from 'next/image';

export default function About() {
  const {user} = useContext(AuthContext);
  const handleClickShowErrorMessage = (e: any, message: string) => {
    e.preventDefault();
    notification.error({message: message});
  };
  const NotificationForEmployee = (e: any) => {
    e.preventDefault();
    notification.error({message: 'You need to register as an Employee to apply a job..'});
  };

  return (
    <>
      <section className="about-new pt-5 text-center">
        <div className="container">
          <h1>
            Your Talent, Our Platform,
            <br />
            <span className="span-yellow">Limitless Possibilities</span>
          </h1>
          <p>Connecting Talent with Opportunities. Find Your Perfect Fit with Us</p>
          <Link href="/" prefetch={false}>
            <Button className="for-job" style={{height: 'auto'}}>
              For Job Seekers
            </Button>
          </Link>
          <Link href="/for-employers" prefetch={false}>
            <Button className="for-employers" style={{height: 'auto'}}>
              For Employers
            </Button>
          </Link>
        </div>
        <img src="images/back-about.jpg" alt="about-us the talent point" className="w-100" />
      </section>
      <section className="our-story">
        <div className="container">
          <div className="row">
            <div className="col-sm-6">
              <h2>Our Story</h2>
            </div>
            <div className="col-sm-6">
              <p>
                At TheTalentPoint, our story is one of empowerment and connection. Founded by a dedicated team with a
                vision to bridge the gap between job seekers and employers, our platform was born out of a commitment to
                simplify the job search and hiring processes. Our journey is fueled by the belief that when talent finds
                its perfect match, both individuals and businesses can thrive. Since our inception, we've grown,
                partnered with top companies, and connected countless job seekers with their dream careers while helping
                businesses find exceptional talent. Join us today and experience a world of possibilities in your job
                search or recruitment journey.
              </p>
            </div>
          </div>
        </div>
      </section>
      <section className="something-part">
        <div className="container">
          <div className="row">
            <div className="col-sm-6">
              <img
                width={'500'}
                height={400}
                alt="about-us the talent point"
                src={process.env.NEXT_PUBLIC_BASE_URL + 'images/something-1.png'}
                className="custom_img"
              />
              {/* <img
                src={process.env.NEXT_PUBLIC_BASE_URL + 'images/something-1.png'}
                alt="about-us the talent point"
                className="something-1"
              /> */}
            </div>
            <div className="col-sm-6 m-center">
              <h2>For Job Seekers</h2>
              <h3>Unlocking Your Potential</h3>
              <p>
                At TheTalentPoint, we understand that your career is a journey filled with opportunities, challenges,
                and growth. Our mission is to empower job seekers like you to reach your full potential by connecting
                you with the right opportunities and providing the resources you need to succeed.We believe that every
                individual has unique talents and skills waiting to be discovered. Whether you are just starting your
                career, seeking a new challenge, or aiming for a career transition, TheTalentPoint is here to support
                you every step of the way.
              </p>
              <p>
                Our platform offers features such as customized job alerts, resume building tools, and interview
                preparation resources to streamline your job search. We are committed to providing you with the best
                job-seeking experience, making it easier for you to find the perfect job that aligns with your skills,
                interests, and career goals.
              </p>

              <Link
                href={
                  user?.id
                    ? user?.role == 'employee'
                      ? '/jobs-in-gulf'
                      : user?.role == 'employer'
                        ? '#'
                        : '/'
                    : '/auth/signup'
                }
                prefetch={false}
                onClick={
                  user?.role == 'employer'
                    ? e => handleClickShowErrorMessage(e, 'You need to login as a candidate to apply on a job')
                    : undefined
                }>
                <Button className="primary-blue" style={{height: 'auto'}}>
                  Apply Now
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
      <section className="something-part m-top-border">
        <div className="container">
          <div className="row reverse">
            <div className="col-sm-6 m-center">
              <h2>For Employers</h2>
              <h3>Your Success Begins with Great Talent</h3>
              <p>
                We understand that your business needs are unique, and finding the perfect match for your team can be
                challenging. That's why TheTalentPoint provides a comprehensive suite of recruitment tools and services
                to simplify your hiring process
              </p>
              <p>
                Our platform offers features such as advanced candidate search, applicant tracking, and candidate
                assessment tools to streamline your hiring process. We are committed to helping you find the talent that
                aligns with your company culture and goals, ultimately driving your organization towards greater
                success.
              </p>

              <Link
                prefetch={false}
                href={
                  user?.id
                    ? user?.role == 'employer'
                      ? '/employer/candidates'
                      : user?.role == 'employee'
                        ? '#'
                        : '/'
                    : '/auth/signup'
                }
                onClick={
                  user?.role == 'employee'
                    ? e => handleClickShowErrorMessage(e, 'You need to register as a employer to hire candidates')
                    : undefined
                }>
                <Button className="primary-blue" style={{height: 'auto'}}>
                  Apply Now
                </Button>
              </Link>
            </div>
            <div className="col-sm-6">
              <img
                width={600}
                height={500}
                src={process.env.NEXT_PUBLIC_BASE_URL + 'images/something-2.png'}
                alt="about-us the talent point"
                className="custom_img"
              />
              {/* <img
                src={process.env.NEXT_PUBLIC_BASE_URL + 'images/something-2.png'}
                alt="about-us the talent point"
                className="something-1"
              /> */}
            </div>
          </div>
        </div>
      </section>
      <HiringTalent NotificationForEmployee={NotificationForEmployee} />
      <TopBlogs />
      <SubscriptionForm />
    </>
  );
}
