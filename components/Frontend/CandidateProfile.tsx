import React, {useState, useEffect, useContext} from 'react';
import {useRouter} from 'next/router';
import {useForm} from 'react-hook-form';
import {notification} from 'antd';
import axios from 'axios';

import {updatebackgroundImage, sendMessage, getFirstMessageCheckCount, getUserDetailsslug} from '@/lib/frontendapi';
import AuthContext from '@/Context/AuthContext';
import <PERSON>rrorHandler from '@/lib/ErrorHandler';
import {Education, language, Portfolio, skill, User, Work} from '@/lib/types';
import PopupModal from '../../components/Common/PopupModal';
import SubscriptionForm from '../Common/SubscriptionForm';
import ProfileInformation from '../Employees/CandidateProfile/ProfileInformation';
import Footer from './layouts/footer';
import Image from 'next/image';

interface CandidateProfileProps {
  slug?: string;
  user: User;
}

export default function CandidateProfile(props: CandidateProfileProps) {
  const {
    register,
    formState: {errors},
    handleSubmit,
  } = useForm({mode: 'onChange'});

  const [userData, setUserData] = useState<User>(props?.user);
  const [workExperience, setWorkExperience] = useState<Work[]>([]);
  const router = useRouter();
  const [selectedFileName, setSelectedFileName] = useState('');
  const [bannerImageModal, setBannerImageModal] = useState(false);
  const [editCompanyLogo, setEditCompanyLogo]: any = useState('');
  const [uploadError, setUploadError] = useState('');
  const [modalCandidateMessagePopup, setModalCandidateMessagePopup] = useState(false);
  const [messageDesc, setMessageDesc] = useState('');
  const [firstMessageCount, setFirstMessageCount] = useState(0);
  const [education, setEducation] = useState<Education[]>([]);
  const [portfolio, setPortfolio] = useState<Portfolio[]>([]);
  const [language, setLanguage] = useState<language[]>([]);
  const [skills, setSkill] = useState<skill[]>([]);
  const {user} = useContext(AuthContext);

  console.log({user});

  const bannerImageModalOpen = () => {
    setBannerImageModal(true);
  };
  const bannerImageModalClose = () => {
    setBannerImageModal(false);
  };

  useEffect(() => {
    const cancelTokenSource = axios.CancelToken.source();

    const data = {sender_id: user?.id, receiver_id: userData?.id};
    getFirstMessageCheckCount(data)
      .then(res => {
        if (res.status == true) {
          setFirstMessageCount(res.total_message_count);
        } else {
          setFirstMessageCount(0);
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
    return cancelTokenSource.cancel;
  }, [user]);

  const handleContactNowClick = (phoneNumber: any) => {
    window.open(`tel:${phoneNumber}`);
  };

  const getJobStatusText = () => {
    switch (userData?.job_status) {
      case 'ready_to_interview':
        return 'Ready To Interview';
      case 'not_looking':
        return 'Not Looking';
      case 'open_to_offer':
        return 'Open To Offer';
      default:
        return 'Ready To Interview';
    }
  };

  const submitUploadAdminImage = (e: any) => {
    e.preventDefault();
    const userId = user?.id;
    const profileImage = editCompanyLogo;
    if (uploadError !== '') {
      notification.error({message: 'Please select banner image.'});
      return;
    }
    if (profileImage === '') {
      notification.error({message: 'Please select banner image.'});
      return;
    }
    const data = new FormData();
    data.append('profile_image', editCompanyLogo);
    updatebackgroundImage(userId, data)
      .then(res => {
        if (res.status === true) {
          notification.success({message: res.message});
          bannerImageModalClose();
          setTimeout(function () {
            window.location.reload();
          }, 1000);
        } else {
          if (res.errors) {
            // notification.error({ message: res.errors.profile_image });
          }
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  };
  const modalConfirmCandidateMessagePopupOpen = () => {
    setModalCandidateMessagePopup(true);
  };
  const modalConfirmCandidateMessagePopupClose = () => {
    setModalCandidateMessagePopup(false);
  };
  const submitMessageForm = (data: any) => {
    let applicants_id = $('.hd_applicants_id').val();
    let candidate_id = $('.hd_candidate_id').val();
    let job_id = $('.hd_job_id').val();
    let current_user_id = user?.id;
    const datas = {
      applicants_id: applicants_id,
      candidate_id: candidate_id,
      job_id: job_id,
      current_user_id: current_user_id,
      message_title: null,
      message_description: data.message_desc,
      attachment_path: null,
      message_type: 'contact',
      message_status: 'unread',
    };
    sendMessage(datas)
      .then(res => {
        if (res.status == true) {
          notification.success({message: res.message});
          modalConfirmCandidateMessagePopupClose();
        } else {
          // notification.error({ message: res.message });
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
        // notification.error({ message: err.message });
      });
  };

  useEffect(() => {
    const cancelTokenSource = axios.CancelToken.source();
    fetchData().then();
    return cancelTokenSource.cancel;
  }, [router?.query?.slug]);

  const fetchData = async () => {
    try {
      if (router?.query?.slug) {
        getUserDetailsslug(userData?.slug)
          .then(res => {
            if (res.status == true) {
              setEducation(res.education);
              setSkill(res.skills);
              setLanguage(res.language);
              setPortfolio(res.portfolio);
              setUserData(res?.user);
            }
          })
          .catch(err => {
            ErrorHandler.showNotification(err);
          });
      }
    } catch (error) {
      ErrorHandler.showNotification(error);
    }
  };

  return (
    <>
      <section className="bg-ebf4ff">
        <div className="container">
          <section className="candidate-profile ">
            <div className="container">
              <div className="pog-r">
                <img
                  src={
                    userData?.background_banner_image
                      ? `${process.env.NEXT_PUBLIC_IMAGE_URL}images/userbannerImage/${userData?.background_banner_image}`
                      : `${process.env.NEXT_PUBLIC_BASE_URL}images/alan.jpg`
                  }
                  alt="alan"
                  className={userData?.background_banner_image ? '' : 'w-100'}
                />
                {user?.role == 'employee' && (
                  <i className="fa-solid fa-pencil edit-banner" onClick={bannerImageModalOpen}></i>
                )}
              </div>
            </div>
          </section>

          <section className="software-part">
            <div className="container">
              <div className="work-experience-fieild pt-1">
                <div className="row">
                  <div className="col-lg-3 col-md-3 img-size-profile text-center">
                    <div className="dash-profile-img m-auto over-h-none text-center profile-img-set">
                      <img
                        src={
                          props?.user?.profile_image
                            ? props.user?.profile_image?.source
                            : `${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-1.png`
                        }
                        alt={userData?.profile_image?.name || 'Avatars-4'}
                        className="profile-candidate"
                      />
                    </div>
                    <button
                      className={`btn-a  w-100 tab-add-sp mt-1 img-r-sp max-button-auto ${
                        !userData?.job_status || userData?.job_status === 'ready_to_interview'
                          ? 'btn-bg-3D9F79 p-2'
                          : userData?.job_status === 'open_to_offer'
                            ? 'btn-bg-D57B11'
                            : userData?.job_status === 'not_looking'
                              ? 'btn-bg-D04E4F'
                              : ''
                      }`}>
                      <img
                        src={
                          process.env.NEXT_PUBLIC_BASE_URL +
                          (!userData?.job_status || userData?.job_status === 'ready_to_interview'
                            ? 'images/icon-1.png'
                            : userData?.job_status === 'not_looking'
                              ? 'images/icon-3.png'
                              : userData?.job_status === 'open_to_offer'
                                ? 'images/icon-2.png'
                                : '')
                        }
                        alt="icon-1"
                        className="w-16"
                      />
                      {getJobStatusText() ? getJobStatusText() : 'Ready To Interview'}
                    </button>
                    {!userData?.showcontact_no ||
                      (userData?.showcontact_no === 1 &&
                        (user?.role === 'employer' ? (
                          firstMessageCount > 0 ? (
                            <a href={'/employer/messages/inbox/' + userData?.id}>
                              <button className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp mt-3 max-button-auto ">
                                Contact Now
                              </button>
                            </a>
                          ) : (
                            <button
                              className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp mt-3 max-button-auto "
                              onClick={modalConfirmCandidateMessagePopupOpen}>
                              Contact Now
                            </button>
                          )
                        ) : (
                          <button
                            className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp mt-3  max-button-auto"
                            onClick={() => handleContactNowClick(userData?.contact_no)}>
                            Contact Now
                          </button>
                        )))}
                    <PopupModal
                      show={modalCandidateMessagePopup}
                      handleClose={modalConfirmCandidateMessagePopupClose}
                      customclass={'modal-lg  header-remove body-sp-0'}>
                      <div className="popup-body">
                        <p className="f-31 c-191919 text-left">Message {userData?.name}</p>
                        <hr className="hr-line"></hr>
                        <div className="form-experience-fieild">
                          <form className="form-experience-fieild" onSubmit={handleSubmit(submitMessageForm)}>
                            <input
                              type="hidden"
                              name="hd_candidate_id"
                              className="hd_candidate_id"
                              value={userData?.id}
                            />
                            <input type="hidden" name="hd_job_id" className="hd_job_id" value="" />
                            <input type="hidden" name="hd_applicants_id" className="hd_applicants_id" value="" />
                            <p className="f-12 c-2C2C2C">Your Message</p>
                            <textarea
                              placeholder="Your Message"
                              className="fild-des"
                              {...register('message_desc', {required: true})}
                              onChange={(e: any) => setMessageDesc(e.target.value)}></textarea>
                            {errors.message_desc && errors.message_desc.type === 'required' && (
                              <p className="text-danger" style={{textAlign: 'left'}}>
                                Message Field is required.
                              </p>
                            )}
                            <div className="text-right mt-3">
                              <div className="row">
                                <div className="col-4">
                                  <button className="cancel  w-100" onClick={modalConfirmCandidateMessagePopupClose}>
                                    Cancel
                                  </button>
                                </div>
                                <div className="col-8">
                                  <button className="save w-100" type="submit">
                                    Send
                                  </button>
                                </div>
                              </div>
                            </div>
                          </form>
                        </div>
                      </div>
                    </PopupModal>
                    <button
                      className="download mt-3 w-100  max-button-auto"
                      onClick={() => {
                        if (userData?.resume_pdf_path) {
                          router.push(
                            process.env.NEXT_PUBLIC_IMAGE_URL + 'images/employee/resume/' + userData?.resume_pdf_path,
                          );
                        } else {
                          notification.error({message: 'User has not uploaded a resume yet.'});
                        }
                      }}>
                      <i className="fa-solid fa-download"></i> View Resume
                    </button>
                  </div>

                  <div className="col-lg-9 col-md-9">
                    <ProfileInformation
                      userData={userData}
                      workExperience={workExperience}
                      education={education}
                      portfolio={portfolio}
                      skills={skills}
                      language={language}
                    />
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
      </section>
      <SubscriptionForm />
      <Footer />
      <PopupModal
        show={bannerImageModal}
        handleClose={bannerImageModalClose}
        customclass={'header-remove upload_profile_image_model_dialog body-sp-0'}>
        <div className="popup-body">
          <h5 className="f-26 c-0055BA w-700 text-center mb-4">Upload Your Banner Image</h5>
          <form onSubmit={submitUploadAdminImage}>
            <div className="upload-file" id="upload-file1">
              <div className="file-up">
                <input
                  type="file"
                  name="job_banner_image"
                  id="job_banner_image"
                  onChange={(e: any) => {
                    const selectedFile = e.target.files[0];
                    setSelectedFileName(selectedFile.name);
                    const maxSizeInBytes = 2 * 1024 * 1024;
                    if (selectedFile.size > maxSizeInBytes) {
                      setUploadError('File size should not exceed 2 MB.');
                      setEditCompanyLogo('');
                      return;
                    }
                    if (!selectedFile.type.includes('image')) {
                      setUploadError('Please upload an image file (JPEG, JPG, PNG).');
                      setEditCompanyLogo('');
                      return;
                    }
                    setEditCompanyLogo(selectedFile);
                    setUploadError('');
                  }}
                  accept=".jpg, .png, .jpeg"
                />
                <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/cemra.png'} alt="cemra" className="cemra" />
              </div>
              <p className="upload-text">Browse and chose the files you want to upload from your computer.</p>
              <p className="max-size">Maximum upload size is 1MB</p>
            </div>
            {uploadError && <p className="error text-start mt-1">{uploadError}</p>}
            {selectedFileName && <p className="text-dark">Selected File: {selectedFileName}</p>}
            <div className="modal-footer">
              <button
                type="button"
                className="cancel-btn m-w-100"
                data-bs-dismiss="modal"
                onClick={bannerImageModalClose}>
                Cancel
              </button>
              <button type="submit" className="update-btn m-w-100">
                Update
              </button>
            </div>
          </form>
        </div>
      </PopupModal>
    </>
  );
}
