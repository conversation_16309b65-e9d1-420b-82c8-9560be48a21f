import React, {useCallback} from 'react';
import {getAllNewAdminSettings} from '@/lib/adminapi';
import Logo from '@/components/footer/Logo';
import FooterLinks from '@/components/footer/FooterLinks';
import SocialIcons from '@/components/footer/SocialIcons';
import packageInfo from './../../../package.json';
import Image from 'next/image';

import styles from './footer.module.css';

interface AdminSettings {
  linkedin_link: string;
  twitter_link: string;
  instagram_link: string;
  facebook_link: string;
  website_url: string;
  logo: string;
}

type SocialMediaLinks = {
  instagram: 'instagram_link';
  facebook: 'facebook_link';
  linkedin: 'linkedin_link';
  twitter: 'twitter_link';
};

interface SettingType {
  setting?: AdminSettings | null;
}

const socialMediaLinks: SocialMediaLinks = {
  instagram: 'instagram_link',
  facebook: 'facebook_link',
  linkedin: 'linkedin_link',
  twitter: 'twitter_link',
};

function Footer({setting = null}: SettingType) {
  const handleSocialIconClick = useCallback(
    (platform: keyof SocialMediaLinks) => {
      const linkKey = socialMediaLinks[platform];
      const link = setting?.[linkKey];
      if (link) {
        window.open(link, '_blank');
      }
    },
    [setting],
  );

  return (
    <>
      <footer className={`bg-white mt-5 ${styles.FooterLinkContainerDesktop}`}>
        <img
          className={styles.FooterImageDesktop}
          alt="Footer background"
          src="/images/footer-back.jpg"
          layout="fill"
          objectFit="cover"
          objectPosition="center"
        />
        <div className={`footer-part ${styles.FooterContent}`}>
          <div className="container">
            <div className="row">
              <div className="col-sm-4">
                <Logo logo={setting?.logo} />
              </div>
              <div className={`col-sm-8 ${styles.FooterLinkContainer}`}>
                <img
                  className={styles.FooterImage}
                  alt="Footer background"
                  src="/images/mobile-footer-back.png"
                  layout="fill"
                  objectFit="cover"
                  objectPosition="center"
                />
                <div className={styles.FooterLinks}>
                  <FooterLinks />
                </div>
              </div>
            </div>
          </div>
        </div>
      </footer>
      <footer className="last-footer">
        <div className="container">
          <div className="row mobile-reverse">
            <div className="col-sm-8">
              <p className="copy-text">© 2024 The Talent Point {packageInfo.version} All rights reserved</p>
            </div>
            <div className="col-sm-4">
              <SocialIcons setting={setting} onClick={handleSocialIconClick} />
            </div>
          </div>
        </div>
      </footer>
    </>
  );
}

export default React.memo(Footer);

export async function getStaticProps() {
  try {
    const response = await getAllNewAdminSettings();
    return {
      props: {
        setting: response,
      },
      revalidate: 3600,
    };
  } catch (error) {
    console.error(error);
    return {
      props: {
        setting: null,
      },
    };
  }
}
