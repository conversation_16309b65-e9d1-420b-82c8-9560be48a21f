import { useRouter } from 'next/router';

import Header from './header';
import Footer from './footer';
import { Props } from './Props';

const dashboardPages = [
  '/setcompanyprofile',
  '/addteammembers',
  '/publishjob',
  '/auth/login',
  '/auth/signup',
  '/buildtellus',
  '/tellus',
  '/join',
  '/createaccount',
  '/auth/otp',
  '/auth/forgotpassword',
  '/career-tips',
  '/salaries',
  '/about-us',
  '/blog',
  '/for-employers',
  '/blog/[slug]',
  '/auth/resetpassword',
  '/jobs-in-gulf',
  '/jobs-popular-search',
  '/jobs-by-location',
];
export default function Layout({ children, ...props }: Props) {
  const router = useRouter();
  const jobInCitySlug = '/' + router.query.jobsInCity;
  const jobInSkillSlug = '/skill/' + router.query.skillJobsInCity;

  return (
    <div>
      <Header />
      <main {...props}>{children}</main>
      {dashboardPages.find(p => p.includes(router.pathname)) ||
        router.asPath === jobInCitySlug ||
        router.asPath === jobInSkillSlug ? (
        <Footer />
      ) : (
        ''
      )}
    </div>
  );
}
