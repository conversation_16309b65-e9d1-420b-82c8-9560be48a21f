import React, {useState, useContext, useCallback, useMemo} from 'react';
import {useRouter} from 'next/router';
import Link from 'next/link';
import Image from 'next/image';
import {Popover, Space} from 'antd';

import AuthContext from '@/Context/AuthContext';
import AuthUserMenu from '@/components/Common/AuthUserMenu';

interface AdminSettings {
  logo: string;
  favicon: string;
}

const initialSettings: AdminSettings = {
  logo: '',
  favicon: '',
};

export default function Header() {
  const router = useRouter();
  const [isHomeEmployer, setIsHomeEmployer] = useState(false);
  const [settings, SetAdminSetting] = useState<AdminSettings>(initialSettings);
  const {user} = useContext(AuthContext);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const handleClick = useCallback(() => {
    setIsHomeEmployer(prev => !prev);
  }, []);

  const toggleMenu = useCallback(() => {
    setIsMenuOpen(prev => !prev);
  }, []);

  const closeMenu = useCallback(() => {
    setIsMenuOpen(false);
  }, []);

  return (
    <header className="header-navigation head-part">
      <nav className="navbar navbar-expand-lg navbar-light p-0">
        <div className="container">
          <div className="logo-width">
            <Link className="navbar-brand" href="/" prefetch={false}>
              <img
                src={
                  settings && settings.logo
                    ? process.env.NEXT_PUBLIC_IMAGE_URL + 'images/' + settings.logo
                    : '/images/Avatars-4.webp'
                }
                alt="logo"
                className="logo-head"
                width={200}
                height={60}
                priority
              />
            </Link>
          </div>
          <button
            className={`navbar-toggler collapsed ${isMenuOpen ? 'active' : ''}`}
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#navbarSupportedContent"
            aria-controls="navbarSupportedContent"
            aria-expanded="false"
            aria-label="Toggle navigation"
            onClick={toggleMenu}>
            <span className="navbar-toggler-icon">
              <i className="fa-solid fa-bars"></i>
            </span>
          </button>
          <div className={`collapse navbar-collapse ${isMenuOpen ? 'show' : ''}`} id="navbarSupportedContent">
            <ul className="navbar-nav ms-auto  mb-lg-0">
              <li className="nav-item">
                <div id="jobs-main-id">
                  <ul className="navbar-nav-main m-0 p-0">
                    <li className="nav-item dropdown m-none">
                      <Link
                        className={
                          router.pathname == '/jobs-in-gulf'
                            ? 'nav-link active job-link  dropdown-item-jab single-menu-space'
                            : 'nav-link single-menu-space'
                        }
                        aria-current="page"
                        href="/jobs-in-gulf"
                        prefetch={false}>
                        JOBS
                      </Link>
                      <ul className="dropdown-menu">
                        <li className="nav-item dropend hover-link-1" id="color-tags">
                          <div className="d-flex justify-content-between align-items-center">
                            <a className="job-link dropdown-toggle dropdown-item-jab" href="/jobs-by-location">
                              Jobs by Location
                            </a>
                            <img
                              src={'/right-arrow.svg'}
                              className="m-2 black-arrow-1"
                              height={15}
                              width={15}
                              alt=""
                              priority
                            />
                            <svg
                              xmlns=""
                              width="15"
                              height="15"
                              viewBox="0 0 7 12"
                              fill="none"
                              className="m-2 hover-blue-arrow-1">
                              <path
                                d="M1 0.5L6 6L1 11.5"
                                stroke="#0070F5"
                                strokeMiterlimit={10}
                                strokeLinejoin={'round'}
                                strokeLinecap={'round'}
                              />
                            </svg>
                          </div>
                          <ul className="dropdown-menu" id="sub-id">
                            <li>
                              <a className="dropdown-item-jab" href="/jobs-in-al-ain-city">
                                Jobs in Al Ain City
                              </a>
                            </li>
                            <li>
                              <a className="dropdown-item-jab" href="/jobs-in-dubai">
                                Jobs in Dubai
                              </a>
                            </li>
                            <li>
                              <a className="dropdown-item-jab" href="/jobs-in-al-fujairah-city">
                                Jobs in Al Fujairah City
                              </a>
                            </li>
                            <li>
                              <a className="dropdown-item-jab" href="/jobs-in-jeddah">
                                Jobs in Jeddah
                              </a>
                            </li>
                            <li>
                              <a className="dropdown-item-jab" href="/jobs-in-riyadh">
                                Jobs in Riyadh
                              </a>
                            </li>
                            <li>
                              <a
                                className="dropdown-item-jab view_all_btn"
                                href="/jobs-by-location"
                                style={{color: '#0070F5 !important', fontWeight: '600'}}>
                                View All
                              </a>
                            </li>
                          </ul>
                        </li>
                        <li className="nav-item dropend hover-link-2" id="color-tags">
                          <div className="d-flex justify-content-between align-items-center">
                            <a className="job-link dropdown-toggle dropdown-item-jab" href="/jobs-popular-search">
                              Popular Searches
                            </a>
                            <svg
                              xmlns=""
                              width="15"
                              height="15"
                              viewBox="0 0 7 12"
                              fill="none"
                              className="m-2 hover-blue-arrow-2">
                              <path
                                d="M1 0.5L6 6L1 11.5"
                                stroke="#0070F5"
                                strokeMiterlimit={10}
                                strokeLinejoin={'round'}
                                strokeLinecap={'round'}
                              />
                            </svg>
                            <img
                              src={'/right-arrow.svg'}
                              className="m-2 black-arrow-2"
                              height={15}
                              width={15}
                              alt=""
                              priority
                            />
                          </div>
                          <ul className="dropdown-menu" id="sub-id">
                            <li>
                              <a className="dropdown-item-jab" href="/skill/part-time-jobs-in-dubai">
                                Part Time jobs in Dubai
                              </a>
                            </li>
                            <li>
                              <a className="dropdown-item-jab" href="/skill/accountant-jobs-in-dubai">
                                Accountant jobs in Dubai
                              </a>
                            </li>
                            <li>
                              <a className="dropdown-item-jab" href="/skill/airport-jobs-in-dubai">
                                Airport Jobs in Dubai
                              </a>
                            </li>
                            <li>
                              <a className="dropdown-item-jab" href="/skill/fresher-jobs-in-dubai">
                                Fresher Jobs in Dubai
                              </a>
                            </li>
                            <li>
                              <a className="dropdown-item-jab" href="/skill/teaching-jobs-in-dubai">
                                Teaching Jobs in Dubai
                              </a>
                            </li>
                            <li>
                              <a
                                className="dropdown-item-jab view_all_btn"
                                href="/jobs-popular-search"
                                style={{color: '#0070F5 !important', fontWeight: '600'}}>
                                View All
                              </a>
                            </li>
                          </ul>
                        </li>
                      </ul>
                    </li>
                    <li className="nav-item dask-none">
                      <div className="ac">
                        <input className="ac-input" id="ac-1" name="ac-1" type="checkbox" />
                        <label className="ac-label border-bottom-2" htmlFor="ac-1">
                          <a href="#" className="jobs-menu-text">
                            JOBS
                          </a>
                        </label>
                        <article className="ac-text mt-2">
                          <div id="accordion-nav">
                            <div className="accordion" id="accordionExample">
                              <div className="accordion-item">
                                <h2 className="accordion-header" id="headingOne">
                                  <span className="accordion-button collapsed open-sans">
                                    <span className="text-start">
                                      <a href="/jobs-by-location">Jobs by Location</a>
                                    </span>
                                    <span
                                      className="text-end"
                                      data-bs-toggle="collapse"
                                      data-bs-target="#collapseOne"
                                      aria-expanded="false"
                                      aria-controls="collapseOne">
                                      <i className="fa-solid fa-chevron-down"></i>
                                    </span>
                                  </span>
                                </h2>
                                <div
                                  id="collapseOne"
                                  className="accordion-collapse collapse "
                                  aria-labelledby="headingOne"
                                  data-bs-parent="#accordionExample">
                                  <div className="accordion-body">
                                    <ul className="faq-nav">
                                      <li>
                                        <a className="dropdown-item-jab" href="/jobs-in-al-ain-city">
                                          Jobs in Al Ain City
                                        </a>
                                      </li>
                                      <li>
                                        <a className="dropdown-item-jab" href="/jobs-in-dubai">
                                          Jobs in Dubai
                                        </a>
                                      </li>
                                      <li>
                                        <a className="dropdown-item-jab" href="/jobs-in-al-fujairah-city">
                                          Jobs in Al Fujairah City
                                        </a>
                                      </li>
                                      <li>
                                        <a className="dropdown-item-jab" href="/jobs-in-jeddah">
                                          Jobs in Jeddah
                                        </a>
                                      </li>
                                      <li>
                                        <a className="dropdown-item-jab" href="/jobs-in-riyadh">
                                          Jobs in Riyadh
                                        </a>
                                      </li>
                                      <li>
                                        <a
                                          className="dropdown-item-jab view_all_btn"
                                          href="/jobs-by-location"
                                          style={{color: '#0070F5 !important', fontWeight: '600'}}>
                                          View All
                                        </a>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                              <div className="accordion-item">
                                <h2 className="accordion-header" id="headingTwo">
                                  {/* <a
                                    href="/jobs-popular-search"
                                    className="accordion-button collapsed open-sans"
                                    type="button"
                                    data-bs-toggle="collapse"
                                    data-bs-target="#collapseTwo"
                                    aria-expanded="false"
                                    aria-controls="collapseTwo">
                                    Popular Searches
                                  </a> */}
                                  <span className="accordion-button collapsed open-sans">
                                    <span className="text-start">
                                      <a href="/jobs-by-location">Popular Searches</a>
                                    </span>
                                    <span
                                      className="text-end"
                                      data-bs-toggle="collapse"
                                      data-bs-target="#collapseTwo"
                                      aria-expanded="false"
                                      aria-controls="collapseTwo">
                                      <i className="fa-solid fa-chevron-down"></i>
                                    </span>
                                  </span>
                                </h2>
                                <div
                                  id="collapseTwo"
                                  className="accordion-collapse collapse"
                                  aria-labelledby="headingTwo"
                                  data-bs-parent="#accordionExample">
                                  <div className="accordion-body">
                                    <ul className="faq-nav">
                                      <li>
                                        <a href="/skill/part-time-jobs-in-dubai">Part Time jobs in Dubai</a>
                                      </li>
                                      <li>
                                        <a href="/skill/accountant-jobs-in-dubai">Accountant jobs in Dubai</a>
                                      </li>
                                      <li>
                                        <a href="/skill/airport-jobs-in-dubai">Airport Jobs in Dubai</a>
                                      </li>
                                      <li>
                                        <a href="/skill/fresher-jobs-in-dubai">Fresher Jobs in Dubai</a>
                                      </li>
                                      <li>
                                        <a href="/skill/teaching-jobs-in-dubai">Teaching Jobs in Dubai</a>
                                      </li>
                                      <li>
                                        <a
                                          className="dropdown-item-jab view_all_btn"
                                          href="/jobs-popular-search"
                                          style={{color: '#0070F5 !important', fontWeight: '600'}}>
                                          View All
                                        </a>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </article>
                      </div>
                    </li>
                  </ul>
                </div>
              </li>
              <li className="nav-item">
                <Link
                  className={`nav-link single-menu-space ${router.pathname == '/career-tips' ? 'active' : ''}`}
                  href="/career-tips"
                  prefetch={false}>
                  CAREER TIPS
                </Link>
              </li>
              {/* <li className="nav-item">
                <Link
                  className={`nav-link single-menu-space ${router.pathname == '/salaries' ? 'active' : ''}`}
                  href="/salaries" prefetch={false}>
                  SALARIES
                </Link>
              </li> */}
              <li className="nav-item">
                <Link
                  className={`nav-link single-menu-space ${router.pathname == '/about-us' ? 'active' : ''}`}
                  href="/about-us"
                  prefetch={false}>
                  ABOUT
                </Link>
              </li>

              <li className="nav-item">
                <Link
                  className={`nav-link single-menu-space ${router.pathname == '/blog' ? 'active' : ''}`}
                  href="/blog"
                  prefetch={false}>
                  BLOG
                </Link>
              </li>

              <li className="nav-item" onClick={handleClick}>
                <Link
                  className={`nav-link single-menu-space ${
                    router.pathname === '/for-employers' || (router.pathname === '/' && isHomeEmployer) ? 'active' : ''
                  }`}
                  href={!isHomeEmployer ? '/for-employers' : '/'}
                  prefetch={false}>
                  {!isHomeEmployer ? 'FOR EMPLOYERS' : 'FOR JOB SEEKERS'}
                </Link>
              </li>
              {user && (
                <li className="nav-item">
                  <Popover content={<AuthUserMenu />} trigger={['click']}>
                    <a
                      className={
                        router.pathname == '/auth/login'
                          ? 'nav-link active dropdown-toggle single-menu-space'
                          : 'nav-link dropdown-toggle single-menu-space'
                      }
                      id="dropdownMenuButton1"
                      data-bs-toggle="dropdown"
                      aria-expanded="false"
                      style={{ cursor: 'pointer' }}>
                      {user.name}
                    </a>
                  </Popover>
                </li>
              )}
            </ul>
            {!user && (
              <>
                <Link href="/auth/login" prefetch={false}>
                  <button className="btn login mobile-w-100">Log In</button>
                </Link>
                <Link href="/auth/signup/[[...slug]]" prefetch={false}>
                  <button className="btn signup mobile-w-100">Sign Up</button>
                </Link>
              </>
            )}
            {/* {user && <Popover content={<AuthUserMenu />} trigger={['click']}>
              <div className="nav-link single-menu-space" style={{display: 'flex', alignItems: 'center', cursor: 'pointer'}}>
                <Space>
                  <img
                    src={
                      user?.profile_image
                        ? user?.profile_image?.source
                        : `${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-1.png`
                    }
                    // src={user?.profile_image ? process.env.NEXT_PUBLIC_IMAGE_URL + user?.profile_image?.source?.replace("/storage/", '') : `${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-1.png`}
                    alt={user?.profile_image?.name || user?.name || 'Employee profile image'}
                    className="w-32"
                    width={32}
                    height={32}
                  />
                  <i className="fa-solid fa-ellipsis-vertical" style={{cursor: 'pointer'}}></i>
                </Space>
              </div>
            </Popover>} */}
          </div>
        </div>
      </nav>
    </header>
  );
}
