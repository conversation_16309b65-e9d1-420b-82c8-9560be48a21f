.FooterLinkContainer,
.FooterLinkContainerDesktop {
  position: relative;
  overflow: hidden;
}

.FooterImage {
  width: 100%;
  height: auto !important;
  z-index: 0;
  position: absolute;
  top: 0;
}

.FooterImageDesktop {
  width: 100%;
  height: auto !important;
  z-index: 0;
  position: absolute;
  top: 0;
  display: none;
}

.FooterLinks,
.FooterContent {
  position: relative;
  z-index: 1;
}

@media screen and (min-width: 767px) {
  .FooterImage {
    display: none;
  }

  .FooterImageDesktop {
    display: block;
  }
}
