import React, {useState, useEffect, useRef, useContext} from 'react';
import Link from 'next/link';
import {usePathname} from 'next/navigation';
import {updateSingleJobBackgroundBannerImage, getJobSkills} from '@/lib/frontendapi';
import {getCountryCode} from 'countries-list';

import {
  FacebookIcon,
  FacebookShareButton,
  LinkedinIcon,
  LinkedinShareButton,
  RedditIcon,
  RedditShareButton,
  WhatsappIcon,
  WhatsappShareButton,
} from 'next-share';
import axios from 'axios';
import {Button, Col, Form, Input, Row, notification} from 'antd';
import {File, Job} from '@/lib/types';
import AuthContext from '@/Context/AuthContext';
import ErrorHandler from '@/lib/ErrorHandler';
import ModalForm from '../Common/ModalForm';
import SubscriptionForm from '../Common/SubscriptionForm';
import JobListItem from '../Common/JobListItem';
import <PERSON>ApplyButton from '../Common/JobApplyButton';
import JobSaveButton from '../Common/JobSaveButton';
import Footer from './layouts/footer';
import MobileViewSingleJobPage from '../../components/Common/MobileViewSingleJobPage';
import Head from 'next/head';
import {FilePond, registerPlugin} from 'react-filepond';
import FilePondPluginImageExifOrientation from 'filepond-plugin-image-exif-orientation';
import FilePondPluginImagePreview from 'filepond-plugin-image-preview';
import 'filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css';
import 'filepond/dist/filepond.min.css';
import Image from 'next/image';
import JsonLd from '../../components/JsonLd';
registerPlugin(FilePondPluginImageExifOrientation, FilePondPluginImagePreview);

interface SingleJobsProps {
  job: Job;
  jobId: any;
  jobPosting: any;
}

export default function SingleJobs({job, jobId, jobPosting}: SingleJobsProps) {
  console.log(jobPosting, 'SingleJobs', job);

  const [imageUrl, setImageUrl] = useState('');
  const [jobSkills, setJobSkills]: any = useState([]);
  const [modalUploadBannerImage, setModalUploadBannerImage] = useState(false);
  const [showShareButtons, setShowShareButtons] = useState(false);
  const anchorRef = useRef<HTMLDivElement | null>(null);
  const [showMobileView, setShowMobileView] = useState(false);
  const {user} = useContext(AuthContext);
  const [files, setFiles] = useState([]);
  const [uploadedFile, setUploadedFile] = useState<File>();
  const [bannerImage, setBannerImage] = useState<File | undefined>();
  const [jobExperience, setJobExperience] = useState('');
  const pathname = usePathname();

  useEffect(() => {
    if (job?.company?.logo?.source) {
      let url = job?.company.logo?.source;
      // let newUrl = url.replace('/storage/', '');
      setImageUrl(url);
    }
    try {
      if (jobId) {
        const newData = {job_id: jobId, user_id: user?.id};

        getJobSkills(newData)
          .then(res => {
            if (res.status == true) {
              setJobSkills(res.data);
            } else {
              setJobSkills([]);
            }
          })
          .catch(err => {
            console.log(err);
          });
      }

      const details = navigator.userAgent;
      const regexp = /android|iphone|kindle|ipad/i;
      const isMobileDevice = regexp.test(details);
      setShowMobileView(isMobileDevice);
    } catch (error) {
      ErrorHandler.showNotification(error);
    }
  }, [user, jobId]);

  const modalUploadBannerImageOpen = () => {
    setModalUploadBannerImage(true);
  };

  const modalUploadBannerImageClose = () => {
    setModalUploadBannerImage(false);
  };

  const onSubmitForm = () => {
    const data = {job_id: jobId, fk_banner_file_uuid: uploadedFile?.uuid};
    updateSingleJobBackgroundBannerImage(data)
      .then(res => {
        if (res.status == true) {
          notification.success({message: res.message});
          setBannerImage(uploadedFile);
          modalUploadBannerImageClose();
        } else {
          notification.error({message: res.message});
          if (res.errors) {
            notification.error({message: res.errors.job_background_banner_img[0]});
          }
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  };

  const extractCurrencyCode = (value: string) => {
    if (!value) {
      return '';
    }
    const currencyCode = value.split('(')[1]?.trim()?.slice(0, -1);
    return currencyCode || '';
  };

  const handleImageClick = () => {
    setShowShareButtons(!showShareButtons);
  };

  useEffect(() => {
    window.addEventListener('click', handleClick);
    return () => {
      window.addEventListener('click', handleClick);
    };
  }, []);

  const handleClick = (event: MouseEvent) => {
    if (anchorRef.current && event.target instanceof Node && !anchorRef.current.contains(event.target)) {
      setShowShareButtons(false);
    }
  };

  const experienceLabels: any = {
    '1': 'fresher',
    '2': '0-1',
    '3': '2-3',
    '4': '3-5',
    '5': '5-7',
    '6': '7-10',
    '7': '10-15',
    '8': '15-20',
    '9': '20+',
  };
  useEffect(() => {
    if (job?.banner) {
      setBannerImage(job?.banner);
    }
    if (job?.experience && experienceLabels[job.experience]) {
      setJobExperience(experienceLabels[job.experience]);
    }
  }, [job]);

  const currency = job?.monthly_fixed_salary_currency?.split('(')[1].split(')')[0];

  // Helper function to map job types to Google's employment type values
  const mapEmploymentType = (jobType: string): string => {
    const typeMapping: {[key: string]: string} = {
      fulltime: 'FULL_TIME',
      parttime: 'PART_TIME',
      contract: 'CONTRACTOR',
      freelance: 'CONTRACTOR',
      temporary: 'TEMPORARY',
      intern: 'INTERN',
      volunteer: 'VOLUNTEER',
    };
    return typeMapping[jobType?.toLowerCase()] || 'OTHER';
  };

  // Helper function to format dates in ISO 8601 format
  const formatDateISO = (dateString: string): string => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return date.toISOString();
    } catch {
      return dateString; // fallback to original if parsing fails
    }
  };

  // Check if job is remote/telecommute
  const isRemoteJob =
    job?.job_description?.toLowerCase().includes('remote') ||
    job?.job_description?.toLowerCase().includes('work from home') ||
    job?.job_description?.toLowerCase().includes('telecommute');

  const jobStructuredData = {
    title: job.job_title,
    description: job.job_description,
    datePosted: formatDateISO(job.created_at),
    validThrough: job.deadline ? formatDateISO(job.deadline) : undefined,
    identifier: {
      '@type': 'PropertyValue',
      name: job?.company?.company_name || 'The Talent Point',
      value: job.id.toString(),
    },
    image: job?.company?.logo
      ? `${process.env.NEXT_PUBLIC_IMAGE_URL}${job?.company?.logo?.source?.replace('/storage/', '')}`
      : process.env.NEXT_PUBLIC_BASE_URL + '/images/logo-img.png',
    experienceRequirements: experienceLabels[job.experience],
    employmentType: mapEmploymentType(job.job_type),
    directApply: true, // Assuming users can apply directly on your site
    ...(isRemoteJob && {jobLocationType: 'TELECOMMUTE'}),
    hiringOrganization: {
      '@type': 'Organization',
      name: job?.company?.company_name,
      sameAs: job?.company?.company_website,
      logo: job?.company?.logo
        ? `${process.env.NEXT_PUBLIC_IMAGE_URL}${job?.company?.logo?.thumbnail?.replace('/storage/', '')}`
        : process.env.NEXT_PUBLIC_BASE_URL + '/images/logo-img.png',
    },
    applicantLocationRequirements: {'@type': 'Country', name: job?.country?.country_name},
    jobLocation: {
      '@type': 'Place',
      address: {
        '@type': 'PostalAddress',
        streetAddress: job?.street_address,
        addressLocality: job?.city?.city_name || job?.country?.capital,
        postalCode: job?.postal_code,
        addressCountry: getCountryCode(job?.country?.country_name as string),
      },
    },
    baseSalary: job?.monthly_fixed_salary_min
      ? {
          '@type': 'MonetaryAmount',
          currency: currency || 'AED',
          value: {
            '@type': 'QuantitativeValue',
            value: parseFloat(job?.monthly_fixed_salary_min),
            ...(job?.monthly_fixed_salary_max && {maxValue: parseFloat(job?.monthly_fixed_salary_max)}),
            unitText: 'MONTH',
          },
        }
      : undefined,
    mainEntityOfPage: {'@type': 'WebPage', '@id': `${process.env.NEXT_PUBLIC_BASE_URL}${pathname.substring(1)}`},
  };

  console.log(process.env.NEXT_PUBLIC_BASE_URL, pathname.substring(1));

  return (
    <>
      <section className="bg-ebf4ff">
        <div className="container">
          <section className="candidate-profile m-none ">
            <div className="container">
              <div className="pog-r">
                <div className={bannerImage?.source ? 'imageCover' : 'withoutImageCover'}>
                  {bannerImage?.source && (
                    <img
                      src={bannerImage?.source || process.env.NEXT_PUBLIC_BASE_URL + 'images/alan.jpg'}
                      alt={bannerImage?.name || 'job background image'}
                      style={{width: '100%'}}
                      width={200}
                      height={200}
                      layout="responsive"
                      priority
                    />
                  )}
                </div>
                {user?.id === job?.user_id && user?.role === 'employer' && (
                  <i className="fa-solid fa-pencil edit-banner" onClick={modalUploadBannerImageOpen}></i>
                )}
                <ModalForm
                  title={'Upload Your Banner Image'}
                  open={modalUploadBannerImage}
                  onCancel={() => setModalUploadBannerImage(false)}>
                  <div className="popup-body">
                    <Form onFinish={onSubmitForm}>
                      <Row gutter={15}>
                        <Col md={24}>
                          {/*@ts-ignore*/}
                          <FilePond
                            files={files}
                            onupdatefiles={setFiles}
                            allowMultiple={false}
                            server={{
                              url: axios.defaults.baseURL + '/file-management/files',
                              process: {
                                headers: {Authorization: axios.defaults.headers.common.Authorization},
                                onload: data => {
                                  const parsed = JSON.parse(data);
                                  setUploadedFile(parsed);
                                },
                              },
                            }}
                            name="file"
                          />
                        </Col>
                      </Row>
                      <div className="modal-footer">
                        <Button
                          type={'primary'}
                          size={'large'}
                          className="cancel-btn"
                          data-bs-dismiss="modal"
                          onClick={modalUploadBannerImageClose}>
                          Cancel
                        </Button>
                        <Button type={'primary'} size={'large'} htmlType="submit" className="update-btn">
                          Update
                        </Button>
                      </div>
                    </Form>
                  </div>
                </ModalForm>
              </div>
            </div>
          </section>
          <section className="software-part">
            <div className="container">
              <div className="work-experience-fieild pt-1 p-2">
                {showMobileView ? (
                  <MobileViewSingleJobPage singleJobData={job} jobId={jobId} />
                ) : (
                  <div className="row">
                    <div className="col-lg-3 col-md-3 img-size-profile text-center">
                      <div className="dash-profile-img m-auto over-h-none">
                        <div className="logoCircle">
                          {job?.company?.logo ? (
                            <img
                              // src={
                              //   imageUrl ? `${process.env.NEXT_PUBLIC_IMAGE_URL}${imageUrl}` : '/images/logo-cir.png'
                              // }
                              src={imageUrl ? imageUrl : '/images/logo-cir.png'}
                              alt={job?.company?.logo?.name}
                              className="w-100 mar-m"
                              height={195}
                              width={195}
                              priority
                            />
                          ) : (
                            <img src={'/images/logo-cir.png'} alt="Avatars-4" className="w-100 mar-m" />
                          )}
                        </div>
                      </div>
                      {user?.id !== job?.user_id ? (
                        <>
                          <JobApplyButton job={job} />
                          <JobSaveButton job={job} />
                        </>
                      ) : (
                        ''
                      )}
                    </div>
                    <div className="col-lg-9 col-md-9">
                      <div className="text-right link-right-icons">
                        <p className="mt-2">
                          <div onClick={handleImageClick} ref={anchorRef} style={{cursor: 'pointer'}}>
                            <img src={'/images/share.png'} alt="share" />
                          </div>
                          {showShareButtons && (
                            <div className="mt-3">
                              <FacebookShareButton url={`${process.env.NEXT_PUBLIC_BASE_URL}job/${job?.job_slug}`}>
                                <FacebookIcon size={32} round className="m-2" />
                              </FacebookShareButton>
                              <RedditShareButton url={`${process.env.NEXT_PUBLIC_BASE_URL}job/${job?.job_slug}`}>
                                <RedditIcon size={32} round className="m-2" />
                              </RedditShareButton>
                              <WhatsappShareButton url={`${process.env.NEXT_PUBLIC_BASE_URL}job/${job?.job_slug}`}>
                                <WhatsappIcon size={32} round className="m-2" />
                              </WhatsappShareButton>
                              <LinkedinShareButton url={`${process.env.NEXT_PUBLIC_BASE_URL}job/${job?.job_slug}`}>
                                <LinkedinIcon size={32} round className="m-2" />
                              </LinkedinShareButton>
                            </div>
                          )}
                        </p>
                      </div>
                      <div className="row">
                        <div className="col-sm-12 col-9">
                          <h1 className="em-name f-54">{job?.job_title ? job?.job_title : 'Job Title'}</h1>
                          {job?.hide_employer_details == 0 ? (
                            <h2 className="f-22 c-0070F5">
                              <Link href={'/companies/' + job?.company?.company_slug}>
                                {job?.company?.company_name}
                              </Link>
                            </h2>
                          ) : (
                            ''
                          )}
                          <ul className="skills mt-1 mb-1">
                            {job?.country?.country_name && (
                              <li>
                                <h2 className="f-16 c-999999">
                                  <i className="fa-solid fa-location-dot"></i>{' '}
                                  {job?.country?.country_name ? job?.country?.country_name : ''}
                                  {job?.city?.city_name ? ', ' + job?.city?.city_name : ''}
                                </h2>
                              </li>
                            )}
                          </ul>
                        </div>
                        <div className="row mt-4">
                          <div className="col-sm-5">
                            <p className="f-12 c-000 mb-0">Salary</p>
                            <ul className="skills skills-f-18 ">
                              <li>
                                <p className="cat w-600" style={{textTransform: 'capitalize'}}>
                                  {extractCurrencyCode(job?.monthly_fixed_salary_currency)}{' '}
                                  {job?.monthly_fixed_salary_min} -{' '}
                                  {extractCurrencyCode(job?.monthly_fixed_salary_currency)}{' '}
                                  {job?.monthly_fixed_salary_max} per month
                                </p>
                              </li>
                            </ul>
                          </div>
                          <div className="col-sm-7">
                            <p className="f-12 c-000 mb-0">Job Type</p>
                            <ul className="skills skills-f-18 ">
                              <li>
                                <p className="cat w-600" style={{textTransform: 'capitalize'}}>
                                  {job?.job_type == 'parttime' ? 'Part-Time' : ''}
                                  {job?.job_type == 'fulltime' ? 'Full-Time' : ''}
                                  {job?.job_type == 'contract' ? 'Contract' : ''}
                                  {job?.job_type == 'freelance' ? 'Freelance' : ''}
                                </p>
                              </li>
                            </ul>
                          </div>
                        </div>
                        <div className="row">
                          {jobExperience ? (
                            <div className="col-sm-2">
                              <p className="f-12 c-000 mb-0">Experience</p>
                              <ul className="skills skills-f-18 mt-0">
                                <li>
                                  <p className="cat w-600">
                                    {job?.experience != '1'
                                      ? jobExperience + ' Years'
                                      : jobExperience.charAt(0).toUpperCase() + jobExperience.slice(1)}
                                  </p>
                                </li>
                              </ul>
                            </div>
                          ) : (
                            ''
                          )}
                          {job?.available_vacancies ? (
                            <div className="col-sm-10">
                              <p className="f-12 c-000 mb-0">No. of Vacancies</p>
                              <ul className="skills skills-f-18 mt-0">
                                <li>
                                  <p className="cat w-600">{job?.available_vacancies}</p>
                                </li>
                              </ul>
                            </div>
                          ) : (
                            ''
                          )}
                        </div>
                        {job?.job_description && (
                          <div className="col-9 mt-4">
                            <p className="f-12 c-000">Job Description</p>
                            <div
                              className="f-16 w-400 c-4D4D4D open-sans description-des"
                              dangerouslySetInnerHTML={{__html: job?.job_description}}
                            />
                          </div>
                        )}
                      </div>
                      <br />
                      <br />
                      {jobSkills.length > 0 && <h3 className="f-16 c-000 mt-4">Skills Required</h3>}
                      <ul className="skills skills-f-18 mt-2">
                        {jobSkills.length > 0 ? (
                          jobSkills.map((job_skills: any, index: any) => {
                            return (
                              <li key={index}>
                                <p className="cat bg-CFE5FF">{job_skills.skills}</p>
                              </li>
                            );
                          })
                        ) : (
                          <li></li>
                        )}
                      </ul>
                    </div>
                  </div>
                )}
                <div className="row mt-5">
                  <div className="col-2"></div>
                  <div className="col-sm-8 col-12">
                    {job.company?.jobs.data && (
                      <h3 className="f-22 c-2C2C2C">Job Openings at {job?.company?.company_name}</h3>
                    )}
                    {job.company?.jobs.data?.length > 0
                      ? job.company?.jobs.data.map((jobs_data: any, index: any) => (
                          <JobListItem
                            job={jobs_data}
                            index={index}
                            f-22
                            c-0070F5
                            key={index}
                            jobInCityName={pathname.substring(1)}
                            jobPosting={false}
                          />
                        ))
                      : ''}
                  </div>
                  <div className="col-2"></div>
                </div>
              </div>
            </div>
          </section>
        </div>
        {jobPosting && (
          <JsonLd
            data={{
              '@context': 'https://schema.org',
              '@type': 'JobPosting',
              title: jobStructuredData.title,
              description: jobStructuredData.description,
              datePosted: jobStructuredData.datePosted,
              ...(jobStructuredData.validThrough && {validThrough: jobStructuredData.validThrough}),
              identifier: jobStructuredData.identifier,
              employmentType: jobStructuredData.employmentType,
              directApply: jobStructuredData.directApply,
              ...(jobStructuredData.jobLocationType && {jobLocationType: jobStructuredData.jobLocationType}),
              hiringOrganization: jobStructuredData.hiringOrganization,
              experienceRequirements: jobStructuredData.experienceRequirements,
              image: [jobStructuredData.image],
              applicantLocationRequirements: jobStructuredData.applicantLocationRequirements,
              jobLocation: jobStructuredData.jobLocation,
              ...(jobStructuredData.baseSalary && {baseSalary: jobStructuredData.baseSalary}),
              mainEntityOfPage: jobStructuredData.mainEntityOfPage,
            }}
          />
        )}
      </section>
      <SubscriptionForm />
      <Footer />
    </>
  );
}
