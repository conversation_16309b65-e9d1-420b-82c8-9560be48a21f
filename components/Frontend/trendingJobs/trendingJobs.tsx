import React, {useState, useEffect, useContext} from 'react';
import Image from 'next/image';
import Link from 'next/link';
import useWindowDimensions from '@/helpers/useWindowDimensions';
import styles from './style.module.css';

interface trendingJobsProps {
  trendJobs: {
    sector_name: string;
    job_count: number;
  }[];
  submitSearchForm: any;
}

export default function TrendingJobs({trendJobs, submitSearchForm}: trendingJobsProps) {
  const windowDimensions: any = useWindowDimensions();
  const [viewmore, setViewmore] = useState<boolean>(false);

  const viewmoreTrendJObs = () => {
    setViewmore(true);
  };

  return (
    <section className={styles['trending_job_sec']}>
      <div className="container">
        <div className={styles['trending_hdng']}>
          <h6>
            Trending Jobs{' '}
            <span>
              <img src="/icons/fire.svg" alt="" width={14} height={18} />
            </span>
          </h6>
        </div>
        {viewmore ? (
          <div className={styles['trending_job_list']}>
            <ul>
              {trendJobs?.map((item, index: number) => (
                <li key={index}>
                  <Link
                    href="javascript:void(0)"
                    onClick={() => submitSearchForm({keywords: item.sector_name, country: undefined})}>
                    {item.sector_name} {item.job_count > 0 && `(${item.job_count})`}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        ) : (
          <div className={styles['trending_job_list']}>
            {windowDimensions.width > 767 ? (
              <ul>
                {trendJobs?.map((item, index: number) => (
                  <li key={index}>
                    <Link
                      href="javascript:void(0)"
                      onClick={() => submitSearchForm({keywords: item.sector_name, country: undefined})}>
                      {item.sector_name} {item.job_count > 0 && `(${item.job_count})`}
                    </Link>
                  </li>
                ))}
              </ul>
            ) : (
              <ul>
                {trendJobs?.slice(0, 2).map((item, index) => (
                  <li key={index}>
                    <Link
                      href="javascript:void(0)"
                      onClick={() => submitSearchForm({keywords: item.sector_name, country: undefined})}>
                      {item.sector_name} {item.job_count > 0 && `(${item.job_count})`}
                    </Link>
                  </li>
                ))}
              </ul>
            )}
            <span onClick={() => viewmoreTrendJObs()}>View More...</span>
          </div>
        )}
      </div>
    </section>
  );
}
