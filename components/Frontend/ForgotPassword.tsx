import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { notification, Button, Form, Input } from 'antd';
import { forgetPassword } from '../../lib/frontendapi';

export default function ForgotPassword() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const onFinish = async (values: any) => {
    setIsLoading(true);

    try {
      const response = await forgetPassword({ email: values.email });
      if (response.success === true) {
        notification.success({ message: 'Password reset email sent successfully' });
        router.push('/auth/login');
      } else {
        setIsLoading(false);
        notification.error({ message: 'Password reset email failed to send' });
      }
    } catch (error) {
      setIsLoading(false);
      notification.error({ message: 'Password reset email failed to send' });
    }
  };

  return (
    <>
      <section className="banner-part-home">
        <div className="container-fluid">
          <div className="row">
            <div className="col-lg-5 col-md-12 tab-none pl-0 back-img-form new-join"></div>
            <div className="col-lg-7 col-md-12">
              <div className="form-pages top-m-sp">
                <h2>Forgot Your Password?</h2>
                <h5>Let’s get you connected.</h5>
                <Form onFinish={onFinish} className="form-get mt-4">
                  <Form.Item
                    name="email"
                    rules={[
                      { required: true, message: 'Email is required' },
                      { type: 'email', message: 'Email is invalid' },
                    ]}>
                    <Input type="email" size="large" placeholder="<EMAIL>" />
                  </Form.Item>
                  <Form.Item>
                    <Button
                      type="primary"
                      htmlType="submit"
                      className="btn-a primary-size-16 b-0 btn-bg-0055BA w-100"
                      loading={isLoading}
                      style={{ height: 'auto' }}>
                      {isLoading ? 'Please wait...' : 'Reset Password'}
                    </Button>
                  </Form.Item>
                  <div className="fotgetpass">
                    <p className="f-12 text-right forgot">
                      <Link href="/auth/login">Back to Login</Link>
                    </p>
                  </div>
                </Form>
              </div>~
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
