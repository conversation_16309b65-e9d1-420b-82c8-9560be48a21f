import React, { useState, useContext, useRef, lazy, Suspense, useCallback } from 'react';
import { Blogs } from '@/lib/types';
import { Button } from 'antd';
import dynamic from 'next/dynamic';
const SubscriptionForm = lazy(() => import('../Common/SubscriptionForm'));
import AuthContext from '@/Context/AuthContext';
// import LatestBlogsection from '../Blog/LatestBlogsection';
// import ExploreBlogsSection from '../Blog/ExploreBlogsSection';
// import BlogPageHiringSection from '../Blog/BlogPageHiringSection';
// import SubscriptionForm from '../Common/SubscriptionForm';
const LatestBlogsection = lazy(() => import('../Blog/LatestBlogsection'));
const ExploreBlogsSection = lazy(() => import('../Blog/ExploreBlogsSection'));
const BlogPageHiringSection = lazy(() => import('../Blog/BlogPageHiringSection'));
interface BlogListProps {
  blogs: Blogs[],
  latestBlog: Blogs | null
  page: number,
  totalCount: number
}
function BlogList({ blogs, latestBlog, page, totalCount }: BlogListProps) {
  const [selectedCategoryID, setSelectedCategoryID] = useState<number | null>(null);
  const blogListRef = useRef<HTMLDivElement>(null);
  const { user } = useContext(AuthContext);


  const categories = [
    { id: null, label: 'All' },
    { id: 1, label: 'Career Advice' },
    { id: 2, label: 'Resume Tips' },
    { id: 3, label: 'Expert Speak' },
    { id: 4, label: 'News/ Updates' },
  ];

  const handleCategoryClick = useCallback((categoryId: number) => {
    if (categoryId === 0) {
      setSelectedCategoryID(null);
    } else {
      setSelectedCategoryID(categoryId);
    }

    if (blogListRef.current) {
      blogListRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);

  return (
    <>
      <section className="blog-blue tab-part sp-80 tab-card">
        <div className="container">
          <h1 aria-level={1} role={"heading"} className="f-54 w-700 text-center c-fff">Job Insights</h1>
          <ul className="nav nav-pills mb-3 mt-5" id="pills-tab" role="tablist">
            {categories?.map(category => (
              <li className="nav-item" role="presentation" key={category.id}>
                <Button
                  className={`nav-link ${selectedCategoryID === category.id ? 'active' : ''}`}
                  onClick={() => handleCategoryClick(Number(category.id))}
                  style={{ height: 'auto' }}>
                  {category.label}
                </Button>
              </li>
            ))}
          </ul>
        </div>
      </section>

      <Suspense fallback={<div>Loading latest blog...</div>}>
        {latestBlog && (
          <section className="blog-goes">
            <LatestBlogsection latestBlog={latestBlog} />
          </section>
        )}
      </Suspense>

      <section className="blog-part" ref={blogListRef}>
        <Suspense fallback={<div>loading blogs...</div>}>
          <ExploreBlogsSection blogs={blogs} page={page} selectedCategoryID={selectedCategoryID} totalCount={totalCount} />
        </Suspense>
      </section>

      <Suspense fallback={<div>Loading hiring section...</div>}>
        <BlogPageHiringSection user={user} />
      </Suspense>

      <Suspense fallback={<div>Loading subscription form...</div>}>
        <SubscriptionForm />
      </Suspense>
    </>
  );
}
export default React.memo(BlogList);
