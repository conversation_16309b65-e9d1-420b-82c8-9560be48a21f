import React, { useState, useEffect, useContext } from 'react';
import { useRouter } from "next/router";
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { addTeamMembers } from '../../lib/frontendapi';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import SuccessToast from "../../components/Common/showSuccessTostrMessage";
import ErrorToast from "../../components/Common/showErrorTostrMessage";
import { useSession } from 'next-auth/react';
import { getCurrentUserData } from "../../lib/session";
import AuthContext from "@/Context/AuthContext";
export default function AddTeamMembers() {
  const { user } = useContext(AuthContext);
  const { register, setError, handleSubmit, formState: { errors } }: any = useForm();
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const router = useRouter();
  const { data: session } = useSession();
  const [showPopupunsave, setShowPopupunsave] = useState(false);
  const [showPopupunerror, setShowPopupunerror] = useState(false);
  const [showmessage, setShowmessage] = useState([]);
  const [inputFields, setInputFields] = useState([{
    team_member_email: '',
  }]);
  useEffect(() => {
    if (user?.role == 'admin') {
      router.push('/admin/dashboard');
    } else if (user?.role == 'employee') {
      router.push('/employees/dashboard');
    } else if (user?.role == 'employer') {
      router.push('/employer/dashboard');
    } else if (user?.role == 'staff') {
      router.push('/staff/dashboard');
    }
  }, [session, user, router]);
  const addInputField = () => {
    setInputFields([...inputFields, {
      team_member_email: '',
    }])
  }
  const removeInputFields = (index: any) => {
    const rows = [...inputFields];
    rows.splice(index, 1);
    setInputFields(rows);
  }
  const handleChange = (index: any, evnt: any) => {
    const { name, value } = evnt.target;
    const list: any = [...inputFields];
    list[index][name] = value;
    setInputFields(list);
  }
  const submitForm = (event: any) => {
    event.preventDefault();
    setIsLoading(true);
    let company_id = window.localStorage.getItem("temp_company_id");
    let user_id = window.localStorage.getItem("temp_user_id");
    const user_data = {
      company_id: company_id,
      created_by_id: user_id,
      team_members_email: inputFields,
      role: 'staff',
      available_resume_count: 0
    }

    addTeamMembers(user_data)
      .then(res => {
        if (res.status == true) {
          setShowmessage(res.message);
          setShowPopupunsave(true);
          setTimeout(() => {
            setShowPopupunsave(false)
          }, 3000)
          setTimeout(() => {
            router.push('/publishjob');
          }, 2000);

        } else {
          setShowmessage(res.message);
          setShowPopupunerror(true);
          setTimeout(() => {
            setShowPopupunerror(false)
          }, 3000)
        }
      })
      .catch(err => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false); // Reset loading state whether the request succeeds or fails
      });
  }
  const handleLinkClick = () => {
    setIsProcessing(true);
  };
  return (
    <>
      <section className='banner-part-home'>
        <div className='container-fluid'>
          <div className='row'>
            <div className='col-lg-5 col-md-12 bg-form-img'>
              <div className='fixd-left-box'>
                <div className='form-left-text  pt-5 pb-5'>
                  <h3>Start building your dream team with us...</h3>
                  <div className='left-list-form active'>
                    <h4>Add Your Company</h4>
                    <p>Empowering Digital Experiences through Innovative Web Solutions.</p>
                  </div>

                  <div className='left-list-form  active'>
                    <h4>Set Company Profile</h4>
                    <p>Crafting Digital Experiences That Inspire and Innovate.</p>
                  </div>

                  <div className='left-list-form  '>
                    <h4>Publish A Job</h4>
                    <p>Unleash Opportunities: Publish Your Job and Build Your Team.</p>
                  </div>
                </div>
              </div>
              <div className='tab-none gap-box'></div>
            </div>
            <div className='col-lg-7 col-md-12'>
              <div className='form-pages  form-left-right-add-sp pt-5 pb-5'>
                <h4>Add team members</h4>
                <p className='f-16-form spacing-left-1920'>Enter the email of your team members and we will send them an invite.</p>
                <form className='form-get mt-4' onSubmit={submitForm}>
                  {
                    inputFields.map((data: any, index) => {
                      const { team_member_email } = data;
                      return (
                        <div className="row my-3" key={index}>
                          <div className="col-sm-10">
                            <div className="form-group">
                              <input type="email" placeholder='<EMAIL>' className='form-control big-input mb-4' onChange={(evnt) => handleChange(index, evnt)} value={team_member_email} name={"team_member_email"} pattern="^([a-zA-Z0-9_\-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([a-zA-Z0-9\-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$" />
                            </div>
                          </div>
                          <div className="col-sm-2">
                            {(inputFields.length !== 1) ? <button className="btn btn-outline-danger danger-sp" onClick={removeInputFields}><i className="fa-solid fa-xmark"></i></button> : ''}
                          </div>
                        </div>
                      )
                    })
                  }
                  {inputFields.length < 5
                    ?
                    <p className='f-16-form add-member'><a href="#" onClick={addInputField}><i className="fa-solid fa-plus"></i> Add Team Member</a></p>
                    :
                    ''
                  }
                  <div className='row'>
                    <div className='col-3'>
                      <Link href="/setcompanyprofile"><button className="btn login  w-100 mt-4" type="button">Back</button></Link>
                    </div>
                    <div className='col-9'>
                      <button className="btn-a primary-size-16 b-0 btn-bg-0055BA w-100 mt-4" disabled={isLoading}>{isLoading ? 'Please wait...' : 'Continue'}</button>
                    </div>
                  </div>
                  <p className='text-center skip mt-4'>
                    {isProcessing ? (
                      <span>Skip</span>
                    ) : (
                      <Link href="/publishjob" onClick={handleLinkClick}>
                        Skip
                      </Link>
                    )}
                  </p>
                </form>
              </div>
            </div>
          </div>
        </div>
      </section>
      <ToastContainer />
      {showPopupunsave &&
          <SuccessToast message={showmessage} />
        }
        {showPopupunerror &&
          <ErrorToast message={showmessage} />
        }
    </>
  )
}
