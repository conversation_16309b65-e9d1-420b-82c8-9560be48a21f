.job-search-top {
  border-radius: 20px;
  margin: 0 auto;

  .fa-solid {
    font-size: 23px;
    color: var(--primary-color);
  }
  .form-container {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  .form-input-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    width: 100%;
  }

  .form-input-container-city {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
  }

  .field-container {
    display: flex;
    align-items: center;
    padding: 0 5px 0 15px;
    overflow: hidden;
    border: 1px solid #b6c8e2;
    border-radius: 8px;
    background: #ffffff;
    height: 48px;
  }
  .job-search-cta {
    background-color: #fdca40;
    color: #151515;
    border-radius: 8px;
    height: 47px;
    display: flex;
    width: 140px;
    padding: 14px 24px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    color: #151515;
    font-family: Archivo;
    font-size: 16px;
    font-weight: 500;
    line-height: 120%;
  }
}
.job-search-home {
  padding: 40px 0 24px 0;
  background-color: #0055ba;
}
.sidebar-filters {
  position: sticky;
  top: 20px;

  .sidebar-filters-form > div {
    background: var(--color_4);
    border: 1px solid var(--color_12);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 20px;
  }
}

.jobs-results {
  .job-item {
    border-radius: 12px;
    border: 1px solid #dfdfdf;
    padding: 15px;
    margin-bottom: 20px;
  }
}
.job-title {
  margin: 0;
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_700);
  font-size: var(--font_18);
  line-height: var(--font_18_line-height);
  color: var(--color_9);
}
.company-name {
  color: #0055ba;
  font-size: 16px;
  font-weight: 600;
  line-height: 140%; /* 22.4px */
  margin-top: 4px;
}
.average-base-salary {
  border-radius: 16px;
  border: 1px solid #eee;
  background: #fff;
  margin: 0 0 24px;
  padding: 12px;
}

.average-base-salary h4 {
  color: #747474;
  font-family: var(--opensans-font);
  font-size: 16px;
  font-weight: 600;
  line-height: 140%; /* 22.4px */
  margin: 0 0 8px;
}

.average-base-salary h3 {
  color: #191919;
  font-size: 22px;
  font-weight: 500;
  line-height: 120%; /* 26.4px */
  margin: 0 0 12px;
}

.average-base-salary a {
  color: #0055ba;
  font-family: var(--opensans-font);
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 140%; /* 22.4px */
}

.salary-insights {
  border-radius: 8px;
  border: 1px solid #eee;
  background: #f9f9f9;
  padding: 12px;
}

.salary-insights p {
  color: #747474;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%; /* 22.4px */
  padding-bottom: 8px;
}

.salary-insights h3 {
  color: #191919;
  font-family: Archivo;
  font-size: 22px;
  font-style: normal;
  font-weight: 500;
  line-height: 120%;
  padding-bottom: 12px;
}

.salary-insights a {
  color: #0055ba;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 140%; /* 22.4px */
  text-align: left;
  color: #0055ba;
  cursor: pointer;
}

.no-result-found h3 {
  font-size: 22px;
  font-weight: 500;
  line-height: 26px;
  letter-spacing: 0em;
  text-align: left;
  color: #191919;
  margin: 0 0 8px;
}

.no-result-found p {
  font-size: 16px;
  font-weight: 600;
  line-height: 22px;
  letter-spacing: 0em;
  text-align: left;
  color: #747474;
  margin: 0;
}
.job_title_container {
  flex-wrap: wrap;
  padding-top: 24px;
}
.job_subtitle,
.job_title {
  width: 100%;
  margin-bottom: 10px;
}

.job_subtitle {
  color: #4d4d4d;
  font-size: 16px;
  line-height: 140%;
}

.salary-insights.average-base {
  background: #fff !important;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 32px 0;
  flex-wrap: wrap;
}

.jobs-container {
  display: grid;
  align-items: start;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 12px;
}

.job-listing {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.joblist-cards {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.jobspage {
  padding: 24px 0;
}
.jobspage ul li {
  padding: 2px 4px;
  margin-right: 24px;
  position: relative;
  color: #bababa;
  font-size: 16px;
  line-height: normal;
  font-weight: 400;
}
.jobspage ul li:last-child {
  margin-right: 0;
}
.jobspage ul li:last-child::after {
  display: none;
}
.jobspage ul li::after {
  content: '';
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: -18px;
  background-image: url(/images/Vector.png);
  width: 10px;
  height: 10px;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
}
.jobspage ul li a {
  font-size: 16px;
  line-height: normal;
  font-weight: 400;
  color: #0070f5;
  display: block;
}

.jobs-results button {
  padding: 10px 0;
  font-size: 16px;
  border-radius: 4px;
}

.pagination-tab {
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #cfe5ff;

  li.ant-pagination-item-active {
    background: #cfe5ff !important;
    border-color: #cfe5ff !important;
    color: #0055ba;
  }
}

.pagination-tab ul {
  text-align: center;
}

.sidebar-filters h4 {
  font-size: 16px;
  line-height: 22px;
}

.sidebar-filters h5 {
  font-size: 16px;
  line-height: 22px;
  line-height: 22px;
}
.uae-new {
  padding: 40px 0;
  text-align: center;
}
.uae-new h1 {
  margin: 0;
  font-size: 40px;
  line-height: 120%;
}
.jobs-by-location {
  ul.uae-part {
    margin-bottom: 16px;
    column-count: 1 !important;
  }
}
.salary-insights {
  border-radius: 16px !important;
  padding: 12px !important;
}
.salary-insights h3 {
  font-size: 19px;
}
.no-result-found h3 {
  font-size: 19px;
}
.job-filter-container {
  padding-top: 80px;
}

@media screen and (max-width: 991px) {
  .jobs-container {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .filter-section {
    padding-top: 0;
    padding-bottom: 16px;
  }
  .job_subtitle {
    font-size: 13px !important;
  }
  .form-container {
    flex-direction: column;
  }
  .form-input-container {
    grid-template-columns: repeat(1, 1fr) !important;
  }
  .field-container {
    width: 100%;
    grid-column: span 1 / span 1 !important;
  }
  .job-search-cta {
    width: 100% !important;
  }
  .form-submit-container {
    width: 100%;
  }
}
