import React, {useState, useEffect, useRef, useMemo} from 'react';
import {useRouter} from 'next/router';
import {usePathname} from 'next/navigation';
import Link from 'next/link';
import {Button, Col, Dropdown, Flex, Form, MenuProps, Pagination, Row, Select} from 'antd';
import axios from 'axios';
import {useForm} from 'antd/lib/form/Form';
import {City, Country, Job, PaginationMeta, Sector} from '@/lib/types';
import {getCountries, searchJobs, getSectorsList} from '@/lib/ApiAdapter';
import ErrorHandler from '@/lib/ErrorHandler';
import 'react-phone-input-2/lib/style.css';
import 'react-toastify/dist/ReactToastify.css';
import useWindowDimensions from '@/helpers/useWindowDimensions';
import JobsOffersExapandSearch from '../expandSearch/expandSearch';
import JobsOffersPageFaq from '../Faq/faq';
import styles from './style.module.css';
import TrendingJobs from '../trendingJobs/trendingJobs';
import Image from 'next/image';
import {JobListCard} from '@/components/Jobs/JobListCard';
import {JobDetailCard} from '@/components/Jobs/JobDetailCard';
import {JobFilterGroup} from '@/components/Jobs/JobFilterGroup';
import {getAllCitiesByCountryName} from '@/lib/frontendapi';
import CustomDropdown from '@/components/Common/Dropdown';
import {useHandleSelectSaveToQuery} from '@/hooks/useHandleSelectSaveToQuery';
import {JobDetailSkeleton} from '@/components/Jobs/JobDetailSkeleton';
import {useGetJobLists} from '@/modules/common/query/useGetJobLists';

interface JobOffersPageProps {
  ssrJobs: Job[];
  trendingJobs?: [];
  locationFaq: [];
  jobInCityName?: string;
  cityCountryName?: string;
}

interface Options {
  value: string[];
  label: string;
  hasCalender?: boolean;
}

export type DropdownType =
  | 'dropdown-white'
  | 'slider'
  | 'dropdown-transparent'
  | 'range-picker'
  | 'checkboxDropdown'
  | 'checkboxWithSearch';

export interface filterItems {
  label: string;
  menuProps?: Options[];
  multiSelect?: boolean;
  onSelect?: (value: any) => void;
  width?: string | number;
  style?: React.CSSProperties;
  type?: DropdownType;
}

export default function JobOffersPage({
  ssrJobs,
  trendingJobs,
  locationFaq,
  cityCountryName,
  jobInCityName,
}: JobOffersPageProps) {
  const router = useRouter();
  const query = router.query;

  const [countries, setCountries] = useState<Country[]>();
  const [sectors, setSectors] = useState<[]>();
  const [form] = useForm();
  const windowDimensions: any = useWindowDimensions();
  const pathname = usePathname();
  const splitText = jobInCityName?.split('-in-') || [];
  const cityName = splitText[splitText.length - 1] || '';
  const [cities, setCities] = useState<City[]>();
  const newCityName = cityName.replace(/-/g, ' ');
  const newCityArr = newCityName.split(' ');
  const isMobile = windowDimensions.width < 991;
  const [selectedJob, setSelectedJob] = useState<Job | null>(ssrJobs?.[0] || null);
  let payload = router.query;

  const queryStringValue = (data: string[]) => {
    const queryString = data
      .map((value: string) => {
        return `country=${encodeURIComponent(value)}`;
      })
      .join('&');
    return queryString;
  };
  if (Array.isArray(router.query.country)) {
    let value = queryStringValue(router.query.country);
    payload = {value};
  }

  const {data, isLoading: loading} = useGetJobLists(payload);

  const {data: jobs, trending = [], faq = [], meta: paginationMeta, favorites} = data || {};

  for (let i = 0; i < newCityArr.length; i++) {
    newCityArr[i] = newCityArr[i].charAt(0).toUpperCase() + newCityArr[i].slice(1);
  }
  const finalCityName = newCityArr.join(' ');

  const {handleSelectSaveToQuery} = useHandleSelectSaveToQuery();

  const filterItems: filterItems[] = [
    {
      label: 'Sort By',
      menuProps: [
        {
          label: 'Jobs in last 24 hours',
          value: ['24'],
        },
        {
          label: 'Jobs in last 3 days',
          value: ['3'],
        },
        {
          label: 'Jobs in last 7 days',
          value: ['7'],
        },
        {
          label: 'Jobs in last 14 days',
          value: ['14'],
        },
        {
          label: 'All-time',
          value: [''],
        },
      ],
      width: 300,
      onSelect: value => handleSelectSaveToQuery(value, 'sort_by'),
    },
    {
      label: 'Experience',
      menuProps: [
        {
          label: 'Internship / No Experience (0 years)',
          value: ['0', '0'],
        },
        {
          label: 'Entry Level (0 - 1 years)',
          value: ['0', '1'],
        },
        {
          label: 'Junior Level (2 - 3 years)',
          value: ['2', '3'],
        },
        {
          label: 'Mid Level (4 - 5 years)',
          value: ['4', '5'],
        },
        {
          label: 'Senior Level (6 - 8 years)',
          value: ['6', '8'],
        },
        {
          label: 'Lead / Management Level (9 - 11 years)',
          value: ['9', '11'],
        },
        {
          label: 'Executive / Director Level (12+ years)',
          value: ['12', '12'],
        },
      ],
      width: 300,
      onSelect: value => handleSelectSaveToQuery(value, 'experience'),
    },
    {
      label: 'job Type',
      menuProps: [
        {
          label: 'Full Time',
          value: ['fulltime'],
        },
        {
          label: 'Part Time',
          value: ['parttime'],
        },
        {
          label: 'Contract',
          value: ['contract'],
        },
        {
          label: 'Freelance',
          value: ['freelance'],
        },
      ],
      multiSelect: true,
      onSelect: value => handleSelectSaveToQuery(value, 'job_type'),
      style: isMobile
        ? {
            right: 0,
          }
        : {
            left: 0,
          },
    },
    {
      label: 'Location',
      menuProps:
        countries?.map((country: Country) => {
          return {label: country.country_name, value: [country.country_name]};
        }) || [],
      onSelect: value => handleSelectSaveToQuery(value, 'country'),
    },
    {
      label: 'Sector',
      menuProps: sectors?.map((sector: Sector) => {
        return {label: sector.sector_name, value: [sector.sector_name]};
      }),
      onSelect: value => handleSelectSaveToQuery(value, 'keywords'),
      multiSelect: true,
      width: 240,
    },
    {
      label: 'Salary',
      width: 224,
      onSelect(value: {salary: number[]; currency: string}) {
        const filteredOptions = {
          ...router.query,
          salary: value.salary,
          currency: value.currency,
        };

        if (value.salary && value.currency) {
          router
            .push(
              {
                pathname: '/jobs-in-gulf',
                query: filteredOptions,
              },
              undefined,
              {shallow: false},
            )
            .then();
        }
      },
      style: isMobile
        ? {
            right: 0,
          }
        : {
            left: 0,
          },
    },
  ];

  useEffect(() => {
    const cancelTokenSource = axios.CancelToken.source();
    getSectorsList()
      .then((res: any) => {
        setSectors(res);
      })
      .catch(error => {});
    getCountries(undefined, undefined, cancelTokenSource)
      .then(res => {
        setCountries(res);
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });

    // setLoading(true);
    return cancelTokenSource.cancel;
  }, []);

  useEffect(() => {
    getAllCitiesByCountryName(cityCountryName)
      .then(res => {
        if (res.status == true) {
          setCities(res.data);
        } else {
          setCities([]);
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
    // return cancelTokenSource.cancel;
  }, [router.query]);

  const submitSearchForm = (values: any) => {
    const filteredOptions = Object.keys(values)
      .filter(function (k) {
        return values[k] != null;
      })
      .reduce(function (acc: any, k) {
        acc[k] = values[k];
        return acc;
      }, {});
    router
      .push(
        {
          pathname: '/jobs-in-gulf',
          query: filteredOptions,
        },
        undefined,
        {shallow: false},
      )
      .then();
    return;
  };

  const handleNavigateSalary = () => {
    router.push('/salaries');
  };

  const arr = [];
  const len = jobs?.length;
  for (let i = 0; i < (len ?? 0); i++) {
    arr.push({
      '@type': 'ListItem',
      position: i,
      url: process.env.NEXT_PUBLIC_BASE_URL + '/job/' + jobs?.[i].job_slug,
    });
  }

  const filterJobDetails = selectedJob;
  const handleJobSelection = (job: Job) => {
    setSelectedJob(job);
  };

  const routerKeyWord = useMemo(() => {
    if (router.query.hasOwnProperty('keywords')) {
      return router.query.keywords;
    } else {
      return null;
    }
  }, [router]);

  useEffect(() => {
    form.setFieldsValue({
      keywords: routerKeyWord,
    });
  }, [router.query]);

  useEffect(() => {
    const job = jobs?.find(job => job.job_slug === router.query.job);
    if (job) {
      setSelectedJob(job);
      return;
    }
    setSelectedJob(jobs?.[0] || null);
  }, [jobs]);

  useEffect(() => {
    if (selectedJob && isMobile) {
      window.scrollTo(0, 0);
    }
  }, [selectedJob]);
  return (
    <>
      {(!filterJobDetails || windowDimensions.width >= 991) && (
        <>
          <section className={` ${styles['job-search-home']}`}>
            <div className="container max-search">
              <div className={styles['job-search-top']}>
                {!loading && (
                  <Form
                    form={form}
                    onFinish={submitSearchForm}
                    size={'large'}
                    initialValues={router.query}
                    className={styles['form-container']}>
                    <div
                      className={`${styles['form-input-container']} ${
                        jobInCityName && cityCountryName && styles['form-input-container-city']
                      }`}>
                      <div
                        className={styles['field-container']}
                        style={{
                          gridColumn: 'span 2 / span 2',
                        }}>
                        <img src="/icons/search.svg" alt="search" width={21} height={21} />
                        <Form.Item name={'keywords'} noStyle>
                          <Select
                            allowClear
                            bordered={false}
                            style={{width: '100%'}}
                            showSearch
                            optionFilterProp={'label'}
                            placeholder={'Job Title, Keyword, Company or Phrase'}
                            options={sectors?.map((sector: Sector) => {
                              return {value: sector.sector_name, label: sector.sector_name};
                            })}
                            value={routerKeyWord}
                          />
                        </Form.Item>
                      </div>
                      <div className={styles['field-container']}>
                        <img src="/icons/location.svg" alt="location" width={21} height={26} />
                        <Form.Item
                          name={'country'}
                          noStyle
                          initialValue={finalCityName === 'Uae' ? 'United Arab Emirates' : cityCountryName}>
                          <Select
                            allowClear
                            bordered={false}
                            style={{width: '100%'}}
                            showSearch
                            optionFilterProp={'label'}
                            placeholder={'Select country'}
                            options={countries?.map((country: any) => {
                              return {value: `${country.country_name.toString()}`, label: country.country_name};
                            })}
                          />
                        </Form.Item>
                      </div>
                      {jobInCityName && cityCountryName && (
                        <div className={styles['field-container']}>
                          <img src="/icons/location.svg" alt="location" width={21} height={26} />
                          <Form.Item name={'city'} noStyle initialValue={finalCityName === 'Uae' ? '' : finalCityName}>
                            <Select
                              style={{width: '100%'}}
                              bordered={false}
                              showSearch
                              title={'Select a country to see cities'}
                              placeholder={'Select city'}
                              options={cities?.map((city: any) => {
                                return {value: city.city_name.toString(), label: city.city_name};
                              })}
                            />
                          </Form.Item>
                        </div>
                      )}
                    </div>
                    <div className={styles['form-submit-container']}>
                      <Button htmlType={'submit'} type={'primary'} block className={styles['job-search-cta']}>
                        Find Jobs
                      </Button>
                    </div>
                  </Form>
                )}
              </div>
            </div>
          </section>

          <div className={styles['jobspage']}>
            <div className="container">
              <ul>
                <li>
                  <Link href="/">Jobs</Link>
                </li>
              </ul>
            </div>
          </div>
          <div>
            <TrendingJobs trendJobs={trending ?? []} submitSearchForm={submitSearchForm} />
          </div>
          <section>
            <div className="container">
              <Flex justify={'space-between'} className={styles['job_title_container']}>
                {router.query.hasOwnProperty('keywords') || router.query.hasOwnProperty('country') ? (
                  <h2 className={styles['job_title']}>{paginationMeta?.total} Jobs Found in Gulf</h2>
                ) : (
                  <></>
                )}

                {paginationMeta && paginationMeta.from && paginationMeta.to && (
                  <h2 className={styles['job_subtitle']}>
                    Displaying {paginationMeta?.from}-{paginationMeta?.to}
                  </h2>
                )}
              </Flex>

              <div className={styles['filter-section']}>
                {filterItems.map((item, index) => {
                  return (
                    <CustomDropdown
                      key={index}
                      item={item}
                      onSelect={
                        item.onSelect
                          ? (value: string[]) => {
                              item.onSelect?.(value);
                            }
                          : () => {}
                      }
                      variant={item.label === 'Salary' ? 'slider' : 'dropdown-white'}
                      countries={countries}
                    />
                  );
                })}
              </div>
            </div>
          </section>
        </>
      )}

      <section className={`${styles['jobs-container']} container`}>
        {(windowDimensions.width >= 991 || (!filterJobDetails && windowDimensions.width < 991)) && (
          <>
            <div className={styles['job-listing']}>
              <div className={styles['salary-insights']}>
                <p>What's your earning potential?</p>
                <h3>Find out what your skills are worth!</h3>
                <a
                  href="javascript:void(0)"
                  onClick={() => {
                    handleNavigateSalary();
                  }}>
                  Try Salary Insights <img src="images/arrow.svg" alt="" />
                </a>
              </div>

              <div className={styles['joblist-cards']}>
                {jobs &&
                  jobs.length > 0 &&
                  jobs.map((job: Job, index: number) => {
                    return (
                      <JobListCard
                        index={index}
                        job={job}
                        jobInCityName={pathname.substring(1)}
                        key={index}
                        onClick={() => handleJobSelection(job)}
                        selectedJob={selectedJob}
                        setSelectedJob={setSelectedJob}
                        jobs={jobs ?? []}
                        favorites={favorites}
                      />
                    );
                  })}
              </div>

              <div className={styles['pagination-tab']}>
                {paginationMeta && (
                  <Pagination
                    onChange={page => {
                      submitSearchForm({...router.query, page, job: undefined});
                    }}
                    total={paginationMeta.total}
                    current={paginationMeta.current_page}
                    pageSize={paginationMeta.per_page}
                    showSizeChanger={false}
                  />
                )}
              </div>

              <div>{<JobsOffersExapandSearch />}</div>
            </div>
          </>
        )}
        {loading ? (
          windowDimensions.width > 991 && <JobDetailSkeleton />
        ) : filterJobDetails ? (
          <JobDetailCard
            job={jobs?.find(job => job.job_slug === router.query.job) || selectedJob}
            setSelectedJob={setSelectedJob}
            jobs={jobs}
            favorites={favorites}
          />
        ) : (
          <></>
        )}
      </section>

      {/* Faq */}
      <JobsOffersPageFaq
        submitSearchForm={submitSearchForm}
        countries={countries}
        jobs={jobs}
        locationFaq={faq}
        cityName={'Gulf'}
        skillJobInCityName={'jobs in Gulf'}
      />
      {/* Faq */}
      <div
        className={
          router.query.hasOwnProperty('keywords') || router.query.hasOwnProperty('country')
            ? styles['job-filter-container']
            : ''
        }>
        <JobFilterGroup countries={countries} sectors={sectors} />
      </div>
    </>
  );
}
