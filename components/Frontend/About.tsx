import React, {useState, useEffect} from 'react';
import {getCurrentUserData} from '../../lib/session';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import Link from 'next/link';
import 'react-toastify/dist/ReactToastify.css';
import SubscriptionForm from '../Common/SubscriptionForm';
import Image from 'next/image';

export default function About() {
  const current_user_data: any = getCurrentUserData();

  useEffect(() => {
    const current_user_data: any = getCurrentUserData();
  }, []);

  return (
    <>
      <section className="banner-part-home sp-80">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-6 col-md-12">
              <div className="text-box-width-cover">
                <h1 className="tab-m-0">About</h1>
                <p className="font-banner-26 mb-4">
                  Our innovative project aims to bridge the gap between job seekers and employment opportunities.
                </p>
                {current_user_data.id ? (
                  current_user_data.role == 'superadmin' || current_user_data.role == 'admin' ? (
                    <Link href="/admin/dashboard">
                      <button className="btn-a primary-size-22 btn-bg-0055BA mr-1 tab-w-100">Create Profile</button>
                    </Link>
                  ) : current_user_data.role == 'employee' ? (
                    <Link href="/employees/myprofile/profile">
                      <button className="btn-a primary-size-22 btn-bg-0055BA mr-1 tab-w-100">Create Profile</button>
                    </Link>
                  ) : (
                    <Link href="/employer/company/profile">
                      <button className="btn-a primary-size-22 btn-bg-0055BA mr-1 tab-w-100">Create Profile</button>
                    </Link>
                  )
                ) : (
                  <Link href="/auth/signup/[[...slug]]">
                    <button className="btn-a primary-size-22 btn-bg-0055BA mr-1 tab-w-100">Create Profile</button>
                  </Link>
                )}
                <Link href="/jobs-in-gulf">
                  <button className="btn-a border-primary-size-22 border-0055BA tab-w-100 tab-m-t-b">
                    Explore Jobs
                  </button>
                </Link>
              </div>
            </div>
            <div className="col-lg-6 col-md-12">
              <img
                src={process.env.NEXT_PUBLIC_BASE_URL + 'images/home/<USER>'}
                alt="Frame 17"
                className="m-none"
                width={821}
                height={650}
                loading="lazy"
                layout="responsive"
              />
            </div>
          </div>
        </div>
      </section>
      <SubscriptionForm />
    </>
  );
}
