import React, {useState, useEffect} from 'react';
import {getUserDetailsslug, updatebackgroundImage, sendMessage, getFirstMessageCheckCount} from '../../lib/frontendapi';
import {getCurrentUserData} from '../../lib/session';
import PopupModal from '../../components/Common/PopupModal';
import {toast} from 'react-toastify';
import {useRouter} from 'next/router';
import 'react-toastify/dist/ReactToastify.css';
import {useForm} from 'react-hook-form';

import SuccessToast from '../../components/Common/showSuccessTostrMessage';
import ErrorToast from '../../components/Common/showErrorTostrMessage';
import Image from 'next/image';

interface Education {
  education_title: string;
  degree: string;
  start_date: string;
  end_date: string;
  currently_study_here: boolean;
  your_score: string;
  max_score: string;
}
interface Work {
  title: string;
  company: string;
  start_date: string;
  end_date: string;
  currently_study_here: boolean;
  description: string;
}
interface Portfolio {
  title: string;
  portfolio_link: string;
  start_date: string;
  end_date: string;
  present: boolean;
  description: string;
}
interface skill {
  skills: string;
  id: string;
}
interface language {
  language: string;
  proficiency: string;
}

export default function CandidateProfile(props: any) {
  let cpId = props.slug;
  const {
    register,
    formState: {errors},
    handleSubmit,
  } = useForm({mode: 'onChange'});
  const [userdata, setuserdata]: any = useState('');
  const [workExperiance, setWorkExperiance] = useState<Work[]>([]);
  const [education, setEducation] = useState<Education[]>([]);
  const [portfolio, setPortfolio] = useState<Portfolio[]>([]);
  const [language, setlanguage] = useState<language[]>([]);
  const [skills, setSkill] = useState<skill[]>([]);
  const router = useRouter();
  const [current_user_role, setCurrentRole] = useState('');
  const [modalConfirm7, setModalConfirm7] = useState(false);
  const [editCompanyLogo, setEditCompanyLogo] = useState('');
  const [modalCandidateMessagePopup, setModalCandidateMessagePopup] = useState(false);
  const [messageDesc, setMessageDesc] = useState('');
  const [firstMessageCount, setFirstMessageCount] = useState(0);

  const [showPopupunsave1, setShowPopupunsave1] = useState(false);
  const [showPopupunerror, setShowPopupunerror] = useState(false);
  const [showmessage, setShowmessage] = useState('');

  const modalConfirmOpen7 = () => {
    setModalConfirm7(true);
  };
  const modalConfirmClose7 = () => {
    setModalConfirm7(false);
  };

  useEffect(() => {
    const current_user_data: any = getCurrentUserData();
    current_user_data.role ? setCurrentRole(current_user_data.role) : setCurrentRole('');
    getUserDetailsslug(cpId)
      .then(res => {
        if (res.status == true) {
          setuserdata(res.user);
          setWorkExperiance(res.work_experience);
          setEducation(res.education);
          setSkill(res.skills);
          setlanguage(res.language);
          setPortfolio(res.portfolio);
        }
      })
      .catch(err => {
        console.log(err);
      });
    const data = {sender_id: current_user_data.id, receiver_id: userdata.id};
    getFirstMessageCheckCount(data)
      .then(res => {
        if (res.status == true) {
          setFirstMessageCount(res.total_message_count);
        } else {
          setFirstMessageCount(0);
        }
      })
      .catch(err => {
        console.log(err);
      });
  }, [cpId, userdata]);

  const handleContactNowClick = (phoneNumber: any) => {
    window.open(`tel:${phoneNumber}`);
  };

  const birthdate = userdata.date_of_birth;

  function calculateAge(birthdate: any) {
    const currentDate = new Date();
    const birthDate = new Date(birthdate);

    let age = currentDate.getFullYear() - birthDate.getFullYear();

    // Check if the current month and day is before the birth month and day
    if (
      currentDate.getMonth() < birthDate.getMonth() ||
      (currentDate.getMonth() === birthDate.getMonth() && currentDate.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    return age;
  }
  const age = calculateAge(birthdate);

  const getJobStatusText = () => {
    switch (userdata.job_status) {
      case 'ready_to_interview':
        return 'Ready To Interview';
      case 'not_looking':
        return 'Not Looking';
      case 'open_to_offer':
        return 'Open To Offer';
      default:
        return 'Ready To Interview';
    }
  };

  const submitUploadAdminImage = (e: any) => {
    e.preventDefault();
    const current_user_data: any = getCurrentUserData();
    const userId = current_user_data.id;
    const profileImage = editCompanyLogo[0];

    if (!profileImage) {
      setShowmessage('Please select a file.');
      setShowPopupunerror(true);
      setTimeout(() => {
        setShowPopupunerror(false);
      }, 3000);
      return;
    }

    updatebackgroundImage(userId, profileImage)
      .then(res => {
        if (res.status === true) {
          setShowmessage(res.message);
          setShowPopupunsave1(true);
          setTimeout(() => {
            setShowPopupunsave1(false);
          }, 3000);
          setTimeout(() => {
            modalConfirmClose7();
          }, 3000);
          setTimeout(function () {
            window.location.reload();
          }, 3000);
        } else {
          setShowmessage(res.message);
          setShowPopupunerror(true);
          setTimeout(() => {
            setShowPopupunerror(false);
          }, 3000);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  const modalConfirmCandidateMessagePopupOpen = () => {
    setModalCandidateMessagePopup(true);
  };
  const modalConfirmCandidateMessagePopupClose = () => {
    setModalCandidateMessagePopup(false);
  };
  const submitMessageForm = (data: any) => {
    const current_user_data: any = getCurrentUserData();
    let applicants_id = $('.hd_applicants_id').val();
    let candidate_id = $('.hd_candidate_id').val();
    let job_id = $('.hd_job_id').val();
    let current_user_id = current_user_data.id;
    const datas = {
      applicants_id: applicants_id,
      candidate_id: candidate_id,
      job_id: job_id,
      current_user_id: current_user_id,
      message_title: null,
      message_description: data.message_desc,
      attachment_path: null,
      message_type: 'contact',
      message_status: 'unread',
    };
    sendMessage(datas)
      .then(res => {
        if (res.status == true) {
          toast.success(res.message, {
            position: toast.POSITION.TOP_RIGHT,
            closeButton: true,
            hideProgressBar: false,
            style: {
              background: '#dcf2ea',
              color: '#0c5a14',
              '--toastify-icon-color-success': '#3D9F79',
              maxWidth: '300px',
              padding: 0,
              fontSize: '15px',
              fontFamily: 'var(--opensans-font)',
            } as React.CSSProperties,
            progressStyle: {background: '#dcf2ea'},
          });
          modalConfirmCandidateMessagePopupClose();
        } else {
          setShowmessage(res.message);
          setShowPopupunerror(true);
          setTimeout(() => {
            setShowPopupunerror(false);
          }, 3000);
        }
      })
      .catch(err => {
        setShowmessage(err);
        setShowPopupunerror(true);
        setTimeout(() => {
          setShowPopupunerror(false);
        }, 3000);
      });
  };

  return (
    <>
      <section className="candidate-profile ">
        <div className="container">
          <div className="pog-r">
            {userdata.background_banner_image ? (
              <img
                src={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/userbannerImage/' + userdata.background_banner_image}
                alt="alan"
              />
            ) : (
              <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/alan.jpg'} alt="alan" className="w-100" />
            )}
            {current_user_role == 'employee' ? (
              <i className="fa-solid fa-pencil edit-banner" onClick={modalConfirmOpen7}></i>
            ) : (
              ''
            )}
          </div>
        </div>
      </section>

      <section className="software-part">
        <div className="container">
          <div className="work-experience-fieild pt-1">
            <div className="row">
              <div className="col-lg-3 col-md-3 img-size-profile">
                <div className="dash-profile-img m-auto over-h-none text-center profile-img-set">
                  {userdata.profile_image ? (
                    <img
                      src={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/userprofileImg/' + userdata.profile_image}
                      alt="Avatars-2"
                    />
                  ) : (
                    <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/Avatars-1.png'} alt="Avatars-4" />
                  )}
                </div>

                {/* <button className="btn-a primary-size-16 btn-bg-3D9F79 w-100 tab-add-sp mt-1 "> */}
                <button
                  className={`btn-a primary-size-16 w-100 tab-add-sp mt-1 img-r-sp ${
                    userdata.job_status === 'ready_to_interview'
                      ? 'btn-bg-3D9F79 p-2'
                      : userdata.job_status === 'open_to_offer'
                        ? 'btn-bg-D57B11'
                        : userdata.job_status === 'not_looking'
                          ? 'btn-bg-D04E4F'
                          : ''
                  }`}>
                  <img
                    src={
                      process.env.NEXT_PUBLIC_BASE_URL +
                      (userdata.job_status === 'ready_to_interview'
                        ? 'images/icon-1.png'
                        : userdata.job_status === 'not_looking'
                          ? 'images/icon-3.png'
                          : userdata.job_status === 'open_to_offer'
                            ? 'images/icon-2.png'
                            : '')
                    }
                    alt="icon-1"
                    className="w-16"
                  />

                  {getJobStatusText()}
                </button>

                {userdata.showcontact_no === 1 &&
                  (current_user_role === 'employer' ? (
                    firstMessageCount > 0 ? (
                      <a href={'/employer/messages/inbox/' + userdata.id}>
                        <button className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp mt-3 ">
                          Contact Now
                        </button>
                      </a>
                    ) : (
                      <button
                        className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp mt-3 "
                        onClick={modalConfirmCandidateMessagePopupOpen}>
                        Contact Now
                      </button>
                    )
                  ) : (
                    <button
                      className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp mt-3 "
                      onClick={() => handleContactNowClick(userdata.contact_no)}>
                      Contact Now
                    </button>
                  ))}
                <PopupModal
                  show={modalCandidateMessagePopup}
                  handleClose={modalConfirmCandidateMessagePopupClose}
                  customclass={'modal-lg  header-remove body-sp-0'}>
                  <div className="popup-body">
                    <p className="f-31 c-191919 text-left">Message {userdata.name}</p>
                    <hr className="hr-line"></hr>
                    <div className="form-experience-fieild">
                      <form className="form-experience-fieild" onSubmit={handleSubmit(submitMessageForm)}>
                        <input type="hidden" name="hd_candidate_id" className="hd_candidate_id" value={userdata.id} />
                        <input type="hidden" name="hd_job_id" className="hd_job_id" value="" />
                        <input type="hidden" name="hd_applicants_id" className="hd_applicants_id" value="" />
                        <p className="f-12 c-2C2C2C">Your Message</p>
                        <textarea
                          placeholder="Your Message"
                          className="fild-des"
                          {...register('message_desc', {required: true})}
                          onChange={(e: any) => setMessageDesc(e.target.value)}></textarea>
                        {errors.message_desc && errors.message_desc.type === 'required' && (
                          <p className="text-danger" style={{textAlign: 'left'}}>
                            Message Field is required.
                          </p>
                        )}
                        <div className="text-right mt-3">
                          <div className="row">
                            <div className="col-4">
                              <button className="cancel  w-100" onClick={modalConfirmCandidateMessagePopupClose}>
                                Cancel
                              </button>
                            </div>
                            <div className="col-8">
                              <button className="save w-100" type="submit">
                                Send
                              </button>
                            </div>
                          </div>
                        </div>
                      </form>
                    </div>
                  </div>
                </PopupModal>
                <button
                  className="download mt-3 w-100 max-100"
                  onClick={() => {
                    if (userdata.resume_pdf_path) {
                      router.push(
                        process.env.NEXT_PUBLIC_IMAGE_URL + 'images/employee/resume/' + userdata.resume_pdf_path,
                      );
                    } else {
                      setShowmessage('"User has not uploaded a resume yet."');
                      setShowPopupunerror(true);
                      setTimeout(() => {
                        setShowPopupunerror(false);
                      }, 3000);
                      //toast.error("User has not uploaded a resume yet.");
                    }
                  }}>
                  <i className="fa-solid fa-download"></i> View Resume
                </button>
              </div>
              <div className="col-lg-9 col-md-9">
                <div className="text-right link-right-icons">
                  <p>
                    {userdata.website_url ? (
                      <a href={userdata.website_url}>
                        <i className="fa-solid fa-globe"></i>
                      </a>
                    ) : null}
                    {userdata.linkedin_link ? (
                      <a href={userdata.linkedin_link}>
                        <i className="fa-brands fa-linkedin"></i>
                      </a>
                    ) : null}
                    {userdata.facebook_link ? (
                      <a href={userdata.facebook_link}>
                        <i className="fa-brands fa-facebook"></i>
                      </a>
                    ) : null}
                    {userdata.twitter_link ? (
                      <a href={userdata.twitter_link}>
                        <i className="fa-brands fa-x-twitter"></i>
                      </a>
                    ) : null}
                    {userdata.instagram_link ? (
                      <a href={userdata.instagram_link}>
                        <i className="fa-brands fa-instagram"></i>
                      </a>
                    ) : null}
                    {/* <FacebookShareButton
                        url={'http://localhost:3000'} >
                        <FacebookIcon size={32} round />
                      </FacebookShareButton>
                      <RedditShareButton
                        url={'http://localhost:3000'} >
                        <RedditIcon size={32} round />
                      </RedditShareButton>
                      <WhatsappShareButton
                        url={'http://localhost:3000'} >
                        <WhatsappIcon size={32} round />
                      </WhatsappShareButton>
                      <LinkedinShareButton
                        url={'http://localhost:3000'} >
                        <LinkedinIcon size={32} round />
                      </LinkedinShareButton> */}
                  </p>
                </div>
                <div className="row">
                  <div className="col-sm-12 col-12 m-center">
                    <h4 className="em-name f-54">{userdata.name}</h4>
                    <h5 className="em-work f-37 w-500">
                      {userdata.current_position} {userdata.company ? `@${userdata.company}` : ''}
                    </h5>
                    <ul className="skills mt-1 mb-1">
                      <li>
                        {userdata.degree ? (
                          <p className="f-16 c-999999">
                            <i className="fa-solid fa-graduation-cap"></i> {userdata.degree}
                          </p>
                        ) : (
                          ''
                        )}
                      </li>
                      {userdata.isShowEmail === 1 && (
                        <li>
                          <p className="f-16 c-999999">
                            <i className="fa-regular fa-envelope"></i>{' '}
                            <a href={'mailto:' + userdata.email}>{userdata.email}</a>
                          </p>
                        </li>
                      )}

                      {userdata.showcontact_no === 1 && (
                        <li>
                          <p className="f-16 c-999999">
                            <i className="fa-solid fa-phone-volume"></i>{' '}
                            <a href={'https://wa.me/' + userdata.contact_no}>{userdata.contact_no}</a>
                          </p>
                        </li>
                      )}
                    </ul>

                    <ul className="skills mb-4">
                      <li>
                        <p className="f-16 c-999999">
                          <i className="fa-solid fa-id-badge"></i> Age: {age}
                        </p>
                      </li>
                      <li>
                        <p className="f-16 c-999999">
                          <i className="fa-regular fa-circle-user"></i> {userdata.gender}{' '}
                        </p>
                      </li>
                      <li>
                        <p className="f-16 c-999999">
                          <i className="fa-solid fa-location-dot"></i> {userdata.country_name}
                        </p>
                      </li>
                    </ul>
                  </div>
                  <div className="col-12">
                    {userdata.bio ? (
                      <>
                        <p className="f-16 c-000">About</p>
                        <p className="f-18 w-400 c-4D4D4D">{userdata.bio}</p>
                      </>
                    ) : (
                      ''
                    )}
                  </div>
                </div>
                <br />
                {workExperiance.length > 0 ? <p className="f-16 c-000 mb-0">Work Experience</p> : ''}

                {workExperiance.map((work, index) => (
                  <div key={index}>
                    <p className="f-22 w-700 c-2C2C2C mt-2 mb-1">{work.title}</p>
                    <p className="f-18 c-0055BA w-600 mb-2">{work.company}</p>
                    <p className="f-16 c-999999 mb-2">
                      {new Date(work.start_date).toLocaleString('en-US', {month: '2-digit', year: 'numeric'})} -{' '}
                      {new Date(work.end_date).toLocaleString('en-US', {month: '2-digit', year: 'numeric'})}
                    </p>
                    <p className="f-18 c-4D4D4D w-400"> {work.description}</p>
                  </div>
                ))}

                <br />
                {/* <p className="f-16 c-000 ">Education</p> */}
                {education.length > 0 ? <p className="f-16 c-000">Education</p> : ''}
                {education.map((edu, index) => (
                  <div className="row" key={index}>
                    <div className="col-sm-6 mt-2">
                      <p className="f-22 mt-4 m-sp-0 w-700 c-2C2C2C">{edu.education_title}</p>
                      <p className="f-18 c-0055BA w-600  mb-2">{edu.degree}</p>
                      <p className="f-16 c-999999 mb-2">
                        {new Date(edu.start_date).toLocaleString('en-US', {month: '2-digit', year: 'numeric'})} -{' '}
                        {new Date(edu.end_date).toLocaleString('en-US', {month: '2-digit', year: 'numeric'})}
                      </p>
                      <p className="f-16 c-999999">
                        Scored: {edu.your_score}/{edu.max_score}
                      </p>
                    </div>
                  </div>
                ))}

                <br />
                {skills.length > 0 ? <p className="f-16 c-000 ">Skills</p> : ''}

                <ul className="skills skills-f-18 mt-3">
                  {skills.map((ski, index) => (
                    <li key={index}>
                      <p className="cat">{ski.skills}</p>
                    </li>
                  ))}
                </ul>

                <br />
                {portfolio.length > 0 ? <p className="f-16 c-000 ">Portfolio/Projects</p> : ''}

                {portfolio.map((port, index) => (
                  <div key={index}>
                    <p className="f-22 mt-2 m-sp-0 w-700 c-2C2C2C">{port.title}</p>
                    <p className="f-18 c-0055BA w-600  mb-2">
                      <i className="fa-solid fa-link"></i>{' '}
                      <a href={port.portfolio_link} target="_blank">
                        {port.portfolio_link}
                      </a>
                    </p>
                    <p className="f-16 c-999999 mb-2">
                      {new Date(port.start_date).toLocaleString('en-US', {month: '2-digit', year: 'numeric'})} -{' '}
                      {new Date(port.end_date).toLocaleString('en-US', {month: '2-digit', year: 'numeric'})}
                    </p>
                    <p className="f-16  c-4D4D4D">{port.description}</p>
                  </div>
                ))}
                <br />
                {language.length > 0 ? <p className="f-16 c-000 ">Languages</p> : ''}

                {language.map((lang, index) => (
                  <div key={index}>
                    <p className="f-22 mt-4 m-sp-0 w-700 c-2C2C2C">{lang.language}</p>
                    <p className="f-18 c-0055BA w-600  mb-2">{lang.proficiency}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="stay-updated  sp-80">
        <div className="container">
          <div className="row">
            <div className="col-sm-6">
              <div className="updated-stay">
                <h2>Stay Updated</h2>
                <p className="font-26">
                  Get latest HR News, Trends, Facts and Advices. We will deliver all of it directly to your inbox.
                </p>
              </div>
            </div>
            <div className="col-sm-6">
              <div className="email-form mt-4 tab-m-0">
                <div className="row">
                  <div className="col-lg-8">
                    <input type="text" placeholder="Enter your email" className="form-white-22-font" />
                  </div>
                  <div className="col-lg-4">
                    <button className="btn-a primary-size-22 btn-bg-fff tab-w-100 tab-sp">Subscribe</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <PopupModal
        show={modalConfirm7}
        handleClose={modalConfirmClose7}
        customclass={'header-remove upload_profile_image_model_dialog body-sp-0'}>
        <div className="popup-body">
          <h5 className="f-26 c-0055BA w-700 text-center mb-4">Upload Your Banner Image</h5>
          <form onSubmit={submitUploadAdminImage}>
            <div className="upload-file" id="upload-file1">
              <div className="file-up">
                <input
                  type="file"
                  name="job_banner_image"
                  id="job_banner_image"
                  onChange={(e: any) => setEditCompanyLogo(e.target.files)}
                  accept="jpg, png"
                />
                <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/cemra.png'} alt="cemra" className="cemra" />
              </div>
              <p className="upload-text">Browse and choose the files you want to upload from your computer.</p>
              <p className="max-size">Maximum upload size is 1MB</p>
            </div>
            <div className="modal-footer">
              <button type="button" className="cancel-btn" data-bs-dismiss="modal" onClick={modalConfirmClose7}>
                Cancel
              </button>
              <button type="submit" className="update-btn">
                Update
              </button>
            </div>
          </form>
        </div>
      </PopupModal>

      {showPopupunsave1 && <SuccessToast message={showmessage} />}
      {showPopupunerror && <ErrorToast message={showmessage} />}
    </>
  );
}
