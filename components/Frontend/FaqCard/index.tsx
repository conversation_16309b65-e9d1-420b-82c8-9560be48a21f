
import Accordion from 'react-bootstrap/Accordion';

export const FAQCard = ({
   index,
   question,
   answer
}: {
   index: string,
   question: string,
   answer: string
}) => {
   return <Accordion defaultActiveKey="0">
      <Accordion.Item eventKey={index}>
         <Accordion.Header bsPrefix='accordion-header-faq'>
            {question}
         </Accordion.Header>
         <Accordion.Body dangerouslySetInnerHTML={{
            __html: answer
         }}>
         </Accordion.Body>
      </Accordion.Item>
   </Accordion>
}
