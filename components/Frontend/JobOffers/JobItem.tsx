import React, {useContext, useEffect, useState} from 'react';
import {Job} from '@/lib/types';
import Image from 'next/image';
import Link from 'next/link';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import {applyJob, toggleFavoriteJob} from '@/lib/frontendapi';
import {notification} from 'antd';
import ErrorHandler from '@/lib/ErrorHandler';
import AuthContext from '@/Context/AuthContext';
import {useRouter} from 'next/router';

dayjs.extend(relativeTime);

interface JobItemProps {
  job: Job;
  isSaved?: boolean;
  onApply?: () => void;
  onSaveJob?: () => void;
}

const JobItem = ({job, onSaveJob, onApply, isSaved = false}: JobItemProps) => {
  const {user} = useContext(AuthContext);
  const [currentSavedStatus, setCurrentSavedStatus] = useState(isSaved);
  const router = useRouter();

  useEffect(() => {
    setCurrentSavedStatus(isSaved);
  }, [isSaved]);

  const handleautoApplyJob = (job: any, a: any, company: number) => {};

  const onToggleFavoriteJob = () => {
    if (!user) {
      notification.info({message: 'Please, login or create and account'});
      router.push('auth/login').then();
      return;
    }
    const data = {job_id: job.id};
    toggleFavoriteJob(data)
      .then((res: any) => {
        notification.success({
          message: currentSavedStatus ? 'Removed from favorites jobs' : 'Saved to favorite jobs',
        });
        setCurrentSavedStatus(!currentSavedStatus);
      })
      .catch((err: any) => {
        ErrorHandler.showNotification(err);
      });
  };

  const autosubmitForm = (jobId: any, userId: any, companyId: any) => {
    const data = {
      company_id: companyId,
      jobpost_by_userid: userId,
      user_id: user?.id,
      job_id: jobId,
      description: '',
      resume_path: 'selectedResume', //resume_path || user.resume_pdf_path ,
      cover_letter: 'selectedCover',
      choice: 'selectedChoice',
      instant_apply: 1,
      name: user?.name,
      contact_no: user?.contact_no,
      gender: user?.gender,
      date_of_birth: user?.date_of_birth,
      where_currently_based: user?.where_currently_based,
    };
    applyJob(data)
      .then(res => {
        notification.success({message: 'Applied'});
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  };

  const handleApplyJob = (jobId: any, company_name: any, userId: any, companyId: any) => {
    /*if (isJobApplied(jobId)) {
      console.log("Job already applied");
    } else {
      setSelectedJobId(jobId);
      setSelectedcompany(company_name);
      setSelectedUserId(userId);
      setcompanyId(companyId);
      modalConfirmOpen2();
    }*/
  };

  let currencyCode: RegExpMatchArray | null = null;
  if (job.monthly_fixed_salary_currency) {
    currencyCode = job.monthly_fixed_salary_currency.match(/(?<=\()(.*)(?=\))/g);
  }

  return (
    <div className="job-item">
      <div className="row">
        <div className="col pr-0 max-w-99  m-m-auto m-none">
          {job.company && (
            <img
              src={job.company.logo ? job.company.logo.thumbnail : '/images/logo-img.png'}
              alt="logo-img"
              className="logo-filter"
              width={78}
              height={78}
              layout="responsive"
            />
          )}
        </div>
        <div className="col">
          <p className="p-18 job-title">
            <Link href={'/job/' + job.job_slug}>{job.job_title}</Link>
          </p>
          <p className="p-14 mt-1 company-name">
            <Link href={'/companies/' + job.company?.company_slug}>{job.company?.company_name}</Link>
          </p>
          <div className="featured-sec">
            {job.is_featured ? (
              <span className="pro">
                Featured
                <img className={'icon'} src={process.env.NEXT_PUBLIC_BASE_URL + 'images/pro.png'} alt="Diamond" />
              </span>
            ) : (
              <span className="pro actively ">
                Actively Hiring <i className="icon fa-solid fa-circle-check"></i>
              </span>
            )}
            <small style={{marginLeft: 10}}>
              {currencyCode} {job.monthly_fixed_salary_min} - {currencyCode} {job.monthly_fixed_salary_max}
            </small>
          </div>

          <ul className="full-time">
            <li>
              <i className="fa-solid fa-business-time sp-right-1" style={{marginRight: '5px'}}></i>
              {job.job_type === 'fulltime'
                ? 'Full-Time'
                : job.job_type === 'parttime'
                  ? 'Part-Time'
                  : job.job_type === 'contract'
                    ? 'Contract'
                    : job.job_type === 'freelance'
                      ? 'Freelance'
                      : ' '}
            </li>
            <li>
              <i className="fa-solid fa-location-dot"></i> {job.country?.country_name}
            </li>
          </ul>
        </div>
        <div className="col-sm-3 text-right m-center">
          {user?.id ? (
            <>
              {job.is_applied ? (
                <Link href="#">
                  <button className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp" disabled>
                    Applied
                  </button>
                </Link>
              ) : Number(user?.unlock_instant_apply) === 1 ? (
                <Link href="#">
                  <button
                    className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp"
                    onClick={() => handleautoApplyJob(job.id, job.user_id, job.company_id)}>
                    {Number(job.unlock_instant_apply) === 1 && Number(job.unlock_instant_apply) === 100 && (
                      <i className="fa-solid fa-bolt"></i>
                    )}{' '}
                    Apply Now
                  </button>
                </Link>
              ) : (
                <Link href="#">
                  <button
                    className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp"
                    onClick={() => handleApplyJob(job.id, job.company?.company_name, job.user_id, job.company_id)}>
                    {Number(job.unlock_instant_apply) === 1 && Number(job.unlock_instant_apply) === 100 && (
                      <i className="fa-solid fa-bolt"></i>
                    )}{' '}
                    Apply Now
                  </button>
                </Link>
              )}
            </>
          ) : (
            <>
              <Link href="/auth/login">
                <button className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp"> Apply Now</button>
              </Link>
            </>
          )}

          <button className="download mt-3 w-100" onClick={onToggleFavoriteJob}>
            <i className="fa-regular fa-bookmark"></i> &nbsp; {currentSavedStatus ? 'Remove save' : 'Save Job'}
          </button>
        </div>
      </div>
      <small className="posted">Posted {dayjs(job.created_at).fromNow()}</small>
    </div>
  );
};

export default JobItem;
