import React, {useState, useEffect, useContext} from 'react';
import {useRouter} from 'next/router';
import Image from 'next/image';
import {Button, Form, Input, Checkbox, notification} from 'antd';
import PhoneInput from 'react-phone-input-2';
import axios from 'axios';
import AuthContext from '@/Context/AuthContext';

export default function CreateNumber() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [redirect, setRedirect] = useState(false);
  const {user} = useContext(AuthContext);

  useEffect(() => {
    console.log('trigger');

    const role = sessionStorage.getItem('signup.role');
    console.log('Role from session storage:', role);
    if (redirect && role) {
      console.log('Role and redirect condition met');
      if (role === 'employee') {
        console.log('Redirecting to /employees/dashboard');
        router.push('/employees/dashboard');
      } else if (role === 'employer') {
        console.log('Redirecting to /employer/dashboard');
        router.push('/employer/dashboard');
      }
    }
  }, [redirect, router]);

  const submitForm = async (data: any) => {
    const userDetails = {
      number: data.phone,
      id: user?.id,
      role: sessionStorage.getItem('signup.role'),
    };
    if (data) {
      axios
        .post('/update-number', userDetails)
        .then(response => {
          console.log('fff', response);
          if (response.status === 200) {
            setRedirect(true);
            // if (sessionStorage.getItem('signup.role') === "employee") {
            //   router.push('/employees/dashboard');
            // } else {
            //   router.push('/employer/dashboard');
            // }
          }
        })
        .catch(error => {
          console.log(error);
          notification.error({
            message: error?.response?.data?.error,
          });
        });
    }
  };
  return (
    <>
      <section className="banner-part-home">
        <div className="container-fluid">
          <div className="row">
            <div className="col-lg-5 col-md-12 tab-none pl-0">
              <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/form-img.png'} alt="form-img" className="w-100" />
            </div>
            <div className="col-lg-7 col-md-12">
              <div className="form-pages top-m-sp  ">
                <h3>Create your Account</h3>
                <Form
                  className="form-get mt-4 frontend-form"
                  layout="vertical"
                  onFinish={submitForm}
                  size={'large'}
                  requiredMark={false}>
                  <Form.Item
                    label="Your contact number"
                    name="phone"
                    rules={[
                      {required: true, message: 'Contact Number is required.'},
                      {min: 10, message: 'Please Enter min length 10.'},
                      {max: 12, message: 'Please Enter max length 12.'},
                    ]}>
                    <PhoneInput country={'ae'} enableSearch />
                  </Form.Item>
                  <Form.Item
                    name="agreeTerms"
                    valuePropName="checked"
                    rules={[
                      {
                        required: true,
                        message: 'You must agree to the Terms and Conditions and Privacy Policy.',
                      },
                    ]}>
                    <Checkbox>
                      By clicking checkbox, you agree to our <a href="#">Terms and Conditions</a> and{' '}
                      <a href="#">Privacy Policy</a>
                    </Checkbox>
                  </Form.Item>
                  <Form.Item>
                    <Button type="primary" htmlType="submit" block loading={loading}>
                      {loading ? 'Please wait..' : 'Submit'}
                    </Button>
                  </Form.Item>
                </Form>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
