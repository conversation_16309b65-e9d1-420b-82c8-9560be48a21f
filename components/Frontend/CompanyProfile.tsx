import React, {useState, useEffect, useContext} from 'react';
import Link from 'next/link';
import {usePathname} from 'next/navigation';
import {Button, notification} from 'antd';
import Image from 'next/image';
import <PERSON>rrorHandler from '@/lib/ErrorHandler';
import JsonLd from '../../components/JsonLd';
import {
  getSingleCompanyUserFollowers,
  saveFollowRequest,
  deleteUnFollowRequest,
  getSingleCompanyFollowersByCompanyId,
} from '@/lib/frontendapi';
import {CompanyProfileProps} from '@/lib/types';
import AuthContext from '@/Context/AuthContext';

import SubscriptionForm from '../Common/SubscriptionForm';
import JobListItem from '../Common/JobListItem';
import Footer from './layouts/footer';

export default function CompanyProfile({company}: CompanyProfileProps) {
  const [imageUrl, setImageUrl] = useState('');
  const [companyFollowers, setCompanyFollowers] = useState('');
  const [companyFollowersCount, setCompanyFollowersCount] = useState('');
  const {user} = useContext(AuthContext);
  const pathname = usePathname();
  useEffect(() => {
    if (company.logo?.source) {
      let url = company.logo?.source;
      // let newUrl = url.replace('/storage/', '');
      setImageUrl(url);
    }
    const data = {
      company_id: company.id,
    };
    getSingleCompanyUserFollowers(data)
      .then(res => {
        if (res.status == true) {
          if (res.companies_follow_count) {
            setCompanyFollowers(res.companies_follow_count);
          } else {
            setCompanyFollowers('');
          }
        } else {
          setCompanyFollowers('');
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
    fetchCompanyFollowersCount(company.id, setCompanyFollowersCount);
  }, [user, company.id]);

  const fetchCompanyFollowersCount = (companyId: any, setCompanyFollowersCount: any) => {
    getSingleCompanyFollowersByCompanyId(companyId)
      .then(res => {
        if (res.status == true) {
          setCompanyFollowersCount(res.count);
        } else {
          setCompanyFollowersCount('');
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  };

  const sendFollowRequest = (companyId: any) => {
    const data = {
      user_id: user?.id,
      company_id: companyId,
    };
    saveFollowRequest(data)
      .then(res => {
        if (res.status == true) {
          notification.success({
            message: res.message,
          });
          fetchCompanyFollowersCount(company.id, setCompanyFollowersCount);
          getSingleCompanyUserFollowers(data)
            .then(res => {
              if (res.status == true) {
                if (res.companies_follow_count) {
                  setCompanyFollowers(res.companies_follow_count);
                } else {
                  setCompanyFollowers('');
                }
              } else {
                setCompanyFollowers('');
              }
            })
            .catch(err => {
              ErrorHandler.showNotification(err);
            });
        } else {
          notification.info({
            message: res.message,
          });
        }
      })
      .catch(err => {
        // notification.error({
        //   message: err.message,
        // });
      });
  };

  const sendDeleteUnFollowRequest = (companyId: any) => {
    const data = {
      user_id: user?.id,
      company_id: companyId,
    };
    deleteUnFollowRequest(data)
      .then(res => {
        if (res.status == true) {
          notification.success({
            message: res.message,
          });
          fetchCompanyFollowersCount(company.id, setCompanyFollowersCount);
          getSingleCompanyUserFollowers(data)
            .then(res => {
              if (res.status == true) {
                if (res.companies_follow_count) {
                  setCompanyFollowers(res.companies_follow_count);
                } else {
                  setCompanyFollowers('');
                }
              } else {
                setCompanyFollowers('');
              }
            })
            .catch(err => {
              ErrorHandler.showNotification(err);
            });
        } else {
          notification.info({
            message: res.message,
          });
        }
      })
      .catch(err => {
        // notification.error({
        //   message: err.message,
        // });
      });
  };

  const arr = [];
  const len = company.jobs?.data.length;
  for (let i = 0; i < len; i++) {
    arr.push({
      '@type': 'ListItem',
      position: i,
      url: process.env.NEXT_PUBLIC_BASE_URL + '/job/' + company.jobs?.data[i].job_slug,
    });
  }

  return (
    <>
      <section className="bg-ebf4ff">
        <div className="container ">
          <section className="candidate-profile " style={{overflow: 'hidden'}}>
            <div className="container">
              <div className="pog-r">
                <img
                  src={'/images/alan.jpg'}
                  alt={company.logo?.name || company.company_name}
                  width={1327}
                  height={227}
                  layout="responsive"
                />
              </div>
            </div>
          </section>
          <section className="software-part">
            <div className="container">
              <div className="work-experience-fieild pt-1">
                <div className="row">
                  <div className="col-lg-3 col-md-3 img-size-profile text-center">
                    <div className="dash-profile-img m-auto over-h-none">
                      <div className="logoCircle">
                        <img
                          // src={imageUrl ? `${process.env.NEXT_PUBLIC_IMAGE_URL}${imageUrl}` : '/images/logo-cir.png'}
                          src={imageUrl ? imageUrl : '/images/logo-cir.png'}
                          alt={company.company_name}
                          className="w-100 mar-m"
                          height={195}
                          width={100}
                        />
                      </div>
                    </div>
                    {user?.role === 'employee' && user?.id !== company.user_id ? (
                      user?.id ? (
                        <>
                          {company.company_contact_no ? (
                            <a href={'tel:+' + company.company_contact_no}>
                              <Button
                                type={'primary'}
                                size={'large'}
                                className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp mt-3 max-button-auto">
                                Contact Now
                              </Button>
                            </a>
                          ) : (
                            ''
                          )}
                          {companyFollowers > '0' ? (
                            <Button
                              type={'primary'}
                              size={'large'}
                              className="download mt-3 w-100 max-button-auto followed_btn"
                              onClick={() => sendDeleteUnFollowRequest(company.id)}>
                              <i className="fa-solid fa-heart"></i> &nbsp; Followed
                            </Button>
                          ) : (
                            <Button
                              type={'primary'}
                              size={'large'}
                              className="download mt-3 w-100 follow_btn max-button-auto"
                              onClick={() => sendFollowRequest(company.id)}>
                              <i className="fa-regular fa-heart"></i> &nbsp; Follow
                            </Button>
                          )}
                        </>
                      ) : (
                        <>
                          <Link href="/auth/login">
                            <Button
                              type={'primary'}
                              size={'large'}
                              className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp mt-3 max-button-auto">
                              Contact Now
                            </Button>
                          </Link>
                          <Link href="/auth/login">
                            <Button type={'primary'} size={'large'} className="download mt-3 w-100 max-button-auto">
                              <i className="fa-regular fa-heart"></i> &nbsp; Follow
                            </Button>
                          </Link>
                        </>
                      )
                    ) : (
                      ''
                    )}
                  </div>
                  <div className="col-lg-7 col-md-9">
                    <div className="text-right link-right-icons">
                      <p className="mt-2">
                        {company.twitter_link && (
                          <Link href={company.twitter_link}>
                            <i className="fa-brands fa-x-twitter"></i>
                          </Link>
                        )}
                        {company.facebook_link && (
                          <Link href={company.facebook_link}>
                            <i className="fa-brands fa-facebook-f"></i>
                          </Link>
                        )}
                        {company.instagram_link && (
                          <Link href={company.instagram_link}>
                            <i className="fa-brands fa-instagram"></i>
                          </Link>
                        )}
                        {company.linkedin_link && (
                          <Link href={company.linkedin_link}>
                            <i className="fa-brands fa-linkedin"></i>
                          </Link>
                        )}
                      </p>
                    </div>
                    <div className="row">
                      <div className="col-sm-12 col-12 m-center mt-4">
                        <h1 className="em-name f-54">{company.company_name}</h1>
                        {company.company_website && (
                          <p className="f-22 c-0070F5">
                            <i className="fa-solid fa-globe"></i>{' '}
                            <a href={company.company_website} target="_blank" rel="nofollow">
                              {company.company_website}
                            </a>
                          </p>
                        )}
                        <ul className="skills mt-1 mb-1">
                          {company.company_email ? (
                            <li>
                              {/* <p className="f-16 c-999999">
                                <i className="fa-regular fa-envelope"></i>{' '}
                                <a href={'mailto:' + company.company_email}>{company.company_email}</a>
                              </p> */}
                            </li>
                          ) : (
                            ''
                          )}
                          {company.company_contact_no ? (
                            <li>
                              <p className="f-16 c-999999">
                                <i className="fa-solid fa-phone-volume"></i>{' '}
                                <a href={'tel:' + company.company_contact_no}>{company.company_contact_no}</a>
                              </p>
                            </li>
                          ) : (
                            ''
                          )}
                        </ul>
                        <ul className="skills mb-4">
                          {company.country?.country_name && (
                            <li>
                              <p className="f-16 c-999999">
                                <i className="fa-solid fa-location-dot"></i> {company.country?.country_name}
                              </p>
                            </li>
                          )}
                        </ul>
                      </div>
                      <div className="row">
                        <div className="col-sm-7">
                          <div className="salary-box mt-4 border-r-0">
                            <h2 className="f-12 c-747474">Sector</h2>
                            <p className="f-22 c-2C2C2C">{company.sector?.sector_name}</p>
                          </div>
                        </div>
                        <div className="col-sm-3">
                          <div className="salary-box mt-4 border-r-0">
                            <h2 className="f-12 c-747474">Company size</h2>
                            <p className="f-22 c-2C2C2C">{company.no_of_employees}</p>
                          </div>
                        </div>
                        <div className="col-sm-2">
                          <div className="salary-box mt-4 border-r-0">
                            <p className="f-12 c-747474">Followers</p>
                            <p className="f-22 c-2C2C2C">{companyFollowersCount ? companyFollowersCount : '0 '}</p>
                          </div>
                        </div>
                      </div>
                      <div className="col-12 mt-4">
                        <h3 className="f-12 c-000">About Our Company</h3>
                        <p
                          className="f-16 w-400 c-4D4D4D open-sans description-des"
                          dangerouslySetInnerHTML={{
                            __html: company.company_description,
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="row mt-5">
                  <div className="col-sm-2"></div>
                  <div className="col-sm-8 col-12">
                    <h3 className="f-22 c-2C2C2C">Job Openings at {company.company_name}</h3>
                    {company.jobs?.data.length > 0 ? (
                      company.jobs?.data.map((jobs_data: any, index: any) => (
                        <JobListItem
                          job={jobs_data}
                          index={index}
                          key={index}
                          jobInCityName={pathname.substring(1)}
                          jobPosting={false}
                        />
                      ))
                    ) : (
                      <div className="filter filter-sp m-center mt-4">
                        <div className="row">
                          <p>No active jobs found</p>
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="col-2"></div>
                </div>
              </div>
            </div>
          </section>
        </div>
        <JsonLd
          data={{
            '@context': 'https://schema.org',
            '@type': 'ItemList',
            itemListElement: arr,
          }}
        />
      </section>
      <SubscriptionForm />
      <Footer />
    </>
  );
}
