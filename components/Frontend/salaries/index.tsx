// @ts-nocheck
import React, {useState, useEffect} from 'react';
import Image from 'next/image';
import {useRouter} from 'next/router';
import Slider from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import SliderRange from 'react-rangeslider';
import Link from 'next/link';
import JobApplyButton from '../../Common/JobApplyButton';
import {JobOffersPageProps} from '../../../lib/types';
import LoadingIndicator from '../../Common/LoadingIndicator/index';

export default function FrontendSalaries({
  resultState,
  similarCareerPaths,
  latestJobsOpenings,
  notFoundMessage,
  name,
  countries,
}: JobOffersPageProps) {
  const router = useRouter();
  const {slug} = router.query;
  if (resultState?.sector) {
    const timestamp = resultState.sector.updated_at;
    const date = new Date(timestamp);
    const day = date.getDate();
    const month = date.toLocaleString('default', {month: 'long'});
    const year = date.getFullYear();
    const formattedDate = `${day} ${month} ${year}`;
    resultState.lastUpdate = formattedDate;
  }
  const value = (data: any) => {
    let countryParam = null;
    let industryParam = null;
    if (slug !== undefined) {
      countryParam = slug[0];
      industryParam = slug[1];
    }
    if (data.country_name) {
      router.push(`/salaries/${data.slug}/${industryParam}`);
    } else {
      router.push(
        `/salaries/${countryParam}/${data.sector_name.replace(/\s+/g, '-').replace(/-+/g, '-').toLowerCase()}`,
      );
    }
  };
  const settings = {
    dots: false,
    infinite: false,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 1,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
          infinite: true,
        },
      },
      {
        breakpoint: 870,
        settings: {
          infinite: true,
          slidesToShow: 2,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 480,
        settings: {
          infinite: true,
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };
  const [selectedOption, setSelectedOption] = useState<string>('Month');
  const handleSelectChange = (event: any) => {
    setSelectedOption(event.target.value);
  };
  const currency = (data: any) => {
    const regex = /\((.*?)\)/;
    const match = data.match(regex);
    return match[1];
  };
  const calculateDisplaySalary = () => {
    let averageSalary = resultState?.salary?.average;
    if (averageSalary != undefined) {
      averageSalary = parseInt(averageSalary);
      if (selectedOption === 'Year') {
        return averageSalary * 12;
      }
      return averageSalary;
    }
    return averageSalary;
  };
  const calculateSalary = () => {
    const minMaxofSalary = {
      min: resultState?.salary?.min,
      max: resultState?.salary?.max,
    };
    if (selectedOption === 'Year') {
      return {
        min: resultState?.salary?.min * 12,
        max: resultState?.salary?.max * 12,
      };
    }
    return minMaxofSalary;
  };
  return (
    <>
      <section className="salaries-main sp-80">
        <div className="container">
          {notFoundMessage != true && Object.entries(resultState).length < 1 && <LoadingIndicator />}
          {notFoundMessage === true && (
            <div className="looking-for">
              <div className="row mb-5">
                <div className="col">
                  <h3>
                    Looking for <span>"{name && name.industryName}"</span> job in{' '}
                    <span>"{name && name.countryName}"</span> ?
                  </h3>
                  <p>We don't have salary data for this job at the moment, but feel free to check out:</p>
                </div>
              </div>
            </div>
          )}
          {Object.entries(resultState).length > 0 && (
            <div className="row">
              <div className="col-lg-8 col-md-8 col-sm-12">
                <div className="salary-based-block mb-4">
                  <h3>
                    <span>{resultState?.sector && resultState?.sector.sector_name}</span> salary in{' '}
                    <span>{resultState?.country && resultState?.country.country_name}</span>
                  </h3>

                  <div className="mb-4 d-flex align-items-center justify-content-between flex-wrap">
                    <span className="last-update">
                      Last updated on {resultState?.lastUpdate && resultState?.lastUpdate}
                    </span>
                    <select className="form-select" name="" id="" onChange={handleSelectChange} value={selectedOption}>
                      <option value="Month">Month</option>
                      <option value="Year">Year</option>
                    </select>
                  </div>
                  <div className="row">
                    <div className="col-lg-4 col-md-4 col-sm-12 mb-4">
                      <div className="average-salary h-100">
                        <h5>
                          Average Base Salary
                          <span>based on {resultState?.salary?.total} salaries</span>
                        </h5>
                        <h4>
                          {currency(resultState?.country.currency)} {calculateDisplaySalary()}
                        </h4>
                      </div>
                    </div>
                    <div className="col-lg-8 col-md-8 col-sm-12 mb-4">
                      <div className="average-range h-100">
                        <div className="average-range-inner">
                          <div className="current-salary">Average: {calculateDisplaySalary()}</div>
                          <SliderRange
                            min={parseInt(calculateSalary().min)}
                            max={parseInt(calculateSalary().max)}
                            step={100}
                            value={calculateDisplaySalary()}
                            orientation="horizontal"
                            ref={null}
                          />
                          <div className="average-bottom d-flex align-items-center justify-content-between">
                            <div>
                              Low: <span>{calculateSalary().min}</span>
                            </div>
                            <div>
                              High: <span>{calculateSalary().max}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <p>
                    The average salary for a {resultState?.sector && resultState?.sector.sector_name} is{' '}
                    {currency(resultState?.country.currency)} {calculateDisplaySalary()} per {selectedOption} in{' '}
                    {resultState?.country && resultState?.country.country_name}. Based on {resultState?.salary?.total}{' '}
                    reported salaries.
                  </p>
                </div>

                <div className="salary-based-block mb-4">
                  <a href="">
                    <h3>
                      <span>{resultState?.sector && resultState?.sector.sector_name}</span> salary based on experience
                    </h3>
                  </a>
                  <div className="mb-4 d-flex align-items-center justify-content-between flex-wrap">
                    <span className="last-update">
                      Last updated on {resultState?.lastUpdate && resultState?.lastUpdate}
                    </span>
                  </div>

                  <div className="row">
                    <div className="col-12">
                      <div className="slider-main">
                        <Slider {...settings}>
                          {resultState?.based_experience &&
                            resultState?.based_experience.map((exp: any) => {
                              return (
                                <div className="experience-by-block" key={exp.someUniqueKey}>
                                  <a href="javascript:void(0)">
                                    {exp.time !== 'any' ? (
                                      <h4>
                                        {exp.time} Years
                                        <span>based on {exp.count} salaries</span>
                                      </h4>
                                    ) : (
                                      <h4>
                                        0 Years
                                        <span>based on {exp.count} salaries</span>
                                      </h4>
                                    )}
                                    <h3>
                                      {currency(resultState?.country.currency)} {exp.average}
                                    </h3>
                                  </a>
                                </div>
                              );
                            })}
                        </Slider>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="salary-based-block mb-4">
                  <a href="">
                    <h3>
                      <span>{resultState?.sector && resultState?.sector.sector_name}</span> salary based on company
                    </h3>
                  </a>
                  <div className="mb-4 d-flex align-items-center justify-content-between flex-wrap">
                    <span className="last-update">
                      Last updated on {resultState?.lastUpdate && resultState?.lastUpdate}
                    </span>
                  </div>
                  <div className="row">
                    <div className="col-12">
                      <div className="slider-main">
                        <Slider {...settings}>
                          {resultState?.based_company?.map((company: any) => {
                            return (
                              <div className="company-block" key={company.company.id}>
                                <a href="javascript:void(0)">
                                  <div className="company-logo">
                                    {company?.company.logo != null ? (
                                      <img
                                        src={company?.company.logo.source}
                                        alt="Company Logo"
                                        width={60}
                                        height={18}
                                      />
                                    ) : (
                                      <img
                                        src="/images/default/dash-icon-4.svg"
                                        alt="Company Logo"
                                        width={60}
                                        height={18}
                                      />
                                    )}
                                  </div>
                                  <h4>
                                    <a
                                      href={`${process.env.NEXT_PUBLIC_BASE_URL}/companies/${company?.company?.company_slug}`}
                                      target="_blank">
                                      {company?.company?.company_name}
                                    </a>
                                  </h4>
                                  <h5>
                                    {currency(resultState?.country.currency)} {company?.average}
                                    <span> /month</span>
                                  </h5>
                                </a>
                              </div>
                            );
                          })}
                        </Slider>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="salary-based-block">
                  <div className="mb-3 d-flex align-items-center justify-content-between">
                    <h3>Popular Searches</h3>
                  </div>
                  <div className="scroll-block">
                    <div className="row">
                      <div className="col-12">
                        <ul className="search-list salaries-search-list">
                          {countries?.map((res: any) => {
                            return (
                              <li
                                key={res.id}
                                onClick={() => {
                                  value(res);
                                }}>
                                <a href="javascript:void(0)">
                                  {resultState?.sector && resultState?.sector.sector_name} job in {res.country_name}
                                </a>
                              </li>
                            );
                          })}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-lg-4 col-md-4 col-sm-12">
                <div className="side-panel mb-5">
                  <h3>Similar Career Paths</h3>
                  {similarCareerPaths?.map((similarCareer: any) => {
                    return (
                      <div className="career-path-block" key={similarCareer?.sector.id}>
                        <h5>
                          {similarCareer?.sector.sector_name}
                          <span>Average base salary</span>
                        </h5>
                        <h4>
                          {parseInt(similarCareer?.salary_average)}
                          <span> /month</span>
                        </h4>
                        <a
                          href="javascript:void(0)"
                          onClick={() => {
                            value(similarCareer?.sector);
                          }}>
                          Explore{' '}
                        </a>
                      </div>
                    );
                  })}
                </div>

                <div className="side-panel">
                  <h3>Latest Openings</h3>
                  {latestJobsOpenings?.map((jobs: any) => {
                    return (
                      <div className="latest-opening-block" key={jobs.id}>
                        <h5>
                          {jobs?.industry?.name}{' '}
                          <Link
                            href={`${process.env.NEXT_PUBLIC_BASE_URL}/companies/${jobs?.company?.company_slug}`}
                            target="_blank">
                            {jobs?.company?.company_name}
                          </Link>
                        </h5>
                        <p>
                          <span>{jobs?.country?.country_name}</span>
                          {jobs?.experience != null ? <span>{jobs?.experience} yrs</span> : <span>0 yrs</span>}
                        </p>
                        <JobApplyButton job={jobs} />
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}
        </div>
      </section>
    </>
  );
}
