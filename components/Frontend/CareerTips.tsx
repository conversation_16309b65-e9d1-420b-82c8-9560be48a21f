import React, {useState, useContext} from 'react';
import Link from 'next/link';
import Image from 'next/image';
import SubscriptionForm from '../Common/SubscriptionForm';
import AuthContext from '@/Context/AuthContext';
import {Button} from 'antd';

export default function CareerTip() {
  const {user} = useContext(AuthContext);

  return (
    <>
      <section className="banner-part-home sp-80">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-6 col-md-12">
              <div className="text-box-width-cover">
                <h1 className="tab-m-0">
                  Career <span className="span-color"> Tips</span>
                </h1>
                <h3 className="font-banner-26 mb-4">
                  Building a successful and fulfilling career involves a combination of strategic planning, continuous
                  learning, and adaptability.{' '}
                </h3>

                <Link
                  href={
                    user?.id
                      ? user?.role == 'superadmin' || user?.role == 'admin'
                        ? '/admin/dashboard'
                        : user?.role == 'employee'
                          ? '/employees/myprofile/profile'
                          : '/employer/company/profile'
                      : '/auth/signup'
                  }>
                  <Button className="btn-a primary-size-22 btn-bg-0055BA mr-1 tab-w-100" style={{height: 'auto'}}>
                    Create Profile
                  </Button>
                </Link>

                <Link href="/jobs-in-gulf">
                  <Button
                    className="btn-a border-primary-size-22 border-0055BA tab-w-100 tab-m-t-b"
                    style={{height: 'auto'}}>
                    Explore Jobs
                  </Button>
                </Link>
              </div>
            </div>
            <div className="col-lg-6 col-md-12">
              <img
                src={process.env.NEXT_PUBLIC_BASE_URL + 'images/home/<USER>'}
                alt="Frame 17"
                className="m-none"
                width={821}
                height={650}
                layout="responsive"
              />
            </div>
          </div>
        </div>
      </section>
      <SubscriptionForm />
    </>
  );
}
