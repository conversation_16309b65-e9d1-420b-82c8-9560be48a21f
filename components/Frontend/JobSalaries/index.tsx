import React, { useState, useEffect } from "react";
import router from "next/router";
import axios from "axios";
import {
  getCountries,
  getIndustries,
} from "@/lib/ApiAdapter";
import {
  Country,
  Industry,
} from "@/lib/types";
import "react-phone-input-2/lib/style.css";
import "react-toastify/dist/ReactToastify.css";
import LocationCareer from "./locationCareer";
export default function JobSalary() {
  const [countries, setCountries] = useState<Country[]>();
  const [industries, setIndustries] = useState<Industry[]>();
  useEffect(() => {
    const cancelTokenSource = axios.CancelToken.source();
    getCountries(undefined, undefined, cancelTokenSource)
      .then((res) => {
        setCountries(res);
      })
      .catch((error) => { });
    getIndustries(undefined, undefined, cancelTokenSource)
      .then((res) => {
        return setIndustries(res);
      })
      .catch((error) => { });
    return cancelTokenSource.cancel;
  }, []);

  const redirection = (data: any) => {
    if (data.country_name) {
      router.push(`/salaries/${data.slug}`);
    } else {
      router.push(`/salaries/united-arab-emirates/${data.name.replace(" ", "-").toLowerCase()}`);
    }
  };
  return (
    <>
      <LocationCareer
        countries={countries}
        industries={industries}
      />
    </>
  );
}
