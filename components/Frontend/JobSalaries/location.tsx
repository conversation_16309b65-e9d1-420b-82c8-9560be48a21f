import React, { useState, useEffect } from "react";

import axios from "axios";

import { getCountries } from "@/lib/ApiAdapter";
import { Country } from "@/lib/types";
import "react-phone-input-2/lib/style.css";
import "react-toastify/dist/ReactToastify.css";
import router from "next/router";


export default function LocationSalary() {
  const handleClickCommon = (value: any) => {
    router.push({
      pathname: "/salaries/carrer",
      query: { location: value },
    });
  };

  const [countries, setCountries] = useState<Country[]>();
  useEffect(() => {
    const cancelTokenSource = axios.CancelToken.source();
    getCountries(undefined, undefined, cancelTokenSource)
      .then((res) => {
        setCountries(res);
      })
      .catch((error) => { });

    return cancelTokenSource.cancel;
  }, []);

  return (
    <>
      <section className="locations-new">
        <div className="container">
          <div className="jobs-by-location">
            <div className="row mb-3">
              <div className="col-sm-8 col-8">
                <h4>
                  Salaries based on <span>location</span>
                </h4>
              </div>
            </div>
            <div className="row">
              <div className="col-12">
                <ul className="uae-part uae4block">
                  {countries &&
                    countries.slice(0, 19).map((country: Country) => (
                      <li
                        key={country.id}
                        onClick={() => handleClickCommon(country)}
                      >
                        {country.country_name}
                      </li>
                    ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
