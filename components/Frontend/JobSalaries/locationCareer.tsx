import React, {useState} from 'react';
import {Country, Sector} from '@/lib/types';
import {LocationCareerProps} from '../../../lib/types';
import router from 'next/router';

export default function LocationCareer(props: LocationCareerProps) {
  const {countries, industries} = props;
  const [location, setLocation] = useState<any>(true);
  const [carrer, setCarrer] = useState<any>(true);
  const redirection = (data: any) => {
    if (data.country_name) {
      router.push(`/salaries/${data.slug}`);
    } else {
      router.push(
        `/salaries/united-arab-emirates/${data.sector_name.replace(/\s+/g, '-').replace(/-+/g, '-').toLowerCase()}`,
      );
    }
  };

  const viewAllHandelClick = (data: any) => {
    if (data == 'location') {
      setCarrer(false);
      setLocation('location');
    } else if (data == 'carrer') {
      setLocation(false);
      setCarrer('carrer');
    }
  };

  return (
    <>
      <section className="locations-new">
        <div className="container">
          {location && (
            <div className="jobs-by-location">
              <div className="row mb-3">
                <div className="col-sm-8 col-8">
                  <h4>
                    Salaries based on <span>location</span>
                  </h4>
                </div>
                <div className="col-sm-4 col-4">
                  <p className="view-right-all text-end">
                    {carrer && (
                      <button
                        onClick={() => {
                          viewAllHandelClick('location');
                        }}>
                        view All
                      </button>
                    )}
                  </p>
                </div>
              </div>
              <div className="row">
                <div className="col-12">
                  {carrer ? (
                    <ul className="uae-part uae4block location-list">
                      {countries &&
                        countries.slice(0, 8).map((country: Country) => (
                          <li
                            key={country.id}
                            onClick={() => {
                              redirection(country);
                            }}>
                            {country.country_name}
                          </li>
                        ))}
                    </ul>
                  ) : (
                    <ul className="uae-part uae4block location-list">
                      {countries &&
                        countries.map((country: Country) => (
                          <li
                            key={country.id}
                            onClick={() => {
                              redirection(country);
                            }}>
                            {country.country_name}
                          </li>
                        ))}
                    </ul>
                  )}
                </div>
              </div>
            </div>
          )}

          {carrer && (
            <div className="jobs-by-location mt-5">
              <div className="row mb-3">
                <div className="col-sm-8 col-8">
                  <h4>
                    Salaries based on <span>career</span>
                  </h4>
                </div>
                <div className="col-sm-4 col-4">
                  <p className="view-right-all text-end">
                    {location && (
                      <button
                        onClick={() => {
                          viewAllHandelClick('carrer');
                        }}>
                        view All
                      </button>
                    )}
                  </p>
                </div>
              </div>
              <div className="row">
                <div className="col-12">
                  {location ? (
                    <ul className="uae-part uae4block overflow-list">
                      {industries &&
                        industries.slice(0, 44).map((industry: Sector) => (
                          <li key={industry.id} onClick={() => redirection(industry)}>
                            {industry.sector_name}
                          </li>
                        ))}
                    </ul>
                  ) : (
                    <ul className="uae-part uae4block overflow-list">
                      {industries &&
                        industries.map((industry: Sector) => (
                          <li key={industry.id} onClick={() => redirection(industry)}>
                            {industry.sector_name}
                          </li>
                        ))}
                    </ul>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </section>
    </>
  );
}
