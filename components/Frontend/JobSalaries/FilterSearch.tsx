import React, {useEffect, useState} from 'react';
import router, {useRouter} from 'next/router';
import Image from 'next/image';
import {notification} from 'antd';
import {useForm} from 'antd/lib/form/Form';
import {Button, Col, Form, Row, Select} from 'antd';
import {filterSearch} from '../../../lib/types';

export default function FilterSearch({countries, industries}: filterSearch) {
  const router = useRouter();
  let pathName: any = [];
  if (typeof window !== 'undefined') {
    pathName = window.location.pathname.split('/');
  }

  const [countrySearch, setCountrySearch] = useState(replaceAndCapitalize(pathName[2]));
  const [industrySearch, setIndustrySearch] = useState(replaceAndCapitalize(pathName[3]));
  const [onchangeCountry, setOnchangeCountry] = useState(false);
  const [onchangeIndustry, setOnchangeIndustry] = useState(false);

  useEffect(() => {
    setCountrySearch('');
    setIndustrySearch('');
    let pathName: any = [];
    if (typeof window !== 'undefined') {
      pathName = window.location.pathname.split('/');
    }
    var countryFromState = replaceAndCapitalize(pathName[2]);
    var industryFromState = replaceAndCapitalize(pathName[3]);

    if (pathName) {
      setCountrySearch(countryFromState);
      setIndustrySearch(industryFromState);
    }
  }, []);

  const [form] = useForm();
  function replaceAndCapitalize(str: any) {
    return str
      ?.split('-')
      .map((word: any) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }
  const submitSearchForm = (values: any) => {
    if (values.keywords === undefined && industrySearch) {
      values.keywords = industrySearch;
    }

    if (values.country === undefined && countrySearch) {
      values.country = countrySearch;
    }

    if (values.keywords !== undefined && values.country !== undefined && !onchangeCountry && !onchangeIndustry) {
      if (countries.length > 0) {
        filterSearch(values);
      }
    } else {
      if (!values.country || onchangeCountry) {
        notification.info({
          message: 'Please select your Country.',
        });
      }
      setTimeout(() => {
        if (!values.keywords || onchangeIndustry) {
          notification.info({
            message: 'Please select your Career.',
          });
        }
      }, 0);
    }
  };
  const check = (value: any) => {
    if (value.hasOwnProperty('industry')) {
      if (!value.industry) {
        setOnchangeIndustry(true);
      } else {
        setOnchangeIndustry(false);
      }
    }
    if (value.hasOwnProperty('country')) {
      if (!value.country) {
        setOnchangeCountry(true);
      } else {
        setOnchangeCountry(false);
      }
    }
  };
  const filterSearch = (values: any) => {
    const countryName = countries.find((res: any) => {
      if (res.country_name === values.country) {
        return res;
      }
    })?.slug;

    router.push({
      pathname: `/salaries/${countryName}/${values.keywords.replace(/\s+/g, '-').replace(/-+/g, '-').toLowerCase()}`,
    });
  };

  return (
    <>
      <section className="uae-new salaries-top bg-dark-blue py-4">
        <div className="container">
          <div className="row">
            <div className="col-md-9 col-sm-12">
              <h1>
                Skyrocket your career using our <span className="span-highlight">salary insights</span>
              </h1>
              <p>Discover careers offering top job satisfaction, competitive salaries, and more</p>
            </div>
            <div className="col-sm-3 text-end">
              <img
                src={process.env.NEXT_PUBLIC_BASE_URL + 'images/ic-search.svg'}
                alt="Banner Image for Salaries"
                className="glas m-none"
                width={175}
                height={90}
              />
            </div>
          </div>
        </div>
        <div className="container max-search ">
          <div className="job-search-top">
            <Form form={form} onFinish={submitSearchForm} size={'large'}>
              <Row gutter={[15, 15]}>
                <Col lg={12} xs={24}>
                  <div className={'field-container'}>
                    <i className="fa-solid fa-magnifying-glass"></i>
                    <Form.Item name={'keywords'} noStyle>
                      <Select
                        id="myDropdown"
                        allowClear
                        bordered={false}
                        style={{width: '100%'}}
                        defaultValue={industrySearch}
                        onChange={selectedOptions => check({industry: selectedOptions})}
                        showSearch
                        optionFilterProp={'label'}
                        placeholder={'Search for careers...'}
                        options={industries?.map((industry: any) => {
                          return {
                            value: industry.sector_name,
                            label: industry.sector_name,
                          };
                        })}
                      />
                    </Form.Item>
                  </div>
                </Col>
                <Col lg={9} xs={24}>
                  <div className={'field-container'}>
                    <i className="fa-solid fa-location-dot"></i>
                    <Form.Item name={'country'} noStyle>
                      <Select
                        allowClear
                        bordered={false}
                        style={{width: '100%'}}
                        defaultValue={countrySearch}
                        onChange={selectedOptions => check({country: selectedOptions})}
                        showSearch
                        optionFilterProp={'label'}
                        placeholder={'Select country'}
                        options={countries?.map((country: any) => {
                          return {
                            value: country.country_name.toString(),
                            label: country.country_name,
                          };
                        })}
                      />
                    </Form.Item>
                  </div>
                </Col>
                <Col lg={3} xs={24}>
                  <Button htmlType={'submit'} type={'primary'} block>
                    Search
                  </Button>
                </Col>
              </Row>
            </Form>
          </div>
        </div>
      </section>
    </>
  );
}
