
import { Industry } from "@/lib/types";
import "react-phone-input-2/lib/style.css";
import "react-toastify/dist/ReactToastify.css";
import { carrerProps } from "../../../lib/types";
import { Sector } from '../../../lib/types';
import router from "next/router";

export default function Carrer({ industries }: carrerProps) {
  const redirection = (data: any) => {
    router.push(`${window.location.href}/${data.sector_name.replace(/\s+/g, '-').replace(/-+/g, '-').toLowerCase()}`);
  };

  return (
    <>
      <section className="locations-new">
        <div className="container">
          <div className="jobs-by-location mt-5">
            <div className="row mb-3">
              <div className="col-sm-8 col-8">
                <h4>
                  Salaries based on <span>career</span>
                </h4>
              </div>
            </div>
            <div className="row">
              <div className="col-12">
                <ul className="uae-part uae4block overflow-list">
                  {industries &&
                    industries.map((industry: Sector) => (
                      <li
                        key={industry.id}
                        onClick={() => redirection(industry)}
                      >
                        {industry.sector_name}
                      </li>
                    ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
