import Image from 'next/image';
import Link from 'next/link';
import React, {useState} from 'react';

interface AuthorProps {
  author?: any;
}

export default function Author({author}: AuthorProps) {
  const [descriptionLength, setDescriptionLength] = useState(50);
  return (
    <div className="author-card-wrapper">
      <Link href={`/author/${author.slug}`}>
        <div className="authorsCard">
          <div className="author-image">
            <img
              src={
                author.profile_image !== null || ''
                  ? process.env.NEXT_PUBLIC_IMAGE_URL + 'images/blogs/author/' + author.profile_image
                  : '/images/Profile_Picture.png'
              }
              alt="user-big.png"
              height={300}
              width={300}
            />
          </div>
          <div className="row align">
            <div className="col-sm-10 col pr-0">
              <Link href={`/author/${author.slug}`}>
                <h4>{author.name}</h4>
              </Link>
            </div>
            <div className="col-sm-2 col pl-0">
              <div className="iconBrand">
                {author.linkedin && (
                  <Link target="_blank" href={author.linkedin}>
                    <i className="fa-brands fa-linkedin-in"></i>
                  </Link>
                )}
              </div>
            </div>
          </div>
          <h5>{author.designation}</h5>
          {author.description && (
            <div
              dangerouslySetInnerHTML={{
                __html: author.description?.substring(0, descriptionLength) + '...',
              }}></div>
          )}
          <span
            style={{color: '#0055BA', cursor: 'pointer', fontSize: '16px', fontWeight: '600'}}
            onClick={() =>
              setDescriptionLength(author.description?.length == descriptionLength ? 100 : author.description.length)
            }>
            Show {author.description?.length == descriptionLength ? 'less' : 'more'}
          </span>
        </div>
      </Link>
    </div>
  );
}
