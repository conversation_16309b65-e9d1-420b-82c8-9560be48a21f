import React, {useState, useContext, useEffect, useCallback} from 'react';
import {useRouter} from 'next/router';
import {addCompanyInfo, getSingleUserDetails, updateOrCreateClaimRequest} from '@/lib/frontendapi';
import AuthContext from '@/Context/AuthContext';
import PopupModal from '../Common/PopupModal';
import {Col, Form, Input, Row, notification} from 'antd';
import {getAllEmployer} from '@/lib/adminapi';
import Image from 'next/image';
import {File, User} from '@/lib/types';
import {FilePond} from 'react-filepond';
import 'filepond/dist/filepond.min.css';
import 'filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css';
import {useForm} from 'antd/lib/form/Form';
import axios from 'axios';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {ButtonUi} from '@/ui/Button';
import {InputUi} from '../Common/Input';
import {Tooltip} from 'antd';

export default function AddCompany() {
  const {user, setUser} = useContext(AuthContext);
  const [modalConfirm, setModalConfirm] = useState(false);
  const [CompanyLogo, setCompanyLogo] = useState('');
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [companies, setCompanies] = useState([]);
  const [companyName, setCompanyName] = useState('');
  const [companyId, setCompanyId] = useState('');
  const [files, setFiles] = useState([]);
  const [userDetails, setUserDetails] = useState<User>();
  const searchCompany = (value: string) => {
    setCompanyName(value);
    setCompanyId('');
    setCompanyLogo('');
    if (companyName.length > 2) {
      getAllEmployer({name: companyName}).then(res => {
        setCompanies(res.data.data);
      });
    } else {
      setCompanies([]);
    }
  };

  const handleExistingCompany = (id: string, name: string, logo: string) => {
    setCompanyId(id);
    setCompanyName(name);
    setCompanyLogo(logo);
    setCompanies([]);
  };

  useEffect(() => {
    getSingleUserDetails(user?.id).then(res => {
      setUserDetails(res?.user);
    });
  }, [user?.id]);

  const modalConfirmClose = () => {
    setModalConfirm(false);
  };

  const submitForm = (data: any) => {
    setIsLoading(true);
    if (companyId) {
      const claimData = {
        user_id: user?.id,
        company_id: companyId,
      };
      updateOrCreateClaimRequest(claimData).then(res => {
        if (res.success) {
          setUser({...user, company_id: companyId, signup_completed: true});
          notification.success({message: res.message});
          router.push('/auth/signup/employer-step-7');
        }
      });
    } else {
      const company_data = {
        user_id: user?.id,
        company_id: companyId,
        company_name: data.company_name,
        designation: data.designation,
        fk_logo_file_uuid: values.fk_logo_file_uuid,
      };
      addCompanyInfo(company_data)
        .then(res => {
          if (res.status == true) {
            setUser({...user, company_id: res.data.id, signup_completed: true});
            notification.success({message: res.message});
            router.push('/auth/signup/employer-step-5');
          } else {
            setIsLoading(false);
          }
        })
        .catch(err => {
          notification.error({
            message: err?.response?.data?.error,
          });
          console.log(err);
        });
    }
  };

  const {values, handleChange, setFieldValue, handleBlur, touched, errors, dirty, isValid, handleSubmit} = useFormik({
    initialValues: {
      company_name: userDetails?.company?.company_name || '',
      designation: userDetails?.company?.designation || '',
      fk_logo_file_uuid: userDetails?.company?.fk_logo_file_uuid || '',
    },
    enableReinitialize: true,
    validationSchema: Yup.object().shape({
      company_name: Yup.string().required('Company name is required'),
      designation: Yup.string().required('Designation is required'),
    }),
    onSubmit: values => {
      console.log('``', values);
      submitForm(values);
    },
  });

  return (
    <>
      <section className="banner-part-home">
        <div className="container-fluid">
          <div className="row">
            <div className="col-lg-5 col-md-12 bg-form-img">
              <div className="fixd-left-box">
                <div className="form-left-text  pt-5 pb-5">
                  <h3>Start building your dream team with us...</h3>
                  <div className="left-list-form active">
                    <h4>Add Your Company</h4>
                    <p>Empowering Digital Experiences through Innovative Web Solutions.</p>
                  </div>

                  <div className="left-list-form  ">
                    <h4>Set Company Profile</h4>
                    <p>Crafting Digital Experiences That Inspire and Innovate.</p>
                  </div>

                  <div className="left-list-form  ">
                    <h4>Publish A Job</h4>
                    <p>Unleash Opportunities: Publish Your Job and Build Your Team.</p>
                  </div>
                </div>
              </div>
              <div className="tab-none gap-box"></div>
            </div>
            <div className="col-lg-7 col-md-12">
              <div className="form-pages  form-left-right-add-sp pt-5 pb-5">
                <h4 className="m-0 p-0 mb-2">Add your company</h4>
                <Row>
                  <Col md={12}>
                    <form onSubmit={handleSubmit}>
                      <div>
                        <div
                          style={{
                            display: 'flex',
                            flexDirection: 'column',
                          }}>
                          <InputUi
                            label="Company Name"
                            style={{
                              height: '32px',
                            }}
                            onChange={handleChange}
                            value={values.company_name}
                            name="company_name"
                            bottomLabel={false}
                            onBlur={handleBlur}
                          />
                          {touched.company_name && errors.company_name && (
                            <span
                              style={{
                                color: 'red',
                                fontSize: '12px',
                              }}>
                              {errors.company_name}
                            </span>
                          )}
                        </div>
                        {companies.length > 0 && (
                          <ul className="dropdown-style add-exist-company-dropdown shadow-lg">
                            {companies.map((item: any, index) => (
                              <li
                                key={index}
                                onClick={e =>
                                  handleExistingCompany(
                                    item.company.id,
                                    item.company.company_name,
                                    item.company.company_logo,
                                  )
                                }
                                style={{cursor: 'pointer'}}>
                                {
                                  <img
                                    src={item.company?.logo?.source || '/images/logo-img.png'}
                                    alt="Avatars-4"
                                    width={50}
                                    height={50}
                                  />
                                }
                                <span className="mx-2">{item.company.company_name}</span>
                              </li>
                            ))}
                          </ul>
                        )}
                      </div>
                      <div
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          paddingTop: '15px',
                        }}>
                        <InputUi
                          label="Designation"
                          bottomLabel={false}
                          style={{
                            height: '32px',
                          }}
                          onChange={handleChange}
                          name="designation"
                          value={values.designation}
                          onBlur={handleBlur}
                        />
                        {touched.designation && errors.designation && (
                          <span
                            style={{
                              color: 'red',
                              fontSize: '12px',
                            }}>
                            {errors.designation}
                          </span>
                        )}
                      </div>
                      {!companyId && (
                        <div className="row pt-3">
                          <div className="col-sm-12 col-7">
                            <Tooltip
                              title="Upload Company Logo (JPEG, JPG, PNG, SVG, WEBP only, max 10MB)"
                              color="#fff"
                              placement="top"
                              overlayInnerStyle={{
                                color: '#1F1F1F',
                                fontSize: '12px',
                                borderRadius: '8px',
                                background: '#FCFCFC',
                                boxShadow:
                                  '0px 1px 3px 0px rgba(31, 31, 31, 0.12), 0px 2px 5px 0px rgba(31, 31, 31, 0.10), 0px 4px 12px 0px rgba(21, 21, 21, 0.12)',
                              }}>
                              <div style={{width: '100%'}}>
                                {/*@ts-ignore*/}
                                <FilePond
                                  files={files}
                                  onupdatefiles={setFiles}
                                  allowMultiple={false}
                                  acceptedFileTypes={[
                                    'image/jpeg',
                                    'image/jpg',
                                    'image/png',
                                    'image/svg+xml',
                                    'image/webp',
                                  ]}
                                  maxFileSize="10MB"
                                  labelIdle='Drag & Drop your company logo or <span class="filepond--label-action">Browse</span>'
                                  beforeAddFile={(file) => {
                                    // Check file type
                                    const acceptedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml', 'image/webp'];
                                    if (!acceptedTypes.includes(file.fileType)) {
                                      notification.error({message: 'Invalid file type. Please upload JPEG, JPG, PNG, SVG or WEBP only.'});
                                      return false;
                                    }

                                    // Check file size (10MB max)
                                    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
                                    if (file.file.size > maxSize) {
                                      notification.error({message: 'File size exceeds 10MB limit.'});
                                      return false;
                                    }

                                    return true;
                                  }}
                                  server={{
                                    url: axios.defaults.baseURL + '/file-management/files',
                                    process: {
                                      headers: {Authorization: axios.defaults.headers.common.Authorization},
                                      onload: data => {
                                        const parsed = JSON.parse(data);
                                        setFieldValue('fk_logo_file_uuid', parsed.uuid);
                                      },
                                      onerror: error => {
                                        notification.error({message: JSON.parse(error)?.error});
                                      },
                                    },
                                  }}
                                  name="file"
                                  accept="image/jpeg,image/jpg,image/png,image/svg+xml,image/webp"
                                />
                              </div>
                            </Tooltip>
                          </div>
                        </div>
                      )}
                      <ButtonUi
                        fullWidth
                        type="submit"
                        onClick={() => {
                          handleSubmit();
                        }}
                        variant="contained"
                        color="primary"
                        disabled={isLoading || !isValid}>
                        {' '}
                        {isLoading ? 'Please wait...' : 'Continue'}
                      </ButtonUi>
                    </form>
                  </Col>
                </Row>
              </div>
            </div>
          </div>
        </div>
      </section>
      <PopupModal
        show={modalConfirm}
        handleClose={modalConfirmClose}
        customclass={'add_company_signup_popup big-size border-r close-icon border-r-img'}
        closebtnclass={'close-x mt-2'}
        closebtnicon={'icon'}>
        <div className="row">
          <div className="col-sm-8">
            <div className="popup-text-areya">
              <div className="popup-left-text ">
                <h3>
                  Finding <span className="span-color">Talent</span> has never been easier.
                </h3>
                <p className="line-height-22">
                  Our platform is designed to make your onboarding process as smooth as possible. Get started right away
                  by following the our curated sign up process.
                </p>
                <div className="left-list-form popup-0EB1D2">
                  <h4>Add Your Company</h4>
                  <p>Empowering Digital Experiences through Innovative Web Solutions.</p>
                </div>
                <div className="left-list-form  popup-0EB1D2">
                  <h4>Set Company Profile</h4>
                  <p>Crafting Digital Experiences That Inspire and Innovate.</p>
                </div>

                <div className="left-list-form  popup-0EB1D2 line-none">
                  <h4>Publish A Job</h4>
                  <p>Unleash Opportunities: Publish Your Job and Build Your Team.</p>
                </div>
                <button className="btn-a primary-size-18 btn-bg-0055BA" onClick={modalConfirmClose}>
                  Get Started
                </button>
              </div>
            </div>
          </div>
          <div className="col-sm-4 popup-right"></div>
        </div>
      </PopupModal>
    </>
  );
}
