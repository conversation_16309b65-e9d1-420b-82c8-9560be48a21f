import React from 'react';
import {useRouter} from 'next/router';
import {Button} from 'antd';
import CreateJobForm from '@/components/Jobs/CreateJobForm';

import 'react-toastify/dist/ReactToastify.css';

export default function PublishJob() {
  const router = useRouter();

  return (
    <>
      <section className="banner-part-home">
        <div className="container-fluid">
          <div className="row">
            <div className="col-lg-5 col-md-12 bg-form-img">
              <div className="fixd-left-box">
                <div className="form-left-text  pt-5 pb-5">
                  <h3>Start building your dream team with us...</h3>
                  <div className="left-list-form active">
                    <h4>Add Your Company</h4>
                    <p>Empowering Digital Experiences through Innovative Web Solutions.</p>
                  </div>
                  <div className="left-list-form  active">
                    <h4>Set Company Profile</h4>
                    <p>Crafting Digital Experiences That Inspire and Innovate.</p>
                  </div>
                  <div className="left-list-form  active">
                    <h4>Publish A Job</h4>
                    <p>Unleash Opportunities: Publish Your Job and Build Your Team.</p>
                  </div>
                </div>
              </div>
              <div className="tab-none gap-box"></div>
            </div>
            <div className="col-lg-7 col-md-12">
              <div className="form-pages form-left-right-add-sp pt-5 pb-5 text-left">
                <h4 className="p-0">Publish a Job</h4>
                <p className="f-16-form spacing-left-1920 p-0 ">Enter basic job details and get started right away.</p>
                <CreateJobForm
                  onCompleted={() => {
                    // @ts-ignore
                    if (window.dataLayer) {
                      // @ts-ignore
                      window.dataLayer = window.dataLayer || [];
                      // @ts-ignore
                      window.dataLayer.push({event: 'conversion'});
                    }
                    window.location.assign('/employer/dashboard');
                  }}
                />

                <Button
                  type={'text'}
                  href="#"
                  onClick={() => {
                    // @ts-ignore
                    if (window.dataLayer) {
                      // @ts-ignore
                      window.dataLayer = window.dataLayer || [];
                      // @ts-ignore
                      window.dataLayer.push({event: 'conversion'});
                    }
                    window.location.assign('/employer/dashboard');
                  }}>
                  Skip
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
