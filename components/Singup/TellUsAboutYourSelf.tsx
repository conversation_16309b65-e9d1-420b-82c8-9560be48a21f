import React, {useState, useEffect, useContext} from 'react';
import {useRouter} from 'next/router';
import axios from 'axios';
import {Form, Input, Select, Button, notification} from 'antd';
import {getAllStatusCountries, getAllNationality, getSingleCountryAllCities} from '@/lib/frontendapi';
import ErrorHandler from '@/lib/ErrorHandler';
import AuthContext from '@/Context/AuthContext';

const {Option} = Select;

export default function TellUsAboutYourSelf() {
  const [Country, setCountry] = useState([]);
  const [nationality, setNationality] = useState([]);
  const [cities, setCities] = useState([]);
  const [selectedCountryId, setSelectedCountryId] = useState('');
  const router = useRouter();
  const {user, setUser} = useContext(AuthContext);
  const [form] = Form.useForm();

  useEffect(() => {
    getAllStatusCountries()
      .then(res => {
        if (res) {
          setCountry(res);
        } else {
          setCountry([]);
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
    getAllNationality()
      .then(res => {
        if (res) {
          setNationality(res);
        } else {
          setNationality([]);
        }
      })
      .catch(error => {
        notification.error({
          message: error?.response?.data?.error,
        });
        ErrorHandler.showNotification(error);
      });
  }, []);

  useEffect(() => {
    if (selectedCountryId) {
      (async () => {
        const response = await getSingleCountryAllCities(selectedCountryId);
        if (response) {
          setCities(response?.data);
        } else {
          setCities([]);
        }
      })();
    }
  }, [selectedCountryId]);

  const onFinish = async (data: any) => {
    try {
      if (typeof window === 'undefined') return;

      const role = sessionStorage.getItem('signup.role');

      const response: any = await axios.post('users/signup/update-info', data);
      if (response.status) {
        notification.success({message: "You've successfully registered"});
        setUser({...user, signup_completed: true});
        if (role === 'employee') {
          router.push('/employees/dashboard').then();
        } else {
          router.push('/auth/signup/employer-step-4').then();
        }
        // @ts-ignore
        if (window.dataLayer) {
          // @ts-ignore
          window.dataLayer = window.dataLayer || [];
          // @ts-ignore
          window.dataLayer.push({event: 'conversion'});
        }
      }
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <>
      <section className="banner-part-home">
        <div className="container-fluid">
          <div className="row">
            <div className="col-lg-5 col-md-12 tab-none pl-0 back-img-form" />
            <div className="col-lg-7 col-md-12">
              <div className="form-pages top-m-sp same-pad-up-down ">
                <h3>Tell us a bit about yourself</h3>
                <Form
                  className="form-get mt-4"
                  onFinish={onFinish}
                  layout={'vertical'}
                  form={form}
                  requiredMark={false}>
                  <Form.Item
                    className="mb-3"
                    label="Where are you currently based?"
                    name="countries"
                    rules={[{required: true, message: 'Where Currently Based is required.'}]}>
                    <Select
                      showSearch
                      filterOption={(input, option: any) =>
                        option?.children?.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      }
                      size="large"
                      onChange={value => {
                        setSelectedCountryId(value);
                        form.setFieldValue('countries', value);
                        form.setFieldValue('cities', undefined);
                      }}>
                      <Option value="">Select country</Option>
                      {Country.length > 0 ? (
                        Country.map(
                          (CountryData: any, index) => (
                            // CountryData.status === 'active' ? (
                            <Option value={CountryData.id} key={index}>
                              {CountryData.country_name}
                            </Option>
                          ),
                          // ) : null,
                        )
                      ) : (
                        <>No Data Found</>
                      )}
                    </Select>
                  </Form.Item>
                  <Form.Item className="mb-3" label="Current City" name="cities">
                    <Select
                      size="large"
                      onChange={values => {
                        form.setFieldValue('cities', values);
                      }}
                      showSearch
                      filterOption={(input, option: any) =>
                        option?.children?.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      }
                      allowClear>
                      <Option value="">Select city</Option>
                      {cities.length > 0 ? (
                        cities.map((CountryData: any, index) =>
                          CountryData.status === 'active' ? (
                            <Option value={CountryData.id} key={index}>
                              {CountryData?.city_name}
                            </Option>
                          ) : null,
                        )
                      ) : (
                        <>No Data Found</>
                      )}
                    </Select>
                  </Form.Item>
                  {/* <Form.Item
                    className="mb-3"
                    label="Current Position"
                    name="current_position"
                    rules={[{required: true, message: 'Current Position is required.'}]}>
                    <Input
                      placeholder="Software Developer"
                      size="large"
                      onChange={e => {
                        form.setFieldValue('current_position', e.target.value);
                      }}
                    />
                  </Form.Item> */}

                  <Form.Item
                    label="Nationality"
                    name="nationality"
                    rules={[{required: true, message: 'Nationality is required.'}]}>
                    <Select
                      size="large"
                      onChange={value => {
                        form.setFieldValue('nationality', value);
                      }}
                      showSearch
                      filterOption={(input, option: any) =>
                        option?.children?.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      }
                      allowClear
                      >
                      <Option value="">Select Nationality</Option>
                      {nationality.length > 0 ? (
                        nationality.map((nationalityData: any, index) => {
                          return (
                            <Option value={nationalityData.id} key={nationalityData?.id}>
                              {nationalityData.country_name}
                            </Option>
                          );
                        })
                      ) : (
                        <>No Data Found</>
                      )}
                    </Select>
                  </Form.Item>
                  <Form.Item>
                    <Button
                      type="primary"
                      htmlType="submit"
                      className="btn-a primary-size-16 b-0 btn-bg-0055BA w-100"
                      style={{height: 'auto'}}>
                      Continue
                    </Button>
                  </Form.Item>
                </Form>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
