import React, {useState, useEffect, useContext} from 'react';
import {useRouter} from 'next/router';
import PasswordStrengthBar from 'react-password-strength-bar';
import PhoneInput from 'react-phone-input-2';
import {Button, Form, Input, Checkbox, notification} from 'antd';
import {completeUserSignUp} from '@/lib/frontendapi';
import AuthContext from '@/Context/AuthContext';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '@/lib/ErrorHandler';
import 'react-phone-input-2/lib/style.css';

export default function CreatePassword() {
  const [password, setPassword] = useState('');
  const [selectedRole, setSelectedRole] = useState<string>();
  const {hasSession, refreshSignUpSession} = useContext(AuthContext);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const [passwordScore, setPasswordScore] = useState<number>(0);

  useEffect(() => {
    const role = sessionStorage.getItem('signup.role');
    if (role) {
      setSelectedRole(role);
    }
  }, []);

  useEffect(() => {
    if (hasSession === false) {
      router.push('/auth/signup').then();
    }
  }, [hasSession, router]);

  const submitForm = async (data: any) => {
    if (passwordScore < 4) {
      notification.info({
        message: 'Your password strength could be stronger.',
        description: `For a more secure account, consider using a longer password with a mix of uppercase and lowercase letters, numbers, and special characters.`,
      });
      return;
    }

    setLoading(true);
    completeUserSignUp(data.password, data.phone, selectedRole)
      .then(res => {
        if (res) {
          setLoading(false);
          refreshSignUpSession();
          if (selectedRole === 'employee') {
            router.push('/auth/signup/candidate-step-3').then();
          } else {
            router.push('/auth/signup/employer-step-3').then();
          }
        }
      })
      .catch(err => {
        setLoading(false);
        notification.error({
          message: err?.response?.data?.error,
        });
        ErrorHandler.showNotification(err);
      });
  };

  return (
    <>
      <section className="banner-part-home">
        <div className="container-fluid">
          <div className="row">
            <div className="col-lg-5 col-md-12 tab-none pl-0 back-img-form new-join"></div>
            <div className="col-lg-7 col-md-12">
              <div className="form-pages top-m-sp same-pad-up-down ">
                <h3>Let's get started!</h3>
                <h5 className="w-500 mb-5">Tell us a bit about yourself</h5>
                <Form
                  className="form-get mt-4 frontend-form"
                  onFinish={submitForm}
                  layout="vertical"
                  size={'large'}
                  requiredMark={false}>
                  <Form.Item
                    label="Your contact number"
                    name="phone"
                    rules={[
                      {required: true, message: 'Contact Number is required.'},
                      {min: 10, message: 'Please Enter min length 10.'},
                      {max: 12, message: 'Please Enter max length 12.'},
                    ]}>
                    <PhoneInput country={'ae'} enableSearch />
                  </Form.Item>
                  <Form.Item
                    label="Password"
                    name="password"
                    rules={[{required: true, message: 'Password is required.'}]}>
                    <Input.Password
                      placeholder="Password"
                      onChange={(e: any) => setPassword(e.target.value)}
                      visibilityToggle
                    />
                  </Form.Item>
                  <PasswordStrengthBar password={password} onChangeScore={score => setPasswordScore(score)} />
                  <Form.Item
                    label="Re-Type Password"
                    name="retype_password"
                    rules={[
                      {required: true, message: 'Retype Password is required.'},
                      ({getFieldValue}) => ({
                        validator(_, value) {
                          if (!value || getFieldValue('password') === value) {
                            return Promise.resolve();
                          }
                          return Promise.reject(new Error('Passwords do not match.'));
                        },
                      }),
                    ]}>
                    <Input.Password placeholder="Retype your Password" visibilityToggle />
                  </Form.Item>
                  <Form.Item
                    name="agreeTerms"
                    valuePropName="checked"
                    rules={[
                      {
                        required: true,
                        message: 'You must agree to the Terms and Conditions and Privacy Policy.',
                      },
                    ]}>
                    <Checkbox>
                      By clicking checkbox, you agree to our <a href="#">Terms and Conditions</a> and{' '}
                      <a href="#">Privacy Policy</a>
                    </Checkbox>
                  </Form.Item>
                  <Form.Item>
                    <Button type="primary" htmlType="submit" block loading={loading}>
                      {loading ? 'Please wait..' : 'Submit'}
                    </Button>
                  </Form.Item>
                </Form>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
