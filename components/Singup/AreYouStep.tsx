import React, {useContext} from 'react';
import {useRouter} from 'next/router';
import axios from 'axios';
import {Button} from 'antd';
import AuthContext from '@/Context/AuthContext';
import Cookies from 'js-cookie';
export default function AreYouStep() {
  const {user} = useContext(AuthContext);
  const router = useRouter();
  const {registerSessionToken, refreshSignUpSession} = useContext(AuthContext);
  const submitForm = (role: string) => {
    if (user != undefined) {
      // console.log(user);
      // console.log(role);
      console.log(Cookies.get('session_token'));
      refreshSignUpSession();
      sessionStorage.setItem('signup.role', role);
      let data = {
        user_id: user?.id,
        role: role,
      };
      axios
        .post('/update-role', data)
        .then(response => {
          console.log(response);
          if (response.status === 200) {
            let role = response.data.data[0].role;
            console.log(role);
            if (role === 'employee') {
              router.push('/employees/dashboard');
            } else {
              router.push('/employer/dashboard');
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    } else {
      sessionStorage.setItem('signup.role', role);
      if (role === 'employee') {
        router.push('/auth/signup/candidate-step-1').then();
      } else {
        router.push('/auth/signup/employer-step-1').then();
      }
    }
  };

  return (
    <>
      <section className="banner-part-home m-pb-3">
        <div className="container-fluid">
          <div className="row">
            <div className="col-lg-5 col-md-12 tab-none pl-0  back-img-form new-join"></div>
            <div className="col-lg-7 col-md-12">
              <div className="form-pages top-m-sp">
                <h3>Join our expanding community today!</h3>
                <div className="login_footer_section">
                  <div className="text-center">
                    <h6 className="f-22  mt-5">Are you:</h6>
                    <Button
                      className="signup-cards white-btn  mt-3 mb-4"
                      icon={<i className="fa-solid fa-magnifying-glass"></i>}
                      onClick={() => submitForm('employee')}
                      style={{height: 'auto'}}>
                      Looking for A Job{' '}
                    </Button>
                    <p className="f-16 c-747474">or</p>
                    <Button
                      className="signup-cards white-btn mt-4 hover-eff"
                      onClick={() => submitForm('employer')}
                      style={{height: 'auto'}}>
                      <i className="fa-solid fa-bag-shopping"></i> Hiring New Talent
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
