import React, {useState, useEffect, useContext} from 'react';
import {Button, Checkbox, Form, Input, notification} from 'antd';
import {useRouter} from 'next/router';
import Link from 'next/link';
import {signIn} from 'next-auth/react';
import axios from 'axios';
import AuthContext from '@/Context/AuthContext';
import {useForm} from 'antd/lib/form/Form';
import <PERSON>rror<PERSON>and<PERSON> from '@/lib/ErrorHandler';
import {toast} from 'react-toastify';

const disposableDomains = [
  'mailinator.com',
  'mailinator.in',
  'guerrillamail.com',
  'tempmail.com',
  '10minutemail.com',
  'sharklasers.com',
  'dispostable.com',
  'yopmail.com',
  'temp-mail.org',
  'mailcatch.com',
  'throwawayemail.com',
  'getairmail.com',
  'jetable.org',
  'trashmail.com',
  'fakeinbox.com',
  'tempmailo.com',
];

interface SignUpProps {
  sessionToken?: string;
}

export default function GetStartedStep({sessionToken}: SignUpProps) {
  const {user} = useContext(AuthContext);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const [form] = useForm();
  let selectedRole: any;
  const {registerSessionToken, refreshSignUpSession} = useContext(AuthContext);
  if (typeof window !== 'undefined') {
    selectedRole = sessionStorage.getItem('signup.role');
  }

  useEffect(() => {
    if (sessionToken) {
      registerSessionToken(sessionToken);
      refreshSignUpSession();
    }
  }, [sessionToken]);

  useEffect(() => {
    console.log('user', user);

    if (user?.contact_no != null) {
      if (user?.role === 'employee') {
        router.push('/employees/dashboard');
      } else {
        router.push('/employer/dashboard');
      }
      console.log('redirection after google signin', user);
    } else if (user?.contact_no === null) {
      if (sessionStorage.getItem('signup.role') === 'employee') {
        router.push('/auth/signup/candidate-step-2');
      } else {
        router.push('/auth/signup/employer-step-2');
      }
    }
  }, [user]);

  const capitalizeFirstLetter = (string: string): string => {
    return string
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const submitForm = async (data: any) => {
    const emailParts = data.email.split('@');
    const domain = emailParts[emailParts.length - 1];
    const role = sessionStorage.getItem('signup.role');

    if (disposableDomains.includes(domain)) {
      notification.error({message: 'Registration using disposable email is not allowed.'});
      return;
    }

    if (!selectedRole) {
      notification.info({message: 'Please complete previous tab'});
      router.push('/auth/signup');
    }

    setLoading(true);
    const userData = {
      name: capitalizeFirstLetter(data.fullname),
      email: data.email,
    };

    axios
      .post('signup/register', userData)
      .then(response => {
        setLoading(false);
        registerSessionToken(response.data.token);
        refreshSignUpSession();

        if (role === 'employee') {
          router.push('/auth/signup/candidate-step-2').then();
        } else {
          router.push('/auth/signup/employer-step-2').then();
        }
      })
      .catch(error => {
        setLoading(false);
        notification.error({
          message: error?.response?.data?.error || error?.response?.data?.errors,
        });
        ErrorHandler.showNotification(error);
      });
  };

  return (
    <>
      <section className="banner-part-home">
        <div className="container-fluid">
          <div className="row">
            <div className="col-lg-5 col-md-12 tab-none pl-0 back-img-form"></div>
            <div className="col-lg-7 col-md-12">
              <div className="form-pages top-m-sp same-pad-up-down same-mm">
                <h2>
                  {typeof window !== 'undefined' && selectedRole === 'employer'
                    ? 'Build your dream team with us!'
                    : "Let's get started!"}
                </h2>
                <h5 className="w-500 mb-5">Create an account to get started.</h5>
                <Form
                  form={form}
                  className="form-get mb-4"
                  onFinish={submitForm}
                  layout={'vertical'}
                  requiredMark={false}
                  size={'large'}>
                  <Form.Item
                    label="Full Name"
                    name="fullname"
                    rules={[{required: true, message: 'Full name is required.'}]}>
                    <Input placeholder="Your Full Name" />
                  </Form.Item>
                  <Form.Item
                    label="Email ID"
                    name="email"
                    rules={[
                      {required: true, message: 'Email is required.'},
                      {type: 'email', message: 'Please enter a correct Email Address.'},
                    ]}>
                    <Input placeholder="Your Email ID" maxLength={70} />
                  </Form.Item>
                  <Form.Item
                    name="agree"
                    valuePropName="checked"
                    rules={[{required: true, message: 'Please agree to the terms and conditions'}]}>
                    <Checkbox>By joining, I agree to receive emails from The Talent Point.</Checkbox>
                  </Form.Item>
                  <br />
                  <Form.Item>
                    <Button block type="primary" htmlType="submit" loading={loading}>
                      Continue
                    </Button>
                  </Form.Item>
                </Form>
                <div className="login_footer_section">
                  <div className="or mb-4">
                    <p>or</p>
                  </div>
                  <button className="google-g w-100 mb-4" onClick={() => signIn('google')}>
                    <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/google-logo.png'} alt="google-logo" /> Continue
                    with Google
                  </button>
                  <button className="linkedin-button w-100 mb-4" onClick={() => signIn('linkedin')}>
                    <i className="fa-brands fa-linkedin"></i>
                    <span> Continue with LinkedIn</span>
                  </button>
                  <center>
                    <p className="f-12-747474">
                      Already a part of The Talent Point Community? <Link href="/auth/login"> Sign In</Link>
                    </p>
                  </center>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
