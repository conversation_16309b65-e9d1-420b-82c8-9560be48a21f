import React, {useState, useContext} from 'react';
import {useRouter} from 'next/router';
import AuthContext from '@/Context/AuthContext';
import {Button, Form, Input, notification} from 'antd';
import PhoneInput from 'react-phone-input-2';
import {updateOrCreateClaimRequest} from '@/lib/frontendapi';

export default function ClaimCompany() {
  const {user} = useContext(AuthContext);
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const {TextArea} = Input;

  const submitForm = async (data: any) => {
    setLoading(true);
    const formData = {
      ...data,
      user_id: user?.id,
    };
    updateOrCreateClaimRequest(formData).then(res => {
      if (res.success) {
        notification.success({message: res.message});
        router.push('/');
      } else {
         notification.error({message: res.message});
        router.push('/auth/signup/step-4');
      }
      setLoading(false);
    });
  };

  return (
    <>
      <section className="banner-part-home">
        <div className="container-fluid">
          <div className="row">
            <div className="col-lg-5 col-md-12 bg-form-img">
              <div className="fixd-left-box">
                <div className="form-left-text  pt-5 pb-5">
                  <h3>Start building your dream team with us...</h3>
                  <div className="left-list-form active">
                    <h4>Claim Your Company</h4>
                    <p>Empowering Digital Experiences through Innovative Web Solutions.</p>
                  </div>
                </div>
              </div>
              <div className="tab-none gap-box"></div>
            </div>
            <div className="col-lg-7 col-md-12">
              <div className="form-pages  form-left-right-add-sp pt-5 pb-5">
                <h4>Confirm your information</h4>
                <h4 style={{fontSize: '18px', fontWeight: '400'}}>
                  Our team will use this information to confirm your claim, ensuring candidates using The Talent Point
                  have access to quality employers.
                </h4>
                <Form
                  layout="vertical"
                  className="form-get mt-4 frontend-form"
                  onFinish={submitForm}
                  size="large"
                  requiredMark={false}>
                  <Form.Item
                    label="Name"
                    initialValue={user?.name}
                    name="name"
                    rules={[{required: true, message: 'Name is required!'}]}>
                    <Input className="big-input" disabled defaultValue={user?.name} />
                  </Form.Item>
                  <Form.Item
                    label="Email ID"
                    initialValue={user?.email}
                    name="email"
                    rules={[
                      {
                        type: 'email',
                        message: 'The input is not valid E-mail!',
                      },
                      {
                        required: true,
                        message: 'Please input your E-mail!',
                      },
                    ]}>
                    <Input className="big-input" disabled type="email" defaultValue={user?.email} />
                  </Form.Item>
                  <Form.Item
                    label="Your contact number"
                    name="phone"
                    rules={[
                      {required: true, message: 'Contact Number is required.'},
                      {min: 10, message: 'Please Enter min length 10.'},
                      {max: 12, message: 'Please Enter max length 12.'},
                    ]}>
                    <PhoneInput country={'ae'} enableSearch />
                  </Form.Item>
                  <Form.Item label="Message" name="message">
                    <TextArea rows={4} placeholder="message..." maxLength={200} />
                  </Form.Item>
                  <Form.Item>
                    <Button type="primary" htmlType="submit" block loading={loading}>
                      {loading ? 'Please wait..' : 'Submit'}
                    </Button>
                  </Form.Item>
                </Form>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
