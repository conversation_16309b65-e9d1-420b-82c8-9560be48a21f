import React, {useState, useEffect, useContext} from 'react';
import {useRouter} from 'next/router';
import Link from 'next/link';
import {useForm} from 'react-hook-form';
import {getAllCountries, UpdateSetCompanyInfo, getAllSectors} from '../../lib/frontendapi';
import {HtmlEditor} from '../Common/HtmlEditor';
import AuthContext from '@/Context/AuthContext';
import {notification} from 'antd';

export default function SetCompanyProfile() {
  const {user, refreshUserData} = useContext(AuthContext);
  const [Country, setCountry] = useState([]);
  const router = useRouter();
  const [sectorData, setSectorData] = useState([]);
  const [description, setDescription]: any = useState('');
  const {
    register,
    handleSubmit,
    formState: {errors},
  }: any = useForm();

  useEffect(() => {
    getAllCountries()
      .then(res => {
        if (res) {
          setCountry(res);
        } else {
          setCountry([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
    getAllSectors()
      .then(res => {
        if (res.success == true) {
          setSectorData(res.sectors);
        } else {
          setSectorData([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  }, []);

  const handleChangeEditorAboutCompanyDesc = (name: any, value: any) => {
    setDescription(value);
  };
  const submitForm = (data: any) => {
    const company_data = {
      user_id: user?.id,
      edit_website: data.website,
      edit_location: data.location,
      edit_sector: data.sector,
      edit_no_of_employees: data.no_of_employees,
      edit_description: description,
    };
    UpdateSetCompanyInfo(user?.company_id, company_data)
      .then(res => {
        if (res.status == true) {
          // Refresh user data to get updated company info
          refreshUserData().then(() => {
            notification.success({message: res.message});
            router.push('/auth/signup/employer-step-6');
          });
        }
      })
      .catch(err => {
        console.log(err);
        notification.error({
          message: err?.response?.data?.error,
        });
      });
  };
  return (
    <>
      <section className="banner-part-home">
        <div className="container-fluid">
          <div className="row">
            <div className="col-lg-5 col-md-12 p-0 bg-form-img">
              <div className="fixd-left-box">
                <div className="form-left-text  pt-5 pb-5">
                  <h3>Start building your dream team with us...</h3>
                  <div className="left-list-form active">
                    <h4>Add Your Company</h4>
                    <p>Empowering Digital Experiences through Innovative Web Solutions.</p>
                  </div>
                  <div className="left-list-form  active">
                    <h4>Set Company Profile</h4>
                    <p>Crafting Digital Experiences That Inspire and Innovate.</p>
                  </div>
                  <div className="left-list-form  ">
                    <h4>Publish A Job</h4>
                    <p>Unleash Opportunities: Publish Your Job and Build Your Team.</p>
                  </div>
                </div>
              </div>
              <div className="tab-none gap-box"></div>
            </div>
            <div className="col-lg-7 col-md-12">
              <div className="form-pages  form-left-right-add-sp pt-5 pb-5">
                <h4>Set company profile</h4>
                <form className="form-get mt-4" onSubmit={handleSubmit(submitForm)}>
                  <div className="form_field_sec">
                    <input
                      type="text"
                      placeholder="https://www.website.com"
                      className="big-input mt-1"
                      value={register.website}
                      {...register('website', {
                        required: true,
                        pattern:
                          /^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/,
                      })}
                    />
                    <label>Website*</label>
                  </div>
                  {errors.website && errors.website.type === 'required' && (
                    <p className="text-danger bottom-0" style={{textAlign: 'left'}}>
                      Website is required.
                    </p>
                  )}
                  {errors.website && errors.website?.type === 'pattern' && (
                    <p className="text-danger bottom-0" style={{textAlign: 'left'}}>
                      Enter a valid website url!
                    </p>
                  )}
                  <div className="form_field_sec">
                    <select className="big-select mt-1" {...register('location', {required: true})}>
                      <option value="">Select location</option>
                      {Country.length > 0 ? (
                        Country.map((CountryData: any, index) => {
                          if (CountryData.status == 'active') {
                            return (
                              <option value={CountryData.id} key={index} selected={location == CountryData.id}>
                                {CountryData.country_name}
                              </option>
                            );
                          }
                        })
                      ) : (
                        <option value="">No Data Found</option>
                      )}
                    </select>
                    <label>Location*</label>
                  </div>
                  {errors.location && errors.location.type === 'required' && (
                    <p className="text-danger bottom-0" style={{textAlign: 'left'}}>
                      Location is required.
                    </p>
                  )}
                  <div className="form_field_sec">
                    <select className="big-select mt-1" {...register('sector', {required: true})}>
                      <option value="">Select sector</option>
                      {sectorData.length > 0 ? (
                        sectorData.map((sector_data: any, index: any) => {
                          return (
                            <option value={sector_data.id} key={index}>
                              {sector_data.sector_name}
                            </option>
                          );
                        })
                      ) : (
                        <option value="">No Data Found</option>
                      )}
                    </select>
                    <label>Sector*</label>
                  </div>
                  {errors.sector && errors.sector.type === 'required' && (
                    <p className="text-danger bottom-0" style={{textAlign: 'left'}}>
                      Sector is required.
                    </p>
                  )}
                  <div className="form_field_sec">
                    <select className="big-select mt-1" {...register('no_of_employees', {required: true})}>
                      <option value="">Select Employees</option>
                      <option value="0-50">0-50</option>
                      <option value="51-100">51-100</option>
                      <option value="101-200">101-200</option>
                      <option value="201-500">201-500</option>
                      <option value="501-1000">501-1000</option>
                      <option value="1001-2000">1001-2000 </option>
                      <option value="2001-5000">2001-5000</option>
                      <option value="5000+">5000+</option>
                    </select>
                    <label>Number of Employees*</label>
                  </div>
                  {errors.no_of_employees && errors.no_of_employees.type === 'required' && (
                    <p className="text-danger bottom-0" style={{textAlign: 'left'}}>
                      No Of Employees is required.
                    </p>
                  )}
                  <label>About Company</label>
                  <HtmlEditor
                    name="description"
                    value={description}
                    onChange={(name: any, val: any) => {
                      handleChangeEditorAboutCompanyDesc(name, val);
                    }}
                  />
                  <div className="row">
                    <div className="col-3">
                      <Link href="/addcompany">
                        <button className="btn login  w-100 mt-4">Back</button>
                      </Link>
                    </div>
                    <div className="col-9">
                      <button className="btn-a primary-size-16 b-0 btn-bg-0055BA w-100 mt-4" type="submit">
                        Continue
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
