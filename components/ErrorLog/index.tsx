import React, { useState, useEffect } from 'react';
import {
  getAllErrorLog,
  deleteerrorlog,
  handledeleteAllData,
} from "../../lib/adminapi";
import { all } from 'axios';
import swal from "sweetalert";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Pagination from "../../components/Common/NewPagination";
import { paginate } from "../../helpers/paginate";
import PopupModal from '../../components/Common/PopupModal';
import Link from "next/link";
import SuccessToast from "../Common/showSuccessTostrMessage";
import ErrorToast from "../Common/showErrorTostrMessage";
import {ErrorsLogs} from "@/lib/types";

export default function ErrorLogs() {
  const [errorsLogs, setErrorsLogs] = useState<ErrorsLogs[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [filteredSectors, setFilteredSectors] = useState<ErrorsLogs[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [name, setSectorName] = useState("");
  const [id, setSectorId] = useState("");
  const [modalConfirm, setModalConfirm] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [showPopupunsave, setShowPopupunsave] = useState(false);
  const [showPopupunsave1, setShowPopupunsave1] = useState(false);
  const [showmessage, setShowmessage] = useState('');

  const modalConfirmClose = () => {
    setModalConfirm(false);
  };
  const pageSize = 20;
  useEffect(() => {
    getAllErrorLog()
      .then((response) => {
        setErrorsLogs(response.data);
        filterBlogs(response.data, searchQuery);

      })
      .catch((error) => {
        console.error(error);
      });
  });
  const fetchData = async () => {
    try {
      const response = await getAllErrorLog();
      setErrorsLogs(response.data);
      filterBlogs(response.data, searchQuery);
    } catch (error) {
      console.error(error);
    }
  };
  const filterBlogs = (errorsLogs: ErrorsLogs[], searchValue: string) => {
    const filteredSectors = errorsLogs.filter((errorLogs) => {
      const errorMessageMatch = errorLogs.error_message.toLowerCase().includes(searchValue.toLowerCase());
      const fileNameMatch = errorLogs.file_name.toLowerCase().includes(searchValue.toLowerCase());
      const lineNumberMatch = errorLogs.line_number.toString().toLowerCase().includes(searchValue.toLowerCase());
      const idMatch = errorLogs.id && errorLogs.id.toString().toLowerCase() === searchValue.toLowerCase();

      return errorMessageMatch || fileNameMatch || lineNumberMatch;
    });
    setFilteredSectors(filteredSectors);
    setCurrentPage(1);
  };
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchValue = e.target.value.toLowerCase();
    setSearchQuery(searchValue);
    filterBlogs(errorsLogs, searchValue);
  };
  const onPageChange = (page: number) => {
    setCurrentPage(page);
  };
  const paginatedSector = paginate(filteredSectors, currentPage, pageSize);
  const handleCancel = () => {
    setModalConfirm(false);
  };
  const resetForm = () => {
    setSectorName("");
    setSectorId("");
  };
  function handledeleteerrorlog(id: any) {
    swal({
      title: "Are you sure?",
      text: "You want to delete the Error Log",
      icon: "warning",
      dangerMode: true,
      buttons: ["Cancel", "Yes, I am sure!"],
    }).then((willDelete: any) => {
      if (willDelete) {
        const data = {
          id: id,
        };
        deleteerrorlog(data)
          .then((res) => {
            if (res.status === true) {
              console.log(res.status)
              window.location.reload();
            } else {
              console.log("Deletion failed");
            }
          })
          .catch(() => {});
      } else {
        console.log("Deletion failed");
      }
    });
  }
  function handledeleteAllErrorLog() {
    swal({
      title: "Are you sure?",
      text: "You want to delete the All Error Log",
      icon: "warning",
      dangerMode: true,
      buttons: ["Cancel", "Yes, I am sure!"],
    }).then((willDelete: any) => {
      if (willDelete) {
        handledeleteAllData()
          .then((res) => {
            if (res.status === true) {
              window.location.reload();
            } else {
              console.log("Deletion failed");
            }
          })
          .catch(() => {});
      } else {
        console.log("Deletion failed");
      }
    });
  }
  return (
    <>
      <div className='dash-right'>
        <div
        className="footer-part"
        style={{
          display: "flex",
          justifyContent: "space-around",
          alignItems: "center",
          padding: "4px 0px"
        }}
      >
        <h5>
          Displaying <span className="span-color-dash">Error</span> Logs
        </h5>
        <button
          className="btn btn-outline-success text-center w-35 m-3 "
          style={{
            backgroundColor: "#0055BA",
            color: "white",
            fontSize: "19px",
          }}
          onClick={() => handledeleteAllErrorLog()}
        >
          Delete All
        </button>
      </div>
        <div className='data-management'>
          <div className='row justify-content-end' id="sector_input">
            <div className='col-lg-5 col-md-12'>
              <div>
                <div className="input-group  mb-2">
                  <input type="text" className="form-control" placeholder="Search message here.." value={searchQuery}
                    onChange={handleSearch} />
                </div>
              </div>
            </div>
            <div className='col-lg-2 col-md-12'>
              <button type='button' className='btn-a primary-size-16 btn-bg-0055BA tab-w-100' onClick={() => { setModalConfirm(true); resetForm(); toast.dismiss(); }}>Search Message</button>
            </div>
          </div>
          <div className="table-part mt-4 max-w">
            <table className="rwd-table">
              <tbody>
                <tr>
                  <th className="text-center border-1 p-2">Action</th>
                  <th className="text-center border-1 p-2">Error Message</th>
                  <th className="text-center border-1 p-2">Line</th>
                  <th className="text-center border-1 p-2">File Name</th>
                  <th className="text-center border-1 p-2">Count</th>
                </tr>
                {paginatedSector.map((item: any, index: any) => (
                  <tr key={index}>
                    <td data-th="Action" className="text-center border-1 p-2">
                      <i
                        className="fa-regular fa-trash-can del-trash"
                        onClick={() => handledeleteerrorlog(item.id)}
                      ></i>
                    </td>
                    <td  data-th="Error Message" className="text-start border-1 p-2">
                      {item.error_message}
                    </td>
                    <td  data-th="Line" className="text-center border-1 p-2">
                      {item.line_number ? item.line_number : "null"}
                    </td>
                    <td  data-th="File Name" className="text-start border-1 p-2">{item.file_name}</td>
                    <td  data-th="Count" className="text-start border-1 p-2">{item.count}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="pagination-wrapper mt-4">
            <div className="pagination-wrapper">
              <Pagination
                items={filteredSectors}
                currentPage={currentPage}
                pageSize={pageSize}
                onPageChange={onPageChange}
                activePage={currentPage} // Add the activePage property here
              />
            </div>
          </div>
        </div>
      </div>
    </>
  )
}