import {useEffect, useState} from 'react';

const NoSSR = WrappedComponent => {
  return function NoSSRComponent(props) {
    const [mounted, setMounted] = useState(false);

    useEffect(() => {
      setMounted(true);
    }, []);

    if (!mounted) {
      return null; // Optionally, return a loading spinner or placeholder
    }

    return <WrappedComponent {...props} />;
  };
};

export default NoSSR;
