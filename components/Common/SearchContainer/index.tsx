import Search from 'antd/lib/transfer/search';
import styles from './style.module.css';
import {SearchProps} from 'antd/lib/input';

interface SearchContainerProps extends SearchProps {
  onChange?: (e: any) => void;
}

export const SearchContainer = ({...props}: SearchContainerProps) => {
  return (
    <div className={styles.search_container}>
      <Search {...props} value={props.value?.toString()} prefixCls={`${styles.search_input} datapoint_search`} />
    </div>
  );
};
