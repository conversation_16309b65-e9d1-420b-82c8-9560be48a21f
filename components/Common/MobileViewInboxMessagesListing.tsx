import React, { useState, useEffect } from 'react';
import { useRouter } from "next/router";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Link from 'next/link';
import { getCurrentUserData } from "../../lib/session";
import PopupModal from './PopupModal';
import { getAllEmployerReceiverMessages, getAllInterviews, getAllEmployerReceiverArchivedMessages, updateMessagesReadUnReadStatus, getTotalMessageUnReadCount } from '../../lib/frontendapi';
import SuccessToast from "../Common/showSuccessTostrMessage";
import ErrorToast from "../Common/showErrorTostrMessage";
import moment from 'moment';
import Image from 'next/image';

export default function MobileViewInboxMessagesListing({ show, setModalConfirm2, selectedJobId, companyName }: any) {
    const [messagesData, setMessagesData]: any = useState([]);
    const [archivedMessagesData, setArchivedMessagesData]: any = useState([]);
    const [interviews, setInterviews] = useState([]);
    const router = useRouter();
    const [totalUnReadMessageCount, setTotalUnReadMessageCount] = useState(0);
    const [showPopupunsave, setShowPopupunsave] = useState(false);
    const [showPopupunsave1, setShowPopupunsave1] = useState(false);
    const [showmessage, setShowmessage] = useState('');
    const [showMobileView, setShowMobileView] = useState(false);
    const current_user_data: any = getCurrentUserData();
    useEffect(() => {
        const current_user_data: any = getCurrentUserData();
        getAllEmployerReceiverMessages(current_user_data.id)
            .then(res => {
                if (res.status == true) {
                    setMessagesData(res.data);
                } else {
                    setMessagesData([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
        const data = {
            user_id: current_user_data.id
        }
        getAllInterviews(data)
            .then(res => {
                if (res.status == true) {
                    setInterviews(res.data);
                } else {
                    setInterviews([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
        getAllEmployerReceiverArchivedMessages(current_user_data.id)
            .then(res => {
                if (res.status == true) {
                    setArchivedMessagesData(res.data);
                } else {
                    setArchivedMessagesData([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
        let details = navigator.userAgent;
        let regexp = /android|iphone|kindle|ipad/i;
        let isMobileDevice = regexp.test(details);
        if (isMobileDevice) {
            setShowMobileView(true);
        } else {
            setShowMobileView(false);
        }
    }, []);
    const handleClickMessageStatusUpdate = (e: any, sender_id: any) => {
        const current_user_data: any = getCurrentUserData();
        const data = {
            sender_id: current_user_data.id,
            receiver_id: sender_id
        }
        updateMessagesReadUnReadStatus(data)
            .then(res => {
                if (res.status == true) {
                    router.push("/employer/messages/inbox/" + sender_id);
                } else {
                    router.push("/employer/messages/inbox/" + sender_id);
                }
            })
            .catch(err => {
                setShowmessage(err);
                setShowPopupunsave1(true);
                setTimeout(() => {
                    setShowPopupunsave1(false)
                }, 3000)
            });
    }
    return (
        <>
            <section className='single-jobs-back bg-fff mb-5'>
                <ul className='list-loc m-m-0 mt-4'>
                    <li className='active'>
                        <Link href="/employer/messages">Inbox <span className="tab-span-sa c-0070F5">1 </span></Link>
                    </li>
                    <li>
                        <Link href="/employer/messages/interviews">Interviews <span className="tab-span-sa"> 0</span></Link>
                    </li>
                    <li>
                        <Link href="/employer/messages/archived">
                            Archived <span className="tab-span-sa"> 0</span>
                        </Link>
                    </li>
                </ul>
                <div className='bg-fff p-2'>
                    <div className="row mt-3">
                        <div className="col-5 pr-0">
                            <p className="f-22 ">Inbox</p>
                        </div>
                        <div className="col-7 text-right">
                            <p className="message-13">You have {messagesData.length} {messagesData.length !== 1 ? 'chats' : 'chat'} and {totalUnReadMessageCount} unread {totalUnReadMessageCount !== 1 ? 'messages' : 'message'}</p>
                        </div>
                    </div>
                    {messagesData.length > 0
                        ?
                        messagesData.map((messages_data: any, index: any) => {
                            let file_url = '';
                            if (messages_data.attachment_path) {
                                file_url = process.env.NEXT_PUBLIC_IMAGE_URL + 'images/messageAttachmentFile/' + messages_data.attachment_path;
                            }
                            const filename = file_url.substring(file_url.lastIndexOf('/') + 1);
                            const extension = filename.split('.').pop();
                            return (
                                messages_data.message_status == 'unread'
                                    ?
                                    messages_data.receiver_id == current_user_data.id
                                        ?
                                        <a href="#" onClick={(e: any) => handleClickMessageStatusUpdate(e, messages_data.sender_id)} key={index}>
                                            <div className='card-filter mb-2'>
                                                <p className='f-13 c-4D4D4D w-700 mb-1'>{messages_data.candidate_name}.{messages_data.candidate_position}</p>
                                                <p className='f-16 w-600 c-2C2C2C mb-1'>
                                                    {messages_data.message_description ? (
                                                        messages_data.message_status === 'unread' ? (
                                                            <span className='message2'>{messages_data.message_description}</span>
                                                        ) : (
                                                            <span className='message1'>{messages_data.message_description}</span>
                                                        )
                                                    ) : messages_data.attachment_path ? (
                                                        extension == 'pdf' || extension == 'xlxs' || extension == 'xlx' || extension == 'docx' || extension == 'doc' ? (
                                                            <a href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${messages_data.attachment_path}`} target='_blank' style={{ "fontSize": "14px" }}><i className="fa-solid fa-file" style={{ "fontSize": "16px" }}></i> {messages_data.attachment_path}</a>
                                                        ) :
                                                            <a href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${messages_data.attachment_path}`} target='_blank' style={{ "fontSize": "14px" }}><i className="fa-solid fa-image" style={{ "fontSize": "16px" }}></i> {messages_data.attachment_path}</a>
                                                    ) : (
                                                        ''
                                                    )
                                                    }
                                                </p>
                                                <p className='text-right f-13 c-4D4D4D w-600 mb-0 mt-2'>{moment.utc(messages_data.created_at).local().startOf('seconds').fromNow()}</p>
                                            </div>
                                        </a>
                                        :
                                        <a href="#" onClick={(e: any) => handleClickMessageStatusUpdate(e, messages_data.receiver_id)} key={index}>
                                            <div className='card-filter mb-2'>
                                                <p className='f-13 c-4D4D4D w-700 mb-1'>{messages_data.candidate_name}.{messages_data.candidate_position}</p>
                                                <p className='f-16 w-600 c-2C2C2C mb-1'>
                                                    {messages_data.message_description ? (
                                                        messages_data.message_status === 'unread' ? (
                                                            <span className='message2'>{messages_data.message_description}</span>
                                                        ) : (
                                                            <span className='message1'>{messages_data.message_description}</span>
                                                        )
                                                    ) : messages_data.attachment_path ? (
                                                        extension == 'pdf' || extension == 'xlxs' || extension == 'xlx' || extension == 'docx' || extension == 'doc' ? (
                                                            <a href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${messages_data.attachment_path}`} target='_blank' style={{ "fontSize": "14px" }}><i className="fa-solid fa-file" style={{ "fontSize": "16px" }}></i> {messages_data.attachment_path}</a>
                                                        ) :
                                                            <a href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${messages_data.attachment_path}`} target='_blank' style={{ "fontSize": "14px" }}><i className="fa-solid fa-image" style={{ "fontSize": "16px" }}></i> {messages_data.attachment_path}</a>
                                                    ) : (
                                                        ''
                                                    )
                                                    }
                                                </p>
                                                <p className='text-right f-13 c-4D4D4D w-600 mb-0 mt-2'>{moment.utc(messages_data.created_at).local().startOf('seconds').fromNow()}</p>
                                            </div>
                                        </a>
                                    :
                                    messages_data.receiver_id == current_user_data.id
                                        ?
                                        <a href="#" onClick={(e: any) => handleClickMessageStatusUpdate(e, messages_data.sender_id)} key={index}>
                                            <div className='card-filter mb-2'>
                                                <p className='f-13 c-4D4D4D w-700 mb-1'>{messages_data.candidate_name}.{messages_data.candidate_position}</p>
                                                <p className='f-16 w-600 c-2C2C2C mb-1'>
                                                    {messages_data.message_description ? (
                                                        messages_data.message_status === 'unread' ? (
                                                            <span className='message2'>{messages_data.message_description}</span>
                                                        ) : (
                                                            <span className='message1'>{messages_data.message_description}</span>
                                                        )
                                                    ) : messages_data.attachment_path ? (
                                                        extension == 'pdf' || extension == 'xlxs' || extension == 'xlx' || extension == 'docx' || extension == 'doc' ? (
                                                            <a href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${messages_data.attachment_path}`} target='_blank' style={{ "fontSize": "14px" }}><i className="fa-solid fa-file" style={{ "fontSize": "16px" }}></i> {messages_data.attachment_path}</a>
                                                        ) :
                                                            <a href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${messages_data.attachment_path}`} target='_blank' style={{ "fontSize": "14px" }}><i className="fa-solid fa-image" style={{ "fontSize": "16px" }}></i> {messages_data.attachment_path}</a>
                                                    ) : (
                                                        ''
                                                    )
                                                    }
                                                </p>
                                                <p className='text-right f-13 c-4D4D4D w-600 mb-0 mt-2'>{moment.utc(messages_data.created_at).local().startOf('seconds').fromNow()}</p>
                                            </div>
                                        </a>
                                        :
                                        <a href="#" onClick={(e: any) => handleClickMessageStatusUpdate(e, messages_data.receiver_id)} key={index}>
                                            <div className='card-filter mb-2'>
                                                <p className='f-13 c-4D4D4D w-700 mb-1'>{messages_data.candidate_name}.{messages_data.candidate_position}</p>
                                                <p className='f-16 w-600 c-2C2C2C mb-1'>
                                                    {messages_data.message_description ? (
                                                        messages_data.message_status === 'unread' ? (
                                                            <span className='message2'>{messages_data.message_description}</span>
                                                        ) : (
                                                            <span className='message1'>{messages_data.message_description}</span>
                                                        )
                                                    ) : messages_data.attachment_path ? (
                                                        extension == 'pdf' || extension == 'xlxs' || extension == 'xlx' || extension == 'docx' || extension == 'doc' ? (
                                                            <a href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${messages_data.attachment_path}`} target='_blank' style={{ "fontSize": "14px" }}><i className="fa-solid fa-file" style={{ "fontSize": "16px" }}></i> {messages_data.attachment_path}</a>
                                                        ) :
                                                            <a href={`${process.env.NEXT_PUBLIC_IMAGE_URL}/images/messageAttachmentFile/${messages_data.attachment_path}`} target='_blank' style={{ "fontSize": "14px" }}><i className="fa-solid fa-image" style={{ "fontSize": "16px" }}></i> {messages_data.attachment_path}</a>
                                                    ) : (
                                                        ''
                                                    )
                                                    }
                                                </p>
                                                <p className='text-right f-13 c-4D4D4D w-600 mb-0 mt-2'>{moment.utc(messages_data.created_at).local().startOf('seconds').fromNow()}</p>
                                            </div>
                                        </a>

                            )
                        })
                        :
                        <div className='work-experience-fieild m-p-10 text-center'>
                            <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blank-3.png'} alt="blank-3" className='' />
                            <p className='f-22 c-BABABA mb-1'>You don't seem to have any messages</p>
                            <p className='f-18 c-BABABA'>Go to  <a href="#" className='c-0070F5' > Messages</a></p>
                        </div>
                    }
                </div>
            </section>
            <section className='single-jobs-back bg-fff'>
                <ul className='list-loc m-m-0 mt-4'>
                    <li className='active'>
                        <Link href="/employer/messages">Inbox <span className="tab-span-sa c-0070F5">1 </span></Link>
                    </li>
                    <li>
                        <Link href="/employer/messages/interviews">Interviews <span className="tab-span-sa"> 0</span></Link>
                    </li>
                    <li>
                        <Link href="/employer/messages/archived">
                            Archived <span className="tab-span-sa"> 0</span>
                        </Link>
                    </li>
                </ul>
                <div className='bg-fff p-2'>
                    <div className="row mt-3">
                        <div className="col-5 pr-0">
                            <p className='c-4D4D4D f-22'><i className="fa-solid fa-arrow-left"></i></p>
                        </div>
                        <div className="col-7 text-right">
                            <ul className='repot'>
                                <li><img src="/images/flag.png" alt='flag.png' className='fla' /> Report</li>
                                <li><img src="/images/archive.png" alt='archive.png' className='fla' /> Archive</li>
                            </ul>
                        </div>
                    </div>
                    <p className='c-191919 w-700 f-22 text-center mb-2'>Name of Position</p>
                    <p className='c-0070F5 w-700 f-22 text-center'>Name of Position</p>
                    <hr />
                    <div className='scrollable'>
                        <div className='card-filter mt-3 mb-4'>
                            <div className='row mt-2    '>
                                <div className='col-7'>
                                    <p className='f-13 c-747474 w-400 open-sans mt-2'>You applied to this position on March 21, 2023.</p>
                                </div>
                                <div className='col-5'>
                                    <button className="view-jobs">View Jobs</button>
                                </div>
                            </div>
                        </div>
                        <p className='f-13 c-2C2C2C w-600 mb-1'>
                            <img src="/images/user-blog.png" alt="Avatars-5" className="user-short" /> Recruiter/Company Name</p>
                        <p className='text-right f-13 c-4D4D4D w-400 mb-0 mt-2  '>March 21, 1:20PM</p>
                        <br />
                        <p className='c-4D4D4D f-16 w-400  open-sans '>Lorem ipsum dolor sit amet consectetur. Mauris arcu gravida nibh diam. Imperdiet nec elementum elementum phasellus sit sit ultrices. In et maecenas nunc eget ac sapien. Morbi arcu scelerisque turpis adipiscing morbi convallis. Mauris at tincidunt rutrum lacus augue arcu amet ultrices volutpat. Vitae blandit et libero sed aenean elit tortor sagittis fames. Porta id libero dictumst nunc tempus...</p>
                        <br />
                        <div className='textarea-filter bg-fff mb-2'>
                            <textarea placeholder='Your message goes here...'></textarea>
                            <div className='row'>
                                <div className='col-2 pr-0'>
                                    <button className="view-jobs b-r-8"><i className="fa-solid fa-paperclip"></i></button>
                                </div>
                                <div className='col-10'>
                                    <button className='btn-a primary-size-16 b-r-4 btn-bg-0055BA mr-1 tab-w-100'>Send</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            {showPopupunsave &&
                <SuccessToast message={showmessage} />
            }
            {showPopupunsave1 &&
                <ErrorToast message={showmessage} />
            }
        </>
    )
}
