import React, {useEffect, useMemo, useRef, useState} from 'react';
import styles from './style.module.css';
import EmailEditor, {EditorRef, EmailEditorProps} from 'react-email-editor';
import {ButtonUi} from '@/ui/Button';
import {useRouter} from 'next/router';
import {useGetDataPoints} from '@/modules/admin/automation/query/useGetDataPoints';
import {useFileUpload} from '@/hooks/useFileUpload';
import {notification} from 'antd';
import {InputUi} from '../Input';
import NiceModal from '@ebay/nice-modal-react';
import {TestEmailSendContainer} from '@/components/Admin/Automation/TemplateSection/EmailEditSection/TestEmailSendContainer';
import {useGetDataPointOptions} from '@/hooks/useGetDataTypeOptions';

interface EmailEditorContainerProps {
  json?: any;
  html?: string;
  onClose?: () => void;
  setTemplateHtml: (html: string) => void;
  setTemplateJson: (json: any) => void;
}

export const EmailEditorContainer = ({
  json,
  html,
  onClose,
  setTemplateHtml,
  setTemplateJson,
}: EmailEditorContainerProps) => {
  const emailEditorRef = useRef<EditorRef>(null);
  const [testEmail, setTestEmail] = useState('');
  const router = useRouter();
  const {mutate: upload} = useFileUpload();
  const {data} = useGetDataPointOptions();

  const allData = data?.dataPoints;

  const saveChange = () => {
    const unlayer = emailEditorRef.current?.editor;
    unlayer?.exportHtml(data => {
      const {design, html} = data;
      setTemplateHtml?.(html);
      setTemplateJson?.(JSON.stringify(design));
      handleClose();
    });
  };

  const onReady: EmailEditorProps['onReady'] = unlayer => {
    if (json) {
      unlayer.loadDesign({
        body: json.body,
        counters: json.counters,
        schemaVersion: json.schemaVersion,
      });
    }
    handleImageUpload();
  };
  const handleImageUpload = () => {
    emailEditorRef?.current?.editor?.registerCallback(
      'image',
      (
        file: {
          accepted: [File];
          attachments: [File];
        },
        done: any,
      ) => {
        var data = new FormData();
        data.append('file', file?.attachments?.[0]);

        upload(data, {
          onSuccess: res => {
            console.log(res, 'res');
            done({url: res.source, progress: 100});
          },
          onError: (err: any) => {
            notification.error(err);
          },
        });
      },
    );
  };

  useEffect(() => {
    if (emailEditorRef.current) {
      handleImageUpload();
    }
  }, [emailEditorRef]);

  const handleClose = () => {
    if (onClose) {
      onClose?.();
    }
  };
  const handleTestEmail = () => {
    NiceModal.show(TestEmailSendContainer);
  };

  return (
    <div className={styles.email_editor_container}>
      <div className={styles.email_editor_button_container}>
        <InputUi
          name="email"
          placeholder="Enter email here"
          bottomLabel={false}
          inputSize="small"
          onChange={e => setTestEmail(e.target.value)}
          value={testEmail}
        />
        <ButtonUi color="primary" variant="outlined" disabled={!testEmail} onClick={handleTestEmail}>
          Test
        </ButtonUi>
        <ButtonUi onClick={handleClose} color="primary" variant="outlined">
          Close
        </ButtonUi>
        <ButtonUi onClick={saveChange} color="primary" variant="contained">
          Save Changes
        </ButtonUi>
      </div>

      <EmailEditor
        ref={emailEditorRef}
        onReady={onReady}
        style={{height: 'calc(100vh - 200px)'}}
        options={
          {
            id: router.query.id as string,
            mergeTags: allData?.map(item => {
              return {
                name: item.label,
                value: `{{${item.label}}}`,
              };
            }),
          } as any
        }
      />
    </div>
  );
};
