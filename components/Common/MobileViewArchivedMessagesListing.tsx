import React, { useState, useEffect } from 'react';
import { useRouter } from "next/router";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getCurrentUserData } from "../../lib/session";
import PopupModal from './PopupModal';
import { applyJob, getSingleUserDetails } from '../../lib/frontendapi';
import ApplicationThankYouPopup from './ApplicationThankYouPopup';
import { HtmlEditor } from "./HtmlEditor";

export default function MobileViewArchivedMessagesListing({ show, setModalConfirm2, selectedJobId,companyName}: any) {
    return (
        <>
            fdgfdgdfgdfdgdfdgdfdg
        </>
    )
}
