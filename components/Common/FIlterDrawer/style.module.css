.drawer_container {
  position: relative;
}
.drawer {
  position: fixed;
  right: -100%;
  top: 75px;
  height: 100%;
  width: 300px;
  transition: right 0.3s ease;
  display: flex;
  flex-direction: column;
  width: 400px;
  max-height: calc(100vh - 80px);
  border-left: 1px solid #eee;
  background: #fff;
  box-shadow: -2px 0px 15px 0px rgba(0, 0, 0, 0.09);
  padding: 12px;
  height: 100%;
  padding-bottom: 80px;
  z-index: 9999;
}

.drawer.open {
  right: 0;
  overflow-y: auto;
}

.header {
  display: flex;
  max-width: 368px;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.header_content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header_content h2 {
  color: #191919;
  font-size: 22px;
  font-weight: 500;
  line-height: 120%;
}

.clearAll {
  color: #747474;
  font-size: 14px;
  line-height: 120%;
  text-decoration-line: underline;
  cursor: pointer;
}

.close {
  font-size: 1.5rem;
  border: none;
  background: none;
  cursor: pointer;
}

.filters {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}
.applyButton_container {
  display: flex;
  position: absolute;
  bottom: 4px;
  width: 368px;
}

.applyButton {
  display: flex;
  padding: 12px 24px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  border-radius: 8px;
  background: #0055ba;
  width: 100%;
}

.ant-checkbox-wrapper {
  display: block;
  margin-bottom: 0.5rem;
}

.ant-radio-wrapper {
  display: block;
  margin-bottom: 0.5rem;
}
.accordion_container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.accordion_item {
  border: none !important;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.accordion_header button {
  border: none !important;
  padding: 8px 0 !important;
  border-bottom: 1px solid #eee !important;
  color: #191919;
  font-size: 16px;
  line-height: 140%;
}
.accordion_header button::after {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9InByaW1lOmFuZ2xlLWRvd24iPgo8cGF0aCBpZD0iVmVjdG9yIiBkPSJNOC4xMDI1NiAxMC45NTc2QzcuOTg0MTggMTAuOTU4MiA3Ljg2Njg5IDEwLjkzNTEgNy43NTc1NSAxMC44ODk3QzcuNjQ4MjIgMTAuODQ0MyA3LjU0OTA1IDEwLjc3NzYgNy40NjU4NyAxMC42OTM0TDMuMjk3MzQgNi40ODg3OUMzLjE4NzU1IDYuMzE5NDUgMy4xMzc3OSA2LjExODE3IDMuMTU2MDMgNS45MTcxOEMzLjE3NDI4IDUuNzE2MiAzLjI1OTQ3IDUuNTI3MTYgMy4zOTc5NiA1LjM4MDM3QzMuNTM2NDQgNS4yMzM1NyAzLjcyMDIgNS4xMzc1MiAzLjkxOTc4IDUuMTA3NjFDNC4xMTkzNiA1LjA3NzcgNC4zMjMyMSA1LjExNTY2IDQuNDk4NjQgNS4yMTU0TDguMTAyNTYgOC44MTkzMkwxMS43MDY1IDUuMjE1NEMxMS44ODE5IDUuMTE1NjYgMTIuMDg1OCA1LjA3NzcgMTIuMjg1MyA1LjEwNzYxQzEyLjQ4NDkgNS4xMzc1MiAxMi42Njg3IDUuMjMzNTcgMTIuODA3MiA1LjM4MDM3QzEyLjk0NTcgNS41MjcxNiAxMy4wMzA4IDUuNzE2MiAxMy4wNDkxIDUuOTE3MThDMTMuMDY3MyA2LjExODE3IDEzLjAxNzYgNi4zMTk0NSAxMi45MDc4IDYuNDg4NzlMOC43MDMyMSAxMC42OTM0QzguNTQ0MDMgMTAuODU0OCA4LjMyOTE2IDEwLjk0OTQgOC4xMDI1NiAxMC45NTc2WiIgZmlsbD0iIzE5MTkxOSIvPgo8L2c+Cjwvc3ZnPgo=') !important;
}

.accordion_header button:not(.collapsed) {
  outline: none;
  background: none !important;
  color: #191919 !important;
  box-shadow: none !important;
}

.accordion_body {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 0 !important;
}
.checkbox_container {
  display: flex;
  flex-direction: column;
  max-height: 200px;
  overflow-y: auto;
  flex-wrap: initial !important;
}
.checkbox_container label {
  display: flex;
  padding: 8px;
  align-items: center;
  gap: 6px;
  background: white;
}
.buttonSelectContainer {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.buttonSelectContainer button {
  display: flex;
  padding: 8px 16px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  border: 1px solid #bababa;
  color: #4d4d4d;
  font-size: 14px;
}

.selectedButton {
  background: #0055ba !important;
  color: white !important;
}

.buttonSelectContainer button:hover {
  background: #0055ba !important;
  color: white !important;
}
.blueDot {
  display: inline-block;
  width: 6px;
  height: 6px;
  background-color: #0070f5;
  border-radius: 50%;
  margin-left: 8px;
}
.salaryDropdownContainer {
  display: flex;
  align-items: center;
  gap: 8px;
}
