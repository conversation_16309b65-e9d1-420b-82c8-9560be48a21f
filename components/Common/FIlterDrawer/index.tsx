import {Accordion} from 'react-bootstrap';
import {Checkbox, Input, Button, Tabs} from 'antd';
import styles from './style.module.css';
import Image from 'next/image';
import {Dispatch, SetStateAction, useEffect, useState} from 'react';
import CustomDropdown from '../Dropdown';
import {BulkAction} from '@/components/Admin/Employees/BulkAction';
import {User} from '@/lib/types';
import {ButtonUi} from '@/ui/Button';

interface FilterDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  filters?: Filter[];
  onFilterApply?: (value: Record<string, any>) => void;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
  variant: 'filter' | 'bulkAction';
  updatedQuery?: any;
  setUpdatedQuery?: Dispatch<SetStateAction<any>>;
  selectedFilters?: Record<string, any>;
  setSelectedFilters?: Dispatch<SetStateAction<Record<string, any>>>;
  selectedItems?: number[];
  setSelectedItems?: Dispatch<SetStateAction<number[]>>;
  drawerType?: 'candidate' | 'employer';
}

export interface Filter {
  label: string;
  items: Record<string, any>[];
  type: 'checkbox' | 'checkboxWithSearch' | 'dropdown' | 'buttonSelect' | 'input';
  key: string;
}

export default function ActionDrawer({
  isOpen,
  onClose,
  filters,
  onFilterApply,
  setIsOpen,
  variant,
  updatedQuery,
  selectedFilters,
  setSelectedFilters,
  selectedItems,
  setSelectedItems,
  drawerType = 'candidate',
}: FilterDrawerProps) {
  const [searchTerms, setSearchTerms] = useState<{[key: string]: string}>({});

  const handleButtonClick = (value: string, filterLabel: string, label: string) => {
    const isCurrentlySelected = selectedFilters?.[filterLabel]?.[0]?.value === value;
    setSelectedFilters?.(prev => ({...prev, [filterLabel]: isCurrentlySelected ? [] : [{value: value, label: label}]}));
  };
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>, filter: Filter) => {
    setSearchTerms(prev => ({...prev, [filter.label]: e.target.value}));
  };

  const handleCheckboxChange = (item: any, filterLabel: string, isMultiSelect: boolean) => {
    setSelectedFilters?.(prev => {
      // Check if this item is already selected
      const isItemSelected = prev?.[filterLabel]?.some(
        (el: any) => el.value === item.value || JSON.stringify(el.value) === JSON.stringify(item.value)
      );

      if (isItemSelected) {
        // If already selected, remove it
        return {
          ...prev,
          [filterLabel]: prev[filterLabel].filter(
            (el: any) => el.value !== item.value && JSON.stringify(el.value) !== JSON.stringify(item.value)
          )
        };
      } else {
        // If not selected, add it
        return {
          ...prev,
          [filterLabel]: isMultiSelect ? [...(prev?.[filterLabel] || []), item] : [item],
        };
      }
    });
  };
  const handleFilterApply = () => {
    const appliedFilters = filters?.reduce(
      (acc, filter) => {
        if (filter.label === 'Salary') {
          const currency = selectedFilters?.['currency'];
          const minSalary = selectedFilters?.['minSalary'];
          const maxSalary = selectedFilters?.['maxSalary'];

          if (currency) acc['currency'] = currency;
          if (minSalary && maxSalary) {
            acc['salary'] = [
              {label: 'Min Salary', value: minSalary},
              {label: 'Max Salary', value: maxSalary},
            ];
          }
        } else if (selectedFilters?.[filter.label]) {
          acc[filter.key] = Array.isArray(selectedFilters[filter.label])
            ? selectedFilters[filter.label].map((item: any) => ({value: item.value, label: item.label}))
            : selectedFilters[filter.label];
        }

        return acc;
      },
      {} as Record<string, any>,
    );

    appliedFilters && onFilterApply?.(appliedFilters);
    setIsOpen?.(false);
  };

  const handleResetFilters = () => {
    setSelectedFilters?.({});
    setSearchTerms({});

    Object.entries(updatedQuery).forEach(([key, value]) => {
      delete updatedQuery[key];
    });
    onFilterApply?.({});
    setIsOpen?.(false);
  };

  const renderFilterItems = (filter: Filter) => {
    switch (filter.type) {
      case 'checkbox':
        return (
          <div className={styles.checkbox_container}>
            {filter.items.map((item, index) => (
              <Checkbox
                key={index}
                value={item}
                checked={
                  !!(selectedFilters?.[filter.label] ?? [])?.some(
                    (el: any) => el.value === item.value || JSON.stringify(el.value) === JSON.stringify(item.value),
                  )
                }
                onChange={e =>
                  handleCheckboxChange(
                    item,
                    filter.label,
                    false, // Ensures only one selection for 'checkbox' type
                  )
                }>
                {item.label}
              </Checkbox>
            ))}
          </div>
        );

      case 'checkboxWithSearch':
        const filteredItems = filter?.items?.filter(item =>
          searchTerms?.[filter?.label]
            ? item.label.toLowerCase().includes(searchTerms?.[filter?.label].toLowerCase())
            : true,
        );
        return (
          <>
            <Input.Search
              placeholder={`Search ${filter.label.toLowerCase()}...`}
              onChange={e => handleSearchChange(e, filter)}
              value={searchTerms[filter.label] || ''}
            />
            <div className={styles.checkbox_container}>
              {filteredItems?.length > 0 ? (
                filteredItems.map((item, index) => (
                  <Checkbox
                    value={item}
                    key={index}
                    checked={
                      !!selectedFilters?.[filter.label]?.some(
                        (el: any) => el.value === item.value || JSON.stringify(el.value) === JSON.stringify(item.value),
                      )
                    }
                    onChange={e => {
                      handleCheckboxChange(item, filter.label, true);
                    }}>
                    {item.label}
                  </Checkbox>
                ))
              ) : (
                <p>No results found</p>
              )}
            </div>
          </>
        );

      case 'input':
        return (
          <>
            <Input
              placeholder={`Search for a ${filter.label.toLowerCase()} by keyword...`}
              value={selectedFilters?.[filter.label] || ''}
              onChange={e => {
                const value = e.target.value;
                setSelectedFilters?.(prev => ({...prev, [filter.label]: value}));
              }}
            />
          </>
        );

      case 'buttonSelect':
        return (
          <div className={styles.buttonSelectContainer}>
            {filter.items.map((item, index) => (
              <Button
                key={index}
                type="default"
                className={`${styles.selectableButton} ${
                  selectedFilters?.[filter.label]?.[0]?.value === item.value ? styles.selectedButton : ''
                }`}
                onClick={() => handleButtonClick(item.value, filter.label, item.label)}>
                {item.label}
              </Button>
            ))}
          </div>
        );

      case 'dropdown':
        return (
          <div className={styles.salaryDropdownContainer}>
            <CustomDropdown
              item={{
                label: 'Currency',
                multiSelect: false,
                menuProps: filter?.items?.find(item => item.type === 'currency')?.items ?? [],
              }}
              onSelect={value => setSelectedFilters?.(prev => ({...prev, currency: value}))}
              variant="dropdown-white"
            />
            <CustomDropdown
              item={{
                label: 'Min Salary',
                multiSelect: false,
                menuProps: filter.items.find(item => item.type === 'minSalary')?.items ?? [],
              }}
              onSelect={value => setSelectedFilters?.(prev => ({...prev, minSalary: value}))}
              variant="dropdown-white"
            />
            <CustomDropdown
              item={{
                label: 'Max Salary',
                multiSelect: false,
                menuProps: filter.items.find(item => item.type === 'maxSalary')?.items ?? [],
              }}
              onSelect={value => setSelectedFilters?.(prev => ({...prev, maxSalary: value}))}
              variant="dropdown-white"
            />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={styles.drawer_container}>
      <div className={`${styles.drawer} ${isOpen ? styles.open : ''}`}>
        <div className={styles.header}>
          <button className={styles.close} onClick={onClose}>
            <img src={'/icons/candidate/collapse.svg'} width={20} height={20} alt="collapse" />
          </button>
          <div className={styles.header_content}>
            <h2>{variant === 'filter' ? 'Filters' : 'Bulk Actions'}</h2>
            {variant === 'filter' && (
              <p className={styles.clearAll} onClick={handleResetFilters}>
                Clear all
              </p>
            )}
          </div>
        </div>
        {variant === 'filter' && (
          <div className={`${styles.filters}`}>
            <Accordion defaultActiveKey="0" className={styles.accordion_container} alwaysOpen>
              {filters?.map((filter, index) => (
                <Accordion.Item eventKey={filter.label} key={index} className={styles.accordion_item}>
                  <Accordion.Header className={styles.accordion_header}>
                    {filter.label}
                    {Array.isArray(selectedFilters?.[filter.label]) && selectedFilters?.[filter.label].length > 0 && (
                      <span className={styles.blueDot}></span>
                    )}
                    {filter.label === 'Salary' &&
                      (selectedFilters?.['currency'] ||
                        selectedFilters?.['minSalary'] ||
                        selectedFilters?.['maxSalary']) && <span className={styles.blueDot}></span>}
                  </Accordion.Header>
                  <Accordion.Body className={styles.accordion_body}>{renderFilterItems(filter)}</Accordion.Body>
                </Accordion.Item>
              ))}
            </Accordion>
          </div>
        )}

        {variant === 'bulkAction' && (
          <div>
            <div className={styles.tab_container}>
              <Tabs
                defaultActiveKey="1"
                items={[
                  {
                    key: '1',
                    label: 'Export',
                    children: (
                      <>
                        <BulkAction
                          variant="export"
                          selectedItems={selectedItems}
                          setIsOpen={setIsOpen}
                          setSelectedItems={setSelectedItems}
                          drawerType={drawerType}
                        />
                      </>
                    ),
                  },
                  {
                    key: '2',
                    label: 'Delete',
                    children: (
                      <>
                        <BulkAction
                          variant="delete"
                          selectedItems={selectedItems}
                          setIsOpen={setIsOpen}
                          setSelectedItems={setSelectedItems}
                          drawerType={drawerType}
                        />
                      </>
                    ),
                  },
                ]}
              />
            </div>
          </div>
        )}
        {variant === 'filter' && (
          <div className={styles.applyButton_container}>
            <ButtonUi
              style={{
                width: '100%',
                borderRadius: '8px',
                height: '49px',
              }}
              rounded={false}
              color="primary"
              variant="contained"
              className={styles.applyButton}
              onClick={handleFilterApply}>
              Apply
            </ButtonUi>
          </div>
        )}
      </div>
    </div>
  );
}
