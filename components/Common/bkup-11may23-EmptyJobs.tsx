import React, {useState, useEffect} from 'react';
import { useRouter } from "next/router";

export default function EmptyJobs() {
    const router = useRouter();
    return(
        <>
            <div className='dash-right'>
                <h1>Jobs</h1>
                <div className='row mt-4'>
                <div className='col-sm-8'>
                    <div className='sort-d-flex mt-2'>
                        <p className='sort-by'>Sort By:</p>
                        <select className='all-recent'>
                            <option>All/Recent/Active/Exp</option>
                            <option>All/Recent/Active/Exp 2</option>
                            <option>All/Recent/Active/Exp 3</option>
                            <option>All/Recent/Active/Exp 4</option>
                        </select>
                    </div>
                </div>
                <div className='col-sm-4 text-right'>
                    {router.pathname.startsWith("/employer") ?
                        <button className="btn-a primary-size-16 btn-bg-0055BA"><i className="fa-solid fa-plus"></i> &nbsp; Post A New Job</button>
                        :
                        ''
                    } 
                </div>
                </div>
                <div className="table-part mt-4"> 
                    {router.pathname.startsWith("/employer") ?
                        <table className="rwd-table">
                            <tbody>
                            <tr>
                                <th>JOB TITLE<i className="fa-solid fa-align-left"></i></th>
                                <th className='w-18'>IMPRESSIONS <i className="fa-solid fa-align-left"></i></th>
                                <th>POSTED ON <i className="fa-solid fa-align-left"></i></th>
                                <th>ALL RESPONSES <i className="fa-solid fa-align-left"></i></th>
                                <th>STATUS  </th>
                                <th>MANAGE  </th>
                            </tr>  
                            </tbody>
                        </table> 
                        :
                            ''
                    }
                    <div className='work-experience-fieild m-p-10 text-center'> 
                        <img src={process.env.NEXT_PUBLIC_BASE_URL+'images/blank-1.jpg'} alt="blank-1" className=''/>
                        <p className='f-22 c-BABABA mb-1'>You don't seem to have any active jobs.</p>
                        <p className='f-18'><a href="#" className='c-0070F5'>Post A Job</a></p>
                    </div>
                </div>
            </div>
        </>
    )
}