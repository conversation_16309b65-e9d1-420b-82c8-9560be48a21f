import Image from 'next/image';
import styles from './style.module.css';

interface ChipCardProps {
  chip: Chip;
  handleChipClick: () => void;
  handleRemoveChip: (index: number) => void;
  index: number;
}

export interface Chip {
  label: string;
  value: string[];
  key: string;
}

export const ChipCard = ({chip, handleChipClick, handleRemoveChip, index}: ChipCardProps) => {
  return (
    <div className={styles.chip} onClick={handleChipClick}>
      {chip.label}
      <img
        src={'/icons/candidate/close.svg'}
        width={16}
        height={16}
        alt="close"
        onClick={e => {
          e.stopPropagation();
          handleRemoveChip(index);
        }}
      />
    </div>
  );
};
