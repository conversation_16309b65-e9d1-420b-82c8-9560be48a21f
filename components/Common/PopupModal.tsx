import React, {useEffect} from 'react';

/**
 *
 * @param handleClose
 * @param show
 * @param children
 * @param customclass
 * @param closebtnclass
 * @param closebtnicon
 * @constructor
 * @deprecated Should be removed
 */
const PopupModal = ({handleClose, show, children, customclass, closebtnclass, closebtnicon}: any) => {
  const showHideClassName = show ? 'modal modal-part d-block' : 'modal modal-part d-none';
  const customclassname = customclass ? 'modal-dialog ' + customclass : 'modal-dialog';
  const closebtnclassname = closebtnclass ? closebtnclass : 'btn-close';
  const closebtniconname = closebtnicon ? <i className="fa-solid fa-xmark"></i> : '';
  useEffect(() => {
    const handleClickOutside = (event: any) => {
      if (event.target.className === 'modal modal-part d-block') {
        handleClose();
      }
    };

    document.addEventListener('click', handleClickOutside);

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [handleClose]);
  if (!show) {
    return;
  }

  return (
    <div className={showHideClassName}>
      <div className={`modal-dialog-centered ${customclassname}`}>
        <div className="modal-content">
          <div className="modal-header border-bottom-0">
            <button type="button" className={closebtnclassname} onClick={handleClose} aria-label="Close">
              {closebtniconname}
            </button>
          </div>
          <div className="modal-body">{children}</div>
        </div>
      </div>
    </div>
  );
};

export default PopupModal;
