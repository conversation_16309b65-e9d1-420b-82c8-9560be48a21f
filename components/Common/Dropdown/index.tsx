import {Button, Checkbox, Input, Select, Slider} from 'antd';
import Image from 'next/image';
import styles from './style.module.css';
import {useEffect, useRef, useState} from 'react';
import {DropdownType, filterItems} from '@/components/Frontend/JobOfferPage';
import {Country} from '@/lib/types';
import Search from 'antd/lib/transfer/search';
import Dayjs from 'dayjs';
import {DatePickerUi} from '../DatePicker';
import {DatePicker} from 'antd/lib';
import {useClickOutside} from '@/hooks/useOutSideClick';

interface CustomDropdownProps {
  item: filterItems;
  onSelect: (value: any) => void;
  countries?: Country[];
  variant: DropdownType;
  className?: string;
}

const CustomDropdown = ({item, onSelect, countries, variant, className}: CustomDropdownProps) => {
  const MAX_SALARY = 70000;
  const isMultiSelect = item.multiSelect || false;
  const [selectedLabels, setSelectedLabels] = useState<string[]>([]);
  const [selectedValues, setSelectedValues] = useState<string[]>([]);
  const [selectedSalary, setSelectedSalary] = useState<number[]>([0, MAX_SALARY]);
  const [selectedCurrency, setSelectedCurrency] = useState<Country | undefined>();
  const [dateRange, setDateRange] = useState<{startDate: string | null; endDate: string | null}>({
    startDate: null,
    endDate: null,
  });
  const calendarRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  useClickOutside(dropdownRef, [calendarRef], () => setIsDropdownOpen(false));

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const handleSelect = (option: {label: string; value: string[]}) => {
    const {label, value} = option;

    if (isMultiSelect) {
      const isSelected = selectedLabels.includes(label);
      const updatedLabels = isSelected ? selectedLabels.filter(l => l !== label) : [...selectedLabels, label];
      const updatedValues = isSelected ? selectedValues.filter(v => !value.includes(v)) : [...selectedValues, ...value];

      setSelectedLabels(updatedLabels);
      setSelectedValues(updatedValues);
      onSelect(updatedValues);
    } else {
      setSelectedLabels([label]);
      setSelectedValues(value);
      setIsDropdownOpen(false);
      onSelect(value);
    }
  };

  useEffect(() => {
    if (variant === 'slider' && !isDropdownOpen) {
      onSelect({salary: selectedSalary, currency: selectedCurrency?.currency});
    }
  }, [isDropdownOpen]);

  const handleCheckboxChange = (checked: boolean, option: {label: string; value: string[]}) => {
    if (checked) {
      handleSelect(option);
    } else {
      const updatedLabels = selectedLabels.filter(label => label !== option.label);
      const updatedValues = selectedValues.filter(value => !option.value.includes(value));
      setSelectedLabels(updatedLabels);
      setSelectedValues(updatedValues);
      onSelect(updatedValues);
    }
  };

  const renderDropdown = () => {
    switch (variant) {
      case 'checkboxDropdown':
        return (
          <div
            className={styles['custom-dropdown']}
            style={{
              minWidth: 220,
            }}>
            {renderCheckboxDropdown()}
          </div>
        );
      case 'checkboxWithSearch':
        return (
          <div
            className={styles['custom-dropdown']}
            style={{
              width: 320,
            }}>
            {renderCheckboxWithSearch()}
          </div>
        );
      case 'slider':
        return renderSliderContent();
      case 'dropdown-white':
        return (
          <ul
            className={styles['custom-dropdown']}
            style={{
              ...item.style,
              width: item.width,
              top: '34px',
            }}>
            {renderMenuItems()}
          </ul>
        );
      case 'dropdown-transparent':
        return (
          <ul
            className={styles['custom-dropdown']}
            style={{
              ...item.style,
              width: item.width,
              top: '60px',
            }}>
            {renderMenuItems()}
          </ul>
        );
      case 'range-picker':
        return renderRangePicker();
      default:
        <ul
          className={styles['custom-dropdown']}
          style={{
            ...item.style,
            width: item.width,
            top: '34px',
          }}>
          {renderMenuItems()}
        </ul>;
    }
  };

  const renderMenuItems = () =>
    item.menuProps?.map((menu, index) => (
      <li
        key={index}
        onClick={() => handleSelect(menu)}
        className={`${styles['dropdown-item']} ${
          selectedLabels.includes(menu.label) && !isMultiSelect ? styles['selected'] : ''
        }`}>
        {isMultiSelect && (
          <Checkbox
            checked={selectedLabels.includes(menu.label)}
            onClick={e => e.stopPropagation()}
            onChange={e => handleCheckboxChange(e.target.checked, menu)}
          />
        )}
        {menu.label}
      </li>
    ));

  const renderSliderContent = () => (
    <div className={`${styles['custom-dropdown']} ${styles['slider-container']}`}>
      <h4>{item.label}</h4>
      <p>
        {selectedCurrency?.currency} {selectedSalary[0]} - {selectedCurrency?.currency} {selectedSalary[1]}
      </p>
      <Slider
        range
        step={100}
        max={MAX_SALARY}
        defaultValue={[0, MAX_SALARY]}
        value={selectedSalary}
        onChange={values => setSelectedSalary(values)}
      />
      <Select
        placeholder="Choose currency"
        style={{width: '100%'}}
        options={countries?.map(c => ({label: c.currency, value: c.id.toString()}))}
        value={selectedCurrency?.id.toString()}
        onChange={value => setSelectedCurrency(countries?.find(c => c.id.toString() === value))}
        className={styles['customize-dropdown']}
      />
    </div>
  );

  const renderCheckboxDropdown = () =>
    item.menuProps?.map((option, index) => (
      <div key={index} className={styles['dropdown-item']}>
        <Checkbox
          checked={selectedLabels.includes(option.label)}
          onChange={e => handleCheckboxChange(e.target.checked, option)}>
          {option.label}
        </Checkbox>
      </div>
    ));

  const renderCheckboxWithSearch = () => (
    <>
      <div className={styles['dropdown-item']}>
        <Search placeholder={`Search ...`} />
      </div>
      {item.menuProps &&
        item?.menuProps?.length > 0 &&
        item.menuProps?.map((option, index) => (
          <div key={index} className={styles['dropdown-item']}>
            <Checkbox
              key={index}
              checked={selectedLabels.includes(option.label)}
              onChange={e => handleCheckboxChange(e.target.checked, option)}>
              <span> {option.label}</span>
            </Checkbox>
          </div>
        ))}
    </>
  );

  const renderRangePicker = () => (
    <div
      className={styles['custom-dropdown']}
      style={{
        minWidth: 320,
      }}>
      {item.menuProps?.map((option, index) => (
        <div key={index}>
          <div className={styles['dropdown-item']}>{option.label}</div>
          {option.hasCalender && (
            <div className={styles.datePicker_container}>
              <DatePicker
                onChange={e => {
                  if (e) {
                    setDateRange({...dateRange, startDate: e.format('YYYY-MM-DD')});
                    onSelect({
                      startDate: e.format('YYYY-MM-DD'),
                      endDate: dateRange.endDate,
                    });
                  }
                }}
                value={dateRange.startDate ? Dayjs(dateRange.startDate) : null}
                getPopupContainer={() => calendarRef.current || document.body}
                dropdownClassName={styles['date-picker-dropdown']}
              />
              <DatePicker
                onChange={e => {
                  if (e) {
                    setDateRange({...dateRange, endDate: e.format('YYYY-MM-DD')});
                    onSelect({
                      startDate: dateRange.startDate,
                      endDate: e.format('YYYY-MM-DD'),
                    });
                  }
                }}
                value={dateRange.endDate ? Dayjs(dateRange.endDate) : null}
                getPopupContainer={() => calendarRef.current || document.body}
                dropdownClassName={styles['date-picker-dropdown']}
              />
            </div>
          )}
        </div>
      ))}
    </div>
  );

  return (
    <div className={styles['dropdown-container']} ref={dropdownRef}>
      <Button
        className={`${styles['dropdown-button']} ${className}`}
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}>
        {item.label}
        <img
          src={variant === 'dropdown-transparent' ? '/icons/white-chevron-down.svg' : '/icons/chevron-down.svg'}
          alt="chevron-down"
          width={20}
          height={20}
        />
      </Button>

      {isDropdownOpen && <div ref={calendarRef}>{renderDropdown()}</div>}
    </div>
  );
};

export default CustomDropdown;
