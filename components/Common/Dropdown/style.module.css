.dropdown-container {
  position: relative;
}
.dropdown-button {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  background: #fff;
  position: relative;
}

.dropdown-button:hover {
  background-color: #f0f0f0;
}

.custom-dropdown {
  position: absolute;
  min-width: 200px;
  max-height: 200px;
  overflow-y: auto;
  width: fit-content;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  border: 1px solid #eee;
  background: #fff;
  box-shadow: 0px 4px 15px 0px rgba(0, 0, 0, 0.09);
}

.slider-container {
  padding: 12px !important;
  width: 400px;
  top: 34px;
}
.slider-container h4 {
  color: #2c2c2c;
  padding-bottom: 8px;
  font-size: 16px;
  font-weight: 600;
  line-height: 140%;
}
.slider-container p {
  color: #2c2c2c;
  font-size: 12px;
  font-weight: 400;
  line-height: 140%;
}

.dropdown-item {
  display: flex;
  padding: 6px 16px;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  color: #2c2c2c;
  font-weight: 400 !important;
  width: 100%;
}
.selected {
  background: #ebf4ff !important;
}

.dropdown-item:hover {
  background: #ebf4ff;
  cursor: pointer;
}

.dropdown-item.selected {
  background-color: #e0e0e0;
  font-weight: bold;
}

.dropdown-button img {
  transition: transform 0.3s ease;
}

.dropdown-button img.rotate {
  transform: rotate(180deg);
}
.checkbox-square {
  display: flex;
  width: 18px;
  height: 18px;
  padding: 2.25px;
  justify-content: center;
  align-items: center;
  gap: 11.25px;
  border-radius: 4.5px;
  border: 1.125px solid #d9d9d9;
  background: #fff;
}
.customize-dropdown {
  border-radius: 4px;
  border: 1px solid #bababa;
  background: #fff;
}

.custom-dropdown span {
  color: #2c2c2c;
  font-size: 12px;
  font-weight: 400;
  line-height: 150%;
  letter-spacing: 0.24px;
}
.datePicker_container {
  display: flex;
  padding: 8px 12px;
  align-items: center;
  gap: 6px;
  align-self: stretch;
  border-top: 1px solid #eee;
}
