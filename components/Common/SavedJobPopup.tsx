import React, { useState, useEffect } from 'react';
import { useRouter } from "next/router";
import Link from 'next/link';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getCurrentUserData } from "../../lib/session";
import PopupModal from '../../components/Common/PopupModal';
import {
    toggleFavoriteJob,
    removeFavoriteJob,
} from '../../lib/frontendapi';
import Image from 'next/image';
export default function SavedJobPopup({ show, setModalConfirmSavedJobs }: any) {
    console.log(show);
    const [current_user_id, setCurrentUserId] = useState('');
    const [current_user_role, setCurrentRole] = useState('');
    useEffect(() => {
        const current_user_data: any = getCurrentUserData();
        current_user_data.id ? setCurrentUserId(current_user_data.id) : setCurrentUserId('');
        current_user_data.role ? setCurrentRole(current_user_data.role) : setCurrentRole('');
    }, []);
    const modalConfirmCloseSavedJobs = () => {
        setModalConfirmSavedJobs(false);
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }
    return (
        <PopupModal show={show} handleClose={modalConfirmCloseSavedJobs} customclass={"modal-lg  header-remove body-sp-0 "}>
            <div className="popup-body">
                <div className="row">
                    <div className="col-sm-10"> </div>
                    <div className="col-sm-2 text-right">
                        <button
                            type="button"
                            className="close-x  bg-0055BA border-design"
                            data-bs-dismiss="modal"
                            aria-label="Close"
                            onClick={modalConfirmCloseSavedJobs}
                        >
                            <i className="fa-solid fa-xmark"></i>
                        </button>
                    </div>
                </div>
                <div className="text-center sp-50">
                    <img
                        src={
                            process.env.NEXT_PUBLIC_BASE_URL + "images/check-blue.png"
                        }
                        alt="check-blue"
                        className="w-120"
                    />
                    <h2 className="f-31">
                        Job <span className="span-color">Saved</span>
                    </h2>
                    <p className="f-18">
                        Go to{" "}
                        <Link href={process.env.NEXT_PUBLIC_BASE_URL + 'employees/jobs/savedjobs'} className="c-0070F5">
                            {" "}
                            Saved Jobs
                        </Link> {" "}
                        to view your saved jobs.
                    </p>
                </div>
            </div>
        </PopupModal>
    )
}
