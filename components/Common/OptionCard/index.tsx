import styles from './style.module.css';
interface OptionCardProps {
  items: {
    label: string;
    value: string;
    onClick: () => void;
    color?: string;
  }[];
}

export const OptionCard = ({items}: OptionCardProps) => {
  return (
    <div className={styles.option_dropdown}>
      <ul>
        {items.map((item, index) => (
          <li key={index} onClick={item.onClick} style={{color: item.color}}>
            {item.label}
          </li>
        ))}
      </ul>
    </div>
  );
};
