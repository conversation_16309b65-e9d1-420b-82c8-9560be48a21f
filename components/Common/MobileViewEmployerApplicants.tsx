import React, {useState, useEffect} from 'react';
import {useRouter} from 'next/router';
import {ToastContainer, toast} from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import {getCurrentUserData} from '../../lib/session';
import PopupModal from './PopupModal';
import {
  getAllJobApplications,
  checkAndUpdateResumesViewed,
  getSingleJobApplications,
  getWorkExperienceEmployee,
  getEducationsEmployee,
  getSingleUserSkill,
  getportfolio,
  getAllLanguages,
  getApplyJobInterview,
  updateHiringStatus,
  addInterview,
  sendMessage,
} from '../../lib/frontendapi';
import ApplicationThankYouPopup from './ApplicationThankYouPopup';
import {HtmlEditor} from './HtmlEditor';
import Pagination from '../../components/Common/Pagination';
import {paginate} from '../../helpers/paginate';
import Link from 'next/link';
import moment from 'moment';
import {useForm} from 'react-hook-form';
import Image from 'next/image';

export default function MobileViewEmployerApplicants({applicationData}: any) {
  const {
    register,
    formState: {errors},
    handleSubmit,
    reset: resetForm1, // Rename the reset variable for the first form
  } = useForm({
    mode: 'onChange',
  });
  const {
    register: register2,
    formState: {errors: errors2},
    handleSubmit: handleSubmit2,
    reset: resetForm2, // Rename the reset variable for the second form
  } = useForm({
    mode: 'onChange',
  });
  const [messageDesc, setMessageDesc] = useState('');
  const [applications, setApplications] = useState([]);
  const [singleApplications, setSingleApplications]: any = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalApplications, setTotalApplications] = useState([]);
  const [isActive, setIsActive] = useState('Maybe');
  const [current_user_id, setCurrentUserId] = useState('');
  const [current_user_name, setCurrentUserName] = useState('');
  const [current_user_role, setCurrentUserRole] = useState('');
  const [companycvcount, setCompanyCvCount] = useState(0);
  const [membershipstatus, setMemberShipStatus] = useState('');
  const [showMobileView, setShowMobileView] = useState(false);
  const [hiringStatus, setHiringStatus] = useState('');
  const [hiringStatusYesCount, setHiringStatusYesCount] = useState('');
  const [hiringStatusNoCount, setHiringStatusNoCount] = useState('');
  const [hiringStatusMaybeCount, setHiringStatusMaybeCount] = useState('');
  const [hiringStatusInstantApplyCount, setHiringStatusInstantApplyCount] = useState('');
  const [modalCandidateProfilePopup, setModalCandidateProfilePopup] = useState(false);
  const [modalCandidateMessagePopup, setModalCandidateMessagePopup] = useState(false);
  const [companyId, setCompanyId] = useState('');
  const [applicantId, setApplicantId] = useState('');
  const [jobId, setJobId] = useState('');
  const [candidateId, setCandidateId] = useState('');
  const [userJobStatus, setUserJobStatus] = useState('');
  const [singleApplicationsWorkExperience, setSingleApplicationsWorkExperience] = useState([]);
  const [singleApplicationsEducation, setSingleApplicationsEducation] = useState([]);
  const [skills, setGetAllSkills] = useState([]);
  const [portfolio, setPortfolio] = useState([]);
  const [languages, setLanguages] = useState([]);
  const [applyJobInterviews, setApplyJobInterviews] = useState('');
  const [showmessage, setShowmessage] = useState('');
  const [showPopupunsave, setShowPopupunsave] = useState(false);
  const [showPopupunsave1, setShowPopupunsave1] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [interviewScheduleDate, setInterviewScheduleDate] = useState('');
  const [interviewFromTime, setInterviewFromTime] = useState('');
  const [interviewToTime, setInterviewToTime] = useState('');
  const [interviewZoomLink, setInterviewZoomLink] = useState('');
  const pageSize = 5;

  const onPageChange = (page: any) => {
    const current_user_data: any = getCurrentUserData();
    setCurrentPage(page);
    const data = {
      user_id: current_user_data.id,
      company_id: current_user_data.company_id,
      role: current_user_data.role,
      hiring_selected_status: isActive,
    };
    getAllJobApplications(data)
      .then(res => {
        if (res.status == true) {
          setTotalApplications(res.data);
          const paginatedPosts = paginate(res.data, page, pageSize);
          setApplications(paginatedPosts);
        } else {
          setApplications([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  useEffect(() => {
    setApplications(applicationData);
    const current_user_data: any = getCurrentUserData();
    current_user_data.username ? setCurrentUserName(current_user_data.username) : setCurrentUserName('');
    current_user_data.role ? setCurrentUserRole(current_user_data.role) : setCurrentUserRole('');
    current_user_data.id ? setCurrentUserId(current_user_data.id) : setCurrentUserId('');
    current_user_data.company_resume_count
      ? setCompanyCvCount(current_user_data.company_resume_count)
      : setCompanyCvCount(0);
    current_user_data.membership ? setMemberShipStatus(current_user_data.membership) : setMemberShipStatus('');
    const data = {
      user_id: current_user_data.id,
      company_id: current_user_data.company_id,
      role: current_user_data.role,
      hiring_selected_status: isActive,
    };
    getAllJobApplicationsdata(data);
    let details = navigator.userAgent;
    let regexp = /android|iphone|kindle|ipad/i;
    let isMobileDevice = regexp.test(details);
    if (isMobileDevice) {
      //console.log("You are using a Mobile Device");
      setShowMobileView(true);
    } else {
      //console.log("You are using Desktop");
      setShowMobileView(false);
    }
  }, []);
  const getAllJobApplicationsdata = async (data: any) => {
    try {
      const res = await getAllJobApplications(data);
      if (res.status == true) {
        setTotalApplications(res.data);
        const paginatedPosts = paginate(res.data, currentPage, pageSize);
        setApplications(paginatedPosts);
        setHiringStatusYesCount(res.hiring_status_yes_count);
        setHiringStatusNoCount(res.hiring_status_no_count);
        setHiringStatusMaybeCount(res.hiring_status_maybe_count);
        setHiringStatusInstantApplyCount(res.hiring_status_instant_apply_count);
      } else {
        setApplications([]);
        setHiringStatusYesCount('');
        setHiringStatusNoCount('');
        setHiringStatusMaybeCount('');
        setHiringStatusInstantApplyCount('');
      }
    } catch (err) {
      console.log(err);
    }
  };
  const handleClickStatusAccordingApplicants = (e: any, hiring_selected_status: any) => {
    e.preventDefault();
    setIsActive(hiring_selected_status);
    const current_user_data: any = getCurrentUserData();
    const data = {
      user_id: current_user_data.id,
      role: current_user_data.role,
      company_id: current_user_data.company_id,
      hiring_selected_status: hiring_selected_status,
    };

    getAllJobApplications(data)
      .then(res => {
        if (res.status == true) {
          setTotalApplications(res.data);
          const paginatedPosts = paginate(res.data, currentPage, pageSize);
          setApplications(paginatedPosts);
          setHiringStatusYesCount(res.hiring_status_yes_count);
          setHiringStatusNoCount(res.hiring_status_no_count);
          setHiringStatusMaybeCount(res.hiring_status_maybe_count);
          setHiringStatusInstantApplyCount(res.hiring_status_instant_apply_count);
          //window.location.reload();
        } else {
          setApplications([]);
          setHiringStatusYesCount('');
          setHiringStatusNoCount('');
          setHiringStatusMaybeCount('');
          setHiringStatusInstantApplyCount('');
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  const modalConfirmCandidateProfilePopupOpen = (
    e: any,
    applicants_id: any,
    candidate_id: any,
    job_id: any,
    company_id: any,
  ) => {
    setModalCandidateProfilePopup(true);
    setJobId(job_id);
    setCompanyId(company_id);
    setApplicantId(applicants_id);
    setCandidateId(candidate_id);
    handleViewCandidateProfile(candidate_id);
    const data = {
      applicants_id: applicants_id,
      candidate_id: candidate_id,
    };
    getSingleJobApplications(data)
      .then(res => {
        if (res.status == true) {
          setSingleApplications(res.data);
          if (res.data.job_status == 'ready_to_interview') {
            setUserJobStatus('Ready to Interview');
          } else if (res.data.job_status == 'open_to_offer') {
            setUserJobStatus('Open to Offer');
          } else {
            setUserJobStatus('Not Looking');
          }
        } else {
          setSingleApplications([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
    getWorkExperienceEmployee(candidate_id)
      .then(res => {
        if (res.status == true) {
          setSingleApplicationsWorkExperience(res.data);
        } else {
          setSingleApplicationsWorkExperience([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
    getEducationsEmployee(candidate_id)
      .then(res => {
        if (res.status == true) {
          setSingleApplicationsEducation(res.education);
        } else {
          setSingleApplicationsEducation([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
    getSingleUserSkill(candidate_id)
      .then(res => {
        if (res.status == true) {
          setGetAllSkills(res.data);
        } else {
          setGetAllSkills([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
    getportfolio(candidate_id)
      .then(res => {
        if (res.status == true) {
          setPortfolio(res.data);
        } else {
          setPortfolio([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
    getAllLanguages(candidate_id)
      .then(res => {
        if (res.status == true) {
          setLanguages(res.data);
        } else {
          setLanguages([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
    const datas = {
      applicants_id: applicants_id,
      job_id: job_id,
    };
    getApplyJobInterview(datas)
      .then(res => {
        if (res.status == true) {
          setApplyJobInterviews(res.data);
        } else {
          setApplyJobInterviews('');
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  const handleViewCandidateProfile = (id: any) => {
    const current_user_data: any = getCurrentUserData();
    const data = {
      user_id: current_user_data.id,
      user_role: current_user_data.role,
      company_id: current_user_data.company_id,
      applicant_id: id,
    };
    checkAndUpdateResumesViewed(data)
      .then(res => {
        if (res.status === true) {
          getAllJobApplicationsdata(data);
          window.localStorage.setItem('company_resume_count', res.company_resume_count);
          setCompanyCvCount(res.company_resume_count);
        } else {
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  const handleClickDownloadResumeValidation = (e: any, resume_path: any, applicants_id: any) => {
    if (resume_path == null || resume_path == '') {
      setShowmessage('Candidate has not yet uploaded the resume.');
      setShowPopupunsave1(true);
      setTimeout(() => {
        setShowPopupunsave1(false);
      }, 1000);
    }
  };
  const handleChangeHiringStatus = (e: any, application_id: any) => {
    const current_user_data: any = getCurrentUserData();
    const data = {
      hiring_status: e.target.value,
    };
    updateHiringStatus(application_id, data)
      .then(res => {
        if (res.status == true) {
          setShowmessage(res.message);
          setShowPopupunsave(true);
          setTimeout(() => {
            setShowPopupunsave(false);
          }, 1000);
          const data = {
            user_id: current_user_data.id,
            company_id: current_user_data.company_id,
            role: current_user_data.role,
            hiring_selected_status: isActive,
          };

          getAllJobApplicationsdata(data);
          // setHiringStatus(res.data.hiring_status);
          //window.location.reload();
        } else {
          setShowmessage(res.message);
          setShowPopupunsave1(true);
          setTimeout(() => {
            setShowPopupunsave1(false);
          }, 1000);
          setHiringStatus('');
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  const modalConfirmCandidateProfilePopupClose = () => {
    setModalCandidateProfilePopup(false);
  };
  const modalConfirmCandidateMessagePopupOpen = () => {
    setModalCandidateMessagePopup(true);
  };
  const modalConfirmCandidateMessagePopupClose = () => {
    setModalCandidateMessagePopup(false);
  };
  const submitInterviewForm = (data: any) => {
    setIsLoading(true);
    const datas = {
      job_id: jobId,
      company_id: companyId,
      applicant_id: candidateId,
      interview_schedule_date: data.schedule_date,
      interview_from_time: data.schedule_from_time,
      interview_to_time: data.schedule_to_time,
      meeting_link: data.schedule_zoom_link,
    };
    addInterview(datas)
      .then(res => {
        if (res.status == true) {
          setShowmessage(res.message);
          setShowPopupunsave(true);
          setTimeout(() => {
            setShowPopupunsave(false);
          }, 1000);
          setTimeout(() => {
            modalConfirmCandidateProfilePopupClose();
          }, 1000);
          setIsLoading(false);
          resetForm2();
        } else {
          setShowmessage(res.message);
          setShowPopupunsave1(true);
          setTimeout(() => {
            setShowPopupunsave1(false);
          }, 1000);
          setIsLoading(true);
        }
      })
      .catch(err => {
        setShowmessage(err);
        setShowPopupunsave1(true);
        setTimeout(() => {
          setShowPopupunsave1(false);
        }, 1000);
      });
  };
  let current_date = new Date().toJSON().slice(0, 10);
  const submitMessageForm = (data: any) => {
    const current_user_data: any = getCurrentUserData();
    let applicants_id = $('.hd_applicants_id').val();
    let candidate_id = $('.hd_candidate_id').val();
    let job_id = $('.hd_job_id').val();
    let current_user_id = current_user_data.id;
    const datas = {
      applicants_id: applicants_id,
      candidate_id: candidate_id,
      job_id: job_id,
      current_user_id: current_user_id,
      message_title: null,
      message_description: data.message_desc,
      attachment_path: null,
      message_type: 'applyJob',
      message_status: 'unread',
    };
    sendMessage(datas)
      .then(res => {
        if (res.status == true) {
          setShowmessage(res.message);
          setShowPopupunsave(true);
          setTimeout(() => {
            setShowPopupunsave(false);
          }, 1000);
          resetForm1();
          setTimeout(() => {
            modalConfirmCandidateMessagePopupClose();
          }, 1000);
        } else {
          setShowmessage(res.message);
          setShowPopupunsave1(true);
          setTimeout(() => {
            setShowPopupunsave1(false);
          }, 1000);
        }
      })
      .catch(err => {
        setShowmessage(err);
        setShowPopupunsave1(true);
        setTimeout(() => {
          setShowPopupunsave1(false);
        }, 1000);
      });
  };
  return (
    <>
      {applicationData.length > 0 ? (
        applicationData.map((applications_data: any, index: any) => {
          console.log(applications_data.name);
          return (
            <section className="candidate-name2 mb-2" key={index}>
              <p className="c-0070F5 f-16 w-700">
                <a href={'/candidate-profile/' + applications_data.employee_profile_slug} target="_blank">
                  {applications_data.name}
                </a>
              </p>
              <p className="c-2C2C2C f-13 w-600">
                <a target="_blank" href={'/companies/' + applications_data.company_slug}>
                  {applications_data.company_name}
                </a>
              </p>
              <ul className="dubai">
                <li>
                  <img src="/images/Vector.svg" alt="Vector" className="ic_outline-work-history" />{' '}
                  {applications_data.country_name}
                </li>
                <li>
                  <img
                    src="/images/ic_outline-work-history.svg"
                    alt="ic_outline-work-history"
                    className="ic_outline-work-history"
                  />{' '}
                  {applications_data.job_status === 'ready_to_interview'
                    ? 'Ready to Interview'
                    : applications_data.job_status === 'open_to_offer'
                      ? 'Open to Offer'
                      : 'Not Looking'}
                </li>
              </ul>
              {/* <button className="seen-by open-sans ">Seen by me </button> */}
              {applications_data.applicant_id && (
                <button className="seen-by open-sans ">
                  Seen by{' '}
                  {applications_data.viewed_user_id == current_user_id
                    ? 'me'
                    : applications_data.viewed_user_name.split(' ')[0]}
                </button>
              )}
              <p className="Status-by mt-3 mb-1">Hiring Status:</p>
              <select
                className="all-Status w-100 pad-sp"
                value={applications_data.hiring_status}
                onChange={(e: any) => handleChangeHiringStatus(e, applications_data.id)}>
                <option value="Yes">Yes</option>
                <option value="No">No</option>
                <option value="Maybe">Maybe</option>
              </select>

              <button
                className="dropdown-toggle btn-a primary-size-16 btn-bg-0055BA w-100 max-100-p mt-3"
                type="button"
                id="dropdownMenuButton1"
                data-bs-toggle="dropdown"
                aria-expanded="false">
                <i className="fa-solid fa-circle-chevron-down sp-right"></i> Actions
              </button>
              <ul className="dropdown-menu view-right" aria-labelledby="dropdownMenuButton1">
                <li>
                  <a
                    className="dropdown-item item-1"
                    href="#"
                    onClick={(e: any) =>
                      modalConfirmCandidateProfilePopupOpen(
                        e,
                        applications_data.id,
                        applications_data.user_id,
                        applications_data.job_id,
                        applications_data.company_id,
                      )
                    }>
                    View application
                  </a>
                </li>
                <li>
                  <a
                    target="_blank"
                    className="dropdown-item item-2"
                    href={'/candidate-profile/' + applications_data.employee_profile_slug}
                    onClick={() => handleViewCandidateProfile(applications_data.user_id)}>
                    View candidate profile
                  </a>
                </li>
                {/* <li>
                  <a
                    target="_blank"
                    className="dropdown-item item-2"
                    href={'/employer/messages/inbox/' + applications_data.user_id}
                    onClick={() => handleViewCandidateProfile(applications_data.user_id)}>
                    Message
                  </a>
                </li> */}
              </ul>
              {applications_data.resume_path ? (
                <a
                  target="_blank"
                  href={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/employee/resume/' + applications_data.resume_path}
                  onClick={() => handleViewCandidateProfile(applications_data.user_id)}>
                  <button className="download mt-3 mb-3 w-100">
                    <i className="fa-solid fa-download"></i> Download Resume
                  </button>
                </a>
              ) : (
                <a href="#">
                  <button
                    className="download mt-3 mb-3 w-100"
                    onClick={(e: any) =>
                      handleClickDownloadResumeValidation(e, applications_data.resume_path, applications_data.user_id)
                    }>
                    <i className="fa-solid fa-download"></i> Download Resume
                  </button>
                </a>
              )}
              {/* <button className="download mt-3 mb-3 w-100"><i className="fa-solid fa-download"></i> Download Resume</button> */}
              {applications_data.instant_apply == '1' ? (
                <p className="c-999999 f-13 w-400 text-center mb-0">Applied through Instant Apply!</p>
              ) : (
                <p className="c-999999 f-13 w-400 text-center mb-0"></p>
              )}
            </section>
          );
        })
      ) : (
        <div className="m-p-10 text-center mt-5">
          <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blank-2.jpg'} alt="blank-2" className="" />
          <p className="f-22 c-BABABA mb-1">No Applicants Found</p>
          <p className="f-18">
            <Link href="/employer/jobs" className="c-0070F5">
              Post A Job
            </Link>
          </p>
        </div>
      )}
      <PopupModal
        show={modalCandidateProfilePopup}
        handleClose={modalConfirmCandidateProfilePopupClose}
        customclass={'   modal-lg  header-remove body-sp-0'}>
        <div className="popup-body candidate_application_popup_body b-r-8">
          <div className="row">
            <div className="col-sm-8">
              <i className="fa-solid fa-arrow-left f-20 c-0055BA" onClick={modalConfirmCandidateProfilePopupClose}></i>
              <p className="f-37 w-700 c-2C2C2C mb-2">
                <Link href={'/candidate-profile/' + singleApplications.slug}>{singleApplications.name}</Link>
              </p>
              <p className="f-22  c-0055BA w-500 Archivo">
                {singleApplications.current_position}@
                <Link href={'/companies/' + singleApplications.company_slug}>{singleApplications.company_name}</Link>
              </p>
            </div>
            <div className="col-sm-4 text-right">
              <p className="f-16 w-400 c-4D4D4D">Hiring Status: </p>
              <select
                className="choose-currency currencymm mt-4 w-100 mb-3"
                defaultValue={singleApplications.hiring_status}
                onChange={(e: any) => handleChangeHiringStatus(e, singleApplications.applicants_id)}>
                <option value="Yes">Yes</option>
                <option value="No">No</option>
                <option value="Maybe">Maybe</option>
                {/* <option value="InstantApply">Instant Apply</option> */}
              </select>
              <button
                className="btn-a primary-size-16 btn-bg-0055BA w-100"
                onClick={modalConfirmCandidateMessagePopupOpen}>
                Message
              </button>
            </div>
          </div>
          <hr className="hr-line" />
          <div className="tab-popup callum ">
            <ul className="nav nav-pills mb-3" id="pills-tab" role="tablist">
              <li className="nav-item" role="presentation">
                <button
                  className="nav-link active f-16"
                  id="pills-home-tab"
                  data-bs-toggle="pill"
                  data-bs-target="#pills-home"
                  type="button"
                  role="tab"
                  aria-controls="pills-home"
                  aria-selected="true">
                  Application
                </button>
              </li>
              {applyJobInterviews.length > 0 ? (
                ''
              ) : (
                <li className="nav-item" role="presentation">
                  <button
                    className="nav-link"
                    id="pills-profile-tab"
                    data-bs-toggle="pill"
                    data-bs-target="#pills-profile"
                    type="button"
                    role="tab"
                    aria-controls="pills-profile"
                    aria-selected="false">
                    Interview
                  </button>
                </li>
              )}
            </ul>
            <div className="tab-content" id="pills-tabContent">
              <div
                className="tab-pane fade show active"
                id="pills-home"
                role="tabpanel"
                aria-labelledby="pills-home-tab">
                <div className="row" style={{marginBottom: '15px'}}>
                  <div className="col-sm-8">
                    <ul className="skills pop-skils">
                      <li>
                        <p className="f-16 c-999999 w-400">
                          <i className="fa-regular fa-envelope"></i>{' '}
                          <a href={`mailto:${singleApplications.email}`}>{singleApplications.email}</a>
                        </p>
                      </li>

                      <li>
                        <p className="f-16 c-999999 w-400">
                          <i className="fa-solid fa-phone"></i>{' '}
                          <a href={`tel:${singleApplications.contact_no}`}>{singleApplications.contact_no}</a>
                        </p>
                      </li>

                      <li>
                        <p className="f-16 c-999999 w-400">
                          <i className="fa-solid fa-briefcase"></i> {userJobStatus}
                        </p>
                      </li>
                      <li>
                        <p className="f-16 c-999999 w-400">
                          <i className="fa-solid fa-location-dot"></i>{' '}
                          {singleApplications.where_currently_based ? singleApplications.country_name : ''}
                        </p>
                      </li>
                      <li>
                        <p className="f-16 c-999999 w-400">
                          <i className="fa-regular fa-user"></i> Age:{' '}
                          {moment().diff(singleApplications.date_of_birth, 'years')}
                        </p>
                      </li>
                      <li>
                        <p className="f-16 c-999999 w-400">
                          <i className="fa-regular fa-circle-user"></i> {singleApplications.gender}
                        </p>
                      </li>
                      <li>
                        <p className="f-16 c-999999 w-400">
                          <i className="fa-solid fa-graduation-cap"></i> {singleApplications.current_position}
                        </p>
                      </li>
                    </ul>
                  </div>
                </div>
                {singleApplications.resume_path ? (
                  <>
                    <p className="f-16 c-000">Resume</p>
                    <div
                      className="row"
                      style={{
                        border: '2px solid #ccc',
                        padding: '20px 0px 20px 10px',
                        borderRadius: '10px',
                        margin: '0px 0px 15px 0px',
                      }}>
                      <div className="col-sm-10">
                        <p className="f-22  c-0055BA w-500 Archivo m-0">
                          <a
                            href={
                              process.env.NEXT_PUBLIC_IMAGE_URL +
                              'images/employee/resume/' +
                              singleApplications.resume_path
                            }
                            target="_blank">
                            {singleApplications.resume_path}
                          </a>
                        </p>
                        <p className="f-16">Uploaded on {moment(singleApplications.created_at).format('MMM DD')}</p>
                      </div>
                      <div className="col-sm-2 text-right">
                        <button
                          style={{
                            padding: '7px 20px 7px 20px',
                            background: '#ebf1f9',
                            border: '2px solid #0055ba',
                            color: '#0055ba',
                            fontSize: '18px',
                            fontWeight: '600',
                            borderRadius: '10px',
                          }}>
                          <a
                            href={
                              process.env.NEXT_PUBLIC_IMAGE_URL +
                              'images/employee/resume/' +
                              singleApplications.resume_path
                            }
                            target="_blank">
                            <i style={{color: '#0055ba'}} className="fa fa-download"></i>
                          </a>
                        </button>
                      </div>
                    </div>
                  </>
                ) : (
                  ''
                )}

                {singleApplications.instant_apply === 0 && (
                  <>
                    <p className="f-16 c-000">Cover Letter</p>
                    {singleApplications.cover_letter ? (
                      <>
                        <div
                          className="row"
                          style={{
                            border: '2px solid #ccc',
                            padding: '20px 0px 20px 10px',
                            borderRadius: '10px',
                            margin: '0px',
                          }}>
                          <div className="col-sm-10">
                            <p className="f-22 c-0055BA w-500 Archivo m-0">
                              <a
                                href={
                                  process.env.NEXT_PUBLIC_IMAGE_URL +
                                  'images/employee/cover_letter/' +
                                  singleApplications.cover_letter
                                }
                                target="_blank">
                                {singleApplications.cover_letter}
                              </a>
                            </p>
                            <p className="f-16">Uploaded on {moment(singleApplications.created_at).format('MMM DD')}</p>
                          </div>
                          <div className="col-sm-2 text-right">
                            <button
                              style={{
                                padding: '7px 20px 7px 20px',
                                background: '#ebf1f9',
                                border: '2px solid #0055ba',
                                color: '#0055ba',
                                fontSize: '18px',
                                fontWeight: '600',
                                borderRadius: '10px',
                              }}>
                              <a
                                href={
                                  process.env.NEXT_PUBLIC_IMAGE_URL +
                                  'images/employee/cover_letter/' +
                                  singleApplications.cover_letter
                                }
                                target="_blank">
                                <i style={{color: '#0055ba'}} className="fa fa-download"></i>
                              </a>
                            </button>
                          </div>
                        </div>
                        <div
                          className="row mt-2"
                          style={{border: '2px solid #ccc', padding: '10px', borderRadius: '5px', margin: '0px'}}>
                          <p className="p-0" dangerouslySetInnerHTML={{__html: singleApplications.description}}></p>
                        </div>
                      </>
                    ) : (
                      <>
                        <span dangerouslySetInnerHTML={{__html: singleApplications.description}}></span>
                      </>
                    )}
                  </>
                )}

                <div className="mt-4">
                  {singleApplications.bio ? (
                    <>
                      <p className="f-16 c-000">About</p>
                      <p className="f-18 c-4D4D4D w-400  mb-4">{singleApplications.bio}</p>
                    </>
                  ) : (
                    ''
                  )}
                  {singleApplicationsWorkExperience.length > 0 ? <p className="f-16 c-000">Work Experience</p> : ''}
                  {singleApplicationsWorkExperience.length > 0
                    ? singleApplicationsWorkExperience.map((single_applications_workexperience: any, index: any) => {
                        return (
                          <>
                            <p className="f-22 w-700 c-2C2C2C mt-2 mb-2">{single_applications_workexperience.title}</p>
                            <p className="f-18 c-0055BA mb-2">{single_applications_workexperience.company}</p>
                            <p className="f-16 c-999999">
                              {single_applications_workexperience.start_date} -{' '}
                              {single_applications_workexperience.currently_work_here == '1'
                                ? 'Currently here'
                                : single_applications_workexperience.end_date}
                            </p>
                            <p className="f-18 c-4D4D4D w-400 mb-3">{single_applications_workexperience.description}</p>
                            {/* <p className='f-22 w-700 c-2C2C2C mt-2 mb-2'>Junior Software Engineer</p>
                                                        <p className='f-18 c-0055BA mb-2'>Twitter</p> */}
                          </>
                        );
                      })
                    : ''}
                  {singleApplicationsEducation.length > 0 ? <p className="f-16 c-000">Education</p> : ''}
                  <div className="row mb-3">
                    {singleApplicationsEducation.length > 0
                      ? singleApplicationsEducation.map((single_applications_education: any, index: any) => {
                          return (
                            <div className="col-sm-6" key={index}>
                              <p className="f-22 w-700 c-2C2C2C mt-2 mb-2">{single_applications_education.degree}</p>
                              <p className="f-18 c-0055BA mb-2">{single_applications_education.education_title}</p>
                              <p className="f-16 c-999999">
                                {single_applications_education.start_date} -{' '}
                                {single_applications_education.currently_study_here == '1'
                                  ? 'Currently here'
                                  : single_applications_education.end_date}
                              </p>
                              <p className="f-16 c-999999">
                                Scored: {single_applications_education.your_score}/
                                {single_applications_education.max_score}
                              </p>
                            </div>
                          );
                        })
                      : ''}
                  </div>
                  {skills.length > 0 ? <p className="f-16 c-000">Skills</p> : ''}
                  <ul className="skills_sections pb-0 scr-x">
                    {skills.length > 0
                      ? skills.map((skills_data: any, index: any) => {
                          return <li key={index}>{skills_data.skills}</li>;
                        })
                      : ''}
                  </ul>
                  {portfolio.length > 0 ? <p className="f-16 c-000">Portfolio/Projects</p> : ''}
                  {portfolio.length > 0
                    ? portfolio.map((portfolio_data: any, index: any) => {
                        return (
                          <>
                            <p className="f-22 w-700 c-2C2C2C mt-2 mb-2">{portfolio_data.title}</p>
                            <p className="f-18 c-0055BA mb-2">
                              <i className="fa-solid fa-link-simple"></i>{' '}
                              <a href={portfolio_data.portfolio_link} target="_blank">
                                {portfolio_data.portfolio_link}
                              </a>
                            </p>
                            <p className="f-16 c-999999">
                              {portfolio_data.start_date} -{' '}
                              {portfolio_data.present == '1' ? 'Present' : portfolio_data.end_date}
                            </p>
                            <p className="f-18 c-4D4D4D w-400 mb-3">{portfolio_data.description}</p>
                          </>
                        );
                      })
                    : ''}
                  {languages.length > 0 ? <p className="f-16 c-000">Languages</p> : ''}
                  {languages.length > 0
                    ? languages.map((languages_data: any, index: any) => {
                        return (
                          <>
                            <p className="f-22 w-700 c-2C2C2C mt-2 mb-2">{languages_data.language}</p>
                            <p className="f-18 c-0055BA pb-4">{languages_data.proficiency}</p>
                          </>
                        );
                      })
                    : ''}
                </div>
              </div>
              <div className="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab">
                <p className="f-18 c-747474 w-400 time-mon-fri">
                  Based on your schedule you are available from <strong>Monday</strong> to <strong>Friday</strong>{' '}
                  <strong>between 09:00AM - 03:00PM</strong>
                </p>
                <p>
                  <a href="#" className="f-16 w-700 c-0070F5">
                    Edit Availability
                  </a>
                </p>
                <form className="form-experience-fieild mt-4" onSubmit={handleSubmit2(submitInterviewForm)}>
                  <label>Interview Schedule Date*</label>
                  <input
                    type="date"
                    placeholder="Schedule Date"
                    className="fild-des"
                    {...register2('schedule_date', {required: true})}
                    onChange={(e: any) => setInterviewScheduleDate(e.target.value)}
                    min={current_date}
                  />
                  {errors2.schedule_date && errors2.schedule_date.type === 'required' && (
                    <p className="text-danger error-m" style={{textAlign: 'left'}}>
                      Schedule Date is required.
                    </p>
                  )}
                  <div className="row">
                    <div className="col-sm-6">
                      <label>From Time*</label>
                      <input
                        type="time"
                        placeholder="9:00 AM"
                        className="fild-des"
                        {...register2('schedule_from_time', {required: true})}
                        onChange={(e: any) => setInterviewFromTime(e.target.value)}
                      />
                      {errors2.schedule_from_time && errors2.schedule_from_time.type === 'required' && (
                        <p className="text-danger error-m" style={{textAlign: 'left'}}>
                          Schedule From Time is required.
                        </p>
                      )}
                    </div>
                    <div className="col-sm-6">
                      <label>To Time*</label>
                      <input
                        type="time"
                        placeholder="3:00 PM"
                        className="fild-des"
                        {...register2('schedule_to_time', {required: true})}
                        onChange={(e: any) => setInterviewToTime(e.target.value)}
                      />
                      {errors2.schedule_to_time && errors2.schedule_to_time.type === 'required' && (
                        <p className="text-danger error-m" style={{textAlign: 'left'}}>
                          Schedule To Time is required.
                        </p>
                      )}
                    </div>
                  </div>
                  <label>Zoom Link*</label>
                  <input
                    type="text"
                    placeholder="linke.linke.zooooom"
                    className="fild-des"
                    {...register2('schedule_zoom_link', {
                      required: true,
                      pattern:
                        /^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/,
                    })}
                    onChange={(e: any) => setInterviewZoomLink(e.target.value)}
                  />
                  {errors2.schedule_zoom_link && errors2.schedule_zoom_link.type === 'required' && (
                    <p className="text-danger error-m" style={{textAlign: 'left'}}>
                      Schedule Zoom Link is required.
                    </p>
                  )}
                  {errors2.schedule_zoom_link?.type === 'pattern' && (
                    <p className="text-danger   error-m" style={{textAlign: 'left'}}>
                      Enter a valid zoom Link!
                    </p>
                  )}
                  <div className="text-right mt-3">
                    <button
                      className="btn-a primary-size-16 btn-bg-0055BA w-100 text-white"
                      id="schedule_inter"
                      type="submit"
                      disabled={isLoading}>
                      {isLoading ? 'Please wait...' : 'Schedule interview'}{' '}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </PopupModal>
      <br />
      <br />
      <PopupModal
        show={modalCandidateMessagePopup}
        handleClose={modalConfirmCandidateMessagePopupClose}
        customclass={'    modal-lg  header-remove body-sp-0'}>
        <div className="popup-body">
          <p className="f-31 c-191919 text-left">Message {singleApplications.name}</p>
          <hr className="hr-line"></hr>
          <div className="form-experience-fieild">
            <form className="form-experience-fieild" onSubmit={handleSubmit(submitMessageForm)}>
              <input
                type="hidden"
                name="hd_candidate_id"
                className="hd_candidate_id"
                value={singleApplications.user_id}
              />
              <input type="hidden" name="hd_job_id" className="hd_job_id" value={singleApplications.job_id} />
              <input
                type="hidden"
                name="hd_applicants_id"
                className="hd_applicants_id"
                value={singleApplications.applicants_id}
              />
              <p className="f-12 c-2C2C2C">Your Message</p>
              <textarea
                placeholder="Your Message"
                className="fild-des"
                {...register('message_desc', {required: true})}
                onChange={(e: any) => setMessageDesc(e.target.value)}></textarea>
              {errors.message_desc && errors.message_desc.type === 'required' && (
                <p className="text-danger" style={{textAlign: 'left'}}>
                  Message Field is required.
                </p>
              )}
              <div className="text-right mt-3">
                <div className="row">
                  <div className="col-4">
                    <button className="cancel  w-100" onClick={modalConfirmCandidateMessagePopupClose}>
                      Cancel
                    </button>
                  </div>
                  <div className="col-8">
                    <button className="save w-100" type="submit">
                      Send
                    </button>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </PopupModal>
      <br />
      <br />
    </>
  );
}
