import React, { useState, useMemo } from "react";
import dynamic from 'next/dynamic'
import "react-quill/dist/quill.snow.css";
//import VisibilityIcon from "@material-ui/icons/Visibility";
export const NewMultipleReactQuillEditorMobile = ({ id, name, value, onChange, onBlur }:any) => {
  const quillRef = React.useRef();
  //const ReactQuill = dynamic(import('react-quill'), {ssr: false})
  const ReactQuill = useMemo(() => dynamic(() => import('react-quill'), { ssr: false }),[]);
  //const [previewOpen, setPreviewOpen] = useState(false);
  //const [previewContent, setPreviewContent] = useState("");

  const CustomToolbar = (id:any) => (
    <div id={`toolbar-${id}`}>
      <button className="ql-bold" />
      <button className="ql-italic" />
      <button className="ql-underline" />
      <button className="ql-strike" />
      <button className="ql-script" value="sub"></button>
      <button className="ql-script" value="super"></button>
      <select
        className="ql-size"
        defaultValue={""}
        onChange={(e) => e.persist()}
      >
        {["small", "false", "large", "huge"].map((value, i) => (
          <option key={value} value={value} />
        ))}
      </select>
      <select
        className="ql-header"
        defaultValue={""}
        onChange={(e) => e.persist()}
      >
        {["1", "2", "3", "4", "5", "6", "false"].map((value) => (
          <option key={value} value={value} />
        ))}
      </select>
      <button className="ql-list" value="ordered"></button>
      <button className="ql-list" value="bullet"></button>
      <button className="ql-indent" value="-1"></button>
      <button className="ql-indent" value="+1"></button>
      <select className="ql-color"></select>
      <select className="ql-background"></select>
      <select className="ql-align"></select>
      <button className="ql-link" />
      <button className="ql-clean" />
      {/*<button className="ql-preview" value="preview">
        <VisibilityIcon />
      </button>*/}
    </div>
  );

  const handleChange = (val:any, delta:any, source:any, editor:any) => {
    onChange(name, val);
    /*if (previewOpen) {
      setPreviewContent(editor.getText());
    }*/
  };

  // const handleBlur = () => {
  //   onBlur(name, true);
  // };

  const quillModules = useMemo(() => {
    return {
      toolbar: {
        container: `#toolbar-${id}`,
        /*handlers: {
          preview: function (value) {
            const html = this.quill.root.innerHTML;
            setPreviewContent(html);
            setPreviewOpen(!previewOpen);
          }
        }*/
      }
    };
  }, [id]);

  const quillFormats = [
    "bold",
    "italic",
    "underline",
    "strike",
    "script",
    "size",
    "header",
    "list",
    "indent",
    "link",
    "color",
    "background",
    "align"
  ];

  return (
    <>
      <div className="text-editor">
        {CustomToolbar(id)}
        <ReactQuill
          style={{ backgroundColor: "white" }}
          theme="snow"
          value={value}
          onChange={(e, delta, source, editor) => {
            handleChange(e, delta, source, editor);
          }}
          //onBlur={handleBlur}
          modules={quillModules}
          formats={quillFormats}
        />
        {/*{previewOpen ? (
          <div dangerouslySetInnerHTML={{ __html: previewContent }} />
        ) : (
          ""
        )}*/}
      </div>
    </>
  );
};
