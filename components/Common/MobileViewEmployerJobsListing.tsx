import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getCurrentUserData } from '../../lib/session';
import PopupModal from './PopupModal';
import { useForm, Controller } from 'react-hook-form';
import {
  getCurrentUserAllJobs,
  getSingleJobs,
  deleteJob,
  getJobViewUsers,
  getCompanyActiveJobs,
  getAllCountries,
  getAllSkills,
  getExperiance,
  getAllSectors,
  updateJob,
} from '../../lib/frontendapi';
import { getAllIndustries } from '../../lib/adminapi';
import moment from 'moment';
import swal from 'sweetalert';
import Pagination from '../../components/Common/Pagination';
import { paginate } from '../../helpers/paginate';
import constantVariable from '../../lib/constant.config.js';
import Link from 'next/link';
//import { NewMultipleReactQuillEditor } from "../../components/Common/NewMultipleReactQuillEditor";
import Select from 'react-select';
import Image from 'next/image';

interface SwalOptions {
  title?: string;
  text?: string;
  icon?: string;
  dangerMode?: boolean;
  buttons?: string[];
  confirmButtonColor?: string;
}
interface JobViewUser {
  email: string;
  name: string;
  slug: string;
}
export default function MobileViewEmployerJobsListing({ jobsdata }: any) {
  const {
    register,
    control,
    handleSubmit,
    formState: { errors },
  } = useForm();
  const [modalConfirm7, setModalConfirm7] = useState(false);
  const [jobviewuser, setJobViewUser] = useState<JobViewUser[]>([]);
  const [showPopupunsave, setShowPopupunsave] = useState(false);
  const [showPopupunsave1, setShowPopupunsave1] = useState(false);
  const [showmessage, setShowmessage] = useState('');
  const [jobId, setJobId] = useState('');
  const [modalConfirmEditPostJob, setModalConfirmEditPostJob] = useState(false);
  const [allIndustries, setAllIndustries] = useState([]);
  const [editJobPostedUserId, setEditJobPostedUserId] = useState('');
  const [editFeatured, setEditFeatured]: any = useState('');
  const [editJobTitle, setEditJobTitle] = useState('');
  const [editJobDescription, setEditJobDescription] = useState('');
  const [edittypeOfPosition, setEditTypeOfPosition] = useState('');
  const [editJobType, setEditJobType] = useState('');
  const [editJobCountry, setEditJobCountry]: any = useState('');
  const [editIndustry, setEditIndustry] = useState('');
  const [editExperience, setEditExperience] = useState('');
  const [editSkills, setEditSkills] = useState([]);
  const [editMonthlyFixedSalaryCurrency, setEditMonthlyFixedSalaryCurrency] = useState('');
  const [editMonthlyFixedMinSalary, setEditMonthlyFixedMinSalary] = useState('');
  const [editMonthlyFixedMaxSalary, setEditMonthlyFixedMaxSalary] = useState('');
  const [editAvailableVacancies, setEditAvailableVacancies] = useState('');
  const [editDeadLine, setEditDeadLine] = useState('');
  const [editHideEmployerDetails, setEditHideEmployerDetails] = useState('');
  const [editCloseJob, setEditCloseJob] = useState('');
  const [editSector, setEditSector] = useState('');
  const [allSkills, setAllSkills]: any = useState([]);
  const [current_user_id, setCurrentUserId] = useState('');
  const [membershipplan, setMemberShipPlan] = useState('');
  const [membershipstatus, setMemberShipStatus] = useState('');
  const [Country, setCountry] = useState([]);
  const [totalJobs, setTotalJobs] = useState([]);
  const [jobs, setJobs] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [experiences, setExperiences] = useState([]);
  const pageSize = constantVariable.paginationn_per_page_records;
  const [sectors, setSectors] = useState([]);
  const [singleJobs, setSingleJobs]: any = useState([]);
  const [currentPage1, setCurrentPage1] = useState(1);
  const pageSize1 = 10;
  const paginatedData = paginate(jobviewuser, currentPage1, pageSize1);
  const [modalConfirmJobSubmitConfirmationPopup, setModalConfirmJobSubmitConfirmationPopup] = useState(false);
  const [modalConfirmJobUpdateConfirmationPopup, setModalConfirmJobUpdateConfirmationPopup] = useState(false);
  const [latestSavedJobsId, setLatestSavedJobsId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [featuredjobcount, setFeaturedJobCount]: any = useState('');
  const [showPopupunerror, setShowPopupunerror] = useState(false);

  const onPageChange1 = (page: number) => {
    setCurrentPage1(page);
  };
  const openModalConfirm7 = () => {
    setModalConfirm7(true);
  };
  const modalConfirmClose7 = () => {
    setModalConfirm7(false);
  };
  useEffect(() => {
    const current_user_data: any = getCurrentUserData();
    current_user_data.id ? setCurrentUserId(current_user_data.id) : setCurrentUserId(current_user_data.id);
    current_user_data.plan ? setMemberShipPlan(current_user_data.plan) : setMemberShipPlan('');
    current_user_data.membership ? setMemberShipStatus(current_user_data.membership) : setMemberShipStatus('');
    getAllCountries()
      .then(res => {
        if (res) {
          setCountry(res);
        } else {
          setCountry([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
    const data = {
      company_id: current_user_data.company_id,
      job_sort: '',
    };
    getCurrentUserAllJobs(data)
      .then(res => {
        if (res.status == true) {
          setTotalJobs(res.data);
          const paginatedPosts = paginate(res.data, currentPage, pageSize);
          setJobs(paginatedPosts);
        } else {
          setJobs([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
    getAllSkills()
      .then(res => {
        if (res.success == true) {
          setAllSkills(res.data);
        } else {
          setAllSkills([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
    getExperiance()
      .then(res => {
        if (res.status == true) {
          setExperiences(res.data);
        } else {
          setExperiences([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
    getAllSectors()
      .then(res => {
        if (res.success == true) {
          setSectors(res.sectors);
        } else {
          setSectors([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  }, []);
  const submitEditJobs = (e: any) => {
    e.preventDefault();
    setIsLoading(true);
    let job_id = jobId;
    let user_id = window.localStorage.getItem('user_id');
    let company_id = window.localStorage.getItem('company_id');
    const editskillsData = editSkills.map((edit_skills_data: any, index: any) => edit_skills_data.value);
    let job_status = '';
    if (editCloseJob == '1') {
      job_status = 'closed';
    } else {
      job_status = 'active';
    }
    let job_posted_user_id = '';
    if (user_id == editJobPostedUserId) {
      job_posted_user_id = user_id;
    } else {
      job_posted_user_id = editJobPostedUserId;
    }
    const job_data = {
      edit_user_id: job_posted_user_id,
      edit_company_id: company_id,
      edit_is_featured: editFeatured,
      edit_job_title: editJobTitle,
      edit_job_description: editJobDescription,
      edit_type_of_position: edittypeOfPosition,
      edit_job_type: editJobType,
      edit_job_country: editJobCountry,
      edit_industry: editIndustry,
      edit_experience: editExperience,
      edit_skills: editskillsData,
      edit_monthly_fixed_salary_currency: editMonthlyFixedSalaryCurrency,
      edit_monthly_fixed_min_salary: editMonthlyFixedMinSalary,
      edit_monthly_fixed_max_salary: editMonthlyFixedMaxSalary,
      edit_available_vacancies: editAvailableVacancies,
      edit_deadline: editDeadLine,
      edit_hide_employer_details: editHideEmployerDetails,
      edit_sector: editSector,
      edit_close_job: job_status,
    };

    updateJob(job_id, job_data)
      .then(res => {
        if (res.status == true) {
          modalConfirmCloseEditPostJob();
          modalConfirmOpenJobUpdateConfirmationPopup();
        } else {
          setShowmessage(res.message);
          setShowPopupunsave1(true);
          setTimeout(() => {
            setShowPopupunsave1(false);
          }, 3000);
        }
      })
      .catch(err => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };
  const deletejob = (id: any) => {
    const options: Partial<SwalOptions> = {
      title: 'Are you sure?',
      text: 'You want to delete the job',
      icon: 'warning',
      dangerMode: true,
      buttons: ['Cancel', 'Yes, I am sure!'],
      confirmButtonColor: '#062B60',
    };
    swal(options).then(willDelete => {
      if (willDelete) {
        deleteJob(id)
          .then(res => {
            if (res.status == true) {
              swal('Your Job has been deleted!', {
                icon: 'success',
              });
              $('#data_' + id).hide();
            } else {
            }
          })
          .catch(err => { });
      } else {
      }
    });
  };
  const getjobviewData = async (e: any, jobId: any) => {
    e.preventDefault();
    getJobViewUsers(jobId)
      .then(res => {
        if (res.status == true) {
          openModalConfirm7();
          setJobViewUser(res.data);
        } else {
          setShowmessage(res.message);
          setShowPopupunsave1(true);
          setTimeout(() => {
            setShowPopupunsave1(false);
          }, 3000);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  const modalConfirmOpenEditPostJob = (id: any, slug: any) => {
    setJobId(id);
    setModalConfirmEditPostJob(true);
    getAllIndustries()
      .then(res => {
        if (res.success == true) {
          setAllIndustries(res.data);
        } else {
          setAllIndustries([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
    getSingleJobs(id)
      .then(res => {
        if (res.status == true) {
          setEditJobPostedUserId(res.data.user_id);
          setEditFeatured(res.data.is_featured);
          setEditJobTitle(res.data.job_title);
          setEditJobDescription(res.data.job_description);
          setEditTypeOfPosition(res.data.type_of_position);
          setEditJobType(res.data.job_type);
          setEditJobCountry(res.data.job_country);
          setEditIndustry(res.data.industry);
          setEditExperience(res.data.experience);
          setEditSector(res.data.sector_id);
          setEditMonthlyFixedSalaryCurrency(res.data.monthly_fixed_salary_currency);
          setEditMonthlyFixedMinSalary(res.data.monthly_fixed_salary_min);
          setEditMonthlyFixedMaxSalary(res.data.monthly_fixed_salary_max);
          setEditAvailableVacancies(res.data.available_vacancies);
          setEditDeadLine(res.data.deadline);
          setEditHideEmployerDetails(res.data.hide_employer_details);
          if (res.data.job_status == 'closed') {
            setEditCloseJob('1');
          } else {
            setEditCloseJob('0');
          }
          if (res.data.skills_required) {
            const filteredSkills = res.data.skills_required
              .split(',')
              .map((skillid: any, index: any) => {
                const skills = allSkills.find((s: any) => s.id === Number(skillid));
                if (skills) {
                  return { value: skills.id, label: skills.skills };
                } else {
                  return null;
                }
              })
              .filter(Boolean);
            setEditSkills(filteredSkills);
          } else {
            setEditSkills([]);
          }
        } else {
          setSingleJobs([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  const modalConfirmCloseEditPostJob = () => {
    setModalConfirmEditPostJob(false);
  };
  const modalConfirmOpenJobSubmitConfirmationPopup = () => {
    setModalConfirmJobSubmitConfirmationPopup(true);
  };
  const modalConfirmCloseJobSubmitConfirmationPopup = () => {
    setModalConfirmJobSubmitConfirmationPopup(false);
    window.location.reload();
  };
  const modalConfirmOpenJobUpdateConfirmationPopup = () => {
    setModalConfirmJobUpdateConfirmationPopup(true);
  };
  const modalConfirmCloseJobUpdateConfirmationPopup = () => {
    setModalConfirmJobUpdateConfirmationPopup(false);
    const current_user_data: any = getCurrentUserData();
    current_user_data.id ? setCurrentUserId(current_user_data.id) : setCurrentUserId(current_user_data.id);
    const data = {
      company_id: current_user_data.company_id,
      job_sort: '',
    };
    getCurrentUserAllJobs(data)
      .then(res => {
        if (res.status == true) {
          setTotalJobs(res.data);
          const paginatedPosts = paginate(res.data, currentPage, pageSize);
          setJobs(paginatedPosts);
        } else {
          setJobs([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  const handelFeatureJobError = () => {
    const featurejobs: any = featuredjobcount;
    setShowmessage('You cannot enable this feature as your plan has only ' + featurejobs + ' featured jobs');
    setShowPopupunerror(true);
    setTimeout(() => {
      setShowPopupunerror(false);
    }, 3000);
  };
  const handleChangeEditorEditJobDesc = (name: any, value: any) => {
    setEditJobDescription(value);
  };
  const handleEditCountryChange = (event: any) => {
    const selectedCountryId: any = event.target.value;
    const selectedCountryData: any = Country.find(
      (cnt: any) => cnt.id === Number(selectedCountryId) && cnt.status === 'active',
    );
    if (selectedCountryData) {
      setEditMonthlyFixedSalaryCurrency(selectedCountryData.currency);
    } else {
      setEditMonthlyFixedSalaryCurrency('');
    }
    setEditJobCountry(selectedCountryId);
  };
  let current_date = new Date().toJSON().slice(0, 10);
  const options: any = allSkills.map((all_skills: any) => {
    return { value: all_skills.id, label: all_skills.skills };
  });
  const extractCurrencyCode = (value: string) => {
    if (!value) {
      return '';
    }
    const currencyCode = value.split('(')[1]?.trim()?.slice(0, -1);
    return currencyCode || '';
  };
  return (
    <>
      <section className="bg-fff p-3 open-sans">
        {jobsdata.length > 0 ? (
          jobsdata.map((jobs_data: any, index: any) => {
            return (
              <>
                <p className="name-title-jobs mb-0 w-700 c-747474 open-sans mb-2">JOB TITLE:</p>
                <div className="row">
                  <div className="col-7">
                    <p className="name-title-jobs w-600 c-0070F5 mb-1 open-sans ">
                      <a target="_blank" href={'/job/' + jobs_data.job_slug}>
                        {jobs_data.job_title}
                      </a>
                    </p>
                    <p className="f-13 c-999999 w-400 open-sans">{jobs_data.country_name}</p>
                  </div>
                  <div className="col-5 text-right">
                    {jobs_data.is_featured == '1' ? (
                      <button className="pro mt-1 mb-3">
                        Featured
                        <img
                          src={process.env.NEXT_PUBLIC_BASE_URL + 'images/pro.png'}
                          alt="Avatars-2"
                          className="w-25"
                        />{' '}
                      </button>
                    ) : (
                      ''
                    )}
                  </div>
                </div>
                <div className="row">
                  <div className="col-4">
                    <p className="c-4D4D4D w-400 f-13">
                      <b className="w-600 c-2C2C2C">{jobs_data.jobs_applicants}</b> Applicants{' '}
                    </p>
                  </div>
                  <div className="col-4">
                    <p className="c-4D4D4D w-400 f-13">
                      <b className="w-600 c-2C2C2C">{jobs_data.shortlisted_jobs_count}</b> Shortlisted{' '}
                    </p>
                  </div>
                  <div className="col-4">
                    <p className="c-4D4D4D w-400 f-13">
                      <b className="w-600 c-2C2C2C">{jobs_data.rejected_jobs_count}</b> Rejected{' '}
                    </p>
                  </div>
                </div>

                <p className="name-title-jobs w-400 c-2C2C2C">Posted by: Me</p>
                <div className="row">
                  <div className="col-6">
                    <p className="name-title-jobs mb-0 w-700 c-747474 open-sans mb-2">IMPRESSIONS:</p>
                    <p className="f-20 c-2C2C2C w-500">
                      {jobs_data.totaljobimpression} &nbsp;{' '}
                      <span className="f-13 c-3D9F79 w-600">
                        {' '}
                        <i className="fa-solid fa-caret-up"></i>{' '}
                        {parseFloat(jobs_data.jobsImpressionpercentage).toFixed(2) + '%'}
                      </span>
                    </p>
                  </div>
                  <div className="col-6">
                    <p className="name-title-jobs mb-0 w-700 c-747474 open-sans mb-2">POSTED ON:</p>
                    <p className="f-13 c-4D4D4D w-400">{moment(jobs_data.created_at).format('LL')}</p>
                  </div>
                </div>

                <p className="name-title-jobs mb-0 w-700 c-747474 open-sans mb-2">ALL RESPONSES:</p>
                <p className="f-view c-0070F5 w-700">
                  {jobs_data.jobs_view_count}{' '}
                  <span className="w-400 c-0070F5">
                    <a style={{ cursor: 'pointer' }} onClick={e => getjobviewData(e, jobs_data.id)}>
                      (View)
                    </a>
                  </span>
                </p>

                <div className="row">
                  <div className="col-6">
                    <p className="name-title-jobs mb-0 w-700 c-747474 open-sans mb-2">STATUS:</p>
                    {jobs_data.job_status == 'active' && (
                      <button className="btn-app bg-3D9F79-app">{jobs_data.job_status}</button>
                    )}
                    {jobs_data.job_status == 'closed' && (
                      <button className="btn-app bg-bababa-app">{jobs_data.job_status}</button>
                    )}
                    {jobs_data.job_status == 'expired' && (
                      <button className="btn-app bg-D04E4F-app">{jobs_data.job_status}</button>
                    )}
                  </div>
                  <div className="col-6 text-right">
                    <p className="name-title-jobs mb-0 w-700 c-747474 open-sans mb-2">MANAGE</p>
                    <a href="#" onClick={() => modalConfirmOpenEditPostJob(jobs_data.id, jobs_data.job_slug)}>
                      <i className="fa-solid fa-pencil edit-pencil"></i>
                    </a>
                    <a href="#" onClick={e => deletejob(jobs_data.id)}>
                      <i className="fa-regular fa-trash-can del-trash"></i>
                    </a>
                  </div>
                </div>
                <hr />
              </>
            );
          })
        ) : (
          <p className="text-center">No Jobs Found</p>
        )}
      </section>
      <PopupModal
        show={modalConfirmEditPostJob}
        handleClose={modalConfirmCloseEditPostJob}
        customclass={'add_company_signup_popup modal-lg body-sp-0 '}
        closebtnclass={'close-x close-b-des  bg-0055BA border-design'}
        closebtnicon={'icon'}>
        <div className="head-box">
          <div className="row">
            <div className="col-sm-10">
              <p className="f-26 mb-2 mt-2">Edit Post a Job</p>
              <p className="f-16"> Enter your basic job details information & get started right away.</p>
            </div>
            <div className="col-sm-2 text-right">
              <button
                type="button"
                className="close-b-des close-x  bg-0055BA border-design"
                data-bs-dismiss="modal"
                aria-label="Close">
                <i className="fa-solid fa-xmark"></i>
              </button>
            </div>
          </div>
        </div>
        <div className="popup-body">
          <form className="form-experience-fieild" onSubmit={(e: any) => submitEditJobs(e)}>
            <div className="row ">
              <div className="col-sm-6">
                <label className="switch switch-sp" style={{ width: '60px', verticalAlign: 'middle' }}>
                  {membershipplan === '3' &&
                    membershipstatus === 'false' &&
                    featuredjobcount >= 25 &&
                    editFeatured == 0 ? (
                    <>
                      <input
                        type="checkbox"
                        value={editFeatured == '1' ? 0 : 1}
                        checked={editFeatured == '1'}
                        disabled={true}
                      />
                      <span className="slider round" onClick={handelFeatureJobError}></span>
                    </>
                  ) : membershipplan === '2' &&
                    membershipstatus === 'false' &&
                    featuredjobcount >= 15 &&
                    editFeatured == 0 ? (
                    <>
                      <input
                        type="checkbox"
                        value={editFeatured == '1' ? 0 : 1}
                        checked={editFeatured == '1'}
                        disabled={true}
                      />
                      <span className="slider round" onClick={handelFeatureJobError}></span>
                    </>
                  ) : membershipplan === '1' ? (
                    <>
                      <input type="checkbox" value={editFeatured == '1' ? 0 : 1} checked={editFeatured == '1'} />
                      <span className="slider round" onClick={handelFeatureJobError}></span>
                    </>
                  ) : (
                    <>
                      <input
                        type="checkbox"
                        onChange={e => setEditFeatured(e.target.value)}
                        value={editFeatured == '1' ? 0 : 1}
                        checked={editFeatured == '1'}
                      />
                      <span className="slider round"></span>
                    </>
                  )}
                </label>
                {membershipplan === '3' && membershipstatus === 'false' ? (
                  <span>Post as Featured job {25 - featuredjobcount} remaining</span>
                ) : membershipplan === '2' && membershipstatus === 'false' ? (
                  <span>Post as Featured job {15 - featuredjobcount} remaining</span>
                ) : membershipplan === '1' ? (
                  <span>Post as Featured job (0 remaining)</span>
                ) : (
                  <span>Post as Featured job (0 remaining)</span>
                )}
              </div>
              <div className="col-sm-6">
                <label className="switch switch-sp" style={{ width: '60px', verticalAlign: 'middle' }}>
                  <input
                    type="checkbox"
                    onChange={e => setEditCloseJob(e.target.value)}
                    value={editCloseJob == '1' ? 0 : 1}
                    checked={editCloseJob == '1'}
                  />
                  <span className="slider round"></span>
                </label>
                <span>Close this Job?</span>
              </div>
            </div>
            <div className="form_field_sec">
              <input
                type="text"
                placeholder="Alan Moore"
                className="fild-des"
                value={editJobTitle}
                onChange={e => setEditJobTitle(e.target.value)}
              />
              <label className="mt-3 ">Job Title* </label>
            </div>
            <label>Job Description Editor</label>
            {/* <NewMultipleReactQuillEditor
                id="edit_job_description"
                value={editJobDescription}
                onChange={(name: any, val: any) => { handleChangeEditorEditJobDesc(name, val) }}
              /> */}

            <div className="form_field_sec mt-2">
              <select className="big-select" onChange={e => setEditJobType(e.target.value)}>
                <option value="fulltime" selected={editJobType == 'fulltime'}>
                  Full Time
                </option>
                <option value="parttime" selected={editJobType == 'parttime'}>
                  Part Time
                </option>
                <option value="contract" selected={editJobType == 'contract'}>
                  Contract
                </option>
                <option value="freelance" selected={editJobType == 'freelance'}>
                  Freelance
                </option>
              </select>
              <label>Job Type*</label>
            </div>
            <div className="form_field_sec">
              <select className="big-select" value={editJobCountry} onChange={handleEditCountryChange}>
                {Country.length > 0 ? (
                  Country.map((CountryData: any, index) => {
                    if (CountryData.status === 'active') {
                      return (
                        <option value={CountryData.id} key={index} selected={editJobCountry == CountryData.id}>
                          {CountryData.country_name}
                        </option>
                      );
                    }
                  })
                ) : (
                  <option value="">Select Country</option>
                )}
              </select>
              <label>Job Country*</label>
            </div>
            <div className="form_field_sec">
              <select className="big-select" value={editIndustry} onChange={e => setEditIndustry(e.target.value)}>
                {allIndustries.length > 0 ? (
                  allIndustries.map((industry: any, index) => {
                    return (
                      <option value={industry.id} key={index}>
                        {industry.name}
                      </option>
                    );
                  })
                ) : (
                  <option value="">No Data Found</option>
                )}
              </select>
              <label>Industry*</label>
            </div>

            <div className="form_field_sec">
              <select className="big-select" value={editSector} onChange={e => setEditSector(e.target.value)}>
                {sectors.length > 0 ? (
                  sectors.map((sector: any, index) => {
                    return (
                      <option value={sector.id} key={index}>
                        {sector.sector_name}
                      </option>
                    );
                  })
                ) : (
                  <option value="">No Data Found</option>
                )}
              </select>
              <label>Sector*</label>
            </div>
            {errors.sector && errors.sector.type === 'required' && (
              <p className="text-danger" style={{ textAlign: 'left' }}>
                Sector is required.
              </p>
            )}

            <label>Skills Required</label>

            <Select
              value={editSkills}
              isMulti
              name="colors"
              options={options}
              className="basic-multi-select"
              classNamePrefix="select"
              onChange={(value: any) => setEditSkills(value)}
              required
            />

            <div className="form_field_sec">
              <select className="big-select" value={editExperience} onChange={e => setEditExperience(e.target.value)}>
                {experiences.length > 0 ? (
                  experiences.map((experianceData: any, index: number) => {
                    const experienceYears = experianceData.name.split('-');
                    let optionValue = '';
                    if (experienceYears[0] === 'fresher') {
                      optionValue = 'Fresher';
                    } else if (experienceYears[0] === '0' && experienceYears[1] === '1') {
                      optionValue = `${experienceYears[0]}-${experienceYears[1]} year`;
                    } else {
                      optionValue = `${experienceYears[0]} years`;
                    }
                    return (
                      <option value={experianceData.name} key={index} selected={editJobCountry == experianceData.name}>
                        {optionValue}
                      </option>
                    );
                  })
                ) : (
                  <option value="">No Data Found</option>
                )}
              </select>
              <label>Experience*</label>
            </div>

            <div className="row mb-4">
              <div className="col-12">
                <label>Monthly Fixed Salary</label>{' '}
              </div>
              <div className="col-sm-3 col-4">
                <select
                  className="big-select"
                  value={extractCurrencyCode(editMonthlyFixedSalaryCurrency)}
                  onChange={e => setEditMonthlyFixedSalaryCurrency(e.target.value)}>
                  <option>{extractCurrencyCode(editMonthlyFixedSalaryCurrency)}</option>
                </select>
              </div>
              <div className="col-sm-4 col-4">
                <input
                  type="number"
                  placeholder="Min"
                  className="big-input"
                  value={editMonthlyFixedMinSalary}
                  onChange={e => {
                    const value = e.target.value;
                    if (value === '' || (parseInt(value) >= 0 && value.length <= 8)) {
                      setEditMonthlyFixedMinSalary(value);
                    }
                  }}
                  required
                />
              </div>
              <div className="col-sm-5 col-4">
                <input
                  type="number"
                  placeholder="Max"
                  className="big-input"
                  value={editMonthlyFixedMaxSalary}
                  onChange={e => {
                    const value = e.target.value;
                    if (value === '' || (parseInt(value) >= 0 && value.length <= 10)) {
                      setEditMonthlyFixedMaxSalary(value);
                    }
                  }}
                  required
                />
              </div>
            </div>
            <div className="form_field_sec">
              <input
                type="number"
                placeholder="1"
                className="big-input mb-2 border-1"
                value={editAvailableVacancies}
                onChange={e => {
                  const value = e.target.value;
                  if (value === '' || (parseInt(value) >= 0 && value.length <= 4)) {
                    setEditAvailableVacancies(value);
                  }
                }}
                required
              />
              <label>Available Vacancies*</label>
            </div>
            <div className="form_field_sec">
              <input
                type="date"
                className="big-input mb-2 border-1"
                value={editDeadLine}
                onChange={e => setEditDeadLine(e.target.value)}
                min={current_date}
              />
              <label>Deadline*</label>
            </div>
            <label className="check-label">
              {' '}
              <input type="checkbox" className="w-16" onChange={e => setEditHideEmployerDetails(e.target.value)} /> Hide
              Employer Details
            </label>

            <div className="text-right mt-3">
              <button className="save" type="submit" disabled={isLoading}>
                {isLoading ? 'Please wait...' : 'Update a Job'}{' '}
              </button>
            </div>
          </form>
        </div>
      </PopupModal>
      <PopupModal
        show={modalConfirmJobSubmitConfirmationPopup}
        handleClose={modalConfirmCloseJobSubmitConfirmationPopup}
        customclass={'modal-lg body-sp-0 b-r-8-p'}
        closebtnclass={'close-x  bg-0055BA border-design close-b-des'}
        closebtnicon={'icon'}>
        <div className="popup-body pt-0">
          <div className="text-center sp-50 pt-0 pb-3">
            <div className="pog-r w-120 m-0auto">
              <img
                src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blue-round.png'}
                alt="blue-round"
                className="w-120 fa-spin"
              />
              <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/check-i.png'} alt="check-i" className="check-i" />
            </div>
            <h2 className="f-31">
              Job <span className="span-color">Posted</span>
            </h2>
            <p className="f-18 w-600">
              Your job post is live
              <Link href={'/job/' + latestSavedJobsId} className="c-0070F5">
                {' '}
                View{' '}
              </Link>
              it here. Go to
              <Link href={'/employer/jobs'} className="c-0070F5">
                {' '}
                Jobs{' '}
              </Link>
              for more insights.
            </p>
          </div>
        </div>
      </PopupModal>
      <PopupModal
        show={modalConfirmJobUpdateConfirmationPopup}
        handleClose={modalConfirmCloseJobUpdateConfirmationPopup}
        customclass={'modal-lg body-sp-0 '}
        closebtnclass={'close-x  bg-0055BA border-design close-b-des'}
        closebtnicon={'icon'}>
        <div className="popup-body">
          <div className="text-center sp-50 pt-0">
            <div className="pog-r w-120 m-0auto">
              <img
                src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blue-round.png'}
                alt="blue-round"
                className="w-120 fa-spin"
              />
              <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/check-i.png'} alt="check-i" className="check-i" />
            </div>
            <h2 className="f-31">
              Job <span className="span-color">Updated</span>
            </h2>
            <p className="f-18 w-600">
              Your job post is live
              <Link href={'/job/' + jobId} className="c-0070F5">
                {' '}
                View{' '}
              </Link>
              it here. Go to
              <Link href={'/employer/jobs'} className="c-0070F5">
                {' '}
                Jobs{' '}
              </Link>
              for more insights.
            </p>
          </div>
        </div>
      </PopupModal>
      <PopupModal show={modalConfirm7} handleClose={modalConfirmClose7} customclass={'header-remove body-sp-0'}>
        <section className="container mt-4 mb-4">
          <div className="work-experience-field">
            <h6 className="staff-member mb-3">
              <a className="arrow-left" onClick={modalConfirmClose7}>
                <i className="fa-solid fa-arrow-left f-20 c-0055BA"></i>
              </a>
              User Views
            </h6>
            <div className="row">
              {paginatedData.length > 0 ? (
                paginatedData.map((jobviewuser: any, index: any) => (
                  <div className="col-lg-12 col-md-9" key={index}>
                    <div className="right-text-edit mb-3">
                      <div className="row mobile-column-reverse">
                        <div className="col-sm-9">
                          {jobviewuser.role == 'admin' ? (
                            ''
                          ) : jobviewuser.role !== 'staff' ? (
                            <a href={`/candidate-profile/${jobviewuser.slug}`}>
                              <h6>{jobviewuser.name}</h6>
                              <p>
                                <strong>{jobviewuser.email}</strong>
                              </p>
                            </a>
                          ) : (
                            <>
                              <h6>{jobviewuser.name}</h6>
                              <p>
                                <strong>{jobviewuser.email}</strong>
                              </p>
                            </>
                          )}
                          {/* {jobviewuser.role !== 'staff' ? (
                            <a href={`/candidate-profile/${jobviewuser.slug}`}>
                              <h6>{jobviewuser.name}</h6>
                              <p><strong>{jobviewuser.email}</strong></p>
                            </a>
                          ) : (
                            <>
                              <h6>{jobviewuser.name}</h6>
                              <p><strong>{jobviewuser.email}</strong></p>
                            </>
                          )} */}
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-center mt-3">No User Views</p>
              )}
              <div className="pagination-wrapper mt-4">
                <div className="pagination-wrapper">
                  <Pagination
                    items={jobviewuser}
                    currentPage={currentPage1}
                    pageSize={pageSize1}
                    onPageChange={onPageChange1}
                    activePage={currentPage1}
                  />
                </div>
              </div>
            </div>
          </div>
        </section>
      </PopupModal>
    </>
  );
}
