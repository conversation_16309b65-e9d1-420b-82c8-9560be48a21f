.loader-container {
  justify-content: center;
  align-items: center;
  display: flex;
  flex-direction: column;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  position: absolute;
  z-index: 50;
  max-height: 100vh;
}
.blog-blue .all-page-loading .loader-container {
  height: 100vh;
  position: fixed;
  width: 100vw;
  z-index: 101;
}

.loader-container.hide {
  animation-duration: 0.3s;
  animation-fill-mode: forwards;
  animation-name: loadingFadeOut;
}

.loader-container.show {
  animation-duration: 0.3s;
  animation-fill-mode: both;
  animation-name: loadingFadeIn;
}

.loader-container.overlay {
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.4);
}

.loader-container span {
  display: block;
  margin-top: 10px;
}
/* Add button css */
.add-button {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  cursor: pointer;
  transition: background-color 0.3s;
  margin: 0 auto;
  margin-top: 4px;
  width: 40px !important;
  height: 40px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  flex: none;
  margin-top: 30px;
}
.delete-button {
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.add-button:hover {
  background-color: #0056b3;
}

.add-button:active {
  background-color: #003f7f;
}

.plus-icon {
  font-size: 24px;
}

.delete-button:hover {
  background-color: #c82333;
}

.delete-button:active {
  background-color: #bd2130;
}
.delete-icon {
  font-size: 24px;
}
/* Add button css */

.loader-animated {
  perspective: 100px;
}
.loader-animated span {
  display: inline-block;
  width: 6px;
  height: 6px;
  background: #d33333;
  border-radius: 100%;
  margin: 3px;
  position: relative;
  animation: jump ease-in-out infinite;
  animation-duration: 0.8s;
  animation-delay: 0s;
}

.loader-animated span:nth-child(1) {
  animation-delay: 0.2s;
}

.loader-animated span:nth-child(2) {
  animation-delay: 0.4s;
}

.loader-animated span:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes jump {
  from {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes loadingFadeIn {
  from {
    opacity: 0;
    display: flex;
  }
  to {
    opacity: 1;
  }
}

@keyframes loadingFadeOut {
  from {
    opacity: 1;
    display: flex;
  }
  98% {
    opacity: 0;
  }
  to {
    opacity: 0;
    display: none;
    visibility: hidden;
  }
}

img.custom_img {
  width: auto !important;
  max-width: 100%;
  height: auto !important;
}
.delete-button {
  width: 40px !important;
  height: 40px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  flex: none;
  margin-top: 30px;
}