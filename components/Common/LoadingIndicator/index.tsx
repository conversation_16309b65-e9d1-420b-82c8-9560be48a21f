import React from 'react';

interface LoadingIndicatorProps {
  overlay?: boolean;
  size?: 'small' | 'large';
  className?: string;
  message?: string;
  visible?: boolean;
}

const LoadingIndicator = ({overlay = true, message, visible = true}: LoadingIndicatorProps) => (
  <div className={`loader-container ${overlay ? 'overlay' : ''} ${visible ? 'show' : 'hide'}`}>
    <div className={'loader-animated'}>
      <span></span>
      <span></span>
      <span></span>
      <span></span>
    </div>
    {message ? null : <span>{message}</span>}
  </div>
);

export default LoadingIndicator;
