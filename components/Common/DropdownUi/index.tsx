import {Select} from 'antd/lib';
import styles from './style.module.css';
import {SelectProps} from 'antd';

interface DropdownUiProps extends SelectProps {
  label?: string;
  dropDownSize?: 'small' | 'medium' | 'large';
}

export const DropdownUi = ({...props}: DropdownUiProps) => {
  return (
    <>
      <label className={styles.location_label}>{props.label}</label>
      <Select
        style={{
          width: '100%',
          ...props.style,
        }}
        className={`${styles.dropdown} ${styles[props.dropDownSize || 'medium']}`}
        optionFilterProp="label"
        {...props}
      />
    </>
  );
};
