import {Tooltip} from 'antd';

export const ToolTipUi = ({title, children}: {title: string; children: React.ReactNode}) => {
  return (
    <Tooltip
      title={title}
      arrow
      placement="bottom"
      color="#fff"
      overlayInnerStyle={{
        color: '#1F1F1F',
        fontSize: '12px',
        letterSpacing: '0.24px',
        borderRadius: '8px',
        background: '#FCFCFC',
        boxShadow:
          '0px 1px 3px 0px rgba(31, 31, 31, 0.12), 0px 2px 5px 0px rgba(31, 31, 31, 0.10), 0px 4px 12px 0px rgba(21, 21, 21, 0.12)',
        height: '17px',
        minHeight: '26px',
        display: 'flex',
        alignItems: 'center',
      }}
      openClassName="candidate-tooltip">
      {children}
    </Tooltip>
  );
};
