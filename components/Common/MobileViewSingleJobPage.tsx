import React, { useState, useEffect, useRef, useContext } from 'react';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getCurrentUserData } from '../../lib/session';
import PopupModal from './PopupModal';
import {
  getUserSingleJobs,
  getSingleUserDetails,
  applyJob,
  toggleFavoriteJob,
  getSingleEmployerActiveJobs,
  removeFavoriteJob,
  getJobSkills,
  updateSingleJobBackgroundBannerImage,
} from '../../lib/frontendapi';
import ApplicationThankYouPopup from './ApplicationThankYouPopup';
//import { NewMultipleReactQuillEditor } from "./NewMultipleReactQuillEditor";
import Link from 'next/link';
import PhoneInput from 'react-phone-input-2';
import {
  FacebookShareButton,
  FacebookIcon,
  PinterestShareButton,
  PinterestIcon,
  RedditShareButton,
  RedditIcon,
  WhatsappShareButton,
  WhatsappIcon,
  LinkedinShareButton,
  LinkedinIcon,
} from 'next-share';

import Image from 'next/image';
import AuthContext from '@/Context/AuthContext';

interface User {
  name: string;
  email: string;
  contact_no: string;
  date_of_birth: string;
  bio: string;
  gender: string;
  years_of_experience: string;
  current_salary: string;
  desired_salary: string;
  where_currently_based: string;
  current_position: string;
  description: string;
  resume_pdf_path: string;
  default_resume: string;
  unlock_instant_apply: string;
  country_name: string;
  resumedate: string;
  profile_complete_percentage: string;
  singleJobData: any
}
export default function MobileViewSingleJobPage({ singleJobData, jobId }: any) {
  console.log("singleJobData", singleJobData);

  const job_id = jobId;
  const [current_user_id, setCurrentUserId] = useState('');
  const [current_user_role, setCurrentRole] = useState('');
  // const [singleJobData, setSingleJobData]: any = useState([]);
  const [companyName, setCompanyName] = useState('');
  const [modalConfirm7, setModalConfirm7] = useState(false);
  const [name, SetName] = useState('');
  const [contact_no, SetContactNo] = useState('');
  const [gender, SetGender] = useState('');
  const [date_of_birth, SetDateOFBirth] = useState('');
  const [currently_location, SetCurrentlyLocation] = useState('');
  const [selectedChoice, setSelectedChoice] = useState('');
  const [imageUrl, setImageUrl] = useState('');
  const [selecteduserId, setSelectedUserId] = useState('');
  const [selectedcompany, setSelectedcompany] = useState('');
  const [selectCompanyId, setcompanyId] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [allcountries, setCountry] = useState([]);
  const [selectedJobId, setSelectedJobId] = useState('');
  const [modalConfirm2, setModalConfirm2] = useState(false);
  const [modalConfirm3, setModalConfirm3] = useState(false);
  const [appliedJobs, setAppliedJobs] = useState<string[]>([]);
  const [selectedResume, setSelectedResume] = useState(null);
  const [selectedCover, setSelectedCover] = useState(null);
  const [covererrorMessage, setcoverErrorMessage] = useState('');
  const [showPopup, setShowPopup] = useState(false);
  const [jobs, setJobs] = useState([]);
  const [showPopupunsave1, setShowPopupunsave1] = useState(false);
  const [showPopupunerror, setShowPopupunerror] = useState(false);
  const [showmessage, setShowmessage] = useState('');
  const [showPopupunsave, setShowPopupunsave] = useState(false);
  const [modalConfirmUnSavedJobs, setModalConfirmUnSavedJobs] = useState(false);
  const [jobSkills, setJobSkills]: any = useState([]);
  const [jobBannerImage, setJobBannerImage]: any = useState('');
  const [uploadError, setUploadError] = useState('');
  const [descriptionError, setDescriptionError] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedPath, setSelectedPath] = useState('');
  const [totalAppliedJobs, setTotalAppliedJobs] = useState('');
  const anchorRef = useRef<HTMLAnchorElement | null>(null);
  const [showShareButtons, setShowShareButtons] = useState(false);
  const MAX_DESCRIPTION_WORDS = 250;
  const currentDate = new Date();
  // const [user, SetUserData] = useState<User>({
  //   name: '',
  //   email: '',
  //   contact_no: '',
  //   date_of_birth: '',
  //   bio: '',
  //   gender: '',
  //   years_of_experience: '',
  //   current_salary: '',
  //   desired_salary: '',
  //   where_currently_based: '',
  //   current_position: '',
  //   description: '',
  //   resume_pdf_path: '',
  //   default_resume: '',
  //   unlock_instant_apply: '',
  //   country_name: '',
  //   resumedate: '',
  //   profile_complete_percentage: '',
  // });
  const { user } = useContext(AuthContext);

  useEffect(() => {
    if (singleJobData?.company?.logo?.source) {
      let url = singleJobData?.company.logo?.source;
      // let newUrl = url.replace('/storage/', '');
      setImageUrl(url);
    }
    // const current_user_data: any = getCurrentUserData();
    // current_user_data.id ? setCurrentUserId(current_user_data.id) : setCurrentUserId('');
    // current_user_data.role ? setCurrentRole(current_user_data.role) : setCurrentRole('');

    const datas = {
      user_id: user?.id,
      job_id: job_id,
    };
    // getUserSingleJobs(datas)
    //   .then(res => {
    //     if (res.status == true) {
    //       setSingleJobData(res.data);
    //       setCompanyName(res.data.company_name);
    //     } else {
    //       setSingleJobData([]);
    //     }
    //   })
    //   .catch(err => {
    //     console.log(err);
    //   });
    // getSingleUserDetails(current_user_data.id)
    //   .then(res => {
    //     if (res.status === true) {
    //       SetUserData(res.user);
    //       SetName(res.user.name);
    //       SetContactNo(res.user.contact_no);
    //       SetGender(res.user.gender);
    //       SetDateOFBirth(res.user.date_of_birth);
    //       SetCurrentlyLocation(res.user.where_currently_based);
    //       if (res.user.resume_path != 'null') {
    //         setSelectedChoice('below');
    //       }
    //     } else {
    //       SetUserData({
    //         name: '',
    //         email: '',
    //         contact_no: '',
    //         date_of_birth: '',
    //         bio: '',
    //         gender: '',
    //         years_of_experience: '',
    //         current_salary: '',
    //         desired_salary: '',
    //         where_currently_based: '',
    //         current_position: '',
    //         description: '',
    //         resume_pdf_path: '',
    //         default_resume: '',
    //         unlock_instant_apply: '',
    //         country_name: '',
    //         resumedate: '',
    //         profile_complete_percentage: '',
    //       });
    //     }
    //   })
    //   .catch(err => {
    //     console.log(err);
    //   });
    const newdata = {
      job_id: job_id,
      user_id: user?.id,
    };
    getJobSkills(newdata)
      .then(res => {
        if (res.status == true) {
          setJobSkills(res.data);
        } else {
          setJobSkills([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  }, [job_id, user]);
  const modalConfirmOpen7 = () => {
    setModalConfirm7(true);
  };
  const modalConfirmClose7 = () => {
    setModalConfirm7(false);
  };
  const modalConfirmOpen2 = () => {
    setModalConfirm2(true);
  };
  const modalConfirmClose2 = () => {
    setModalConfirm2(false);
  };
  const modalConfirmOpen3 = () => {
    setModalConfirm3(true);
  };
  const modalConfirmClose3 = () => {
    setModalConfirm3(false);
  };
  const modalConfirmClose8 = () => {
    setShowPopupunsave(false);
  };
  const handleautoApplyJob = (jobId: any, userId: any, companyId: any) => {
    if (Number(user?.unlock_instant_apply) === 1) {
      autosubmitForm(jobId, userId, companyId);
      console.log(`Auto apply job with jobId: ${jobId}`);
    } else {
      console.log(`Cannot auto apply job with jobId: ${jobId}`);
    }
  };
  const handleApplyJob = (jobId: any, company_name: any, userId: any, companyId: any) => {
    if (isJobApplied(jobId)) {
    } else {
      setSelectedJobId(jobId);
      setSelectedcompany(company_name);
      setSelectedUserId(userId);
      setcompanyId(companyId);
      modalConfirmOpen2();
    }
  };
  const isJobApplied = (jobId: any) => {
    return appliedJobs.includes(jobId);
  };
  const handleFileChange = (event: any) => {
    const file = event.target.files[0];
    setSelectedResume(file);
    if (file) {
      const allowedFormats = ['pdf', 'doc', 'docx'];
      const fileExtension = file.name.split('.').pop().toLowerCase();
      if (!allowedFormats.includes(fileExtension)) {
        setErrorMessage('Invalid file. Only PDF or DOC files are allowed.');
        setSelectedResume(null);
      } else {
        setErrorMessage('');
      }
    }
  };
  const handleCoverChange = (event: any) => {
    const file = event.target.files[0];
    setSelectedCover(file);
    if (file) {
      const allowedFormats = ['pdf', 'doc', 'docx'];
      const fileExtension = file.name.split('.').pop().toLowerCase();
      if (!allowedFormats.includes(fileExtension)) {
        setcoverErrorMessage('Invalid file. Only PDF or DOC files are allowed.');
        setSelectedCover(null);
      } else {
        setcoverErrorMessage('');
      }
    }
  };
  const autosubmitForm = (jobId: any, userId: any, companyId: any) => {
    //const current_user_data: any = getCurrentUserData();
    const data = {
      company_id: companyId,
      jobpost_by_userid: userId,
      user_id: user?.id,
      job_id: jobId,
      description: '',
      resume_path: selectedResume, //resume_path || user.resume_pdf_path ,
      cover_letter: selectedCover,
      choice: selectedChoice,
      instant_apply: 1,
      name: name ? name : user?.name,
      contact_no: contact_no ? contact_no : user?.contact_no,
      gender: gender ? gender : user?.gender,
      date_of_birth: date_of_birth ? date_of_birth : user?.date_of_birth,
      where_currently_based: currently_location ? currently_location : user?.where_currently_based,
    };
    applyJob(data)
      .then(res => {
        if (res.status) {
          modalConfirmOpen3();
          setTimeout(() => {
            window.location.reload();
          }, 3000);
        }
      })
      .catch(err => {
        toast.error('An error occurred while applying for the job.', {
          position: toast.POSITION.TOP_RIGHT,
          closeButton: true,
          hideProgressBar: false,
          style: {
            background: '#ffe6e6',
            color: '#d04e4f',
            maxWidth: '300px',
            padding: 0,
            margin: 0,
            fontSize: '15px',
            fontFamily: 'var(--opensans-font)',
            paddingRight: '10px',
            paddingLeft: '10px',
          },
          progressStyle: {
            background: '#ffe6e6',
          },
          icon: () => <i className="fa-solid fa-circle-xmark" style={{ color: '#d04e4f', fontSize: '16px' }}></i>,
        });
      });
  };
  const modalConfirmCloseUnSavedJobs = () => {
    setModalConfirmUnSavedJobs(false);
  };
  const savedjobs = (job_ids: any, company_id: any) => {
    const data = {
      user_id: user?.id,
      company_id: company_id,
      job_id: job_ids,
    };
    toggleFavoriteJob(data)
      .then(res => {
        if (res.status == true) {
          setShowPopup(true);
          setTimeout(() => {
            setShowPopup(false);
          }, 3000);
          //const current_user_data: any = getCurrentUserData();
          const datas = {
            user_id: user?.id,
            job_id: job_id,
          };
          // getUserSingleJobs(datas)
          //   .then(res => {
          //     if (res.status == true) {
          //       setSingleJobData(res.data);
          //     } else {
          //       setSingleJobData([]);
          //     }
          //   })
          //   .catch(err => {
          //     console.log(err);
          //   });
          const data = {
            job_id: job_id,
            user_id: user?.id,
          };
          getSingleEmployerActiveJobs(data)
            .then(res => {
              if (res.status == true) {
                setJobs(res.data);
              } else {
                setJobs([]);
              }
            })
            .catch(err => {
              console.log(err);
            });
        } else {
          setShowmessage(res.message);
          setShowPopupunerror(true);
          setTimeout(() => {
            setShowPopupunerror(false);
          }, 3000);
        }
      })
      .catch(err => {
        setShowmessage(err.message);
        setShowPopupunerror(true);
        setTimeout(() => {
          setShowPopupunerror(false);
        }, 3000);
      });
  };
  const unSavedjobs = (id: any, job_ids: any, company_id: any) => {
    const data = {
      user_id: user?.id,
      company_id: company_id,
      job_id: job_ids,
    };
    removeFavoriteJob(id)
      .then(res => {
        if (res.status == true) {
          setShowPopupunsave(true);
          setTimeout(() => {
            setShowPopupunsave(false);
          }, 3000);
          setTimeout(() => {
            modalConfirmCloseUnSavedJobs();
            //const current_user_data: any = getCurrentUserData();
            const datas = {
              user_id: user?.id,
              job_id: job_id,
            };
            // getUserSingleJobs(datas)
            //   .then(res => {
            //     if (res.status == true) {
            //       setSingleJobData(res.data);
            //     } else {
            //       setSingleJobData([]);
            //     }
            //   })
            //   .catch(err => {
            //     console.log(err);
            //   });
            const data = {
              job_id: job_id,
              user_id: user?.id,
            };
            getSingleEmployerActiveJobs(data)
              .then(res => {
                if (res.status == true) {
                  setJobs(res.data);
                } else {
                  setJobs([]);
                }
              })
              .catch(err => {
                console.log(err);
              });
          }, 3000);
        } else {
          setShowmessage(res.message);
          setShowPopupunerror(true);
          setTimeout(() => {
            setShowPopupunerror(false);
          }, 3000);
        }
      })
      .catch(err => {
        setShowmessage(err.message);
        setShowPopupunerror(true);
        setTimeout(() => {
          setShowPopupunerror(false);
        }, 3000);
      });
  };
  const submitUploaBackgroundBannerImage = (e: any, job_id: any) => {
    e.preventDefault();
    const data = {
      job_id: job_id,
    };
    // Pending work of upload image
    updateSingleJobBackgroundBannerImage(data)
      .then(res => {
        if (res.status == true) {
          setShowmessage(res.message);
          setShowPopupunsave1(true);
          setTimeout(() => {
            setShowPopupunsave1(false);
          }, 1000);
          modalConfirmClose7();
          setTimeout(function () {
            window.location.reload();
          }, 1000);
        } else {
          setShowmessage(res.message);
          setShowPopupunerror(true);
          setTimeout(() => {
            setShowPopupunerror(false);
          }, 1000);
          if (res.errors) {
            setShowmessage(res.errors.job_background_banner_img[0]);
            setShowPopupunerror(true);
            setTimeout(() => {
              setShowPopupunerror(false);
            }, 1000);
          }
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  const submitForm = (event: any) => {
    event.preventDefault();
    //const current_user_data: any = getCurrentUserData();
    const description = user?.description;
    if (!description || description.trim() === '') {
      setShowmessage('Please enter a description.');
      setShowPopupunerror(true);
      setTimeout(() => {
        setShowPopupunerror(false);
      }, 3000);
      return;
    }
    const descriptionWords = user.description ? user?.description.split(/\s+/).filter(Boolean).length : 0;
    if (descriptionWords > MAX_DESCRIPTION_WORDS) {
      setDescriptionError(`Description should not exceed ${MAX_DESCRIPTION_WORDS} words.`);
      return;
    } else {
      setDescriptionError('');
    }
    setIsLoading(true);
    const data = {
      company_id: selectCompanyId,
      jobpost_by_userid: selecteduserId,
      user_id: user?.id,
      job_id: selectedJobId,
      description: user?.description,
      resume_path: selectedResume, //resume_path || user.resume_pdf_path ,
      cover_letter: selectedCover,
      choice: selectedChoice,
      instant_apply: 0,
      name: name ? name : user?.name,
      contact_no: contact_no ? contact_no : user?.contact_no,
      gender: gender ? gender : user?.gender,
      date_of_birth: date_of_birth ? date_of_birth : user?.date_of_birth,
      where_currently_based: currently_location ? currently_location : user?.where_currently_based,
    };

    applyJob(data)
      .then(res => {
        if (res.status) {
          setIsLoading(false);
          modalConfirmClose2();
          modalConfirmOpen3();
          setTimeout(() => {
            window.location.reload();
          }, 3000);
        } else {
          if (res.error === 'job_already_applied') {
            setShowmessage('You have already applied for this job');
            setShowPopupunerror(true);
            setTimeout(() => {
              setShowPopupunerror(false);
            }, 3000);
          } else {
            setShowmessage('An error occurred while applying for the job.');
            setShowPopupunerror(true);
            setTimeout(() => {
              setShowPopupunerror(false);
            }, 3000);
          }
        }
      })
      .catch(err => {
        setShowmessage('An error occurred while applying for the job.');
        setShowPopupunerror(true);
        setTimeout(() => {
          setShowPopupunerror(false);
        }, 3000);
      })
      .finally(() => {
        setIsLoading(false); // Reset loading state whether the request succeeds or fails
      });
  };
  // const EditChange = (name: any, value: any) => {
  //   SetUserData(prevState => {
  //     return {
  //       ...prevState,
  //       [name]: value,
  //     };
  //   });
  // };
  const handleCancel = () => {
    modalConfirmClose2();
  };
  const handleImageClick = () => {
    setShowShareButtons(!showShareButtons);
  };
  const handleClick = (event: MouseEvent) => {
    if (anchorRef.current && event.target instanceof Node && !anchorRef.current.contains(event.target)) {
      setShowShareButtons(false);
    }
  };
  const extractCurrencyCode = (value: string) => {
    if (!value) {
      return '';
    }
    const currencyCode = value.split('(')[1]?.trim()?.slice(0, -1);
    return currencyCode || '';
  };


  console.log("singleJobData", singleJobData);
  return (
    <>
      <section className="single-jobs-back bg-fff">
        <div className="pog-r">
          {singleJobData.background_banner_image ? (
            <>
              <img
                src={
                  process.env.NEXT_PUBLIC_IMAGE_URL + 'images/jobbannerImage/' + singleJobData.background_banner_image
                }
                alt="alan"
                style={{ width: '100%' }}
              />
              {user?.id == singleJobData.user_id && user?.role == 'employer' ? (
                <a href="#" className="edit-icon-bg-white" onClick={modalConfirmOpen7}>
                  <i className="fa-solid fa-pencil"></i>
                </a>
              ) : (
                ''
              )}
            </>
          ) : (
            <>
              <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/alan.jpg'} alt="alan" style={{ width: '100%' }} />
              {user?.id == singleJobData.user_id && user?.role == 'employer' ? (
                <a href="#" className="edit-icon-bg-white" onClick={modalConfirmOpen7}>
                  <i className="fa-solid fa-pencil"></i>
                </a>
              ) : (
                ''
              )}
            </>
          )}
          <PopupModal
            show={modalConfirm7}
            handleClose={modalConfirmClose7}
            customclass={'header-remove upload_profile_image_model_dialog body-sp-0'}>
            <div className="popup-body">
              <h5 className="f-26 c-0055BA w-700 text-center mb-4">Upload Your Banner Image</h5>
              <form onSubmit={(e: any) => submitUploaBackgroundBannerImage(e, singleJobData.id)}>
                <div className="upload-file">
                  <div className="file-up">
                    <input
                      type="file"
                      name="job_banner_image"
                      id="job_banner_image"
                      onChange={(e: any) => setJobBannerImage(e.target.files[0])}
                      accept=".jpg, .png, .jpeg"
                    />
                    <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/cemra.png'} alt="cemra" className="cemra" />
                  </div>
                  <p className="upload-text">Browse and chose the files you want to upload from your computer.</p>
                  <p className="max-size">Maximum upload size is 1MB</p>
                </div>
                {uploadError && <p className="error mt-2">{uploadError}</p>}
                <div className="modal-footer">
                  <button
                    type="button"
                    className="cancel-btn m-w-100"
                    data-bs-dismiss="modal"
                    onClick={modalConfirmClose7}>
                    Cancel
                  </button>
                  <button type="submit" className="update-btn m-w-100">
                    Update
                  </button>
                </div>
              </form>
            </div>
          </PopupModal>
          {/* <img src='/images/alan.jpg' className='w-100'/>
                    <a href="#" className='edit-icon-bg-white'><i className="fa-solid fa-pencil"></i></a> */}
          <div className="text-center pog-r">
            {singleJobData?.company?.logo ? (
              <img
                // src={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/companylogo/' + singleJobData.company_logo}
                // src={imageUrl ? `${process.env.NEXT_PUBLIC_IMAGE_URL}${imageUrl}` : '/images/logo-cir.png'}
                src={imageUrl ? imageUrl : '/images/logo-cir.png'}
                alt="Avatars-5"
                className="mar-m img-w-short"
              />
            ) : (
              <img
                src={process.env.NEXT_PUBLIC_BASE_URL + 'images/user-blog.png'}
                alt="Avatars-5"
                className="mar-m img-w-short"
              />
            )}
            {/* <img src="/images/user-blog.png" alt="Avatars-5" className="mar-m img-w-short"/> */}
            <Link href="#" onClick={handleImageClick} ref={anchorRef} className="share-icon-img">
              <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/share.png'} alt='share' />
            </Link>
            {showShareButtons && (
              <div className="mt-3">
                <FacebookShareButton url={`${process.env.NEXT_PUBLIC_BASE_URL}job/${singleJobData?.job_slug}`}>
                  <FacebookIcon size={32} round className="m-2" />
                </FacebookShareButton>
                <RedditShareButton url={`${process.env.NEXT_PUBLIC_BASE_URL}job/${singleJobData?.job_slug}`}>
                  <RedditIcon size={32} round className="m-2" />
                </RedditShareButton>
                <WhatsappShareButton url={`${process.env.NEXT_PUBLIC_BASE_URL}job/${singleJobData?.job_slug}`}>
                  <WhatsappIcon size={32} round className="m-2" />
                </WhatsappShareButton>
                <LinkedinShareButton url={`${process.env.NEXT_PUBLIC_BASE_URL}job/${singleJobData?.job_slug}`}>
                  <LinkedinIcon size={32} round className="m-2" />
                </LinkedinShareButton>
              </div>
            )}
            {/* <a href="/jobs/afghanistan/graphics-designer-2#" className='share-icon-img'>
                            <img src="http://localhost:3000/images/share.png" />
                        </a> */}
            <h4 className="em-name f-54">{singleJobData.job_title ? singleJobData.job_title : 'Job Title'}</h4>
            {singleJobData.hide_employer_details == 0 ? (
              <p className="f-22 c-0070F5 mb-1">
                <Link href={'/companies/' + singleJobData.company.company_slug}>{singleJobData.company.company_name}</Link>
              </p>
            ) : (
              ''
            )}
            {/* <p className="f-22 c-0070F5 mb-1"><a href="/companies/1">Binary data Pvt. Ltd.</a></p> */}
            <ul className="skills mt-1 mb-1">
              <li>
                <p className="f-16 c-999999">
                  <i className="fa-solid fa-location-dot"></i>{' '}
                  {singleJobData.country ? singleJobData.country.country_name : 'Country Name'}
                </p>
              </li>
            </ul>
            {user?.id != singleJobData.user_id ? (
              user?.id && user?.role == 'employee' ? (
                <>
                  {singleJobData.is_appied ? (
                    <button className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp mt-3" disabled>
                      Applied
                    </button>
                  ) : Number(user?.unlock_instant_apply) == 1 && Number(user?.profile_complete_percentage) == 100 ? (
                    <button
                      className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp mt-3"
                      onClick={() =>
                        handleautoApplyJob(singleJobData.id, singleJobData.user_id, singleJobData.company_id)
                      }>
                      <i className="fa-solid fa-bolt"></i> Apply Now
                    </button>
                  ) : (
                    <button
                      className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp mt-3"
                      onClick={() =>
                        handleApplyJob(
                          singleJobData.id,
                          singleJobData.company_name,
                          singleJobData.user_id,
                          singleJobData.company_id,
                        )
                      }>
                      <i className="fa-solid fa-bolt"></i> Apply Now
                    </button>
                  )}
                  {singleJobData.saved_id ? (
                    <button
                      className="download mt-3  w-100 max-100"
                      onClick={() => unSavedjobs(singleJobData.saved_id, singleJobData.id, singleJobData.company_id)}>
                      <i className="fa-solid fa-bookmark"></i> &nbsp; Saved Job{' '}
                    </button>
                  ) : (
                    <button
                      className="download mt-3  w-100 max-100"
                      onClick={() => savedjobs(singleJobData.id, singleJobData.company_id)}>
                      <i className="fa-regular fa-bookmark"></i> &nbsp; Save Job
                    </button>
                  )}
                </>
              ) : (
                <>
                  <Link href="/auth/login">
                    <button className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp mt-3  ">Apply Now</button>
                  </Link>
                  <Link href="/auth/login">
                    <button className="download mt-3  w-100 max-100">
                      <i className="fa-regular fa-bookmark"></i> &nbsp; Save Job
                    </button>
                  </Link>
                </>
              )
            ) : (
              ''
            )}
            {/* <button className="btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp mt-3  ">Apply Now</button>
                        <button className="download mt-3  w-100 max-100"><i className="fa-regular fa-bookmark"></i> &nbsp; Save Job</button> */}
            <div className="text-left mt-4 pb-3">
              <p className="f-12 c-000 mb-1">Salary</p>
              <ul className="skills skills-f-18 ">
                <li>
                  <p className="cat w-600">
                    {extractCurrencyCode(singleJobData.monthly_fixed_salary_currency)}{' '}
                    {singleJobData.monthly_fixed_salary_min} -{' '}
                    {extractCurrencyCode(singleJobData.monthly_fixed_salary_currency)}{' '}
                    {singleJobData.monthly_fixed_salary_max} per month
                  </p>
                </li>
              </ul>
              <p className="f-12 c-000 mb-1">Job Type</p>
              <ul className="skills skills-f-18 ">
                <li>
                  <p className="cat w-600">{singleJobData.job_type ? singleJobData.job_type : 'Part-Time'}</p>
                </li>
              </ul>
              <p className="f-12 c-000 mb-1">Experience</p>
              <ul className="skills skills-f-18 ">
                <li>
                  <p className="cat w-600">{singleJobData.experience ? singleJobData.experience : 4}Years</p>
                </li>
              </ul>
              <p className="f-12 c-000 mb-1">No. of Vacancies</p>
              <ul className="skills skills-f-18 ">
                <li>
                  <p className="cat w-600">
                    {singleJobData.available_vacancies ? singleJobData.available_vacancies : '5'}
                  </p>
                </li>
              </ul>
              {singleJobData.job_description && (
                <div className="col-12 mt-4">
                  <p className="f-12 c-000">Job Description</p>
                  <p
                    className="f-16 w-400 c-4D4D4D open-sans description-des"
                    dangerouslySetInnerHTML={{ __html: singleJobData.job_description }}></p>
                </div>
              )}
              <p className="f-16 c-000 mt-4">Skills Required</p>
              <ul className="skills skills-f-18 mt-2">
                {jobSkills.length > 0 ? (
                  jobSkills.map((job_skills: any, index: any) => {
                    return (
                      <li key={index}>
                        <p className="cat bg-CFE5FF">{job_skills.skills}</p>
                      </li>
                    );
                  })
                ) : (
                  <li></li>
                )}
              </ul>
            </div>
          </div>
        </div>
      </section>
      <PopupModal
        show={modalConfirm2}
        handleClose={modalConfirmClose2}
        customclass={'add_company_signup_popup modal-lg body-sp-0 '}
        closebtnclass={'close-x  bg-0055BA border-design close-b-des'}
        closebtnicon={'icon'}>
        <div className="head-box">
          <div className="row">
            <div className="col-sm-10">
              <p className="f-26 mb-2 mt-2"> Apply to {selectedcompany}</p>
              <p className="f-16"> Enter your basic information & get apply.</p>
            </div>
            <div className="col-sm-2 text-right">
              <button
                type="button"
                className="close-x  bg-0055BA border-design close-b-des"
                data-bs-dismiss="modal"
                aria-label="Close">
                <i className="fa-solid fa-xmark" onClick={modalConfirmClose2}></i>
              </button>
            </div>
          </div>
        </div>
        <div className="popup-body scroll-pop-h">
          <form className="form-experience-fieild" onSubmit={submitForm}>
            <div className="row ">
              <div className="col-sm-7 text-right">
                <p className="f-22 mt-2">Upload your recent Resume/CV:</p>
              </div>
              <div className="col-sm-5">
                <div className="uploade-btn">
                  <input type="file" name="resume" onChange={handleFileChange} />
                  <button className="download ">
                    <i className="fa-solid fa-upload"></i> Upload Resume
                  </button>
                </div>
                {errorMessage && <p className="error">{errorMessage}</p>}
              </div>
            </div>
            {selectedResume && (
              <div className="w-box bg-fff mt-3 mb-3 p-3">
                <div className="row">
                  <div className="col-sm-8">
                    <p className="f-18 w-600 mb-2">{user?.name}-Resume</p>
                    <p className="f-16 c-999999">
                      Uploaded on{' '}
                      {currentDate.toLocaleString('en-US', {
                        month: 'short',
                        day: 'numeric',
                      })}
                    </p>
                  </div>
                  <div className="col-sm-4 text-right">
                    <span
                      className={selectedChoice == 'above' ? 'default' : 'selected'}
                      style={{ cursor: 'pointer' }}
                      onClick={() => {
                        setSelectedChoice('above');
                        // setSelectedPath("path for the above choice");
                      }}>
                      Choose
                      { }
                    </span>
                    &nbsp;&nbsp;
                  </div>
                </div>
              </div>
            )}

            {user?.resume_pdf_path && Number(user?.default_resume) === 1 ? (
              <div className="w-box bg-fff mt-3 mb-3 p-3">
                <div className="row">
                  <div className="col-sm-8">
                    <p className="f-18 w-600 mb-2">{user?.name}-Default Resume</p>
                    <p className="f-16 c-999999">
                      Uploaded on{' '}
                      {new Date(user.resumedate ? user?.resumedate : '').toLocaleString('en-US', {
                        month: 'short',
                        day: 'numeric',
                      })}
                    </p>
                  </div>

                  <div className="col-sm-4 text-right">
                    <span
                      className={selectedChoice == 'below' ? 'default' : 'selected'}
                      style={{ cursor: 'pointer' }}
                      onClick={() => {
                        setSelectedChoice('below');
                        setSelectedPath('path for the below choice');
                      }}>
                      Choose
                    </span>
                    &nbsp;&nbsp;
                  </div>
                </div>
              </div>
            ) : null}
            <div className="row">
              <div className="col-sm-6">
                <label>Your Name*</label>
                <input
                  type="text"
                  placeholder="Alan Moore"
                  className="fild-des"
                  name="name"
                  value={name || ''}
                  onChange={e => SetName(e.target.value)}
                />
              </div>
              <div className="col-sm-6">
                <label>Email ID*</label>
                <input
                  type="email"
                  placeholder="<EMAIL>"
                  className="fild-des filed_disabled"
                  //value={user.email}
                  defaultValue={user?.email}
                  disabled
                />
              </div>
            </div>
            <div className="row">
              <div className="col-sm-6">
                <label>Contact Number*</label>
                <PhoneInput
                  country={'us'}
                  value={contact_no || ''}
                  inputClass="fild-des-contact"
                  onChange={contactNumber => SetContactNo(contactNumber)}
                />
              </div>
              <div className="col-sm-6">
                <label>Gender</label>
                <select
                  className="fild-des"
                  name="gender"
                  value={gender || ''}
                  onChange={e => SetGender(e.target.value)}>
                  <option value="">Select gender</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>

            <div className="row">
              <div className="col-sm-6">
                <label>Date of Birth*</label>
                <input
                  type="date"
                  placeholder="<EMAIL>"
                  className="fild-des"
                  name="date_of_birth"
                  value={date_of_birth || ''}
                  onChange={e => SetDateOFBirth(e.target.value)}
                />
              </div>
              <div className="col-sm-6">
                <label>Where are you currently based?*</label>

                <select
                  className="fild-des"
                  value={currently_location || ''}
                  onChange={e => SetCurrentlyLocation(e.target.value)}>
                  {allcountries.map((countries: any) => (
                    <option key={countries.id} value={countries.id}>
                      {countries.country_name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <label>Your Cover Letter*</label>
            {/* <NewMultipleReactQuillEditor
                        id="edit_job_description"
                        value={user.description || ""}
                        onChange={(name: any, value: any) => {
                        EditChange("description", value);
                        }}

                        className="fild-des"
                    /> */}
            {descriptionError && <div className="text-danger mt-2">{descriptionError}</div>}
            {/* <p className="font-12 text-right words">250 words</p> */}

            {/* <div className='d-flex justify-content-center'>
                        <button className="download d-flex justify-content-center ">
                            <i className="fa-solid fa-upload"></i> Upload Cover Letter
                        </button>
                        </div> */}
            <div className="d-flex justify-content-center mt-4">
              <div className="uploade-btn ">
                <input type="file" name="resume" accept=".pdf, .docx" onChange={handleCoverChange} />
                <button className="download ">
                  <i className="fa-solid fa-upload"></i> Upload Cover Letter
                </button>
                {covererrorMessage && <div className="text-danger mt-2">{covererrorMessage}</div>}
              </div>
            </div>
            {selectedCover && (
              <div className="w-box bg-fff mt-3 mb-3 p-3">
                <div className="row">
                  <div className="col-sm-8">
                    <p className="f-18 w-600 mb-2">{user?.name}-Cover-Letter</p>
                    <p className="f-16 c-999999">
                      Uploaded on{' '}
                      {currentDate.toLocaleString('en-US', {
                        month: 'short',
                        day: 'numeric',
                      })}
                    </p>
                  </div>
                </div>
              </div>
            )}
            {Number(totalAppliedJobs) == 0 && (
              <p className="pt-4 text-center" style={{ color: '#D04E4F' }}>
                Note: An incomplete profile may affect your chances of landing your dream job.
              </p>
            )}
            <div className="text-right mt-3">
              <a className="cancel" onClick={handleCancel}>
                Cancel
              </a>
              <button className="save" type="submit" disabled={isLoading}>
                {isLoading ? 'Please wait...' : 'Save'}
              </button>
            </div>
          </form>
          <ToastContainer />
        </div>
      </PopupModal>
      <PopupModal
        show={modalConfirm3}
        handleClose={modalConfirmClose3}
        customclass={'modal-lg  header-remove body-sp-0 '}>
        <div className="popup-body">
          <div className="row">
            <div className="col-sm-10"> </div>
            <div className="col-sm-2 text-right">
              <button
                type="button"
                className="close-x  bg-0055BA border-design"
                data-bs-dismiss="modal"
                aria-label="Close">
                <i className="fa-solid fa-xmark" onClick={modalConfirmClose3}></i>
              </button>
            </div>
          </div>
          <div className="text-center sp-50">
            <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/check-blue.png'} alt="check-blue" className="w-120" />
            <h2 className="f-31">
              Application <span className="span-color">Submitted</span>
            </h2>
            <p className="f-18">
              Go to{' '}
              <a href="#" className="c-0070F5">
                {' '}
                My Applications
              </a>{' '}
              to view your application status.
            </p>
          </div>
        </div>
      </PopupModal>
      {showPopup && (
        <div className="toast-box color-0055BA code-job">
          <div className="toast-footer">
            <div className="row">
              <div className="col-7">
                <h5>
                  <i className="fa-solid fa-circle-info"></i> Your job has been saved.
                </h5>
              </div>
              <div className="col-5 text-right">
                <p>
                  <Link href="/employees/jobs/savedjobs" className="link-12">
                    View Saved Jobs
                  </Link>{' '}
                  <i
                    className="fa-solid fa-xmark xmark-icon c-0070F5"
                  //onClick={modalConfirmClose5}
                  ></i>
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
      {showPopupunsave && (
        <div className="toast-box color-0055BA code-job">
          <div className="toast-footer">
            <div className="row">
              <div className="col-7">
                <h5>
                  <i className="fa-solid fa-circle-info"></i> Your job has been Unsaved.
                </h5>
              </div>
              <div className="col-5 text-right">
                <p>
                  <i className="fa-solid fa-xmark xmark-icon c-0070F5" onClick={modalConfirmClose8}></i>
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
