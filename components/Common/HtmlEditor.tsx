import React from "react";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import TextEditorMenuBar from "@/components/Common/TextEditorMenuBar";
import TextAlign from "@tiptap/extension-text-align";
import Image from "@tiptap/extension-image";
import Link from "@tiptap/extension-link";

interface HtmlEditorProps {
  name?: string;
  value?: string;
  onChange: (name?: string, html?: string) => void;
}

export const HtmlEditor = ({ name, value, onChange }: HtmlEditorProps) => {
  const onContentUpdate = (event: any) => {
    onChange(name, event.editor.getHTML());
  };

  const editor = useEditor(
    {
      extensions: [
        StarterKit,
        TextAlign.configure({
          types: ["heading", "paragraph"],
        }),
        Image.configure({
          allowBase64: true,
        }),
        Link.configure({
          openOnClick: false,
          linkOnPaste: true,
          autolink: true,
        }),
      ],
      onBlur: onContentUpdate,
      content: value,
    },
    [value],
  );

  return (
    <>
      <div className="html-editor-wrapper">
        <TextEditorMenuBar editor={editor} />
        <EditorContent className={"editor-content"} editor={editor} />
      </div>
    </>
  );
};
