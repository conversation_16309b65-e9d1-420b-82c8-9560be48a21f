import React, {useState} from 'react';
import styles from './style.module.css';
import {useClickOutside} from '@/hooks/useOutSideClick';

interface DropdownProps<T> {
  items: T[];
  labelExtractor: (item: T) => string;
  valueExtractor?: (item: T) => string;
  onSelect: (selected: any) => void;
  placeholder?: string;
  customContent?: (item: T) => React.ReactNode;
  className?: string;
  optionClassName?: string;
  open: boolean;
  setOpen: any;
  excludeRef?: any;
  outSideRef?: any;
  style?: any;
}

export const CustomOptionDropdown = <T,>({
  items,
  labelExtractor,
  valueExtractor,
  onSelect,
  placeholder,
  customContent,
  className,
  optionClassName,
  open,
  setOpen,
  excludeRef,
  outSideRef,
  style,
}: DropdownProps<T>) => {
  const [selectedItem, setSelectedItem] = useState<any | null>(null);
  const ref = React.useRef<HTMLDivElement>(null);
  useClickOutside(outSideRef ?? ref, [excludeRef], () => {
    setOpen(false);
  });

  const handleOptionClick = (item: any) => {
    setSelectedItem(item);
    onSelect(item);
    setOpen(false);
  };

  const handleClear = (e: any) => {
    e.stopPropagation();
    setSelectedItem(null);
    onSelect(null);
  };

  return (
    <div className={`${styles.dropdown} ${className}`} ref={ref}>
      <div
        className={styles.selected}
        style={{width: 'fit-content', minWidth: 160, ...style}}
        onClick={() => setOpen(!open)}>
        {selectedItem ? labelExtractor(selectedItem) : placeholder || 'Select'}
        <div className={`${styles.icon_container} ${selectedItem ? styles.selected_value : ''}`}>
          <span className={styles.arrow}>
            <img src={'/icons/automation/chevron-down.png'} width={16} height={8} alt="arrow" />
          </span>
          <span className={styles.clear} onClick={handleClear}>
            <img src={'/icons/cross_black.svg'} width={24} height={24} alt="clear" />
          </span>
        </div>
      </div>
      {open && (
        <div className={`${styles.options} ${optionClassName}`}>
          {' '}
          {items?.length > 0 ? (
            items.map((item: any, index) => (
              <>
                <div
                  key={valueExtractor ? valueExtractor(item) : index}
                  className={`${styles.option} ${selectedItem?.value === item?.value ? styles.selectedOption : ''}`}
                  onClick={e => {
                    e.stopPropagation();
                    item.disabled ? () => {} : handleOptionClick(item);
                  }}>
                  <div className={styles.option_content}>{labelExtractor(item)}</div>
                </div>
                {customContent && customContent(item)}
              </>
            ))
          ) : (
            <div className={styles.no_data_found}>No Data Found...</div>
          )}
        </div>
      )}
    </div>
  );
};
