.dropdown {
  position: relative;
  font-family: Arial, sans-serif;
}

.selected {
  background-color: white;
  border: 1px solid #d9d9d9;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  color: #999;
  font-feature-settings:
    'liga' off,
    'clig' off;
  font-size: 14px;
  line-height: 24px;
  letter-spacing: -0.5px;
  position: relative;
}

.selected:hover {
  border-color: #40a9ff;
}

.arrow {
  font-size: 12px;
  color: #999;
}

.options {
  position: absolute;
  top: 100%;
  right: 0;
  width: 100%;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  margin-top: 4px;
  z-index: 1000;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  min-width: 200px;
  max-height: 400px;
  overflow-y: auto;
}

.option {
  padding: 8px 12px;
  cursor: pointer;
}

.option:hover {
  background-color: #f5f5f5;
}

.selectedOption {
  background-color: #e6f7ff;
}

.customRange {
  display: flex;
  gap: 8px;
  padding: 8px 12px;
  border-top: 1px solid #f0f0f0;
}

.dateInput {
  width: calc(50% - 4px);
  padding: 6px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
}

.dateInput:focus {
  border-color: #40a9ff;
  outline: none;
}
.no_data_found {
  padding: 12px;
  min-height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.icon_container {
  display: flex;
  justify-content: center;
  align-items: center;
  max-height: 20px;
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
}
.selected_value.icon_container:hover {
  right: 10px !important;
}
.clear {
  display: none;
  cursor: pointer;
}
.selected_value.icon_container:hover .arrow {
  display: none;
}

.selected_value.icon_container:hover .clear {
  display: block;
}
