import React, {useState, useEffect} from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {getAllBlogs, getHomeTopBlogs} from '@/lib/adminapi';
import {Blogs} from '@/lib/types';
import {Button} from 'antd';
import <PERSON>rror<PERSON>andler from '@/lib/ErrorHandler';
import styles from './style.module.css';

export default function TopBlogs(props: any) {
  const [blogs, setAllBlogs] = useState<Blogs[]>([props.initialBlogs]);
  const [selectedCategoryID, setSelectedCategoryID] = useState<number | null>(null);

  useEffect(() => {
    getHomeTopBlogs()
      .then(res => {
        setAllBlogs(res.data);
      })
      .catch(error => {
        ErrorHandler.showNotification(error);
      });
  }, []);

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    const options: Intl.DateTimeFormatOptions = {year: 'numeric', month: 'long', day: 'numeric'};
    return date.toLocaleDateString(undefined, options);
  };

  const categories = [
    {id: null, label: 'All'},
    {id: 1, label: 'Career Advice'},
    {id: 2, label: 'Resume Tips'},
    {id: 3, label: 'Expert Speak'},
    {id: 4, label: 'News/ Updates'},
  ];

  return (
    <section className="tab-part sp-80 tab-card">
      <div className="container">
        <h3 className="title-heading text-center">
          Looking for <span className="span-color"> inspiration?</span>
        </h3>
        <p className="f-26 w-700 text-center mobile-f-23">Check out our career resources to help you succeed!</p>

        <ul className={`nav nav-pills ${styles.categories_lists}`} id="pills-tab" role="tablist">
          {categories.map(category => (
            <li className="nav-item" role="presentation" key={category.id}>
              {category.label !== 'All' ? (
                <Link href={`/blog`} prefetch={false}>
                  <Button
                    className={`nav-link ${selectedCategoryID === category.id ? 'active' : ''}`}
                    //onClick={() => setSelectedCategoryID(category.id)}
                    style={{height: 'auto'}}>
                    {category.label}
                  </Button>
                </Link>
              ) : (
                <Button
                  className={`nav-link ${selectedCategoryID === category.id ? 'active' : ''}`}
                  onClick={() => setSelectedCategoryID(category.id)}
                  style={{height: 'auto'}}>
                  {category.label}
                </Button>
              )}
            </li>
          ))}
        </ul>
        <div className="tab-content" id="pills-tabContent">
          <div className="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab">
            <div className="row">
              {blogs.length === 0 ? (
                <p>No blogs yet</p>
              ) : (
                blogs
                  // .filter(
                  //   blog =>
                  //     selectedCategoryID === null ||
                  //     (blog.blog_category_id && parseInt(blog.blog_category_id) === selectedCategoryID),
                  // )
                  .map((blog, index) => (
                    <div className="col-lg-4 col-md-6 col-12" key={index}>
                      <div className={styles.blog_card}>
                        <Link href={`/blog/${blog?.slug}`} prefetch={false}>
                          <img
                            src={
                              blog?.image
                                ? `${process.env.NEXT_PUBLIC_IMAGE_URL}images/blogs/${blog.image}`
                                : `${process.env.NEXT_PUBLIC_BASE_URL}images/placeholder.jpg`
                            }
                            alt={blog?.image ? 'tab-1' : 'Default Blog'}
                            className="tab-1"
                            width={384}
                            height={214}
                            layout="responsive"
                          />
                        </Link>
                        <Link href={`/blog/${blog?.slug}`} prefetch={false}>
                          {blog?.tag !== undefined && blog?.tag !== null && (
                            <ul className="list-tags">
                              {blog?.tag.split(',').map((tag: any, index: any) => <li key={index}>{tag}</li>)}
                            </ul>
                          )}
                        </Link>
                        <Link href={`/blog/${blog?.slug}`} prefetch={false} className={styles.blog_name}>
                          <h4>{blog?.name?.slice(0, 25)}</h4>
                        </Link>
                        <Link href={`/blog/${blog?.slug}`} prefetch={false}>
                          <div className="row mt-4">
                            <div className="col-sm-2 col-3 pr-0">
                              <img
                                src={
                                  blog?.author?.profile_image
                                    ? `${process.env.NEXT_PUBLIC_IMAGE_URL}images/blogs/author/${blog.author?.profile_image}`
                                    : `${process.env.NEXT_PUBLIC_BASE_URL}images/users.jpg`
                                }
                                alt={blog?.image ? 'tab-1' : 'Default Blog'}
                                className="w-48"
                                width={60}
                                height={60}
                                layout="responsive"
                              />
                            </div>
                            <div className="col-sm-9 col-9 pl-0 px-2">
                              <h6 className="tab-name-user">{blog?.author?.name}</h6>
                              <p className="tab-interview">{formatDate(blog?.created_at || '')} - 5 min read</p>
                            </div>
                          </div>
                        </Link>
                      </div>
                    </div>
                  ))
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export async function getServerSideProps() {
  try {
    const response = await getHomeTopBlogs();
    const blogs: Blogs[] = await response.data;

    return {
      props: {
        initialBlogs: blogs,
      },
    };
  } catch (error) {
    ErrorHandler.showNotification(error);
  }
}
