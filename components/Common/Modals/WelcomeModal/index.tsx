import React from 'react'

interface ModalProps {
  onClose?: () => void;
}

export default function WelcomeModal({ onClose }: ModalProps) {

  const handleClose = () => {
    if (onClose) {
      onClose();
    }
  };

  return (
        <div className='row'>
          <div className='col-sm-4 popup-right border-left'></div>
          <div className='col-sm-8'>
            <div className="text-right">
            </div>

            <div className="popup-left-text sp-80 text-center">
              <h3>Welcome  <span className="span-color"> to The Talent Point! </span></h3>
              <p className='f-22 c-2C2C2C'>The gateway to finding your dream job</p>
              <p className='f-18 c-4D4D4D'>Complete your profile to level up your job search and unlock the true potential of our platform.</p>

              <button className="btn login  mt-4" type="submit"  onClick={handleClose}>Get Started</button>
            </div>
          </div>
        </div>
  )
}
