import React, {useContext, useEffect, useState} from 'react';
import Link from 'next/link';
import Image from 'next/image';
import JobApplyButton from '../JobApplyButton';
import JobSaveButton from '../JobSaveButton';
import {Job} from '@/lib/types';
import useWindowDimensions from '@/helpers/useWindowDimensions';

import JsonLd from '../../../components/JsonLd';
import AuthContext from '@/Context/AuthContext';

interface JobListItemProps {
  job: Job;
  index: number;
  jobInCityName: any;
  jobPosting: boolean;
}

const JobListItem = ({job, index, jobInCityName, jobPosting}: JobListItemProps) => {
  const [imageUrl, setImageUrl] = useState('');
  const windowDimensions: any = useWindowDimensions();
  const [jobPosted, setJobPosted] = useState('');
  const {user} = useContext(AuthContext);

  const truncateString = (str: any, maxLength: number) => {
    if (str?.length > maxLength) {
      return str.substring(0, maxLength) + '...';
    }
    return str;
  };
  useEffect(() => {
    const time = dateDifference(job.created_at);
    if (job.company?.logo?.source) {
      let url = job.company?.logo?.source;
      // let newUrl = url.replace('/storage/', '');
      setImageUrl(url);
    }
    // Output the difference
    if (time.days === 0) {
      setJobPosted(time.hours.toString() + ' ' + 'hours ago');
    } else if (time.days < 30) {
      setJobPosted(time.days.toString() + ' ' + 'days ago');
    } else {
      // Format the given date to "MMM DD" (e.g., "Feb 21")
      const formattedDate = time.givenData.toLocaleDateString('en-US', {month: 'short', day: 'numeric'});
      setJobPosted(formattedDate);
    }
  }, [job]);

  const dateDifference = (givenDate: any) => {
    givenDate = new Date(givenDate);

    const currentDate: any = new Date();

    // Calculate the difference in milliseconds
    const differenceInMilliseconds = currentDate - givenDate;
    // Convert milliseconds to seconds
    const differenceInSeconds = Math.floor(differenceInMilliseconds / 1000);

    // Convert seconds to minutes, hours, days, etc.
    const seconds = differenceInSeconds % 60;
    const minutes = Math.floor(differenceInSeconds / 60) % 60;
    const hours = Math.floor(differenceInSeconds / 3600) % 24;
    const days = Math.floor(differenceInSeconds / (3600 * 24));
    return {
      hours: hours,
      days: days,
      givenData: givenDate,
    };
  };

  return (
    job && (
      <div className="job-list-item-wrapper">
        <div className="filter filter-sp m-center b-r-16" key={index}>
          <div className="row justify-content-between">
            <div className="col pr-0 max-w-99  m-m-auto">
              {/* {process.env.NEXT_PUBLIC_IMAGE_URL} {imageUrl} */}
              <img
                // src={imageUrl ? `${process.env.NEXT_PUBLIC_IMAGE_URL}${imageUrl}` : '/images/logo-img.png'}
                src={imageUrl ? imageUrl : '/images/logo-img.png'}
                alt="logo-img"
                className="logo-filter"
                priority
                width={78}
                height={78}
              />
            </div>
            <div className="col-sm-7">
              <Link prefetch={false} target="_blank" href={'/job/' + job.job_slug}>
                <p className="p-18">{job.job_title}</p>
              </Link>
              <div className="job_feature">
                <Link prefetch={false} className="job" target="_blank" href={'/companies/' + job.company?.company_slug}>
                  <p className="p-16 mt-1 c_name">{truncateString(job.company?.company_name, 25)}</p>
                </Link>
                {job.is_featured !== 0 ? (
                  <div className="feat">
                    <p className="feature">
                      <i className="fa-regular fa-gem"></i>
                      <span>Featured</span>
                    </p>
                  </div>
                ) : (
                  ''
                )}
              </div>
              <ul className="full-time">
                <li className="text-capitalize">
                  <i className="fa-solid fa-business-time"></i>
                  {job.job_type}
                </li>
                <li className="text-capitalize">
                  <i className="fa-solid fa-location-dot"></i> {job.country?.country_name}
                </li>
              </ul>
            </div>
            {windowDimensions.width > 767 && (
              <div className="col-sm-4 col-md-4 col-lg-4 col-xl-3 col-xxl-3 text-right m-center">
                {job?.company_id !== (user?.company_id || user?.company?.id) && <JobApplyButton job={job} />}
                <JobSaveButton job={job} />
              </div>
            )}
          </div>
          <div className="jobs_post_date">
            <p>Posted {jobPosted}</p>
          </div>
        </div>

        <JsonLd
          data={{
            '@context': 'https://schema.org/',
            '@type': 'Occupation',
            name: job.job_title,
            mainEntityOfPage: {
              '@type': 'WebPage',
              lastReviewed: job.updated_at,
            },
            description: job.job_description,
            estimatedSalary: [
              {
                '@type': 'MonetaryAmountDistribution',
                name: 'base',
                currency: job?.country?.currency,
                duration: 'P1Y',
                minValue: job.monthly_fixed_salary_min || 0,
                percentile25: '',
                median: '',
                percentile75: '',
                maxValue: job.monthly_fixed_salary_max || 0,
              },
            ],
            occupationLocation: [
              {
                '@type': 'City',
                name: job?.country?.capital,
              },
            ],
          }}
        />
      </div>
    )
  );
};
export default JobListItem;
