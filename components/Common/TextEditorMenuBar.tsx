import React, { useCallback } from "react";
import { MdStrikethroughS, MdOutlineFormatBold, MdOutlineFormatItalic } from "react-icons/md";
import {
  AiOutlineAlignLeft,
  AiOutlineAlignRight,
  AiOutlineAlignCenter,
  AiOutlineOrderedList,
  AiOutlineUnorderedList,
  AiOutlineLink,
  AiOutlineFileImage,
} from "react-icons/ai";
import { Button, Dropdown, MenuProps, Space } from "antd";
import ButtonGroup from "antd/lib/button/button-group";
import { DownOutlined } from "@ant-design/icons";

interface TextEditorMenuBarProps {
  editor: any;
  buttonShape?: "circle" | "default" | "round" | undefined;
}

const TextEditorMenuBar = ({ editor }: TextEditorMenuBarProps) => {
  const setLink = useCallback(() => {
    const previousUrl = editor.getAttributes("link").href;
    const url = window.prompt("URL", previousUrl);

    if (url === null) {
      return;
    }

    if (url === "") {
      editor.chain().focus().extendMarkRange("link").unsetLink().run();
      return;
    }

    editor.chain().focus().extendMarkRange("link").setLink({ href: url }).run();
  }, [editor]);

  const uploadImage = useCallback(() => {
    const fileInput = document.createElement('input');
    fileInput.setAttribute('type', 'file');
    fileInput.setAttribute('accept', 'image/*');
    fileInput.addEventListener('change', (e: any) => {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (event: any) => {
          const imageUrl = event.target.result;
          editor.chain().focus().setImage({ src: imageUrl }).run();
        };
        reader.readAsDataURL(file);
      }
    });
    fileInput.click();
  }, [editor]);

  const items: MenuProps["items"] = [
    { label: "H1", key: "1" },
    { label: "H2", key: "2" },
    { label: "H3", key: "3" },
    { label: "H4", key: "4" },
    { label: "H5", key: "5" },
    { label: "H6", key: "6" },
  ];

  const handleMenuClick: MenuProps["onClick"] = (e) => {
    editor
      .chain()
      .focus()
      .toggleHeading({ level: parseInt(e.key) })
      .run();
  };

  const menuProps = {
    items,
    onClick: handleMenuClick,
  };

  const buttonProps: any = {
    type: "text",
    shape: "round",
    size: "small",
  };
  return !editor ? null : (
    <div className={"editor-menubar"}>
      <Space wrap size={"small"}>
        <Dropdown menu={menuProps}>
          <Button {...buttonProps}>
            <Space>
              Head
              {editor.isActive("heading", { level: 1 }) ? "H1" : ""}
              {editor.isActive("heading", { level: 2 }) ? "H2" : ""}
              {editor.isActive("heading", { level: 3 }) ? "H3" : ""}
              {editor.isActive("heading", { level: 4 }) ? "H4" : ""}
              {editor.isActive("heading", { level: 5 }) ? "H5" : ""}
              {editor.isActive("heading", { level: 6 }) ? "H6" : ""}
              <DownOutlined />
            </Space>
          </Button>
        </Dropdown>
        <Button
          {...buttonProps}
          onClick={() => editor.chain().focus().toggleBold().run()}
          color={"#ff0000"}
          type={editor.isActive("bold") ? "primary" : "text"}
        >
          <MdOutlineFormatBold className={"icon"} />
        </Button>
        <Button
          {...buttonProps}
          onClick={() => editor.chain().focus().toggleItalic().run()}
          type={editor.isActive("italic") ? "primary" : "text"}
        >
          <MdOutlineFormatItalic className={"icon"} />
        </Button>
        <Button
          {...buttonProps}
          onClick={() => editor.chain().focus().toggleStrike().run()}
          type={editor.isActive("strike") ? "primary" : "text"}
        >
          <MdStrikethroughS className={"icon"} />
        </Button>
        <Button
          {...buttonProps}
          onClick={() => editor.chain().focus().setTextAlign("left").run()}
          type={editor.isActive("left") ? "primary" : "text"}
        >
          <AiOutlineAlignLeft className={"icon"} />
        </Button>
        <Button
          {...buttonProps}
          onClick={() => editor.chain().focus().setTextAlign("center").run()}
          type={editor.isActive("center") ? "primary" : "text"}
        >
          <AiOutlineAlignCenter className={"icon"} />
        </Button>
        <Button
          {...buttonProps}
          onClick={() => editor.chain().focus().setTextAlign("right").run()}
          type={editor.isActive("textAlign") ? "primary" : "text"}
        >
          <AiOutlineAlignRight className={"icon"} />
        </Button>
        <Button
          {...buttonProps}
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          type={editor.isActive("orderedList") ? "primary" : "text"}
        >
          <AiOutlineOrderedList className={"icon"} />
        </Button>
        <Button
          {...buttonProps}
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          type={editor.isActive("bulletList") ? "primary" : "text"}
        >
          <AiOutlineUnorderedList className={"icon"} />
        </Button>
        <Button {...buttonProps} onClick={setLink} type={editor.isActive("link") ? "primary" : "text"}>
          <AiOutlineLink className={"icon"} />
        </Button>
        <Button {...buttonProps} onClick={uploadImage} type={editor.isActive("image") ? "primary" : "text"}>
          <AiOutlineFileImage className={"icon"} />
        </Button>
      </Space>
    </div>
  );
};

export default TextEditorMenuBar;
