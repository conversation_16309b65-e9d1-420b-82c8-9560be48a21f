import {StarSelection} from '@/components/Company/StartSelection';
import styles from './style.module.css';

interface StarInputProps {
  label: string;
  required: boolean;
  value: number;
  onChange: (value: number) => void;
  size: 'small' | 'medium' | 'large';
}

export const StarInput = ({label, required, value, onChange, size}: StarInputProps) => {
  return (
    <div>
      <label className={styles.label}>
        {label}
        {required && <span className={styles.require}>*</span>}
      </label>
      <div
        style={{
          marginTop: 10,
        }}>
        <StarSelection value={value} onChange={onChange} size={size} type="multiple" variant="selectable" />
      </div>
    </div>
  );
};
