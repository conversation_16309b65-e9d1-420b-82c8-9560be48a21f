import React from "react";
import styles from "../../styles/Home.module.css";
import Link from "next/link";

interface PaginationProps {
  items: any[];
  currentPage: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  activePage: number;
  totalCount?: number | null
}

const Pagination = ({
  items,
  pageSize,
  currentPage,
  onPageChange,
  activePage,
  totalCount = null
}: PaginationProps) => {
  const pagesCount = Math.ceil((totalCount || items?.length) / pageSize);
  if (pagesCount === 1) return null;
  const pages = Array.from({ length: pagesCount }, (_, i) => i + 1);

  return (
    <div>
      <ul className={styles.pagination}>
        {pages.map((page) => (
          (page < 6 || (page > currentPage - 2 && page < currentPage + 2) ||
            page > pages.length - 2) ?
            <li
              key={page}
              className={
                page === activePage
                  ? `${styles.pageItem} ${styles.pageItemActive}`
                  : styles.pageItem
              }
              onClick={() => onPageChange(page)}
            >
              <a className={styles.pageLink}>{page}</a>
            </li>
            :
            pages.length > 8 && page < 7 ? '...'
              :
              page < pages.length && page == currentPage + 2 ? '...'
                : null
        ))}
      </ul>
    </div >
  );
};

export default Pagination;
