import {Radio, RadioProps} from 'antd';
import styles from './style.module.css';

import {InputHTMLAttributes} from 'react';

interface RadioUiProps extends RadioProps {
  label?: string;
}

export const RadioUi = (props: RadioUiProps) => {
  const {label} = props;
  return (
    <div className={`${styles.radio_container} `}>
      <Radio {...props} type="radio" className={styles.radio}>
        {label}
      </Radio>
    </div>
  );
};
