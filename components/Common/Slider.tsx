// @ts-nocheck
import React from 'react';
import Slider from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import Image from 'next/image';
import ts from 'typescript';

const SimpleSlider: React.FC = () => {
  const settings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 5,
    slidesToScroll: 1,
    autoplay: true, // Enable autoplay
    autoplaySpeed: 3000, // Set autoplay speed (in milliseconds)
    responsive: [
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 3,
        },
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 2,
        },
      },
    ],
  };
  return (
    <Slider {...settings} className="pt-3 mt-2">
      <div className="mx-2">
        <img
          src={process.env.NEXT_PUBLIC_BASE_URL + 'images/brandlogo/1.png'}
          alt="Atlys -TalentPoint"
          width={150}
          height={90}
        />
      </div>
      <div className="mx-2">
        <img
          src={process.env.NEXT_PUBLIC_BASE_URL + 'images/brandlogo/2.png'}
          alt="FreelanceDXB -TalentPoint"
          width={150}
          height={90}
        />
      </div>
      <div className="mx-2">
        <img
          src={process.env.NEXT_PUBLIC_BASE_URL + 'images/brandlogo/3.png'}
          alt="Freezoner -TalentPoint"
          width={150}
          height={90}
        />
      </div>
      <div>
        <img
          src={process.env.NEXT_PUBLIC_BASE_URL + 'images/brandlogo/4.png'}
          alt="GoFreelance -TalentPoint"
          width={150}
          height={90}
        />
      </div>
      <div>
        <img
          src={process.env.NEXT_PUBLIC_BASE_URL + 'images/brandlogo/5.png'}
          alt="TheVisaServices -TalentPoint"
          width={150}
          height={90}
        />
      </div>
      <div>
        <img
          src={process.env.NEXT_PUBLIC_BASE_URL + 'images/brandlogo/6.png'}
          alt="Twofour54AbuDhabi -TalentPoint"
          width={150}
          height={90}
        />
      </div>
      <div>
        <img
          src={process.env.NEXT_PUBLIC_BASE_URL + 'images/brandlogo/7.png'}
          alt="Twofour54AbuDhabi -TalentPoint"
          width={150}
          height={90}
        />
      </div>
      <div>
        <img
          src={process.env.NEXT_PUBLIC_BASE_URL + 'images/brandlogo/8.png'}
          alt="Twofour54AbuDhabi -TalentPoint"
          width={150}
          height={90}
        />
      </div>
      <div>
        <img
          src={process.env.NEXT_PUBLIC_BASE_URL + 'images/brandlogo/9.png'}
          alt="Twofour54AbuDhabi -TalentPoint"
          width={150}
          height={90}
        />
      </div>
    </Slider>
  );
};

export default SimpleSlider;
