import styles from "../../styles/Home.module.css";
const Pagination = ({ items, pageSize, currentPage, onPageChange,activePage }:any) => {
  const pagesCount = Math.ceil(items / pageSize); // 100/10
  if (pagesCount === 1) return null;
  const pages = Array.from({ length: pagesCount }, (_, i) => i + 1);
  return (
    <div>
        <ul className={styles.pagination}>
        {pages.map((page) => (
          (page < 6 || (page > currentPage - 2 && page < currentPage + 2) || 
          page > pages.length - 2 )  ?
            <li
              key={page}
              className={
                page === activePage
                  ? `${styles.pageItem} ${styles.pageItemActive}`
                  : styles.pageItem
              }
              onClick={() => onPageChange(page)}
            >
              <a className={styles.pageLink}>{page}</a>
            </li>
          :
            pages.length>8 && page < 7 ? '...'
          : 
            page < pages.length && page == currentPage+2 ? '...'
          :null
        ))}
      </ul>
    </div>
  );
};
export default Pagination;