import React, {useState, useContext} from 'react';
import {useRouter} from 'next/router';
import PopupModal from '../../components/Common/PopupModal';
import 'react-toastify/dist/ReactToastify.css';
import swal from 'sweetalert2';
import AuthContext from '@/Context/AuthContext';
import {purchasePlan} from '@/lib/frontendapi';
import {notification} from 'antd';

export default function PlanPopup({show, setModalConfirm}: any) {
  const router = useRouter();
  const {user} = useContext(AuthContext);

  const modalConfirmPopupClose = () => {
    setModalConfirm(false);
  };

  const selectPlan = (e: any, plan_id: any) => {
    if (plan_id == 1) {
      swal.fire({
        icon: 'info',
        title: 'Free Plan',
        text: 'Your free plan has been activated when you create an account and is valid for one day',
      });
    } else {
      if (user) {
        const data = {
          user_id: user?.id,
          company_id: user?.company_id,
          plan_id: plan_id,
        };

        purchasePlan(data).then(res => {
          if (res.success) {
            notification.success({message: res.message});
            router.push('/payment');
          }
        });
      }
    }
  };

  return (
    <PopupModal show={show} handleClose={modalConfirmPopupClose} customclass={'modal-xl pricing-pop head-d-none'}>
      <section className="pricing-part pt-0 pb-2 mt-0">
        <div className="container">
          <h2>
            Hire Smarter With Our <br />
            <span> Flexible Pricing Plans</span>
          </h2>
          <h3 className="choose-plan">Choose a plan that’s right for you</h3>
          <div className="row mt-5">
            <div className="col-lg-4 col-md-12">
              <div className="plan-box">
                <h4>Free</h4>
                <p>Ideal for small businesses or startups with limited hiring needs and a tight budget.</p>
                <h3>
                  <small>USD</small> 0 <sup>/ Month</sup>
                </h3>
                <button className="started-part mt-3 mb-3" onClick={(e: any) => selectPlan(e, 1)}>
                  Get Started Now
                </button>
                <ul className="check-close">
                  <li>
                    <i className="fa-solid fa-check"></i> Valid for 1 Day.
                  </li>
                  <li>
                    <i className="fa-solid fa-check"></i> 30 CV Search.
                  </li>
                  <li>
                    <i className="fa-solid fa-check"></i> 30 CV Views.
                  </li>
                  <li>
                    <i className="fa-solid fa-check"></i> 30 CV Downdload.
                  </li>

                  <li className="dis">
                    <i className="fa-solid fa-xmark"></i> Unlimited Cv Search.
                  </li>
                  <li className="dis">
                    <i className="fa-solid fa-xmark"></i> Unlimited Cv Downloading and Emailing Per Month.
                  </li>
                  <li className="dis">
                    <i className="fa-solid fa-xmark"></i> Free unlimited User access.
                  </li>
                  {/* <li className="dis">
                    <i className="fa-solid fa-xmark"></i> No Job Posting.
                  </li> */}
                  <li className="dis">
                    <i className="fa-solid fa-xmark"></i> Email Notification of Matching CV.
                  </li>
                  <li className="dis">
                    <i className="fa-solid fa-xmark"></i> Dedicated Relationship manager.
                  </li>
                  <li className="dis">
                    <i className="fa-solid fa-xmark"></i> Mass Messaging to selected candidates.
                  </li>
                  <li className="dis">
                    <i className="fa-solid fa-xmark"></i> Unlimited Job Share on Linkedin
                  </li>
                </ul>
              </div>
            </div>
            <div className="col-lg-4 col-md-12">
              <div className="plan-box blue-plan">
                <h4>12 Month</h4>
                <p>Ideal for businesses with specific hiring needs and advanced search features.</p>
                <h3>
                  <small>USD</small> 2500 <sup>/12 Month</sup>{' '}
                </h3>
                <button className="started-part mt-3 mb-3" onClick={(e: any) => selectPlan(e, 2)}>
                  Get Started Now
                </button>
                <ul className="check-close">
                  <li>
                    <i className="fa-solid fa-check"></i> Unlimited Cv Search.
                  </li>
                  <li>
                    <i className="fa-solid fa-check"></i> Unlimited Cv Downloading and Emailing Per Month.
                  </li>
                  <li>
                    <i className="fa-solid fa-check"></i> 500 CV Views.
                  </li>
                  {/* <li>
                    <i className="fa-solid fa-check"></i> 25 Job Posting.
                  </li> */}
                  <li>
                    <i className="fa-solid fa-check"></i> Free unlimited User access.
                  </li>
                  <li>
                    <i className="fa-solid fa-check"></i> Email Notification of Matching CV.
                  </li>
                  <li>
                    <i className="fa-solid fa-check"></i> Dedicated Relationship manager.
                  </li>
                  <li>
                    <i className="fa-solid fa-check"></i> Mass Messaging to selected candidates.
                  </li>
                  <li>
                    <i className="fa-solid fa-check"></i> Unlimited Job Share on Linkedin.
                  </li>

                  <li className="dis">
                    <i className="fa-solid fa-xmark"></i>35 Job Posting.
                  </li>
                </ul>
              </div>
            </div>
            <div className="col-lg-4 col-md-12">
              <div className="plan-box">
                <h4>Enterprise Plan</h4>
                <p>Ideal for large organizations with extensive hiring needs and dedicated support.</p>
                <h3>
                  <small>USD</small> 3500 <sup>/18 Month</sup>
                </h3>
                <button className="started-part mt-3 mb-3" onClick={(e: any) => selectPlan(e, 3)}>
                  Get Started Now
                </button>
                <ul className="check-close">
                  <li>
                    <i className="fa-solid fa-check"></i> Unlimited Cv Search.
                  </li>
                  <li>
                    <i className="fa-solid fa-check"></i> Unlimited Cv Downloading and Emailing Per Month.
                  </li>
                  <li>
                    <i className="fa-solid fa-check"></i> Unlimited CV Views.
                  </li>
                  <li>
                    <i className="fa-solid fa-check"></i> 35 Job Posting.
                  </li>
                  <li>
                    <i className="fa-solid fa-check"></i> Free unlimited User access.
                  </li>
                  <li>
                    <i className="fa-solid fa-check"></i> Email Notification of Matching CV.
                  </li>
                  <li>
                    <i className="fa-solid fa-check"></i> Dedicated Relationship manager.
                  </li>
                  <li>
                    <i className="fa-solid fa-check"></i> Mass Messaging to selected candidates.
                  </li>
                  <li>
                    <i className="fa-solid fa-check"></i> Unlimited Job Share on Linkedin.
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>
    </PopupModal>
  );
}
