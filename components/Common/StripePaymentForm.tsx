import React, { useState, useEffect, useContext } from 'react';
import { useRouter } from 'next/router';
import { savePlanPayment } from '../../lib/frontendapi';
import 'react-toastify/dist/ReactToastify.css';
import Swal from 'sweetalert2';
import AuthContext from '@/Context/AuthContext';
import <PERSON><PERSON>r<PERSON><PERSON><PERSON> from '@/lib/ErrorHandler';
import { Button, Form, Input, Row, Col } from 'antd';
import Image from 'next/image';

export default function CheckoutForm() {
  const { user, memberPlanInfo } = useContext(AuthContext);
  const [isProcessing, setIsProcessing] = useState(false);
  const router = useRouter();
  const [form] = Form.useForm();

  useEffect(() => {
    if (user) {
      form.setFieldsValue({
        name: user.name || '',
        email: user.email || '',
        cardNumber: user.card_number?.toString() || '',
        expiryMonth: user.card_exp_month?.toString() || '',
        expiryYear: user.card_exp_year?.toString() || '',
        cvc: user.card_cvv?.toString() || '',
      });
    }

    console.log(memberPlanInfo);
  }, [user, form, memberPlanInfo]);

  if (!memberPlanInfo) {
    return <p>Loading member plan info...</p>;
  }

  const onFinish = async (values: any) => {
    setIsProcessing(true);
    const data = {
      name: values.name,
      email: values.email,
      card_number: values.cardNumber,
      exp_month: values.expiryMonth,
      exp_year: values.expiryYear,
      cvc: values.cvc,
      amount: memberPlanInfo?.amount,
      user_id: user?.id,
      company_id: user?.company_id,
      custom_plan_id: memberPlanInfo?.plan_id,
    };
    savePlanPayment(data)
      .then(res => {
        setIsProcessing(false);
        Swal.fire({
          icon: 'success',
          title: '<strong>Thank You</strong>',
          text: 'Your payment of amount ' + memberPlanInfo?.amount + ' USD has been successfully done',
          showConfirmButton: true,
        }).then(function () {
          router.push('/employer/dashboard');
        });
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  };
  return (
    <>
      <section className="over-s">
        <div className="container-fluid p-lg-0 sp-0-cont">
          <div className="row g-3 pb-5 mt-4 align-items-center justify-content-center">
            <div className="col-lg-6 col-md-12 pb-5  tab-sp-b ">
              <div className="contact_text mt-3">
                <div className="row">
                  <div className="col-sm-6">
                    <h3 className="mb-4">Payment Details</h3>
                  </div>
                  <div className="col-sm-6 display-td text-right">
                    <img
                      className="img-responsive pull-right"
                      src={process.env.NEXT_PUBLIC_BASE_URL + 'images/payment_icons.png'}
                      alt='Payment Icons'
                      style={{ height: '35px' }}
                    />
                  </div>
                </div>
                <p className="mb-4">
                  Stripe is Trustworthy payment processing made easy with our reliable and user-friendly platform
                </p>
                <Form form={form} onFinish={onFinish} className="pb-2 stripe_form">
                  <Row gutter={[16, 16]}>
                    <Col span={24} md={12}>
                      <Form.Item name="name" rules={[{ required: true, message: 'Please enter your name' }]}>
                        <Input placeholder="Enter Name" size="large" />
                      </Form.Item>
                    </Col>
                    <Col span={24} md={12}>
                      <Form.Item
                        name="email"
                        rules={[
                          { required: true, message: 'Please enter your email' },
                          { type: 'email', message: 'Invalid email address' },
                        ]}>
                        <Input placeholder="Enter Email" size="large" />
                      </Form.Item>
                    </Col>
                    <Col span={24}>
                      <Form.Item
                        name="cardNumber"
                        rules={[
                          { required: true, message: 'Please enter your card number' },
                          { pattern: /^\d{16}$/, message: 'Invalid card number' },
                        ]}>
                        <Input placeholder="Enter Card Number" maxLength={16} size="large" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="expiryMonth"
                        rules={[
                          { required: true, message: 'Please enter your card expiry month' },
                          { pattern: /^((0[1-9])|(1[0-2]))$/, message: 'Invalid expiry month' },
                        ]}>
                        <Input placeholder="MM" maxLength={2} size="large" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="expiryYear"
                        rules={[
                          { required: true, message: 'Please enter your card expiry year' },
                          { pattern: /^\d{4}$/, message: 'Invalid expiry year' },
                        ]}>
                        <Input placeholder="YYYY" maxLength={4} size="large" />
                      </Form.Item>
                    </Col>
                    <Col span={24}>
                      <Form.Item
                        name="cvc"
                        rules={[
                          { required: true, message: 'Please enter your card security code (CVC)' },
                          { pattern: /^\d{3}$/, message: 'Invalid CVC' },
                        ]}>
                        <Input placeholder="ex. 311" maxLength={3} size="large" />
                      </Form.Item>
                    </Col>
                    <Col span={24} className="text-right">
                      <Button type="primary" htmlType="submit" loading={isProcessing} style={{ width: '100%' }}>
                        {isProcessing ? 'Processing...' : `Pay ${memberPlanInfo?.amount} USD`}
                      </Button>
                    </Col>
                  </Row>
                </Form>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
