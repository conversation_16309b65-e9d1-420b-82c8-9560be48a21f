import React, {useState, useContext} from 'react';
import Image from 'next/image';
import Link from 'next/link';
import SimpleSlider from '../Common/Slider';
import AuthContext from '@/Context/AuthContext';
import {Button} from 'antd';
export default function HiringTalent(props: any) {
  return (
    <div className="desktop-view-slider" id="who-hiring-section">
      <section className="talent sp-50">
        <div className="container">
          <h2 className="title-heading" aria-level={2} role="heading">
            Who’s Hiring <span className="span-color">Talent?</span>
          </h2>
          <div className="row mt-5 tab-m-0">
            <div className="col-lg-3 col-md-12 ">
              <div className="we-to">
                <h5>We’re home to</h5>
                <h2>1000+</h2>
                <h5>job listings!</h5>
              </div>
            </div>

            <div className="col-lg-9 col-md-12 ">
              <img
                src={'/images/home/<USER>'}
                alt="Logos"
                className="m-none"
                width={100}
                loading="lazy"
                height={442}
                layout="responsive"
              />
              <div className="mobile-view-slider">
                <SimpleSlider />
              </div>
            </div>
          </div>
          <div className="text-center mt-5">
            <Button style={{height: 'auto'}} className="btn-a primary-size-18 btn-bg-0055BA" onClick={props.onClick}>
              Apply Now
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
