import React, {useEffect, useState} from 'react';
import {But<PERSON>, Col, Form, Row, notification, Spin} from 'antd';
import {updateCompanyLogo, updateUserDetails, getCurrentUserDetails} from '@/lib/frontendapi';
import ModalForm from '../ModalForm';
import axios from 'axios';
import {FilePond, registerPlugin} from 'react-filepond';
import FilePondPluginImageExifOrientation from 'filepond-plugin-image-exif-orientation';
import FilePondPluginImagePreview from 'filepond-plugin-image-preview';
import 'filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css';
import 'filepond/dist/filepond.min.css';
import {File} from '@/lib/types';
registerPlugin(FilePondPluginImageExifOrientation, FilePondPluginImagePreview);
import Image from 'next/image';

interface UserProfileImageProps {
  className?: string;
  user?: any;
  company?: any;
  width?: number;
  height?: number;
  showModel?: boolean;
  showUploadButton?: boolean;
  isSidebarCollapsed?: boolean;
}
const DEMO_USER_IMAGE = `${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-1.png`;
export default function UserProfileImage({
  className,
  user,
  company,
  width = 118,
  height = 118,
  showModel = false,
  showUploadButton = false,
  isSidebarCollapsed = false,
}: UserProfileImageProps) {
  const [profileImageUploadModal, setProfileImageUploadModal] = useState(showModel);
  const [previewImage, setPreviewImage] = useState('');
  const [uploadedFile, setUploadedFile] = useState<File>();
  const [isLoading, setIsLoading] = useState(false);
  const [userData, setUserData] = useState(user);

  // Fetch user data if company is missing
  useEffect(() => {
    if (user && !user.company && user.id && user.role === 'employer') {
      setIsLoading(true);
      getCurrentUserDetails(user.id)
        .then(res => {
          if (res.status === true) {
            setUserData({...user, ...res.user});
          }
          setIsLoading(false);
        })
        .catch(err => {
          console.error('Error fetching user details:', err);
          setIsLoading(false);
        });
    } else {
      setUserData(user);
    }
  }, [user]);

  useEffect(() => {
    if (userData) {
      if (company && userData?.company) {
        setPreviewImage(userData.company.logo?.source || userData?.profile_image?.source || DEMO_USER_IMAGE);
      } else if (userData?.company?.logo?.source) {
        setPreviewImage(userData.company.logo?.source || DEMO_USER_IMAGE);
      } else {
        setPreviewImage(userData?.profile_image?.source || DEMO_USER_IMAGE);
      }
    }
  }, [userData, company]);

  const openProfileImageUploadModal = () => {
    setProfileImageUploadModal(true);
  };
  const closeProfileImageUploadModal = () => {
    setProfileImageUploadModal(false);
  };

  const onSubmitForm = () => {
    if (userData?.role === 'employer' && (company || userData?.company)) {
      submitUploadCompanyLogo();
    } else {
      const data = {
        user_id: userData?.id,
        fk_profile_file_uuid: uploadedFile?.uuid,
      };
      updateUserDetails(userData?.id, data)
        .then(res => {
          if (res.status == true) {
            notification.success({message: 'Profile upload successfully.'});
            setTimeout(() => {
              window.location.reload();
            }, 2000);
          }
          closeProfileImageUploadModal();
        })
        .catch(err => {
          console.log(err);
        });
    }
  };

  const submitUploadCompanyLogo = async () => {
    const data = {
      company_id: userData?.company_id,
      fk_logo_file_uuid: uploadedFile?.uuid,
    };
    updateCompanyLogo(data)
      .then(res => {
        if (res.status == true) {
          notification.success({message: res.message});
          setTimeout(() => {
            window.location.reload();
          }, 2000);
        } else {
          if (res.errors) {
            notification.error({message: res?.errors?.fk_logo_file_uuid?.join(',')});
          }
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  if (isLoading) {
    return (
      <div className={`mb-4 m-auto user-profile-image-wrapper ${className}`} style={{textAlign: 'center'}}>
        <Spin size="large" />
      </div>
    );
  }

  if (!userData) {
    return (
      <div className={`mb-4 m-auto user-profile-image-wrapper ${className}`}>
        <div className="d-flex gap-3">
          <div className={!isSidebarCollapsed ? 'pro-diamond' : ''}>
            <img
              src={DEMO_USER_IMAGE}
              alt="Default profile"
              className="logo-filter"
              width={width}
              height={height}
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`mb-4 m-auto user-profile-image-wrapper ${className}`}>
      <div className="d-flex gap-3">
        <div className={!isSidebarCollapsed ? 'pro-diamond' : ''}>
          {!isSidebarCollapsed &&
          userData &&
          userData.role === 'employer' &&
          ((userData.plan != 1 && userData.plan != null) || userData.membership == true) ? (
            <div className="gem-diamond">
              <i className="fa-regular fa-gem"></i>
            </div>
          ) : null}
          <img
            src={previewImage}
            alt={company ? company.logo?.name || userData?.profile_image?.name : userData?.profile_image?.name}
            className="logo-filter"
            width={width}
            height={height}
          />
        </div>
        {!isSidebarCollapsed && !showUploadButton && (
          <a
            href="#"
            onClick={openProfileImageUploadModal}
            className="edit-img"
            style={{
              zIndex: 20,
            }}>
            <i className="fa-solid fa-pen"></i>{' '}
          </a>
        )}
        {!isSidebarCollapsed && showUploadButton && (
          <button
            className="btn-a primary-size-16 btn-bg-0055BA mt-5 mb-4 mobile-m-0 max-340"
            onClick={openProfileImageUploadModal}
            type="button">
            <i className="fa-solid fa-upload"></i> Upload A New Photo
          </button>
        )}
      </div>
      <ModalForm
        title={`Upload Your ${(company || userData?.company) ? 'Company Logo' : 'Profile Picture'}`}
        open={profileImageUploadModal}
        onCancel={closeProfileImageUploadModal}>
        <Form onFinish={onSubmitForm}>
          <Row gutter={15}>
            <Col md={24}>
              {/*@ts-ignore*/}
              <FilePond
                allowMultiple={false}
                acceptedFileTypes={['image/*']}
                server={{
                  url: axios.defaults.baseURL + '/file-management/files',
                  process: (fieldName, file, metadata, load, error, progress, abort) => {
                    if (!file.type.startsWith('image/')) {
                      notification.error({message: 'Only image files are allowed!'});
                      abort();
                      return;
                    }

                    const formData = new FormData();
                    formData.append(fieldName, file);

                    axios
                      .post('/file-management/files', formData, {
                        headers: {Authorization: axios.defaults.headers.common.Authorization},
                        onUploadProgress: e => progress(true, e.loaded, e.total ?? 0),
                      })
                      .then(res => {
                        load(res.data);
                        console.log(res, 'res');
                        setUploadedFile(res.data);
                      })
                      .catch(() => {
                        error('Upload failed');
                      });
                  },
                }}
                name="file"
              />
            </Col>
          </Row>
          <div className="modal-footer">
            <Button
              type={'primary'}
              size={'large'}
              className="cancel-btn"
              data-bs-dismiss="modal"
              onClick={closeProfileImageUploadModal}>
              Cancel
            </Button>
            <Button type={'primary'} size={'large'} htmlType="submit" className="update-btn" disabled={!uploadedFile}>
              Update
            </Button>
          </div>
        </Form>
      </ModalForm>
    </div>
  );
}
