import React, {useContext, useState} from 'react';
import {Button, Space, message} from 'antd';
import Image from 'next/image';
import Link from 'next/link';
import {signOut} from 'next-auth/react';

import AuthContext from '@/Context/AuthContext';
import {removeStorageData} from '@/lib/session';

interface AuthUserMenuProps {
  onClick?: () => void;
}

const AuthUserMenu = ({onClick}: AuthUserMenuProps) => {
  const {user, logout} = useContext(AuthContext);
  const [goingOut, setGoingOut] = useState(false);
  const [generatingSitemap, setGeneratingSitemap] = useState(false);

  const handleLogout = () => {
    removeStorageData();
    setGoingOut(true);
    signOut({redirect: false}).then();
    logout().then(() => {
      window.location.href = '/auth/login';
      setGoingOut(false);
    });
  };

  const handleGenerateSitemap = async () => {
    try {
      setGeneratingSitemap(true);
      const response = await fetch('/api/regenerate-sitemap', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        message.success('Sitemap regenerated successfully');
      } else {
        message.error('Failed to regenerate sitemap');
      }
    } catch (error) {
      message.error('An error occurred while regenerating sitemap');
      console.error('Sitemap generation error:', error);
    } finally {
      setGeneratingSitemap(false);
    }
  };

  if (!user) {
    return null;
  }

  let rolePath = user.role;

  if (user.role === 'employee') {
    rolePath = 'employees';
  }

  return (
    <div className="candidate-box">
      <Space>
        <img
          // src={user?.profile_image ? process.env.NEXT_PUBLIC_IMAGE_URL + user?.profile_image?.source?.replace("/storage/", '') : `${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-1.png`}
          src={
            user?.profile_image
              ? user?.profile_image?.source
              : `${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-1.png`
          }
          alt={user?.profile_image?.name || user?.name || 'Employee profile image'}
          width={32}
          height={32}
        />
        <div>
          {user?.role === 'employee' ? (
            <Link href={`/candidate-profile/${user?.slug}`} target="_blank">
              <span className="username">{user.name}</span>{' '}
            </Link>
          ) : (
            <span className="username">{user.name}</span>
          )}
          <span className={'role'}>{user.role === 'employee' ? 'Candidate' : user.role}</span> <br />
          <small className={'email'}>{user.email}</small>
        </div>
      </Space>
      {/* {user.role === 'employee' && (
        <div className="interview">{user.job_status && user.job_status.replaceAll('_', ' ')}</div>
      )} */}
      {(user.signup_completed || user?.role !== '') && (
        <ul className="can-list">
          <li>
            <Link className="dropdown-item" href={['admin', 'employer', 'employees'].some(role => window.location.href.includes(role)) ? `/` :`/${rolePath}/dashboard`}>
              <i className="fa-solid fa-check"></i> {['admin', 'employer', 'employees'].some(role => window.location.href.includes(role)) ? "Go to Website" : "Dashboard"}
            </Link>
          </li>
          <li>
            <Link href={`/${rolePath}/settings`} onClick={onClick}>
              <i className="fa-solid fa-gear"></i> Settings
            </Link>
          </li>
          {user.role !== 'admin' && <li>
            <Link href={`/${rolePath}/notifications`} onClick={onClick}>
              <i className="fa-regular fa-bell"></i> Notifications
            </Link>
          </li>}
          {/* {user.role === 'admin' && (
            <li>
              <a href="#" onClick={handleGenerateSitemap}>
                <i className="fa-solid fa-sitemap"></i> Regenerate Sitemap
              </a>
            </li>
          )} */}
          <li className="log-out"></li>
        </ul>
      )}
      <Button
        loading={goingOut}
        type={'link'}
        danger
        icon={<i className="fa-solid fa-right-from-bracket"></i>}
        onClick={() => handleLogout()}>
        Logout
      </Button>
      {user.role === 'admin' && (
        <Button
          loading={generatingSitemap}
          type={'link'}
          icon={<i className="fa-solid fa-sitemap"></i>}
          onClick={handleGenerateSitemap}
          style={{ marginTop: '8px' }}>
          Regenerate Sitemap
        </Button>
      )}
    </div>
  );
};

export default AuthUserMenu;
