import React from "react";
import "react-quill/dist/quill.snow.css";
import dynamic from 'next/dynamic';

const ReactQuill = dynamic(() => import('react-quill'), { ssr: false });

export const modules = {
   toolbar: [
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
      [{ 'font': [] }],
      [{ 'list': 'ordered' }, { 'list': 'bullet' }],
      ['bold', 'italic', 'underline'],
      ['link', 'image'],
      [{ 'align': [] }],
      ['clean']
   ],
};

interface HtmlEditorProps {
   name?: string;
   value?: string;
   onChange: (name?: string, html?: string) => void;
}

export const QuillHtmlEditor = ({ name, value, onChange }: HtmlEditorProps) => {
   const handleChange = (content: string) => {
      console.log(content, 'content inside editor')
      onChange(name, content);
   };

   return (
      <div className="html-editor-wrapper">
         <ReactQuill
            value={value || ""}
            onChange={handleChange}
            modules={modules}
            className="editor-content"
         />
      </div>
   );
};
