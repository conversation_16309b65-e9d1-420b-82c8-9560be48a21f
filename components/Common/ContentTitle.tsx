import React from 'react';
import {Col, Row, Space} from 'antd';

interface ContentTitleProps {
  title: string;
  tools?: React.ReactNode;
  total?: number;
}

const ContentTitle = ({title, tools, total}: ContentTitleProps) => {
  return (
    <Row justify={'space-between'}>
      <Col
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '10px',
        }}>
        <h1>{title}</h1>
        {total ? (
          <p
            style={{
              padding: '2px 8px',
              justifyContent: 'center',
              alignItems: 'center',
              gap: '8px',
              borderRadius: '9999px',
              border: '0.5px solid rgba(0, 85, 186, 0.08)',
              background: '#cfe5ff',
              color: 'var(--Grayscale-08, #2c2c2c)',
              fontSize: '12px',
              fontWeight: 600,
              lineHeight: '140%',
            }}>
            {total}
          </p>
        ) : null}
      </Col>
      <Col>
        <Space>{tools}</Space>
      </Col>
    </Row>
  );
};

export default ContentTitle;
