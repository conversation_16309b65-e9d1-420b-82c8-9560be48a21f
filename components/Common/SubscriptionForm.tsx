import React, { useState, useCallback } from 'react';
import { subscribe } from '../../lib/frontendapi';
import { notification, Button } from 'antd';

const SubscriptionForm: React.FC = () => {
  const [email, setEmail] = useState('');
  const [processing, setProcessing] = useState(false);

  const handleEmailChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
  }, []);

  const isFakeFillerEmail = useCallback((email: string) => {
    const domain = email.split('@')[1];
    return domain === 'mailinator.com';
  }, []);

  const handleSubscribe = useCallback(async () => {
    try {
      if (!email) {
        notification.error({ message: 'Please fill in the email field.' });
        return;
      }

      if (isFakeFillerEmail(email)) {
        notification.error({ message: 'Please provide a valid email address.' });
        return;
      }

      setProcessing(true);

      const response = await subscribe({ email });
      if (response.status) {
        notification.success({ message: response.message });
      } else {
        notification.error({ message: response.message });
      }

      setProcessing(false);
      setEmail('');
    } catch (error) {
      notification.error({ message: 'An error occurred while subscribing. Please try again later.' });
      setProcessing(false);
    }
  }, [email, isFakeFillerEmail]);

  return (
    <section className="stay-updated sp-80">
      <div className="container">
        <div className="row">
          <div className="col-sm-6">
            <div className="updated-stay">
              <h2 aria-level={2} role="heading">Stay Updated</h2>
              <p className="font-26">
                Get latest HR News, Trends, Facts and Advices. We will deliver all of it directly to your inbox.
              </p>
            </div>
          </div>
          <div className="col-sm-6">
            <div className="email-form mt-4 tab-m-0">
              <div className="row">
                <div className="col-lg-8 mt-2">
                  <input
                    type="email"
                    placeholder="Enter your email"
                    className="form-white-22-font"
                    value={email}
                    onChange={handleEmailChange}
                  />
                </div>
                <div className="col-lg-4 mt-2">
                  <Button
                    className="btn-a primary-size-22 border-0 tab-w-100"
                    style={{ height: 'auto', color: '#0055ba' }}
                    onClick={handleSubscribe}
                    loading={processing}
                  >
                    {processing ? 'Please wait...' : 'Subscribe'}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default React.memo(SubscriptionForm);
