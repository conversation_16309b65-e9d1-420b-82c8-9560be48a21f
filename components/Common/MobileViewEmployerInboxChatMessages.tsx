import React, { useState, useEffect, useRef } from 'react';
import { getCurrentUserData } from "../../lib/session";
import { getAllEmployerSingleUserMessages, getAllReceiverUserMessages, sendMessage, updateArchivedMessages, getAllEmployerReceiverMessages, getAllInterviews, getAllEmployerReceiverArchivedMessages } from '../../lib/frontendapi';
import { useForm } from 'react-hook-form';
import Link from 'next/link';
import 'react-toastify/dist/ReactToastify.css';
import $ from 'jquery';
import SuccessToast from "../Common/showSuccessTostrMessage";
import ErrorToast from "../Common/showErrorTostrMessage";
import Image from 'next/image';

export default function MobileViewInboxMessagesListing({ props }: any) {
    let candidate_user_id = props.userId;
    const chatWindowRef: any = useRef(null);
    const {
        register,
        setError,
        formState: { errors },
        handleSubmit,
    } = useForm({
        mode: "onChange",
    });
    const [messageDesc, setMessageDesc] = useState('');
    const current_user_data: any = getCurrentUserData();
    const [messagesData, setMessagesData]: any = useState([]);
    const [allInboxMessagesData, setAllInboxMessagesData]: any = useState([]);
    const [chatMessagesData, setChatMessagesData]: any = useState([]);
    const [archivedMessagesData, setArchivedMessagesData]: any = useState([]);
    const [interviews, setInterviews] = useState([]);
    const [attachmentFile, setAttachmentFile] = useState('');
    const [previewCompanyLogo, setPreviewCompanyLogo] = useState('');
    const [showPopupunsave, setShowPopupunsave] = useState(false);
    const [showPopupunsave1, setShowPopupunsave1] = useState(false);
    const [showmessage, setShowmessage] = useState('');
    useEffect(() => {
        const current_user_data: any = getCurrentUserData();
        getAllEmployerSingleUserMessages(candidate_user_id)
            .then(res => {
                if (res.status == true) {
                    setMessagesData(res.data);
                } else {
                    setMessagesData([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
        setInterval(async () => {
            refreshChatMessages();
        }, 5000);
        getAllEmployerReceiverMessages(current_user_data.id)
            .then(res => {
                if (res.status == true) {
                    setAllInboxMessagesData(res.data);
                } else {
                    setAllInboxMessagesData([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
        const data = {
            user_id: current_user_data.id
        }
        getAllInterviews(data)
            .then(res => {
                if (res.status == true) {
                    setInterviews(res.data);
                } else {
                    setInterviews([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
        getAllEmployerReceiverArchivedMessages(current_user_data.id)
            .then(res => {
                if (res.status == true) {
                    setArchivedMessagesData(res.data);
                } else {
                    setArchivedMessagesData([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
    }, [candidate_user_id]);
    const refreshChatMessages = () => {
        const messages_chat_section = document.getElementById('messages_chat_section');
        let shouldScroll = 0;
        if (messages_chat_section) {
            let shouldScroll = messages_chat_section.scrollTop + messages_chat_section.clientHeight === messages_chat_section.scrollHeight;
        }
        if (!shouldScroll) {
            if (messages_chat_section) {
                messages_chat_section.scrollTop = messages_chat_section.scrollHeight;
            }
        }
        const data = {
            sender_id: current_user_data.id,
            receiver_id: candidate_user_id
        }
        getAllReceiverUserMessages(data)
            .then(res => {
                if (res.status == true) {
                    setChatMessagesData(res.data);
                } else {
                    setChatMessagesData([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
    }
    const onEnterPress = (e: any) => {
        if (e.which === 13 && e.shiftKey == false) {
            e.preventDefault();
            submitMessageForm(e);
        }
    }
    const submitMessageForm = (e: any) => {
        e.preventDefault();
        const messages_chat_section = document.getElementById('messages_chat_section');
        let shouldScroll = 0;
        if (messages_chat_section) {
            let shouldScroll = messages_chat_section.scrollTop + messages_chat_section.clientHeight === messages_chat_section.scrollHeight;
        }
        if (!shouldScroll) {
            if (messages_chat_section) {
                messages_chat_section.scrollTop = messages_chat_section.scrollHeight;
            }
        }
        $(".message_send_filed").val('');
        setMessageDesc('');
        //$(".attach_ment_file").val('');
        const current_user_data: any = getCurrentUserData();
        let applicants_id = $(".hd_applicants_id").val();
        let candidate_id = candidate_user_id;
        let job_id = $(".hd_job_id").val();
        let current_user_id = current_user_data.id;
        const datas = {
            applicants_id: applicants_id,
            candidate_id: candidate_id,
            job_id: job_id,
            current_user_id: current_user_id,
            message_title: null,
            message_description: messageDesc,
            message_type: 'applyJob',
            message_status: 'unread',
        }
        sendMessage(datas, attachmentFile[0])
            .then(res => {
                if (res.status == true) {
                } else {
                    setShowmessage(res.message);
                    setShowPopupunsave1(true);
                    setTimeout(() => {
                        setShowPopupunsave1(false)
                    }, 10000)
                    if (res.errors) {
                        setShowmessage(res.errors.attachment_path[0]);
                        setShowPopupunsave1(true);
                        setTimeout(() => {
                            setShowPopupunsave1(false)
                        }, 10000)
                    }
                }
            })
            .catch(err => {
                setShowmessage(err);
                setShowPopupunsave1(true);
                setTimeout(() => {
                    setShowPopupunsave1(false)
                }, 10000)
            });
    }
    const handleClickArchived = () => {
        const data = {
            sender_id: current_user_data.id,
            receiver_id: candidate_user_id
        }
        updateArchivedMessages(data)
            .then(res => {
                if (res.status == true) {
                    setShowmessage(res.message);
                    setShowPopupunsave(true);
                    setTimeout(() => {
                        setShowPopupunsave(false)
                    }, 3000)
                    getAllEmployerReceiverMessages(current_user_data.id)
                        .then(res => {
                            if (res.status == true) {
                                setAllInboxMessagesData(res.data);
                            } else {
                                setAllInboxMessagesData([]);
                            }
                        })
                        .catch(err => {
                            console.log(err);
                        });
                    const data = {
                        user_id: current_user_data.id
                    }
                    getAllInterviews(data)
                        .then(res => {
                            if (res.status == true) {
                                setInterviews(res.data);
                            } else {
                                setInterviews([]);
                            }
                        })
                        .catch(err => {
                            console.log(err);
                        });
                    getAllEmployerReceiverArchivedMessages(current_user_data.id)
                        .then(res => {
                            if (res.status == true) {
                                setArchivedMessagesData(res.data);
                            } else {
                                setArchivedMessagesData([]);
                            }
                        })
                        .catch(err => {
                            console.log(err);
                        });
                } else {
                    setShowmessage(res.message);
                    setShowPopupunsave1(true);
                    setTimeout(() => {
                        setShowPopupunsave1(false)
                    }, 3000)
                }
            })
            .catch(err => {
                setShowmessage(err);
                setShowPopupunsave1(true);
                setTimeout(() => {
                    setShowPopupunsave1(false)
                }, 3000)
            });
    }
    const handleImageChange = (event: any) => {
        if (event.target.files && event.target.files[0]) {
            setAttachmentFile(event.target.files);
        }
    };
    return (
        <>
            <section className='single-jobs-back bg-fff'>
                <ul className='list-loc m-m-0 mt-4'>
                    <li className='active'>
                        <Link href="/employer/messages">Inbox <span className="tab-span-sa c-0070F5">1 </span></Link>
                    </li>
                    <li>
                        <Link href="/employer/messages/interviews">Interviews <span className="tab-span-sa"> 0</span></Link>
                    </li>
                    <li>
                        <Link href="/employer/messages/archived">
                            Archived <span className="tab-span-sa"> 0</span>
                        </Link>
                    </li>
                </ul>
                <div className='bg-fff p-2'>
                    <div className="row mt-3">
                        <div className="col-5 pr-0">
                            <p className='c-4D4D4D f-22'><i className="fa-solid fa-arrow-left"></i></p>
                        </div>
                        <div className="col-7 text-right">
                            <ul className='repot'>
                                <li><img src="/images/flag.png" alt='flag.png' className='fla' /> Report</li>
                                <li><img src="/images/archive.png" alt='archive.png' className='fla' /> Archive</li>
                            </ul>
                        </div>
                    </div>
                    <p className='c-191919 w-700 f-22 text-center mb-2'>Name of Position</p>
                    <p className='c-0070F5 w-700 f-22 text-center'>Name of Position</p>
                    <hr />
                    <div className='scrollable'>
                        <div className='card-filter mt-3 mb-4'>
                            <div className='row mt-2    '>
                                <div className='col-7'>
                                    <p className='f-13 c-747474 w-400 open-sans mt-2'>You applied to this position on March 21, 2023.</p>
                                </div>
                                <div className='col-5'>
                                    <button className="view-jobs">View Jobs</button>
                                </div>
                            </div>
                        </div>
                        <p className='f-13 c-2C2C2C w-600 mb-1'>
                            <img src="/images/user-blog.png" alt="Avatars-5" className="user-short" /> Recruiter/Company Name</p>
                        <p className='text-right f-13 c-4D4D4D w-400 mb-0 mt-2  '>March 21, 1:20PM</p>
                        <br />
                        <p className='c-4D4D4D f-16 w-400  open-sans '>Lorem ipsum dolor sit amet consectetur. Mauris arcu gravida nibh diam. Imperdiet nec elementum elementum phasellus sit sit ultrices. In et maecenas nunc eget ac sapien. Morbi arcu scelerisque turpis adipiscing morbi convallis. Mauris at tincidunt rutrum lacus augue arcu amet ultrices volutpat. Vitae blandit et libero sed aenean elit tortor sagittis fames. Porta id libero dictumst nunc tempus...</p>
                        <br />
                        <div className='textarea-filter bg-fff mb-2'>
                            <textarea placeholder='Your message goes here...'></textarea>
                            <div className='row'>
                                <div className='col-2 pr-0'>
                                    <button className="view-jobs b-r-8"><i className="fa-solid fa-paperclip"></i></button>
                                </div>
                                <div className='col-10'>
                                    <button className='btn-a primary-size-16 b-r-4 btn-bg-0055BA mr-1 tab-w-100'>Send</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            {showPopupunsave &&
                <SuccessToast message={showmessage} />
            }
            {showPopupunsave1 &&
                <ErrorToast message={showmessage} />
            }
        </>
    )
}
