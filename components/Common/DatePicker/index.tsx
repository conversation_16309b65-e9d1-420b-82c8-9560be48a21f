import {DatePicker, DatePickerProps} from 'antd';
import styles from './style.module.css';
import Image from 'next/image';

interface DatePickerUiProps extends DatePickerProps {
  label?: string;
  isRequired?: boolean;
  type?: 'date' | 'month';
  bottomLabel?: boolean;
}

export const DatePickerUi = (props: DatePickerUiProps) => {
  const {label, type = 'month', isRequired, bottomLabel = true, ...rest} = props;
  const {MonthPicker} = DatePicker;
  return (
    <div className={styles.dropdown_container}>
      {label && (
        <label>
          {label}
          {isRequired && <span>*</span>}
        </label>
      )}
      <div>
        {type === 'month' ? (
          <MonthPicker
            {...rest}
            size="large"
            style={{
              padding: '15px 11px',
              width: '100%',
              ...rest.style,
            }}
            suffixIcon={<img src={'/icons/company/calendar.svg'} alt="calendar" height={16} width={16} />}
          />
        ) : (
          <DatePicker
            {...rest}
            size="large"
            style={{
              padding: '15px 11px',
              width: '100%',
              ...rest.style,
            }}
            suffixIcon={<img src={'/icons/company/calendar.svg'} alt="calendar" height={16} width={16} />}
          />
        )}
      </div>
      {bottomLabel && <div className={styles.container}></div>}
    </div>
  );
};
