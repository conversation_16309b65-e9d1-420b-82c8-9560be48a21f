import React, { useContext, useEffect, useState } from 'react';
import PopupModal from '../PopupModal';
import ApplyForJobModal from '@/components/Employees/Modals/ApplyForJobModal';
import JobAppliedSuccessModal from '@/components/Employees/Modals/JobAppliedSuccessModal';
import AuthContext from '@/Context/AuthContext';
import { applyJob, jobAppliedOrNot } from '@/lib/frontendapi';
import ErrorHandler from '@/lib/ErrorHandler';
import { getAllCountries } from '@/lib/adminapi';
import { notification } from 'antd';
import { getSingleOwnResume } from '@/lib/employeeapi';
import Jobs from '@/pages/admin/jobs';
import { useRouter } from 'next/router';

interface ApplyButtonProps {
  job: any;
  jobId?: number;
  className?: string;
}

export default function JobApplyButton({ job, jobId, className }: ApplyButtonProps) {
  const { user } = useContext(AuthContext);
  const [applyJobModal, setApplyJobModal] = useState(false);
  const [isApplied, setIsApplied] = useState(false);
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [allCountries, setCountry] = useState([]);
  const router = useRouter();
  const submitForm = () => {
    setIsApplied(true);
    setFormSubmitted(true);
    setApplyJobModal(false);
  };

  useEffect(() => {
    if (user && job) {
      console.log('user', user);
      console.log('job', job);

      if ('is_applied' in job) {
        setIsApplied(job.is_applied);
      } else {
        jobAppliedOrNot({
          userId: user.id,
          jobId: job.id,
        }).then(res => {
          if (res.status) {
            setIsApplied(res.data.isApplied);
          }
        });
      }
      getSingleOwnResumeData(user?.id);
    }
    getAllCountries()
      .then(res => setCountry(res || []))
      .catch(error => {
        ErrorHandler.showNotification(error);
      });
  }, [job, user]);

  const getSingleOwnResumeData = async (id: any) => {
    try {
      const res = await getSingleOwnResume(id);
      console.log(res);
    } catch (error) {
      ErrorHandler.showNotification(error);
    }
  };

  const handleAutoApplyJob = (jobId: any, userId: any, companyId: any) => {
    if (Number(user?.unlock_instant_apply) === 1) {
      autoSubmitForm(jobId, userId, companyId);
    }
  };

  const handleApplyWithoutLogin = () => {
    if (!user) {
      router.push('/auth/signup');
    }
  };

  const openJobApplyModal = () => {
    handleApplyWithoutLogin();
    if (user) {
      setApplyJobModal(true);
    }
  };

  const autoSubmitForm = (jobId: any, userId: any, companyId: any) => {
    handleApplyWithoutLogin();
    if (!companyId) {
      companyId = job.company.id;
    }
    const data = {
      company_id: companyId,
      jobpost_by_userid: job.user_id,
      user_id: user?.id,
      job_id: jobId,
      description: '',
      instant_apply: 1,
      name: user?.name,
      contact_no: user?.contact_no,
      gender: user?.gender,
      date_of_birth: user?.date_of_birth,
      where_currently_based: user?.where_currently_based,
    };

    applyJob(data)
      .then(res => {
        if (res.status) {
          setFormSubmitted(true);
          setIsApplied(true);
        }
      })
      .catch(err => {
        console.log(err);
        ErrorHandler.showNotification(err);
      });
  };

  return (
    <>
      {isApplied || job.is_appied ? (
        <button className={`btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp addCoverWidth ${className}`} disabled>
          Applied
        </button>
      ) : Number(user?.unlock_instant_apply) === 1 && Number(user?.profile_complete_percentage) == 100 ? (
        <button
          className={`btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp addCoverWidth ${className}`}
          onClick={() => handleAutoApplyJob(job.id, job.user_id, job.company_id)}>
          {' '}
          Apply Now
        </button>
      ) : (
        <button
          className={`btn-a primary-size-16 btn-bg-0055BA w-100 tab-add-sp addCoverWidth ${className}`}
          onClick={openJobApplyModal}>
          {' '}
          Apply Now
        </button>
      )}
      <PopupModal
        show={applyJobModal}
        handleClose={() => setApplyJobModal(false)}
        customclass={'text-start modal-lg'}
        closebtnclass={'close-x  bg-0055BA border-design close-b-des'}
        closebtnicon={'icon'}>
        <ApplyForJobModal
          onClose={() => setApplyJobModal(false)}
          job={job}
          allCountries={allCountries}
          onSubmit={submitForm}
        />
      </PopupModal>

      <PopupModal
        show={formSubmitted}
        handleClose={() => setFormSubmitted(false)}
        customclass={'modal-lg  header-remove body-sp-0 '}>
        <JobAppliedSuccessModal onClose={() => setFormSubmitted(false)} />
      </PopupModal>
    </>
  );
}
