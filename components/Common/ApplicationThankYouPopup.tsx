import React, { useState, useEffect } from 'react';
import { useRouter } from "next/router";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getCurrentUserData } from "../../lib/session";
import PopupModal from '../../components/Common/PopupModal';
import { applyJob, getSingleUserDetails } from '../../lib/frontendapi';
import Link from 'next/link';
import Image from 'next/image';


export default function ApplyJobPopup({ show, setModalConfirm3 }: any) {
    const modalConfirmClose3 = () => {
        setModalConfirm3(false);
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }
    return (
        <PopupModal show={show} handleClose={modalConfirmClose3} customclass={"modal-lg  header-remove body-sp-0 "}>
            <div className="popup-body">
                <div className="row">
                    <div className="col-sm-10"> </div>
                    <div className="col-sm-2 text-right">
                        <button
                            type="button"
                            className="close-b-des close-x  bg-0055BA border-design"
                            data-bs-dismiss="modal"
                            aria-label="Close"
                            onClick={modalConfirmClose3}
                        >
                            <i className="fa-solid fa-xmark"></i>
                        </button>
                    </div>
                </div>
                <div className="text-center sp-50">
                    <img
                        src={
                            process.env.NEXT_PUBLIC_BASE_URL + "images/check-blue.png"
                        }
                        alt="check-blue"
                        className="w-120"
                    />
                    <h2 className="f-31">
                        Application <span className="span-color">Submitted</span>
                    </h2>
                    <p className="f-18">
                        Go to{" "}
                        <Link href="/employees/applications" className="c-0070F5 w-700  ">
                            {" "}
                            My Applications
                        </Link>{" "}
                        to view your application status.
                    </p>
                </div>
            </div>
        </PopupModal>
    )
}
