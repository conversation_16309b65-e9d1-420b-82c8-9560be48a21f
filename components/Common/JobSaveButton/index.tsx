import AuthContext from '@/Context/AuthContext';
import {searchJobs} from '@/lib/ApiAdapter';
import <PERSON>rror<PERSON>and<PERSON> from '@/lib/ErrorHandler';
import {toggleFavoriteJob} from '@/lib/frontendapi';
import {notification} from 'antd';
import axios from 'axios';
import React, {useCallback, useContext, useEffect, useState} from 'react';
import {toast} from 'react-toastify';

interface SaveButtonProps {
  job: any;
}

export default function JobSaveButton({job}: SaveButtonProps) {
  const {user} = useContext(AuthContext);
  const [savedJob, setSavedJob] = useState(job?.is_saved);

  const handleJobSaved = useCallback(() => {
    const cancelTokenSource = axios.CancelToken.source();
    const data = {
      user_id: user?.id,
      job_id: job.id,
    };
    searchJobs(data, cancelTokenSource)
      .then(res => {
        if (res.favorites) {
          if (res.favorites.includes(job.id)) {
            setSavedJob(true);
          }
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  }, [user, job, setSavedJob]);

  useEffect(() => {
    if (job) {
      handleJobSaved();
    }
  }, [job, handleJobSaved]);

  const unSaveJob = (id: any, job_id: any, company_id: any) => {
    const data = {job_id: job_id};
    toggleFavoriteJob(data)
      .then((res: any) => {
        notification.success({message: 'Job unsaved successfully.'});
        setSavedJob(false);
      })
      .catch((err: any) => {
        ErrorHandler.showNotification(err);
      });
  };

  const saveJob = (job_id: any, company_id: any) => {
    if (!user) {
      notification.info({
        message: 'Please register your account.',
      });
    } else {
      const data = {
        user_id: user?.id,
        company_id: company_id,
        job_id: job_id,
      };
      toggleFavoriteJob(data)
        .then(res => {
          if (res.status) {
            setSavedJob(true);
            notification.success({message: 'Job saved successfully.'});
          } else {
            toast.info(res.message);
          }
        })
        .catch(err => {
          toast.error(err.message);
        });
    }
  };

  return (
    <div className="job-apply-button-wrapper">
      {savedJob ? (
        <button
          className="download mt-3 w-100 addCoverWidth"
          onClick={() => unSaveJob(job.saved_id, job.id, job.company_id)}>
          <i className="fa-solid fa-bookmark"></i> &nbsp; Saved Job
        </button>
      ) : (
        <button className="download mt-3 w-100 addCoverWidth" onClick={() => saveJob(job.id, job.company_id)}>
          <i className="fa-regular fa-bookmark"></i> &nbsp; Save Job
        </button>
      )}
    </div>
  );
}
