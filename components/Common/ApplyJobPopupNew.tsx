import React, { useState, useEffect } from 'react';
import { useRouter } from "next/router";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getCurrentUserData } from "../../lib/session";
import PopupModal from '../../components/Common/PopupModal';
import { applyJob, getSingleUserDetails, getAllCountries } from '../../lib/frontendapi';
import { HtmlEditor } from "./HtmlEditor";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";

export default function ApplyJobPopupNew({
  show,
  selectedJobId,
  selecteduserId,
  selectedcompany,
  selectCompanyId,
}: any) {

  interface User {
    name: string;
    email: string;
    contact_no: string;
    date_of_birth: string;
    bio: string;
    gender: string;
    years_of_experience: string;
    current_salary: string;
    desired_salary: string;
    where_currently_based: string;
    current_position: string;
    description: string;
    resume_pdf_path: string;
    default_resume: string;
    unlock_instant_apply: string;
    country_name: string;
    resumedate: string;
  }

  const [name, SetName] = useState('');
  const [contact_no, SetContactNo] = useState('');
  const [gender, SetGender] = useState('');
  const [date_of_birth, SetDateOFBirth] = useState('');
  const [currently_location, SetCurrentlyLocation] = useState('');
  const [selectedResume, setSelectedResume] = useState(null);
  const [selectedCover, setSelectedCover] = useState(null);
  const [errorMessage, setErrorMessage] = useState('');
  const [covererrorMessage, setcoverErrorMessage] = useState('');
  const [selectedChoice, setSelectedChoice] = useState('');
  const [descriptionError, setDescriptionError] = useState<string>("");
  const [localSelectedJobId, setLocalSelectedJobId] = useState('');
  const [localSelecteduserId, setLocalSelecteduserId] = useState('');
  const [localSelectedcompany, setLocalSelectedcompany] = useState('');
  const [localSelectCompanyId, setLocalSelectCompanyId] = useState('');
  const [modalConfirm3, setModalConfirm3] = useState(false);
  const currentDate = new Date();

  const MAX_DESCRIPTION_WORDS = 250;

  const [allcountries, setCountry] = useState([]);
  const [selectedPath, setSelectedPath] = useState('');

  const [user, SetUserData] = useState<User>({
    name: "",
    email: "",
    contact_no: "",
    date_of_birth: "",
    bio: "",
    gender: "",
    years_of_experience: "",
    current_salary: "",
    desired_salary: "",
    where_currently_based: "",
    current_position: "",
    description: "",
    resume_pdf_path: '',
    default_resume: '',
    unlock_instant_apply: '',
    country_name: '',
    resumedate: ''
  });



  const EditChange = (name: any, value: any) => {
    SetUserData((prevState) => {
      return {
        ...prevState,
        [name]: value,
      };
    });
  };



  useEffect(() => {

    setLocalSelectedJobId(selectedJobId);
    setLocalSelecteduserId(selecteduserId);
    setLocalSelectedcompany(selectedcompany);
    setLocalSelectCompanyId(selectCompanyId);

    const current_user_data: any = getCurrentUserData();
    getSingleUserDetails(current_user_data.id)
      .then(res => {
        if (res.status === true) {

          SetUserData(res.user);

          SetName(res.user.name);
          SetContactNo(res.user.contact_no);
          SetGender(res.user.gender);
          SetDateOFBirth(res.user.date_of_birth);
          SetCurrentlyLocation(res.user.where_currently_based);

          if (res.user.resume_path != 'null') {
            setSelectedChoice("below");
          }

        } else {
          SetUserData({
            name: "",
            email: "",
            contact_no: "",
            date_of_birth: "",
            bio: "",
            gender: "",
            years_of_experience: "",
            current_salary: "",
            desired_salary: "",
            where_currently_based: "",
            current_position: "",
            description: "",
            resume_pdf_path: '',
            default_resume: '',
            unlock_instant_apply: '',
            country_name: '',
            resumedate: ''
          });
        }
      })
      .catch(err => {
        console.log(err);
      });
    getAllCountries()
      .then(res => {
        if (res) {
          setCountry(res);
        } else {
          setCountry([]);
        }
      })

  }, [selectedJobId, selecteduserId, selectedcompany, selectCompanyId]);


  const handleFileChange = (event: any) => {
    const file = event.target.files[0];
    setSelectedResume(file);
    if (file) {
      const allowedFormats = ['pdf', 'doc', 'docx'];
      const fileExtension = file.name.split('.').pop().toLowerCase();
      if (!allowedFormats.includes(fileExtension)) {
        setErrorMessage('Invalid file. Only PDF or DOC files are allowed.');
        setSelectedResume(null);

      } else {
        setErrorMessage('');
        setSelectedChoice("above");
      }
    }
  };

  const handleCoverChange = (event: any) => {
    const file = event.target.files[0];
    setSelectedCover(file);
    if (file) {
      const allowedFormats = ['pdf', 'doc', 'docx'];
      const fileExtension = file.name.split('.').pop().toLowerCase();
      if (!allowedFormats.includes(fileExtension)) {
        setcoverErrorMessage('Invalid file. Only PDF or DOC files are allowed.');
        setSelectedCover(null);
      } else {
        setcoverErrorMessage('');

      }
    }
  };

  const submitForm = (event: any) => {
    event.preventDefault();
    const current_user_data: any = getCurrentUserData();

    const description = user.description;
    if (!description || description.trim() === '') {
      toast.error('Please enter a description.', {
        position: toast.POSITION.TOP_RIGHT,
        closeButton: true,
        hideProgressBar: false,
        style: {
          background: '#ffe6e6',
          color: '#d04e4f',
          maxWidth: "300px",
          padding: 0,
          margin: 0,
          fontSize: "15px",
          fontFamily: "var(--opensans-font)",
          paddingRight: "10px",
          paddingLeft: "10px",
        },
        progressStyle: {
          background: '#ffe6e6',
        },
        icon: () => <i className="fa-solid fa-circle-xmark" style={{ color: "#d04e4f", fontSize: "16px", }}></i>,
      });
      return;
    }

    const descriptionWords = user.description.split(/\s+/).filter(Boolean).length;
    if (descriptionWords > MAX_DESCRIPTION_WORDS) {
      setDescriptionError(`Description should not exceed ${MAX_DESCRIPTION_WORDS} words.`);
      return;
    } else {
      setDescriptionError("");
    }

    const data = {
      company_id: localSelectCompanyId,
      jobpost_by_userid: localSelecteduserId,
      user_id: current_user_data.id,
      job_id: localSelectedJobId,
      description: user.description,
      resume_path: selectedResume,//resume_path || user.resume_pdf_path ,
      cover_letter: selectedCover,
      choice: selectedChoice,
      instant_apply: 0,
      name: name ? name : user.name,
      contact_no: contact_no ? name : user.contact_no,
      gender: gender ? gender : user.gender,
      date_of_birth: date_of_birth ? date_of_birth : user.date_of_birth,
      where_currently_based: currently_location ? currently_location : user.where_currently_based,
    }

    console.log(data);
    //console.log(data)
    applyJob(data)
      .then((res) => {
        if (res.status) {
          setTimeout(() => {

          }, 100);
          setTimeout(() => {
            modalConfirmClose2();
            //window.location.reload();
            resetForm();




          }, 100);

        } else {
          if (res.error === 'job_already_applied') {
            toast.error('You have already applied for this job', {
              position: toast.POSITION.TOP_RIGHT,
              closeButton: true,
              hideProgressBar: false,
              style: {
                background: '#ffe6e6',
                color: '#d04e4f',
                maxWidth: "300px",
                padding: 0,
                margin: 0,
                fontSize: "15px",
                fontFamily: "var(--opensans-font)",
                paddingRight: "10px",
                paddingLeft: "10px",
              },
              progressStyle: {
                background: '#ffe6e6',
              },
              icon: () => <i className="fa-solid fa-circle-xmark" style={{ color: "#d04e4f", fontSize: "16px", }}></i>,
            });
          } else {
            toast.error('An error occurred while applying for the job.', {
              position: toast.POSITION.TOP_RIGHT,
              closeButton: true,
              hideProgressBar: false,
              style: {
                background: '#ffe6e6',
                color: '#d04e4f',
                maxWidth: "300px",
                padding: 0,
                margin: 0,
                fontSize: "15px",
                fontFamily: "var(--opensans-font)",
                paddingRight: "10px",
                paddingLeft: "10px",
              },
              progressStyle: {
                background: '#ffe6e6',
              },
              icon: () => <i className="fa-solid fa-circle-xmark" style={{ color: "#d04e4f", fontSize: "16px", }}></i>,
            });
          }
        }
      })
      .catch((err) => {
        toast.error('An error occurred while applying for the job.', {
          position: toast.POSITION.TOP_RIGHT,
          closeButton: true,
          hideProgressBar: false,
          style: {
            background: '#ffe6e6',
            color: '#d04e4f',
            maxWidth: "300px",
            padding: 0,
            margin: 0,
            fontSize: "15px",
            fontFamily: "var(--opensans-font)",
            paddingRight: "10px",
            paddingLeft: "10px",
          },
          progressStyle: {
            background: '#ffe6e6',
          },
          icon: () => <i className="fa-solid fa-circle-xmark" style={{ color: "#d04e4f", fontSize: "16px", }}></i>,
        });
      });
  };

  const resetForm = () => {
    SetUserData({ ...user, description: '' });
    setSelectedResume(null);
    setSelectedCover(null);

  };

  const modalConfirmClose2 = () => {

  }

  const handleCancel = () => {

  };

  const modalConfirmClose3 = () => {
    setModalConfirm3(false);
  }

  return (
    <>
      <PopupModal
        show={show}
        handleClose={modalConfirmClose2}
        customclass={"add_company_signup_popup modal-lg body-sp-0 "}
        closebtnclass={"close-x  bg-0055BA border-design close-b-des"}
        closebtnicon={"icon"}
      >
        <div className="head-box">
          <div className="row">
            <div className="col-sm-10">
              <p className="f-26 mb-2 mt-2"> Apply to {selectedcompany}</p>
              <p className="f-16">
                {" "}
                Enter your basic information & get apply.
              </p>
            </div>
            <div className="col-sm-2 text-right">
              <button
                type="button"
                className="close-x  bg-0055BA border-design close-b-des"
                data-bs-dismiss="modal"
                aria-label="Close"
              >
                <i
                  className="fa-solid fa-xmark"
                  onClick={modalConfirmClose2}
                ></i>
              </button>
            </div>
          </div>
        </div>
        <div className="popup-body scroll-pop-h">


          <form className="form-experience-fieild" onSubmit={submitForm}>
            <div className="row ">
              <div className="col-sm-7 text-right">
                <p className="f-22 mt-2">Upload your recent Resume/CV:</p>
              </div>
              <div className="col-sm-5">
                <div className="uploade-btn">
                  <input
                    type="file"
                    name="resume"
                    onChange={handleFileChange}
                  />
                  <button className="download ">
                    <i className="fa-solid fa-upload"></i> Upload Resume
                  </button>
                  {errorMessage && (
                    <div className="text-danger mt-2">{errorMessage}</div>
                  )}
                </div>
              </div>
            </div>
            {selectedResume && (
              <div className="w-box bg-fff mt-3 mb-3 p-3">
                <div className="row">
                  <div className="col-sm-8">
                    <p className="f-18 w-600 mb-2">{user.name}-Resume</p>
                    <p className="f-16 c-999999">
                      Uploaded on{" "}
                      {currentDate.toLocaleString("en-US", {
                        month: 'short',
                        day: 'numeric',
                      })}
                    </p>
                  </div>
                  <div className="col-sm-4 text-right">
                    <span
                      className={
                        selectedChoice == "above" ? "default" : "selected"
                      } style={{ cursor: 'pointer' }}
                      onClick={() => {
                        setSelectedChoice("above");
                        // setSelectedPath("path for the above choice");
                      }}
                    >
                      Choose

                      { }
                    </span>
                    &nbsp;&nbsp;
                  </div>
                </div>
              </div>
            )}

            {user.resume_pdf_path && Number(user.default_resume) === 1 ? (
              <div className="w-box bg-fff mt-3 mb-3 p-3">
                <div className="row">
                  <div className="col-sm-8">
                    <p className="f-18 w-600 mb-2">{user.name}-Resume</p>
                    <p className="f-16 c-999999">
                      Uploaded on{" "}

                      {new Date(user.resumedate).toLocaleString('en-US', {
                        month: 'short',
                        day: 'numeric',

                      })}
                    </p>
                  </div>
                  <div className="col-sm-4 text-right">
                    <span
                      className={
                        selectedChoice == "below" ? "default" : "selected"
                      } style={{ cursor: 'pointer' }}
                      onClick={() => {
                        setSelectedChoice("below");
                        setSelectedPath("path for the below choice");
                      }}
                    >
                      Choose
                    </span>
                    &nbsp;&nbsp;
                  </div>
                </div>
              </div>
            ) : null}



            <div className="row">
              <div className="col-sm-6">
                <label>Your Name*</label>
                <input
                  type="text"
                  placeholder="Alan Moore"
                  className="fild-des"
                  name="name"
                  value={name}
                  onChange={(e) => SetName(e.target.value)}

                />
              </div>
              <div className="col-sm-6">
                <label>Email ID*</label>
                <input
                  type="email"
                  placeholder="<EMAIL>"
                  className="fild-des filed_disabled"
                  //value={user.email}
                  defaultValue={user.email}
                  disabled
                />
              </div>
            </div>

            <div className="row">
              <div className="col-sm-6">
                <label>Contact Number*</label>

                <PhoneInput
                  country={"us"}
                  value={contact_no}
                  inputClass="fild-des-contact"
                  onChange={contactNumber => SetContactNo(contactNumber)}
                />
              </div>
              <div className="col-sm-6">
                <label>Gender</label>
                <select
                  className="fild-des"
                  name="gender"
                  value={gender}
                  onChange={(e) => SetGender(e.target.value)}
                >
                  <option value="">Select gender</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>

            <div className="row">
              <div className="col-sm-6">
                <label>Date of Birth*</label>
                <input
                  type="date"
                  placeholder="<EMAIL>"
                  className="fild-des"
                  name="date_of_birth"
                  value={date_of_birth}
                  onChange={(e) => SetDateOFBirth(e.target.value)}
                />
              </div>
              <div className="col-sm-6">
                <label>Where are you currently based?*</label>

                <select
                  className="fild-des"
                  value={currently_location}
                  onChange={(e) => SetCurrentlyLocation(e.target.value)}
                >
                  {allcountries.map((countries: any) => {
                    if (countries.status === 'active') {
                      return (
                        <option key={countries.id} value={countries.id}>
                          {countries.country_name}
                        </option>
                      );
                    }
                  })}
                </select>
              </div>
            </div>


            <label>Your Cover Letter*</label>
            <HtmlEditor
              name="edit_job_description"
              value={user.description}
              onChange={(name: any, value: any) => {
                EditChange("description", value);
              }}
            />
            {descriptionError && <div className="text-danger mt-2">{descriptionError}</div>}
            <p className="font-12 text-right words">250 words</p>

            {/* <div className='d-flex justify-content-center'>
              <button className="download d-flex justify-content-center ">
                    <i className="fa-solid fa-upload"></i> Upload Cover Letter
              </button>
              </div> */}
            <div className="d-flex justify-content-center">
              <div className="uploade-btn ">
                <input
                  type="file"
                  name="resume"
                  accept='.pdf, .docx'
                  onChange={handleCoverChange}
                />
                <button className="download ">
                  <i className="fa-solid fa-upload"></i> Upload Cover Letter
                </button>
                {covererrorMessage && (
                  <div className="text-danger mt-2">{covererrorMessage}</div>
                )}
              </div>
            </div>
            {selectedCover && (
              <div className="w-box bg-fff mt-3 mb-3 p-3">
                <div className="row">
                  <div className="col-sm-8">
                    <p className="f-18 w-600 mb-2">{user.name}-Cover-Letter</p>
                    <p className="f-16 c-999999">
                      Uploaded on{" "}
                      {currentDate.toLocaleString("en-US", {
                        month: "short",
                        day: "numeric",
                      })}
                    </p>
                  </div>
                </div>
              </div>
            )}

            <div className="text-right mt-3">
              <a className="cancel" onClick={handleCancel}>
                Cancel
              </a>
              <button className="save">Save</button>
            </div>


          </form>
          <ToastContainer />
        </div>
      </PopupModal>


    </>
  )
}
