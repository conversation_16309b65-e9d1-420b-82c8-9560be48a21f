import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getCurrentUserData } from '../../lib/session';
import PopupModal from '../../components/Common/PopupModal';
import { applyJob, getSingleUserDetails } from '../../lib/frontendapi';
import ApplicationThankYouPopup from '../../components/Common/ApplicationThankYouPopup';
import { HtmlEditor } from './HtmlEditor';

export default function ApplyJobPopup({ show, setModalConfirm2, selectedJobId, companyName }: any) {
  const [user, SetUserData]: any = useState([]);
  const [selectedResume, setSelectedResume] = useState(null);
  const [modalConfirm3, setModalConfirm3] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [selectedFileName, setSelectedFileName] = useState('');
  const [descriptionError, setDescriptionError] = useState<string>('');
  const MAX_DESCRIPTION_WORDS = 250;
  const [covererrorMessage, setcoverErrorMessage] = useState('');
  const [selectedCover, setSelectedCover] = useState(null);
  const currentDate = new Date();
  const CompnayName = companyName;

  useEffect(() => {
    const current_user_data: any = getCurrentUserData();
    getSingleUserDetails(current_user_data.id)
      .then(res => {
        if (res.status === true) {
          SetUserData(res.user);
        } else {
          SetUserData([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  }, []);
  const modalConfirmClose2 = () => {
    setModalConfirm2(false);
  };
  const modalConfirmOpen3 = () => {
    setModalConfirm3(true);
  };
  const handleFileChange = (event: any) => {
    const file = event.target.files[0];
    setSelectedFileName(file.name);
    setSelectedResume(file);
    if (file) {
      const allowedFormats = ['pdf', 'doc', 'docx'];
      const fileExtension = file.name.split('.').pop().toLowerCase();
      if (!allowedFormats.includes(fileExtension)) {
        setErrorMessage('Invalid file. Only PDF or DOC files are allowed.');
        setSelectedResume(null);
      } else {
        setErrorMessage('');
      }
    }
  };
  const EditChange = (name: any, value: any) => {
    SetUserData((prevState: any) => {
      return {
        ...prevState,
        [name]: value,
      };
    });
  };
  const handleCancel = () => {
    modalConfirmClose2();
  };
  const submitForm = (event: any) => {
    event.preventDefault();
    const current_user_data: any = getCurrentUserData();
    const description = user.description;
    if (!description || description.trim() === '') {
      toast.error('Please enter a description.', {
        position: toast.POSITION.TOP_RIGHT,
        closeButton: true,
        hideProgressBar: false,
        style: {
          background: '#ffe6e6',
          color: '#d04e4f',
          maxWidth: '300px',
          padding: 0,
          margin: 0,
          fontSize: '15px',
          fontFamily: 'var(--opensans-font)',
          paddingRight: '10px',
          paddingLeft: '10px',
        },
        progressStyle: {
          background: '#ffe6e6',
        },
        icon: () => <i className="fa-solid fa-circle-xmark" style={{ color: '#d04e4f', fontSize: '16px' }}></i>,
      });
      return;
    }

    const descriptionWords = user.description.split(/\s+/).filter(Boolean).length;
    if (descriptionWords > MAX_DESCRIPTION_WORDS) {
      setDescriptionError(`Description should not exceed ${MAX_DESCRIPTION_WORDS} words.`);
      return;
    } else {
      setDescriptionError('');
    }

    const resume_files = event.target.resume.files;
    const resume_path = resume_files && resume_files[0] ? resume_files[0] : user.resume_pdf_path || '';
    const data = {
      user_id: current_user_data.id,
      job_id: selectedJobId,
      description: user.description,
      resume_path: resume_path, //event.target.resume.files[0],
      default_resume: 0,
    };
    applyJob(data)
      .then(res => {
        if (res.status) {
          modalConfirmClose2();
          setTimeout(() => {
            modalConfirmOpen3();
          }, 1000);
        } else {
          if (res.error === 'job_already_applied') {
            toast.error('You have already applied for this job', {
              position: toast.POSITION.TOP_RIGHT,
              closeButton: true,
              hideProgressBar: false,
              style: {
                background: '#ffe6e6',
                color: '#d04e4f',
                maxWidth: '300px',
                padding: 0,
                margin: 0,
                fontSize: '15px',
                fontFamily: 'var(--opensans-font)',
                paddingRight: '10px',
                paddingLeft: '10px',
              },
              progressStyle: {
                background: '#ffe6e6',
              },
              icon: () => <i className="fa-solid fa-circle-xmark" style={{ color: '#d04e4f', fontSize: '16px' }}></i>,
            });
          } else {
            toast.error('An error occurred while applying for the job.', {
              position: toast.POSITION.TOP_RIGHT,
              closeButton: true,
              hideProgressBar: false,
              style: {
                background: '#ffe6e6',
                color: '#d04e4f',
                maxWidth: '300px',
                padding: 0,
                margin: 0,
                fontSize: '15px',
                fontFamily: 'var(--opensans-font)',
                paddingRight: '10px',
                paddingLeft: '10px',
              },
              progressStyle: {
                background: '#ffe6e6',
              },
              icon: () => <i className="fa-solid fa-circle-xmark" style={{ color: '#d04e4f', fontSize: '16px' }}></i>,
            });
          }
        }
      })
      .catch(err => {
        toast.error('An error occurred while applying for the job.', {
          position: toast.POSITION.TOP_RIGHT,
          closeButton: true,
          hideProgressBar: false,
          style: {
            background: '#ffe6e6',
            color: '#d04e4f',
            maxWidth: '300px',
            padding: 0,
            margin: 0,
            fontSize: '15px',
            fontFamily: 'var(--opensans-font)',
            paddingRight: '10px',
            paddingLeft: '10px',
          },
          progressStyle: {
            background: '#ffe6e6',
          },
          icon: () => <i className="fa-solid fa-circle-xmark" style={{ color: '#d04e4f', fontSize: '16px' }}></i>,
        });
      });
  };

  const handleCoverChange = (event: any) => {
    const file = event.target.files[0];
    setSelectedCover(file);
    if (file) {
      const allowedFormats = ['pdf', 'doc', 'docx'];
      const fileExtension = file.name.split('.').pop().toLowerCase();
      if (!allowedFormats.includes(fileExtension)) {
        setcoverErrorMessage('Invalid file. Only PDF or DOC files are allowed.');
        setSelectedCover(null);
      } else {
        setcoverErrorMessage('');
      }
    }
  };

  return (
    <>
      <PopupModal
        show={show}
        handleClose={modalConfirmClose2}
        customclass={'add_company_signup_popup modal-lg body-sp-0 '}
        closebtnclass={'close-b-des close-x  bg-0055BA border-design'}
        closebtnicon={'icon'}>
        <div className="head-box">
          <div className="row">
            <div className="col-sm-10">
              <p className="f-26 mb-2 mt-2"> Apply to {CompnayName}</p>
              <p className="f-16"> Enter your basic information & get apply.</p>
            </div>
            <div className="col-sm-2 text-right">
              <button
                type="button"
                className="close-x  bg-0055BA border-design"
                data-bs-dismiss="modal"
                aria-label="Close">
                <i className="fa-solid fa-xmark"></i>
              </button>
            </div>
          </div>
        </div>
        <div className="popup-body">
          <form className="form-experience-fieild" onSubmit={submitForm}>
            <div className="row ">
              <div className="col-sm-7 text-right">
                <p className="f-22 mt-2">Upload your recent Resume/CV:</p>
              </div>
              <div className="col-sm-5">
                <div className="uploade-btn">
                  <input type="file" name="resume" onChange={handleFileChange} />
                  <button className="download ">
                    <i className="fa-solid fa-upload"></i> Upload Resume
                  </button>
                </div>
                {errorMessage && <div className="text-danger mt-2">{errorMessage}</div>}
                {selectedFileName && <p className="text-dark">Selected File: {selectedFileName}</p>}
              </div>
            </div>
            {selectedResume && (
              <div className="w-box bg-fff mt-3 mb-3 p-3">
                <div className="row">
                  <div className="col-sm-8">
                    <p className="f-18 w-600 mb-2">{user.name}-Resume</p>
                    <p className="f-16 c-999999">Uploaded on </p>
                  </div>
                  <div className="col-sm-4 text-right">
                    <span className="default">Choose</span>
                    &nbsp;&nbsp;
                  </div>
                </div>
              </div>
            )}
            {!selectedResume && user.resume_pdf_path && Number(user.default_resume) === 1 ? (
              <div className="w-box bg-fff mt-3 mb-3 p-3">
                <div className="row">
                  <input type="hidden" name="resume" value={user.resume_pdf_path} />
                  <div className="col-sm-8">
                    <p className="f-18 w-600 mb-2">{user.name}-Resume</p>
                    <p className="f-16 c-999999">Uploaded on {/* Add the uploaded date here */}</p>
                  </div>
                  <div className="col-sm-4 text-right">
                    <span className="default">Choose</span>
                    &nbsp;&nbsp;
                  </div>
                </div>
              </div>
            ) : null}
            <label>Your Name*</label>
            <input type="text" placeholder="Alan Moore" className="fild-des" name="name" value={user.name} />
            <div className="row">
              <div className="col-sm-6">
                <label>Email ID*</label>
                <input
                  type="email"
                  placeholder="<EMAIL>"
                  className="fild-des"
                  value={user.email}
                  readOnly
                />
              </div>
              <div className="col-sm-6">
                <label>Contact Number*</label>
                <input
                  type="text"
                  placeholder="(+971) 123 – 456 – 7890"
                  className="fild-des"
                  name="contact_no"
                  value={user.contact_no}
                />
              </div>
            </div>
            <div className="row">
              <div className="col-sm-6">
                <label>Date of Birth*</label>
                <input
                  type="date"
                  placeholder="<EMAIL>"
                  className="fild-des"
                  name="date_of_birth"
                  value={user.date_of_birth}
                />
              </div>
              <div className="col-sm-6">
                <label>Gender</label>
                <select className="fild-des" name="gender" value={user.gender}>
                  <option>Male</option>
                  <option>Female</option>
                </select>
              </div>
            </div>
            <label>Where are you currently based?*</label>
            <input
              type="text"
              placeholder="Dubai"
              className="fild-des"
              value={user.country_name}
              name="where_currently_based"
            />
            <label>Your Cover Letter*</label>
            <HtmlEditor
              name="edit_job_description"
              value={user.description}
              onChange={(name: any, value: any) => {
                EditChange('description', value);
              }}
            />
            {descriptionError && <div className="text-danger mt-2">{descriptionError}</div>}
            {/* <p className="font-12 text-right words">250 words</p> */}
            <div className="d-flex justify-content-center mt-4">
              <div className="uploade-btn ">
                <input type="file" name="resume" accept=".pdf, .docx" onChange={handleCoverChange} />
                <button className="download ">
                  <i className="fa-solid fa-upload"></i> Upload Cover Letter
                </button>
                {covererrorMessage && <div className="text-danger mt-2">{covererrorMessage}</div>}
              </div>
            </div>
            {selectedCover && (
              <div className="w-box bg-fff mt-3 mb-3 p-3">
                <div className="row">
                  <div className="col-sm-8">
                    <p className="f-18 w-600 mb-2">{user.name}-Cover-Letter</p>
                    <p className="f-16 c-999999">
                      Uploaded on{' '}
                      {currentDate.toLocaleString('en-US', {
                        month: 'short',
                        day: 'numeric',
                      })}
                    </p>
                  </div>
                </div>
              </div>
            )}
            <div className="text-right mt-3">
              <a className="cancel" onClick={handleCancel}>
                Cancel
              </a>
              <button className="save">Save</button>
            </div>
          </form>
          <ToastContainer />
        </div>
      </PopupModal>
      <ApplicationThankYouPopup show={modalConfirm3} setModalConfirm3={(bool: any) => setModalConfirm3(bool)} />
    </>
  );
}
