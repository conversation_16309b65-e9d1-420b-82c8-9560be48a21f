import React, { useState } from "react";

const SuccessToast = ({ message }: any) => {
    const [showToast, setShowToast] = useState(true);

    const handleCloseToast = () => {
        setShowToast(false);
    };

    return (
        <>
            {showToast && (
                <div className="toast-box color-0C5A14 code-job1">
                    <div className="toast-footer">
                        <div className="row align-items-center">
                            <div className="col-11">
                                <h5 className="msg-toastr">
                                    <i className="fa-solid fa-circle-check"></i> {message}
                                </h5>
                            </div>
                            <div className="col-1 text-md-end p-md-2 p-0">
                                <p>
                                    <a
                                        href="#"
                                        className="link-12"
                                    >
                                        <i className="fa-solid fa-xmark xmark-icon" onClick={handleCloseToast}></i>
                                    </a>{" "}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default SuccessToast;
