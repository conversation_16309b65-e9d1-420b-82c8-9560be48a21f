import React from 'react';
import {Pagination} from 'antd';
import styles from './style.module.css';
import {Spinner} from 'react-bootstrap';

export interface TableColumn {
  key: string;
  title: React.ReactNode;
  render?: (item: any, index: number) => React.ReactNode;
  dataIndex?: string;
  onClick?: (item: any, index: number) => void;
}

interface TableProps {
  columns: TableColumn[];
  data: any[];
  loading?: boolean;
  pagination?: {
    total: number;
    current: number;
    pageSize: number;
    onChange: (page: number, pageSize?: number) => void;
  };
}

const CommonTable: React.FC<TableProps> = ({columns, data, loading, pagination}) => {
  return (
    <div className={`table-part mt-4 admin-tab-table ${styles.table_container}`}>
      <table className={`rwd-table ${styles.table}`}>
        <thead>
          <tr>{columns.map(column => column.title)}</tr>
        </thead>
        <tbody className={styles.body}>
          {loading ? (
            <tr aria-colspan={columns.length}>
              <td colSpan={columns.length} className={styles.noData} align="center" aria-colspan={columns.length}>
                <div style={{textAlign: 'center', marginTop: 16}}>
                  <Spinner
                    style={{
                      color: '#0070F5',
                    }}
                  />
                </div>
              </td>
            </tr>
          ) : data.length > 0 ? (
            data.map((item, index) => (
              <tr key={index}>
                {columns.map(column => (
                  <td
                    key={column.key}
                    onClick={() => column.onClick && column.onClick(item, index)}
                    style={{cursor: column.onClick ? 'pointer' : 'default'}}
                    data-th={column.title}>
                    {column.render ? column.render(item, index) : item[column.dataIndex || '']}
                  </td>
                ))}
              </tr>
            ))
          ) : (
            <tr aria-colspan={columns.length}>
              <td colSpan={columns.length} className={styles.noData} align="center" aria-colspan={columns.length}>
                <div style={{textAlign: 'center'}}>No data found</div>
              </td>
            </tr>
          )}
        </tbody>
      </table>
      {pagination && (
        <div className={styles.pagination}>
          <Pagination
            total={pagination.total}
            current={pagination.current}
            pageSize={pagination.pageSize}
            onChange={pagination.onChange}
          />
        </div>
      )}
    </div>
  );
};

export default CommonTable;
