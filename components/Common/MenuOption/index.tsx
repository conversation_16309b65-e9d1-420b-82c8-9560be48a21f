import {useRouter} from 'next/router';
import styles from './style.module.css';

interface MenuOptionProps {
  items: {
    label: string;
    onClick: () => void;
    color?: string;
  }[];
  open: boolean;
  setOpen: (value: boolean) => void;
}

export const MenuOption = ({items, open, setOpen}: MenuOptionProps) => {
  const router = useRouter();
  return (
    <div className={styles.template_card_option} onClick={() => setOpen(!open)}>
      {open && (
        <div className={styles.option_dropdown}>
          <ul>
            {/* <li onClick={() => router.push('/admin/automation/template-list/edit-template')}>Edit</li>
            <li>Preview</li>
            <li>Copy</li>
            <li
              style={{
                color: '#D04E4F',
              }}>
              Delete
            </li> */}
            {items.map((item, index) => {
              return (
                <li key={index} onClick={item.onClick} style={{color: item.color}}>
                  {item.label}
                </li>
              );
            })}
          </ul>
        </div>
      )}
    </div>
  );
};
