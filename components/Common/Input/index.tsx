import styles from './style.module.css';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement | HTMLTextAreaElement> {
  label?: string;
  isRequired?: boolean;
  characterLimit?: number;
  isMultiline?: boolean;
  bigLabel?: boolean;
  bottomLabel?: boolean;
  inputSize?: 'small' | 'medium' | 'large';
}

export const InputUi = (props: InputProps) => {
  const {label, isRequired, inputSize, bottomLabel = true, ...rest} = props;
  return (
    <div className={styles['input-container']}>
      {label && (
        <label className={`${styles.label} ${props.bigLabel ? styles.bigLabel : ''}`}>
          {label}
          {isRequired && <span className={styles.required}>*</span>}
        </label>
      )}
      {props.isMultiline ? (
        <textarea {...rest} className={`${styles.input} ${styles.multiline}`} />
      ) : (
        <input
          {...rest}
          className={`${styles.input} ${styles[inputSize || 'medium']}`}
          onInput={e => {
            if (props.characterLimit) {
              if (e.currentTarget.value.length > props.characterLimit) {
                e.currentTarget.value = e.currentTarget.value.slice(0, props.characterLimit);
              }
            }
          }}
          autoComplete="off"
        />
      )}

      {bottomLabel && (
        <div className={styles.character_limit_container}>
          {props.characterLimit && (
            <span className={styles.character_limit}>{`${props.value?.toString().length}/${
              props.characterLimit
            }`}</span>
          )}
        </div>
      )}
    </div>
  );
};
