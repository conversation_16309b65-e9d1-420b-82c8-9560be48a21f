.input-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.label {
  color: #2c2c2c;
  font-size: 12px;
  font-weight: 400;
  line-height: 140%;
}

.required {
  color: #d04e4f;
}
.input {
  display: flex;
  height: 56px;
  padding: 10px 16px;
  align-items: center;
  gap: 6px;
  align-self: stretch;
  border-radius: 4px;
  border: 1px solid var(--Grayscale-04, #bababa);
  background: var(--Gray<PERSON>le-White, #fff);
  color: black;
  font-feature-settings:
    'liga' off,
    'clig' off;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  letter-spacing: -0.5px;
}
.input::placeholder {
  color: #999;
}
.character_limit_container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  min-height: 16px;
}
.character_limit {
  color: #474d66;
  text-align: right;
  font-feature-settings:
    'liga' off,
    'clig' off;
  font-family: Inter;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: -0.5px;
}
.multiline {
  display: flex;
  height: 200px;
  padding: 10px 16px;
  align-items: flex-start;
  gap: 6px;
  align-self: stretch;
  resize: none;
}
.bigLabel {
  font-weight: 600;
  font-size: 14px;
}
.small {
  height: 45px;
}
