import React, { useContext } from 'react';
import Head from 'next/head';
import AuthContext from '@/Context/AuthContext';
import { useRouter } from 'next/router';

const HeadLayout = ({ settingsData }: any) => {
  const { settings } = useContext(AuthContext);
  const router = useRouter();

  /*useEffect(() => {
    if (user) {
      if (user.role) {
        if (!user.role.includes('admin') && router.pathname.startsWith('/admin')) {
          if (user.id && user.role.includes('admin')) {
            router.push('/admin/dashboard');
          } else if (user.id && user.role.includes('employee')) {
            router.push('/employees/dashboard');
          } else if (user.id && user.role.includes('employer')) {
            //router.push("/employer/dashboard");
          } else if (user.id && user.role.includes('staff')) {
            router.push('/staff/dashboard');
          } else {
            //router.push("/auth/login"); // Replace '/unauthorized' with the route you want to redirect unauthorized users to.
          }
        }
        if (!user.role.includes('employee') && router.pathname.startsWith('/employees')) {
          if (user.id && user.role.includes('admin')) {
            router.push('/admin/dashboard');
          } else if (user.id && user.role.includes('employee')) {
            router.push('/employees/dashboard');
          } else if (user.id && user.role.includes('employer')) {
            router.push('/employer/dashboard');
          } else if (user.id && user.role.includes('staff')) {
            router.push('/staff/dashboard');
          } else {
            //router.push("/auth/login"); // Replace '/unauthorized' with the route you want to redirect unauthorized users to.
          }
        }
        if (!user.role.includes('employer') && router.pathname.startsWith('/employer')) {
          if (user.id && user.role.includes('admin')) {
            router.push('/admin/dashboard');
          } else if (user.id && user.role.includes('employee')) {
            router.push('/employees/dashboard');
          } else if (user.id && user.role.includes('employer')) {
            router.push('/employer/dashboard');
          } else if (user.id && user.role.includes('staff')) {
            router.push('/staff/dashboard');
          } else {
            //router.push("/auth/login"); // Replace '/unauthorized' with the route you want to redirect unauthorized users to.
          }
        }
        if (!user.role.includes('staff') && router.pathname.startsWith('/staff')) {
          if (user.id && user.role.includes('admin')) {
            router.push('/admin/dashboard').then();
          } else if (user.id && user.role.includes('employee')) {
            router.push('/employees/dashboard').then();
          } else if (user.id && user.role.includes('employer')) {
            router.push('/employer/dashboard').then();
          } else if (user.id && user.role.includes('staff')) {
            router.push('/staff/dashboard').then();
          } else {
            router.push('/auth/login').then(); // Replace '/unauthorized' with the route you want to redirect unauthorized users to.
          }
        }
      } else {
        console.log('route---', router.pathname.startsWith('/buildtellus'));
        if (!router.pathname.startsWith('/join') && !router.pathname.startsWith('/buildtellus')) {
          router.push('/join').then();
        }
      }
    }

    //const current_user_data:any = getCurrentUserData();
    //current_user_data.role ? setCurrentUserRole(current_user_data.role) : setCurrentUserRole('');
  }, [router, user]);*/

  let HeadTitle = 'The Talent Point';
  let MetaDesc = '';
  let MetaKeyword = '';
  let MetaOGTitle = '';
  let MetaOGDescription = '';
  let MetaOGImage = '';
  let MetaOGUrl = '';
  let MetaOGType = '';
  let MetaTwitterCard = '';
  let MetaTwitterSite = '';
  let MetaTwitterTitle = '';
  let MetaTwitterDescription = '';
  let MetaTwitterImage = '';

  if (router.pathname.startsWith('/admin')) {
    HeadTitle = 'Admin Dashboard';
    MetaDesc = '';
    MetaKeyword = '';
    MetaOGTitle = '';
    MetaOGDescription = '';
    MetaOGImage = '';
    MetaOGUrl = '';
    MetaOGType = '';
    MetaTwitterCard = '';
    MetaTwitterSite = '';
    MetaTwitterTitle = '';
    MetaTwitterDescription = '';
    MetaTwitterImage = '';
  } else if (router.pathname.startsWith('/employees')) {
    HeadTitle = 'Employees Dashboard';
    MetaDesc = '';
    MetaKeyword = '';
    MetaOGTitle = '';
    MetaOGDescription = '';
    MetaOGImage = '';
    MetaOGUrl = '';
    MetaOGType = '';
    MetaTwitterCard = '';
    MetaTwitterSite = '';
    MetaTwitterTitle = '';
    MetaTwitterDescription = '';
    MetaTwitterImage = '';
  } else if (router.pathname.startsWith('/employer')) {
    HeadTitle = 'Employer Dashboard';
    MetaDesc = '';
    MetaKeyword = '';
    MetaOGTitle = '';
    MetaOGDescription = '';
    MetaOGImage = '';
    MetaOGUrl = '';
    MetaOGType = '';
    MetaTwitterCard = '';
    MetaTwitterSite = '';
    MetaTwitterTitle = '';
    MetaTwitterDescription = '';
    MetaTwitterImage = '';
  } else if (router.pathname.startsWith('/staff')) {
    HeadTitle = 'Staff Dashboard';
    MetaDesc = '';
    MetaKeyword = '';
    MetaOGTitle = '';
    MetaOGDescription = '';
    MetaOGImage = '';
    MetaOGUrl = '';
    MetaOGType = '';
    MetaTwitterCard = '';
    MetaTwitterSite = '';
    MetaTwitterTitle = '';
    MetaTwitterDescription = '';
    MetaTwitterImage = '';
  } else if (router.pathname == '/salaries') {
    const currentYear = new Date().getFullYear();
    HeadTitle = "Average Salary Insights in Middle East -" + currentYear;
    MetaDesc = "Explore the average salary for jobs in Middle east by Job Titles & Location in " + currentYear + ". Discover the average compensation by job titles & locations across GCC & Find out what your skills are worth in today's job market";
    MetaKeyword = '';
    MetaOGTitle = '';
    MetaOGDescription = '';
    MetaOGImage = '';
    MetaOGUrl = '';
    MetaOGType = '';
    MetaTwitterCard = '';
    MetaTwitterSite = '';
    MetaTwitterTitle = '';
    MetaTwitterDescription = '';
    MetaTwitterImage = '';
  } else if (router.pathname == '/jobs-in-gulf') {
    HeadTitle = settingsData.settings.jobs_meta_title ? settingsData.settings.jobs_meta_title : "The Talent Point";
    MetaDesc = settingsData.settings.jobs_meta_description ? settingsData.settings.jobs_meta_description : "";
    MetaKeyword = '';
    MetaOGTitle = settingsData.settings.jobs_meta_title ? settingsData.settings.jobs_meta_title : `Search  for Active Jobs in Middle East`;
    MetaOGDescription = settingsData.settings.blog_listing_meta_description ? settingsData.settings.blog_listing_meta_description : `Search from 40000+ active jobs in Middle east UAE, Qatar, Saudi Arabia, Oman, Bahrain, Kuwait, Jordan and Lebanon. Search jobs by employers location, sector, salary, Job Style.`;
    MetaOGImage = `${process.env.NEXT_PUBLIC_BASE_URL}images/share-logo-img.png`;
    MetaOGUrl = new URL(router.asPath, process.env.NEXT_PUBLIC_BASE_URL).href;
    MetaOGType = 'website';
    MetaTwitterCard = 'summary_large_image';
    MetaTwitterSite = '@theTalentPoint';
    MetaTwitterTitle = settingsData.settings.jobs_meta_title ? settingsData.settings.jobs_meta_title : `Search  for Active Jobs in Middle East`;
    MetaTwitterDescription = settingsData.settings.blog_listing_meta_description ? settingsData.settings.blog_listing_meta_description : `Search from 40000+ active jobs in Middle east UAE, Qatar, Saudi Arabia, Oman, Bahrain, Kuwait, Jordan and Lebanon. Search jobs by employers location, sector, salary, Job Style.`;
    MetaTwitterImage = `${process.env.NEXT_PUBLIC_BASE_URL}images/share-logo-img.png`;
  } else if (router.pathname == '/jobs-by-location') {
    HeadTitle = "The Talent Point - Jobs by Location";
    MetaDesc = "Explore job opportunities in different locations. Find jobs in the United Arab Emirates, Saudi Arabia, Qatar, Oman, Kuwait, and more. Discover career options in cities like Dubai, Riyadh, Doha, and others.";
    MetaKeyword = 'The Talent Point, jobs by location, United Arab Emirates, Saudi Arabia, Qatar, Oman, Kuwait, job opportunities, Dubai, Riyadh, Doha';
    MetaOGTitle = 'The Talent Point - Jobs by Location';
    MetaOGDescription = 'Explore job opportunities in different locations. Find jobs in the United Arab Emirates, Saudi Arabia, Qatar, Oman, Kuwait, and more. Discover career options in cities like Dubai, Riyadh, Doha, and others.';
    MetaOGImage = `${process.env.NEXT_PUBLIC_BASE_URL}images/share-logo-img.png`;
    MetaOGUrl = new URL(router.asPath, process.env.NEXT_PUBLIC_BASE_URL).href;
    MetaOGType = 'website';
    MetaTwitterCard = 'summary_large_image';
    MetaTwitterSite = '@TheTalentPoint';
    MetaTwitterTitle = 'The Talent Point - Jobs by Location';
    MetaTwitterDescription = 'Explore job opportunities in different locations. Find jobs in the United Arab Emirates, Saudi Arabia, Qatar, Oman, Kuwait, and more. Discover career options in cities like Dubai, Riyadh, Doha, and others.';
    MetaTwitterImage = `${process.env.NEXT_PUBLIC_BASE_URL}images/share-logo-img.png`;
  } else if (router.pathname == '/jobs-popular-search') {
    HeadTitle = "The Talent Point - Jobs by Popular Search in the Middle East";
    MetaDesc = "Explore popular job searches in the Middle East. Find part-time jobs, accountant jobs, teaching jobs, and more. Discover opportunities to work in Dubai, Abu Dhabi, and other regions.";
    MetaKeyword = 'The Talent Point, jobs, Middle East, popular job searches, part-time jobs, accountant jobs, teaching jobs, Dubai, Abu Dhabi';
    MetaOGTitle = 'The Talent Point - Jobs by Popular Search in the Middle East';
    MetaOGDescription = 'Explore popular job searches in the Middle East. Find part-time jobs, accountant jobs, teaching jobs, and more. Discover opportunities to work in Dubai, Abu Dhabi, and other regions.';
    MetaOGImage = `${process.env.NEXT_PUBLIC_BASE_URL}images/share-logo-img.png`;
    MetaOGUrl = new URL(router.asPath, process.env.NEXT_PUBLIC_BASE_URL).href;
    MetaOGType = 'website';
    MetaTwitterCard = 'summary_large_image';
    MetaTwitterSite = '@TheTalentPoint';
    MetaTwitterTitle = 'The Talent Point - Jobs by Popular Search in the Middle East';
    MetaTwitterDescription = 'Explore popular job searches in the Middle East. Find part-time jobs, accountant jobs, teaching jobs, and more. Discover opportunities to work in Dubai, Abu Dhabi, and other regions.';
    MetaTwitterImage = `${process.env.NEXT_PUBLIC_BASE_URL}images/share-logo-img.png`;
  } else if (router.pathname == '/skill/[skillJobsInCity]') {
    const date = new Date();
    var months = ['Jan', 'Feb', 'March', 'April', 'May', 'June', 'July', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    HeadTitle = settingsData.finalSkillName + " Jobs in " + settingsData.finalCityName + " - " + settingsData.jobs.length + " vacancies in " + months[date.getMonth()] + " " + date.getFullYear();
    MetaDesc = "Search from Over " + settingsData.jobs.length + " vacancies in " + settingsData.finalCityName + ". Register Today & apply for " + settingsData.finalSkillName + " Jobs in " + settingsData.finalCityName + "  on thetalentpoint.com";
    MetaKeyword = '';
    MetaOGTitle = '';
    MetaOGDescription = '';
    MetaOGImage = '';
    MetaOGUrl = '';
    MetaOGType = '';
    MetaTwitterCard = '';
    MetaTwitterSite = '';
    MetaTwitterTitle = '';
    MetaTwitterDescription = '';
    MetaTwitterImage = '';
  } else if (router.pathname == '/[jobsInCity]') {
    const date = new Date();
    var months = ['Jan', 'Feb', 'March', 'April', 'May', 'June', 'July', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    HeadTitle = "Jobs in " + settingsData.finalCityName + " - " + settingsData.jobs.length + " vacancies in " + months[date.getMonth()] + " " + date.getFullYear();
    MetaDesc = "Apply for " + settingsData.jobs.length + " Jobs in " + settingsData.finalCityName + ". Apply for Multiple  Vacancies in " + settingsData.finalCityName + "on thetalentpoint.com by Top employers  in multiple industries & sectors";
    MetaKeyword = '';
    MetaOGTitle = '';
    MetaOGDescription = '';
    MetaOGImage = '';
    MetaOGUrl = '';
    MetaOGType = '';
    MetaTwitterCard = '';
    MetaTwitterSite = '';
    MetaTwitterTitle = '';
    MetaTwitterDescription = '';
    MetaTwitterImage = '';
    // } else if (router.pathname == '/salaries/[...slug]') {
    //   const currentYear = new Date().getFullYear();
    //   HeadTitle = settingsData.resultState.length > 0 ? `${settingsData.resultState.sector.sector_name} Salary in ${settingsData.resultState.country.country_name} ${currentYear}` : 'The Talent Point';
    //   MetaDesc = '';
    //   MetaKeyword = '';
    //   MetaOGTitle = '';
    //   MetaOGDescription = settingsData.resultState.length > 0 ? `${settingsData.resultState.sector.sector_name ? `The average salary for ${settingsData.resultState.sector.sector_name} Salary in` : ''} ${settingsData.resultState.country.country_name ? settingsData.resultState.country.country_name : 'The Talent Point'} ${settingsData.resultState.based_experience && settingsData.resultState.based_experience.length > 0 ? `${currentYear} years is ${settingsData.resultState.salary.average || 'The Talent Point'} per month. Salaries are based on ${settingsData.resultState.based_experience[0].count ? `${settingsData.resultState.based_experience[0].count} reported on The Talentpoint by employers` : 'The Talent Point'}` : ''}` : '';
    //   MetaOGImage = '';
    //   MetaOGUrl = '';
    //   MetaOGType = '';
    //   MetaTwitterCard = '';
    //   MetaTwitterSite = '';
    //   MetaTwitterTitle = '';
    //   MetaTwitterDescription = '';
    //   MetaTwitterImage = '';
  } else if (router.pathname == '/companies/[slug]') {
    HeadTitle = settingsData.company.meta_tag ? settingsData.company.meta_tag : "The Talent Point";
    MetaDesc = settingsData.company.meta_desc ? settingsData.company.meta_desc : "";
    MetaKeyword = '';
    MetaOGTitle = '';
    MetaOGDescription = '';
    MetaOGImage = '';
    MetaOGUrl = '';
    MetaOGType = '';
    MetaTwitterCard = '';
    MetaTwitterSite = '';
    MetaTwitterTitle = '';
    MetaTwitterDescription = '';
    MetaTwitterImage = '';
  } else if (router.pathname == '/author/[slug]') {
    HeadTitle = settingsData.author.name ? settingsData.author.name : "The Talent Point";
    MetaDesc = settingsData.author.description ? settingsData.author.description : "";
    MetaKeyword = '';
    MetaOGTitle = settingsData.author.name ? settingsData.author.name : "The Talent Point";
    MetaOGDescription = settingsData.author.description ? settingsData.author.description : "";
    MetaOGImage = `${process.env.NEXT_PUBLIC_BASE_URL}images/share-logo-img.png`;
    MetaOGUrl = new URL(router.asPath, process.env.NEXT_PUBLIC_BASE_URL).href;
    MetaOGType = 'website';
    MetaTwitterCard = 'summary_large_image';
    MetaTwitterSite = '@theTalentPoint';
    MetaTwitterTitle = settingsData.author.name ? settingsData.author.name : "The Talent Point";
    MetaTwitterDescription = settingsData.author.description ? settingsData.author.description : "";
    MetaTwitterImage = `${process.env.NEXT_PUBLIC_BASE_URL}images/share-logo-img.png`;
  }
  return (
    <Head>
      <title>{HeadTitle}</title>
      {settings && settings.favicon ? (
        <>
          <link rel="icon" href={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/' + settings.favicon} />
        </>
      ) : (
        <>
          <link rel="icon" href={process.env.NEXT_PUBLIC_BASE_URL + 'images/Favicon.png'} />
        </>
      )}
      <meta charSet="utf-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <meta name="description" content={MetaDesc} />
      <meta name="keywords" content={MetaKeyword} />
      {/* Open Graph tags */}
      <meta property="og:title" content={MetaOGTitle} />
      <meta property="og:description" content={MetaOGDescription} />
      <meta property="og:image" content={MetaOGImage} />
      <meta property="og:url" content={MetaOGUrl} />
      <meta property="og:type" content={MetaOGType} />
      {/* Twitter card */}
      <meta name="twitter:card" content={MetaTwitterCard} />
      <meta name="twitter:site" content={MetaTwitterSite} />
      <meta name="twitter:title" content={MetaTwitterTitle} />
      <meta name="twitter:description" content={MetaTwitterDescription} />
      <meta property="twitter:image" content={MetaTwitterImage} />
    </Head>
  );
};

export default HeadLayout;
