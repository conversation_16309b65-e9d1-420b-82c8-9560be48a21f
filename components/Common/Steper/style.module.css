.content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: absolute;
  top: 0;
}
.stepperContainer {
  display: flex;
  position: relative;
  padding: 40px 0;
}
.stepConnector {
  width: 160px;
  height: 2px;
  background: #cfe5ff;
}
.index_content {
  display: flex;
  width: 32px;
  justify-content: center;
  height: 32px;
  align-items: center;
  border-radius: 50%;
  background: #ebf4ff;
  color: #0055ba;
  font-size: 18px;
  font-weight: 600;
  line-height: 160%;
}
.active .index_content,
.finished .index_content {
  background-color: #0055ba;
  color: #ebf4ff !important;
}
.lineFinished {
  background: #0055ba !important;
}
.title {
  color: #cfe5ff;
  font-size: 12px;
  font-weight: 600;
  line-height: 140%;
}
.titleFinished,
.titleActive {
  color: #0055ba;
}
