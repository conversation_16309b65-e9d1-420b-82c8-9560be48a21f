import React from 'react';
import styles from './style.module.css';
import Image from 'next/image';

interface StepperUiProps {
  steps: {title: string}[];
  currentStep: number;
  onStepChange?: (currentStep: number) => void;
}

export const StepperUi: React.FC<StepperUiProps> = ({steps, currentStep, onStepChange}) => {
  return (
    <div className={styles.stepperContainer}>
      {steps.map((step, index) => (
        <div
          key={index}
          className={`${styles.step} ${
            index < currentStep ? styles.finished : index === currentStep ? styles.active : styles.pending
          }`}
          onClick={() => onStepChange && onStepChange(index)}>
          <div className={styles.content}>
            <span
              className={`${styles.title} ${
                index < currentStep
                  ? styles.titleFinished
                  : index === currentStep
                    ? styles.titleActive
                    : styles.titlePending
              }`}>
              {step.title}
            </span>
            <span className={styles.index_content}>
              {currentStep > index ? (
                <img src={'/icons/automation/check-white.svg'} width={20} height={20} alt="check" />
              ) : (
                index + 1
              )}
            </span>
          </div>
          {index !== steps.length - 1 && (
            <div
              className={`${styles.stepConnector} ${index < currentStep ? styles.lineFinished : styles.linePending}`}
            />
          )}
        </div>
      ))}
    </div>
  );
};
