import React from 'react';

interface SocialIconsProps {
   setting?: {
      instagram_link?: string;
      facebook_link?: string;
      linkedin_link?: string;
      twitter_link?: string;
   } | null;
   onClick: (platform: keyof SocialMediaLinks) => void;
}

type SocialMediaLinks = {
   instagram: 'instagram_link';
   facebook: 'facebook_link';
   linkedin: 'linkedin_link';
   twitter: 'twitter_link';
};

const socialMediaLinks: SocialMediaLinks = {
   instagram: 'instagram_link',
   facebook: 'facebook_link',
   linkedin: 'linkedin_link',
   twitter: 'twitter_link',
};

const SocialIcons: React.FC<SocialIconsProps> = ({ setting, onClick }) => {
   return (
      <ul className="social-icons">
         {setting && setting.instagram_link && (
            <li onClick={() => onClick('instagram')}>
               <span className="icon-social-set">
                  <i className="fa-brands fa-instagram"></i>
               </span>
            </li>
         )}
         {setting && setting.facebook_link && (
            <li onClick={() => onClick('facebook')}>
               <span className="icon-social-set">
                  <i className="fa-brands fa-facebook"></i>
               </span>
            </li>
         )}
         {setting && setting.linkedin_link && (
            <li onClick={() => onClick('linkedin')}>
               <span className="icon-social-set">
                  <i className="fa-brands fa-linkedin-in"></i>
               </span>
            </li>
         )}
         {setting && setting.twitter_link && (
            <li onClick={() => onClick('twitter')}>
               <span className="icon-social-set">
                  <i className="fa-brands fa-x-twitter box-sizing"></i>
               </span>
            </li>
         )}
      </ul>
   );
};

export default React.memo(SocialIcons);
