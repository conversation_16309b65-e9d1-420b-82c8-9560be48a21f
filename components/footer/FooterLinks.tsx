import React from 'react';
import Link from 'next/link';

const FooterLinks: React.FC = () => {
   return (
      <div className="row">
         <div className="col-sm-4 top-culumn">
            <Link prefetch={false} href="/">
               <p className="first-link">For Candidates</p>
            </Link>
            <Link prefetch={false} href="/for-employers">
               <p className="first-link">For Employers</p>
            </Link>
         </div>
         <div className="col-sm-3">
            <h5>Quick Links</h5>
            <ul className="ft-list">
               <li>
                  <Link prefetch={false} href="/">Home</Link>
               </li>
               <li>
                  <Link prefetch={false} href="/jobs-in-gulf"></Link>
               </li>
               <li>
                  <Link prefetch={false} href="/blog">Insights</Link>
               </li>
               <li>
                  <Link prefetch={false} href="#">Releases</Link>
               </li>
            </ul>
         </div>
         <div className="col-sm-3">
            <h5 role='heading' aria-level={5}>Company</h5>
            <ul className="ft-list">
               <li>
                  <Link prefetch={false} href="/about-us">About</Link>
               </li>
               <li>
                  <Link prefetch={false} href="#">UpdLinktes</Link>
               </li>
               <li>
                  <Link prefetch={false} href="#">Careers</Link>
               </li>
               <li>
                  <Link prefetch={false} href="#">Contact</Link>
               </li>
               <li>
                  <Link prefetch={false} href="#">Partners</Link>
               </li>
               <li>
                  <Link prefetch={false} href="/author">Authors</Link>
               </li>
            </ul>
         </div>
         <div className="col-sm-2">
            <h5 role='heading' aria-level={5}>Support</h5>
            <ul className="ft-list">
               <li>
                  <Link prefetch={false} href="#">Help Center</Link>
               </li>
               <li>
                  <Link prefetch={false} href="#">Terms of service</Link>
               </li>
               <li>
                  <Link prefetch={false} href="#">Legal</Link>
               </li>
               <li>
                  <Link prefetch={false} href="#">Privacy Policy</Link>
               </li>
            </ul>
         </div>
      </div>
   );
};

export default React.memo(FooterLinks);
