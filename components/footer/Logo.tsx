import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

interface LogoProps {
  logo?: string;
}

const Logo: React.FC<LogoProps> = ({logo}) => {
  const logoSrc = logo ? process.env.NEXT_PUBLIC_IMAGE_URL + 'images/' + logo : '/images/Avatars-4.webp';

  return (
    <div className="logo-width">
      <Link prefetch={false} className="navbar-brand" href="/">
        <img src={logoSrc} alt="logo" className="logo-head" width={200} height={60} priority />
      </Link>
    </div>
  );
};

export default React.memo(Logo);
