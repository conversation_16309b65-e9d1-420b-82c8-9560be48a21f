.sidebar_list_active li {
  border-right: none !important;
  border-radius: 100% !important;
  height: 48px;
  width: 48px;
  background: var(--color_3) !important;
}
.sidebar_list a {
  transition: none !important;
}

.sidebar_list a:hover {
  border-right: none !important;
  border-radius: 100% !important;
  height: 48px;
  width: 48px;
}
.sidebar_list a img {
  margin-right: 0px !important;
  height: 24px !important;
  width: 24px !important;
}
.sidebar_list a i {
  margin-right: 0px !important;
  height: 24px !important;
  width: 24px !important;
  display: flex;
  justify-content: center;
  align-items: center;
}
.sidebar_list .icon-a,
.sidebar_list .icon-hover {
  width: 18px;
  height: 18px;
  transition: 0.5s;
}

.sidebar_list_active .icon-a {
  display: none !important;
}

.sidebar_list_active .icon-hover {
  display: block !important;
}
.collapsible_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.collapsible_list {
  max-height: 0;
  overflow: hidden;
  transition:
    max-height 0.3s ease,
    opacity 0.3s ease;
  opacity: 0;
}

.collapsible_open {
  max-height: 300px;
  opacity: 1;
  overflow-y: auto;
}

.collapsible_closed {
  max-height: 0;
  opacity: 0;
}

.icon_rotated {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}
.sidebar_label {
  display: flex !important;
  width: 100%;
  justify-content: space-between;
  align-items: center;
}
.left_label {
  display: flex;
  align-items: center;
}
.chevron_down {
  margin-left: 12px;
}
.sidebar_child_label {
  font-size: 14px !important;
  font-weight: 400 !important;
  line-height: 140%;
  padding-left: 25px !important;
}
