import React, {useContext, Dispatch, SetStateAction} from 'react';
import Link from 'next/link';
import Image from 'next/image';
import UserProfileImage from '@/components/Common/UserProfileImage';
import AuthContext from '@/Context/AuthContext';
import styles from './sidebar.module.css';
import {useAdminSideBarItems} from '@/hooks/useSideBarItems/useAdminSideBarItem';
import {SideBarItem} from './sideBarItem';

interface SidebarProps {
  isSidebarCollapsed: boolean;
  setIsSidebarCollapsed: Dispatch<SetStateAction<boolean>>;
}

export default function Sidebar({isSidebarCollapsed, setIsSidebarCollapsed}: SidebarProps) {
  const {user} = useContext(AuthContext);
  const sideBarItems = useAdminSideBarItems();

  const handleSideBarCollapse = () => {
    setIsSidebarCollapsed(prevState => !prevState);
  };

  return (
    <div className={`left-bar ${isSidebarCollapsed && styles.left_sideBar}`}>
      <div className="text-center mt-4">
        <div
          className={`${styles['sidebar_double_arrow']} ${isSidebarCollapsed && styles['closed_sidebar']}`}
          onClick={handleSideBarCollapse}>
          <img
            src={isSidebarCollapsed ? '/icons/double_arrow_closed.svg' : '/icons/double_arrow.svg'}
            alt="toggle-icon"
            width={20}
            height={20}
          />
        </div>

        <div className="dash-profile-img mb-2 m-auto">
          <div className="pro-diamond">
            <UserProfileImage
              user={user}
              showModel={false}
              isSidebarCollapsed={isSidebarCollapsed}
              width={isSidebarCollapsed ? 56 : 118}
              height={isSidebarCollapsed ? 56 : 118}
            />
          </div>
        </div>

        {!isSidebarCollapsed && (
          <>
            <h4 className="name-text">{user?.company?.company_name}</h4>
            <h5 className="roll">{user?.role === 'admin' ? 'Admin' : user?.role === 'user' ? 'User' : null}</h5>
          </>
        )}

        <ul className="side-menu-left mt-4">
          {sideBarItems.map(item => (
            <SideBarItem key={item.route} item={item} isSidebarCollapsed={isSidebarCollapsed} setSidebarCollapsed={setIsSidebarCollapsed} />
          ))}
        </ul>
      </div>
    </div>
  );
}
