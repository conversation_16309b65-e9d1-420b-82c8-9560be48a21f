import {useEffect, useState} from 'react';
import {SideBarItems} from '@/hooks/useSideBarItems/useAdminSideBarItem';
import Link from 'next/link';
import styles from './sideBarItem.module.css';
import {useRouter} from 'next/router';

interface SideBarItemProps {
  item: SideBarItems;
  isSidebarCollapsed: boolean;
  setSidebarCollapsed: (value: boolean) => void;
}

export const SideBarItem = ({item, isSidebarCollapsed, setSidebarCollapsed}: SideBarItemProps) => {
  const {route, label, icons, customIcon, isActive, children, showChildren} = item;
  const [isExpanded, setIsExpanded] = useState(false);
  const router = useRouter();
  const toggleExpand = () => {
    setIsExpanded(prevState => !prevState);
    if (!isExpanded && isSidebarCollapsed) {
      setSidebarCollapsed(false);
    }
  };
  useEffect(() => {
    if (isSidebarCollapsed) {
      setIsExpanded(false);
    }
  },[isSidebarCollapsed]);

  return (
    <>
      <li
        className={`${isActive ? (isSidebarCollapsed ? styles.sidebar_list_active : 'active') : ''} ${
          isSidebarCollapsed && styles.sidebar_list
        }`}>
        <div className={styles.collapsible_header} onClick={children ? toggleExpand : undefined}>
          <a href={children && showChildren ? undefined : route} className={styles.sidebar_label}>
            <div className={styles.left_label}>
              {icons ? (
                <>
                  <img
                    src={icons.default}
                    alt={`${label}-icon`}
                    className={`icon-a ${isSidebarCollapsed && styles['icon-a']}`}
                  />
                  <img
                    src={icons.active}
                    alt={`${label}-icon-active`}
                    className={`icon-hover ${isSidebarCollapsed && styles['icon-hover']}`}
                  />
                </>
              ) : (
                customIcon
              )}
              {!isSidebarCollapsed && label}
            </div>
            {children && children.length > 0 && !isSidebarCollapsed && showChildren && (
              <img
                src="/icons/data-management/chevron_down.svg"
                alt="chevron"
                className={`${styles.chevron_down} ${isExpanded ? styles.icon_rotated : ''}`}
                height={12}
                width={13}
              />
            )}
          </a>
        </div>
      </li>
      {children && showChildren && (
        <ul
          className={`${styles.collapsible_list} ${isExpanded ? styles.collapsible_open : styles.collapsible_closed}`}>
          {children.map(({route, label, icons, customIcon, isActive}) => {
            return (
              <li key={route} className={isActive ? 'active' : ''}>
                <a
                  href={route}
                  className={styles.sidebar_child_label}
                  onClick={() => {
                    router.push(route);
                  }}>
                  {icons ? (
                    <>
                      <img
                        src={process.env.NEXT_PUBLIC_BASE_URL + icons.default}
                        alt={`${label}-icon`}
                        className={`icon-a ${isSidebarCollapsed && styles['icon-a']}`}
                      />
                      <img
                        src={process.env.NEXT_PUBLIC_BASE_URL + icons.active}
                        alt={`${label}-icon-active`}
                        className={`icon-hover ${isSidebarCollapsed && styles['icon-hover']}`}
                      />
                    </>
                  ) : (
                    customIcon
                  )}
                  {!isSidebarCollapsed && label}
                </a>
              </li>
            );
          })}
        </ul>
      )}
    </>
  );
};
