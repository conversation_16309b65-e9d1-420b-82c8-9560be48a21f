.sidebar_double_arrow {
  position: absolute;
  right: 16px;
  top: 16px;
  cursor: pointer;
  transition: all 0.5s;
}
.sidebar_double_arrow.closed_sidebar {
  position: relative !important;
  left: 50%;
  top: 0 !important;
  margin-top: 16px;
  margin-bottom: 20px;
  width: 20px;
  transform: translate(-50%, 0);
}
.sidebar_double_arrow.closed_sidebar img {
  transform: rotate(180deg);
}
.sidebar_double_arrow .active_arrow {
  background-color: #ebf4ff;
  border-radius: 6px;
}
.sidebar_double_arrow .active_arrow {
  display: none;
}

.sidebar_double_arrow:hover .active_arrow {
  display: block;
}

.sidebar_double_arrow:hover .inactive_arrow {
  display: none !important;
}

.left_sideBar {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  position: relative !important;
  width: auto !important;
}
