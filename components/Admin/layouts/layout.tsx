import Header from './header';
import Sidebar from './sidebar';
import { Props } from './Props';
import { useState } from 'react';
import styles from './layout.module.css';

export default function Layout({ children, ...props }: Props) {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  return (
    <div>
      <Header />
      <section className={`dashboard-page`}>
        <div>
          <div className={`${styles.row_container} `}>
            <div
              className={`bg-fff ${styles.sidebar_container} ${!!isSidebarCollapsed && styles.sidebar_collapsed_container
                } ${!isSidebarCollapsed && styles.sidebar_not_collapsed}`}>
              <Sidebar isSidebarCollapsed={!!isSidebarCollapsed} setIsSidebarCollapsed={setIsSidebarCollapsed} />
            </div>
            <div className={`col-lg-9 col-md-12 container ${styles.main_container}`} style={{ flex: "1 0 auto !important"}}>
              <main {...props}>{children}</main>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
