import React, {useState, useEffect, useRef, useContext} from 'react';
import Link from 'next/link';
import Image from 'next/image';
import moment from 'moment';

import {getAllCompanyAndJobs, getNotifications} from '@/lib/frontendapi';
import {Company, Job} from '@/lib/types';
import AuthContext from '@/Context/AuthContext';
import {Router} from 'express';
import {useRouter} from 'next/router';
import AuthUserMenu from '@/components/Common/AuthUserMenu';
import {Popover, Space} from 'antd';

export default function Header() {
  const router = useRouter();
  const [showNotifications, setShowNotifications] = useState(false);
  const [searchInputValue, setSearchInputValue] = useState('');
  const [companies, setCompanies] = useState<Company[]>([]);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [jobs, setJobs] = useState<Job[]>([]);
  const [employeedata, setEmployeeData] = useState([]);
  const anchorRef = useRef<HTMLAnchorElement | null>(null);
  const [readNotifications, setReadNotifications] = useState([]);
  const {user, logout} = useContext(AuthContext);
  const inputRef = useRef<HTMLInputElement>(null);
  const searchRef = useRef<HTMLDivElement | null>(null);

  const showBellIconOnclick = (showVal: any) => {
    setShowNotifications(showVal);
    getNotifications().then(res => {
      if (res) {
        setReadNotifications(res);
      } else {
        setReadNotifications([]);
      }
    });
    router.push('admin/settings/notifications');
  };
  const handleDropdown = () => {
    setShowDropdown(!showDropdown);
  };

  function handleLogout(e: any) {
    e.preventDefault();
    logout().then(() => {
      window.location.href = '/auth/login';
    });
  }

  useEffect(() => {
    getNotifications()
      .then(res => {
        if (res.status == true) {
          setReadNotifications(res.data);
        } else {
          setReadNotifications([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  }, []);

  function handleSearchInputChange(event: any) {
    const value = event.target.value.trim();
    setSearchInputValue(value);
    if (value.length >= 3) {
      const data = {
        value: value,
      };
      getAllCompanyAndJobs(data)
        .then(response => {
          if (response.status == true) {
            setCompanies(response.companies);
            setJobs(response.jobs);
            setEmployeeData(response.employee);
          }
        })
        .catch(error => {
          console.error(error);
        });
    }
  }

  useEffect(() => {
    window.addEventListener('click', handleClick);
    return () => {
      window.addEventListener('click', handleClick);
    };
  });
  const handleClick = (event: MouseEvent) => {
    if (anchorRef.current && event.target instanceof Node && !anchorRef.current.contains(event.target)) {
      showBellIconOnclick(false);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (inputRef.current && !inputRef.current.contains(event.target as Node) && (searchRef.current && !searchRef.current.contains(event.target as Node))) {
        setSearchInputValue('');
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <>
      <header className="head-part mar-bot-75">
        <nav className="navbar navbar-expand-lg navbar-light fixd-head fixed-top bg-fff">
          <div className="container-fluid full-width">
            <div className="logo-width">
              <Link className="navbar-brand" href="/">
                <img
                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/Avatars-4.png'}
                  alt="Talent Point"
                  width={200}
                  height={60}
                />
              </Link>
            </div>
            <button
              className="navbar-toggler collapsed"
              type="button"
              data-bs-toggle="collapse"
              data-bs-target="#navbarSupportedContent"
              aria-controls="navbarSupportedContent"
              aria-expanded="false"
              aria-label="Toggle navigation">
              <span className="navbar-toggler-icon">
                <i className="fa-solid fa-bars"></i>
                <i className="fa-solid fa-x close-x"></i>
              </span>
            </button>
            <div className="collapse navbar-collapse j-end " id="navbarSupportedContent">
              <form className="d-flex mobile-single ">
                <div className="search-in">
                  <input
                    className="form-control me-2"
                    type="search"
                    placeholder="Search for jobs, companies or candidates"
                    aria-label="Search"
                    value={searchInputValue}
                    onChange={handleSearchInputChange}
                    ref={inputRef}
                  />
                  <i className="fa-solid fa-magnifying-glass glass-ser"></i>
                  {searchInputValue && (
                    <div className="company_jobs_search" ref={searchRef}>
                      <div id="search-results" className="companysas">
                        <p className="title_heading text-start">Company</p>
                        <ul
                          className="list-unstyled"
                          id="company"
                          style={{height: companies.length >= 3 ? '150px' : 'auto'}}>
                          {companies.length > 0 ? (
                            companies.map((companies: any, index: any) => {
                              return (
                                <a
                                  key={index}
                                  target="_blank"
                                  href={`/companies/${companies.company_slug}`}
                                  className="search_result_para">
                                  <li>{companies.company_name}</li>
                                </a>
                              );
                            })
                          ) : (
                            <li className="search_result_para">
                              No company records found or enter aleast three character to get the results
                            </li>
                          )}
                        </ul>
                      </div>
                      <hr></hr>
                      <div id="search-results" className="josas">
                        <p className="title_heading  text-start">Jobs</p>
                        <ul
                          className="list-unstyled"
                          id="company"
                          style={{height: jobs.length >= 3 ? '150px' : 'auto'}}>
                          {jobs.length > 0 ? (
                            jobs.map((jobs: any, index: any) => {
                              return (
                                <a
                                  key={index}
                                  target="_blank"
                                  className="search_result_para"
                                  href={`/job/${jobs.job_slug}`}>
                                  <li>{jobs.job_title}</li>
                                </a>
                              );
                            })
                          ) : (
                            <li className="search_result_para">
                              No job records found or enter aleast three character to get the results
                            </li>
                          )}
                        </ul>
                      </div>
                      <div id="search-results" className="companysas">
                        <p className="title_heading text-start">Candidates</p>
                        <ul
                          className="list-unstyled"
                          id="company"
                          style={{height: employeedata.length >= 3 ? '150px' : 'auto'}}>
                          {employeedata.length > 0 ? (
                            employeedata.map((employee: any, index: any) => {
                              return (
                                <a
                                  key={index}
                                  target="_blank"
                                  href={`/candidate-profile/${employee.slug}`}
                                  className="search_result_para">
                                  <li>{employee.name}</li>
                                </a>
                              );
                            })
                          ) : (
                            <li className="search_result_para">
                              No candidates records found or enter aleast three character to get the results
                            </li>
                          )}
                        </ul>
                      </div>
                    </div>
                  )}
                </div>
                <div className="dask-tab-mobile-d-flex">
                  <p className="head-icon notifications">
                    {showNotifications ? (
                      <>
                        <a href="#" className="fill-bell" onClick={e => showBellIconOnclick(false)} ref={anchorRef}>
                          <i className="fa-solid fa-bell fill-bell"></i>
                        </a>
                        <div className="box-noti">
                          <div className="bell-box">
                            <div className="row">
                              <div className="col-7">
                                <h4 className="not-title">Notifications</h4>
                              </div>
                              <div className="col-5">
                                {readNotifications?.length > 0 ? <p className="mark-as">Mark as Read</p> : ''}
                              </div>
                            </div>
                            {readNotifications.length > 0 ? (
                              readNotifications.slice(0, 3).map((read_notification_data: any, index: any) => {
                                const created_at = moment.utc(read_notification_data.created_at);
                                const currentTime = moment();
                                const yesterday = moment().subtract(1, 'day');
                                let timeFormatted;

                                if (created_at.isSame(currentTime, 'day')) {
                                  timeFormatted = created_at.local().format('hh:mmA');
                                } else if (created_at.isSame(yesterday, 'day')) {
                                  timeFormatted = 'Yesterday';
                                } else {
                                  timeFormatted = created_at.local().format('MMMM D');
                                }

                                return (
                                  <div className="row mt-4" key={index}>
                                    <div className="col-2 pr-0">
                                      {read_notification_data.user_by.profile_image ? (
                                        <img
                                          src={`${process.env.NEXT_PUBLIC_IMAGE_URL}images/userprofileImg/${read_notification_data.user_by.profile_image}`}
                                          alt="Avatars-4"
                                          className="w-24"
                                          width={24}
                                          height={24}
                                          // layout='responsive'
                                        />
                                      ) : (
                                        <small
                                          title={read_notification_data.user_by.name}
                                          className="text-uppercase w-24 notfication_name">
                                          {read_notification_data.user_by.name.split(' ').length === 1
                                            ? read_notification_data.user_by.name.substring(0, 2)
                                            : read_notification_data.user_by.name
                                                .split(' ')
                                                .map((word: any, index: any) =>
                                                  index === 0 || index === 1 ? word[0] : '',
                                                )
                                                .join('')}
                                        </small>
                                      )}
                                    </div>
                                    <div className="col-10 text-left pl-0">
                                      <p
                                        className="f-16"
                                        dangerouslySetInnerHTML={{__html: read_notification_data.notification}}></p>
                                      <p className="f-12">
                                        {/* {moment.utc(read_notification_data.created_at).local().startOf('seconds').fromNow()} */}
                                        {timeFormatted}
                                      </p>
                                    </div>
                                  </div>
                                );
                              })
                            ) : (
                              <div className="text-center">
                                <img
                                  src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blank-5.png'}
                                  alt="blank-5"
                                  width="80px"
                                />
                                <p className="f-16 c-2C2C2C mb-2">No New Notifications</p>
                                <p className="f-12 c-BABABA w-400">
                                  Check this section for job updates, and general
                                  <br /> notifications.{' '}
                                </p>
                              </div>
                            )}
                            <p>
                              <Link href="/admin/notifications" className="view-all">
                                View All
                              </Link>
                            </p>
                          </div>
                        </div>
                      </>
                    ) : (
                      <a href="#" onClick={e => showBellIconOnclick(true)}>
                        <i className="fa-regular fa-bell"></i>
                      </a>
                    )}
                  </p>
                  <div className="dropdown w-400-list pog-none d-flex" id="heaedr1">
                    {/* <p
                      className="user-img tab-none "
                      id="headerdropdownMenuButton1"
                      data-bs-toggle="dropdown"
                      aria-expanded="false">
                      <img
                        src={user?.profile_image?.source || '/images/Avatars-1.png'}
                        alt={user?.profile_image?.name || user?.name || 'Employee profile image'}
                        className="w-40"
                        width={40}
                        height={40}
                      />
                    </p> */}
                    {/* {
                      <p
                        onClick={() => setShowDropdown(!showDropdown)}
                        className="head-icon dots-three"
                        id="headerdropdownMenuButton1"
                        data-bs-toggle="dropdown"
                        aria-expanded="false">
                        <i className="fa-solid fa-ellipsis-vertical" style={{ cursor: 'pointer' }}></i>
                      </p>
                    } */}
                    {/* <p
                      className="head-icon dots-three"
                      id="headerdropdownMenuButton1"
                      data-bs-toggle="dropdown"
                      aria-expanded="false">
                      <i className="fa-solid fa-ellipsis-vertical" style={{ cursor: 'pointer' }}></i>
                    </p> */}
                    {/* <ul className="dropdown-menu  m-w-100" aria-labelledby="headerdropdownMenuButton1">
                      <div className="show candidate-box  pb-3">
                        <li>
                          <Link className="dropdown-item item-2" href="/admin/settings">
                            <i className="fa-solid fa-cog"></i> Settings
                          </Link>
                        </li>
                        <li>
                          <Link className="dropdown-item item-2" href="/admin/settings/notifications">
                            <i className="fa-regular fa-bell"></i> Notifications{' '}
                          </Link>
                        </li>
                        <li>
                          <Link className="dropdown-item item-2" href="#" onClick={e => handleLogout(e)}>
                            <i className="fa fa-sign-out"></i> Logout
                          </Link>
                        </li>
                      </div>
                    </ul> */}
                    {/* {
                      showDropdown && (
                        <ul className="admin-dropdown  m-w-100" aria-labelledby="headerdropdownMenuButton1">
                          <div className="show candidate-box  pb-3">
                            <li>
                              <Link className="dropdown-item item-2" href="/admin/settings">
                                <i className="fa-solid fa-cog"></i> Settings
                              </Link>
                            </li>
                            <li>
                              <Link className="dropdown-item item-2" href="/admin/settings/notifications">
                                <i className="fa-regular fa-bell"></i> Notifications{' '}
                              </Link>
                            </li>
                            <li>
                              <Link className="dropdown-item item-2" href="#" onClick={e => handleLogout(e)}>
                                <i className="fa fa-sign-out"></i> Logout
                              </Link>
                            </li>
                          </div>
                        </ul>
                      )
                    } */}
                    <Popover content={<AuthUserMenu />} trigger={['click']}>
                      <Space>
                        <img
                          src={
                            user?.profile_image
                              ? user?.profile_image?.source
                              : `${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-1.png`
                          }
                          // src={user?.profile_image ? process.env.NEXT_PUBLIC_IMAGE_URL + user?.profile_image?.source?.replace("/storage/", '') : `${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-1.png`}
                          alt={user?.profile_image?.name || user?.name || 'Employee profile image'}
                          className="w-32"
                          width={32}
                          height={32}
                        />

                        <i className="fa-solid fa-ellipsis-vertical" style={{cursor: 'pointer'}}></i>
                      </Space>
                    </Popover>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </nav>
      </header>
    </>
  );
}
