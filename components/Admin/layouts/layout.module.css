.sidebar_collapsed_container {
  width: 80px !important;
  padding: 0 !important;
}
.sidebar_not_collapsed {
  width: 268px;
}
.sidebar_container {
  transition: width 0.3s;
  overflow-y: auto;
}

.row_container {
  display: flex;
  justify-content: start;
  gap: 12px;
  height: calc(100vh - 80px);
}
.main_container {
  max-height: calc(100vh - 80px);
  overflow-y: auto;
}

@media screen and (max-width: 991px) {
  .row_container {
    flex-direction: column;
    justify-content: center;
  }
  .sidebar_container {
    margin: 0 auto;
    width: 100%;
  }
  .main_container {
    width: 100% !important  ;
    max-width: 100%;
  }
}
