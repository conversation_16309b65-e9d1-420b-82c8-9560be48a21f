.dropDownButton {
  border-radius: 8px;
  border: 1px solid #d9d9d9 !important;
  background: #fff !important;
  display: flex;
  height: 40px;
  padding: 11px 12px 11px 16px;
  justify-content: center;
  align-items: center;
  color: #2c2c2c !important;
  font-size: 16px;
  font-weight: 500;
}
.dropDownButton:focus {
  box-shadow: none !important;
}
.dropDownButton::after {
  content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none'%3E%3Cpath d='M8.10256 10.9576C7.98418 10.9582 7.86689 10.9351 7.75755 10.8897C7.64822 10.8443 7.54905 10.7776 7.46587 10.6934L3.29734 6.48879C3.18755 6.31945 3.13779 6.11817 3.15603 5.91718C3.17428 5.7162 3.25947 5.52716 3.39796 5.38037C3.53644 5.23357 3.7202 5.13752 3.91978 5.10761C4.11936 5.0777 4.32321 5.11566 4.49864 5.2154L8.10256 8.81932L11.7065 5.2154C11.8819 5.11566 12.0858 5.0777 12.2853 5.10761C12.4849 5.13752 12.6687 5.23357 12.8072 5.38037C12.9457 5.52716 13.0308 5.7162 13.0491 5.91718C13.0673 6.11817 13.0176 6.31945 12.9078 6.48879L8.70321 10.6934C8.54403 10.8548 8.32916 10.9494 8.10256 10.9576Z' fill='%232C2C2C'/%3E%3C/svg%3E") !important;
  border: none !important ;
  margin-top: 8px;
}
.divider {
  margin: 0 !important;
}
.dropdownHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #eee !important;
  background: #fff;
}
.dropdownHeader p {
  overflow: hidden;
  color: #191919;
  text-overflow: ellipsis;
  font-size: 16px;
  font-weight: 600;
  line-height: 120%;
}
.dropdownHeader span {
  color: #747474;
  font-size: 12px;
  font-weight: 300;
  line-height: 140%;
}
.dropdownMenu {
  padding: 0;
  width: 320px;
}
.dropdownItem {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background-color: white;
  align-items: center;
  overflow: hidden;
  color: #2c2c2c;
  text-overflow: ellipsis;
  font-size: 14px;
  line-height: 120%;
}
.dropdownItem:hover {
  background-color: #0d6efd !important;
  color: white !important;
}

.dropdownItem span,
.collectionItem .number {
  display: flex;
  padding: 1px 8px;
  justify-content: center;
  align-items: center;
  border-radius: 9999px;
  border: 0.5px solid rgba(0, 85, 186, 0.08);
  background: #cfe5ff;
  color: var(--Grayscale-08, #2c2c2c);
  font-size: 12px;
  font-weight: 600;
  line-height: 140%;
}
.newCollectionText {
  display: flex;
  padding: 16px 8px 8px 8px;
  align-items: center;
  gap: 6px;
  background-color: white;
  overflow: hidden;
  color: #0070f5;
  text-overflow: ellipsis;
  font-size: 14px;
  line-height: 120%;
  cursor: pointer;
}
.newCollectionInputContainer {
  display: flex;
  padding: 8px;
  align-items: center;
  gap: 6px;
  background: #fff;
}

.newCollectionInputContainer input {
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  background: white;
}

.newCollectionInputContainer button {
  border-radius: 8px;
  background: #0055ba;
  display: flex;
  padding: 8px 12px;
  justify-content: center;
  align-items: center;
}
.choose_text {
  display: flex;
  padding: 8px;
  align-items: center;
  color: #191919;
  font-size: 14px;
  line-height: 120%;
}
.collectionItem {
  display: flex;
  padding: 8px;
  align-items: center;
  justify-content: space-between;
}

.collectionItem p {
  overflow: hidden;
  color: var(--Grayscale-08, #2c2c2c);
  text-overflow: ellipsis;
  font-size: 14px;
  line-height: 120%;
  display: flex;
  align-items: center;
  gap: 6px;
}
.modalAction_container {
  padding-top: 12px;
  display: flex;
  gap: 8px;
}
.cancel {
  display: flex;
  padding: 8px 12px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  border: 2px solid #0055ba;
  background: rgba(0, 85, 186, 0.08);
}

.done {
  border-radius: 8px;
  display: flex;
  padding: 8px 12px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex: 1 0 0;
  background: #0055ba;
}
.disabled {
  background: #61a9ff !important  ;
  color: #fff !important;
}
