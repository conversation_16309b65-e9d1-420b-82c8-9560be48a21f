import {User} from '@/lib/types';
import styles from './candidateCard.module.css';
import {Checkbox} from 'antd';
import {jobStatusToIconData} from '@/utils/jobStatusToData';
import {ToolTipUi} from '@/components/Common/TooltipUi';
import {Dispatch, SetStateAction} from 'react';

interface CandidateCardProps {
  user: User;
  setSelectedUser: Dispatch<SetStateAction<User | undefined>>;
  selectedUser?: User;
  checked: boolean;
  handleSelectUser: (user: User) => void;
  selectedUsers?: User[];
}

export const CandidateCard = ({user, setSelectedUser, selectedUser, checked, handleSelectUser}: CandidateCardProps) => {
  const toolTipValue = (status: 'ready_to_interview' | 'open_to_offer' | 'not_looking') => {
    switch (status) {
      case 'ready_to_interview':
        return 'Ready to Interview';
      case 'open_to_offer':
        return 'Open to offers';
      case 'not_looking':
        return 'Not looking';
      default:
        return 'Ready to Interview';
    }
  };

  const handleUserCardClick = (e: any) => {
    e.stopPropagation();
    setSelectedUser(user);
  };

  const handleCheckboxChange = (e: any) => {
    e.stopPropagation();
    handleSelectUser(user);
  };

  return (
    <div
      className={`${styles.candidate_card} ${user.id === selectedUser?.id && styles.active_candidate_card}`}
      onClick={handleUserCardClick}>
      <div className={styles.candidate_card_checkbox}>
        <Checkbox checked={checked} onChange={handleCheckboxChange} />
      </div>
      <div className={styles.candidate_card_content}>
        <div className={styles.candidate_name}>
          <h5>{user.name}</h5>
        </div>
        <div className={styles.candidate_info}>
          {user.current_position && <p className={styles.job_position}>{user.current_position}</p>}
          <p className={styles.candidate_email}>{user.email}</p>
        </div>
      </div>
      <div
        className={styles.candidate_card_icons}
        style={{
          backgroundColor: jobStatusToIconData(user.job_status as any)?.bgColor,
        }}>
        <ToolTipUi title={toolTipValue(user.job_status as any)}>
          <img src={jobStatusToIconData(user.job_status as any)?.href} alt="job status" />
        </ToolTipUi>
      </div>
    </div>
  );
};
