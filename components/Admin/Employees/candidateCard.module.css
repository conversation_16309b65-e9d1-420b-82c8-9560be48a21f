.candidate_card {
  display: flex;
  padding: 12px;
  align-items: start;
  gap: 8px;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0px 0px 0px 2px #f9f9f9;
  cursor: pointer;
}
.active_candidate_card {
  box-shadow: 0px 0px 0px 2px #0070f5;
}
.candidate_card_checkbox {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  align-self: stretch;
  padding-top: 3px;
}
.candidate_card_content {
  display: flex;
  width: 320px;
  flex-direction: column;
  gap: 4px;
}
.candidate_card_icons {
  display: flex;
  padding: 4px 8px;
  align-items: center;
  gap: 8px;
  border-radius: 4px;
  padding-top: 3px;
}
.candidate_card_content {
  display: flex;
  width: 320px;
  flex-direction: column;
  gap: 4px;
}
.candidate_name h5 {
  color: #2c2c2c;
  font-size: 18px;
  font-weight: 700;
  line-height: 160%;
}
.candidate_info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.candidate_info .job_position {
  color: #999;
  font-size: 16px;
  font-weight: 400;
  line-height: 140%;
}
.candidate_email {
  color: #0070f5;
  font-size: 16px;
  font-weight: 400;
  line-height: 140%;
}
.candidate_tooltip {
  background: #fff;
  color: #2c2c2c;
  font-size: 12px;
  font-weight: 400;
  line-height: 140%;
}
