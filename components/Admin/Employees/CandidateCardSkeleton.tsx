import styles from './candidateCardSkeleton.module.css';

export const CandidateCardSkeleton = () => {
   return (
      <div className={styles.candidate_card}>
         <div className={styles.candidate_card_checkbox}>
            <div className={styles.skeleton_checkbox}></div>
         </div>

         <div className={styles.candidate_card_content}>
            <div className={styles.skeleton_text} style={{ width: '120px', height: '20px' }}></div>

            <div className={styles.skeleton_text} style={{ width: '80px', height: '16px' }}></div>

            <div className={styles.skeleton_text} style={{ width: '150px', height: '16px' }}></div>
         </div>

         <div className={styles.candidate_card_icons}>
            <div className={styles.skeleton_icon}></div>
         </div>
      </div>
   );
};
