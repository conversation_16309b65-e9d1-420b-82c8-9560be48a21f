import {Button, Checkbox, Modal, Pagination, PaginationProps, notification} from 'antd';
import styles from './candidate.module.css';
import Image from 'next/image';
import {useContext, useEffect, useMemo, useState} from 'react';
import {Country, Industry, PaginationMeta, Sector, Skill, User} from '@/lib/types';
import axios from 'axios';
import ErrorHandler from '@/lib/ErrorHandler';
import {CandidateCard} from './CandidateCard';
import {CandidateDetailCard} from './CandidateDetailCard';
import {CandidateCardSkeleton} from './CandidateCardSkeleton';
import ActionDrawer from '@/components/Common/FIlterDrawer';
import qs from 'qs';
import {getAllSectors} from '@/lib/frontendapi';
import {getAllIndustries} from '@/lib/adminapi';
import {EmployeeCollection, Option} from './EmployeeCollection';
import AuthContext from '@/Context/AuthContext';
import {useHandleAction} from '@/hooks/useHandleFIlter';
import {ChipCard} from '@/components/Common/ChipCard';
import {useGetCandidateList} from '@/modules/admin/query/useGetCandidateList';

interface EmployeeCandidateProps {
  countries: Country[];
  skills: Skill[];
  variant: 'Admin' | 'Employee';
}

export const EmployeeCandidate = ({countries, skills, variant}: EmployeeCandidateProps) => {
  // const [allUsers, setAllUsers] = useState<any[]>([]);

  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [pageSize, setPageSize] = useState(10);
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
  const [selectedUserId, setSelectedUserId] = useState<number[]>([]);
  const [selectAllChecked, setSelectAllChecked] = useState(false);
  const [filterDrawerOpen, setFilterDrawerOpen] = useState(false);
  const [bulkDrawerOpen, setBulkDrawerOpen] = useState(false);

  const [sectors, setSectors] = useState<Sector[]>([]);
  const [industries, setIndustries] = useState<Industry[]>([]);
  const [isShowCollectionModal, setIsShowCollectionModal] = useState<boolean>(false);
  const [collection, setCollection] = useState<EmployeeCollection[]>();
  const [selectedCollection, setSelectedCollection] = useState<Option>();
  const [itemsNumber, setItemsNumber] = useState<number>(0);

  const {user} = useContext(AuthContext);

  const {
    chipData,
    filterData,
    handleFilterApply,
    selectedFilters,
    setSelectedFilters,
    updatedQuery,
    handleRemoveChip,
    setUpdatedQuery,
    selectedUser,
    setSelectedUser,
  } = useHandleAction({
    countries,
    industries,
    sectors,
    skills,
    variant: 'candidate',
  });

  const onShowSizeChange: PaginationProps['onShowSizeChange'] = (current, pageSize) => {
    setPage(current);
    setPageSize(pageSize);
  };

  const cancelTokenSource = axios.CancelToken.source();

  const newUpdatedQuery = {...updatedQuery};

  if (newUpdatedQuery.experience && Array.isArray(newUpdatedQuery.experience)) {
    newUpdatedQuery.experience = newUpdatedQuery.experience;
  }

  const configParams = {
    page: page.toString(),
    pageSize: pageSize.toString(),
    ...newUpdatedQuery,
  };

  const config = {
    cancelToken: cancelTokenSource.token,
    params: configParams,
    paramsSerializer: (params: any) => {
      if (params.experience) {
        return qs.stringify(params, {arrayFormat: 'repeat'});
      }
      return qs.stringify(params);
    },
  };

  const {data} = useGetCandidateList({
    config: config,
  });
  // const allUsers = data?.data;
  const allUsers = useMemo(() => {
    const fetchedUsers = data?.data || [];

    if (!selectedCollection) {
      return fetchedUsers;
    }

    const usersInSelectedCollection = collection?.find(el => el.id === selectedCollection.id)?.user_ids || [];

    // Only filter if we have both users and a non-empty collection
    if (usersInSelectedCollection.length > 0) {
      return fetchedUsers.filter(user => usersInSelectedCollection.includes(user.id));
    }

    return [];
  }, [data, selectedCollection, collection]);

  const [paginationMeta, setPaginationMeta] = useState<PaginationMeta | undefined>(data?.meta);

  useEffect(() => {
    const cancelTokenSource = axios.CancelToken.source();

    (async () => {
      try {
        const response = await getAllSectors();
        if (!!response?.success) {
          setSectors(response.sectors);
        } else {
          setSectors([]);
        }
      } catch (error) {
        ErrorHandler.showNotification(error);
      }
    })();

    (async () => {
      try {
        const response = await getAllIndustries();
        if (!!response?.success) {
          setIndustries(response.data);
        } else {
          setIndustries([]);
        }
      } catch (error) {
        ErrorHandler.showNotification(error);
      }
    })();

    return () => {
      cancelTokenSource.cancel();
    };
  }, [page, pageSize, updatedQuery, selectedCollection]);

  useEffect(() => {
    if (selectedCollection?.id) {
      (async () => {
        try {
          const response = await axios.get(`/collection/user-collection-data?collection_id=${selectedCollection?.id}`);
          if (response) {
            setItemsNumber(new Set(response.data.data).size)
            setPaginationMeta(response?.data?.meta);
          }
        } catch (error: any) {
          if (error?.response?.status === 404) {
            notification.error({
              message: 'Collection not found',
            });
          }
        }
      })();
    } else {
      setPaginationMeta(data?.meta);
    }
  }, [data?.meta, selectedCollection]);

  useEffect(() => {
    (async () => {
      try {
        if (!!user?.id) {
          const cancelTokenSource = axios.CancelToken.source();
          const response = await axios.get(`/collection/by-user?user_id=${user?.id}`, {
            cancelToken: cancelTokenSource.token,
          });
          setCollection(response.data.collections);
        }
      } catch (error) {
        ErrorHandler.showNotification(error);
      }
    })();
  }, [user, selectedCollection]);

  useEffect(() => {
    setPage(1);
  },[selectedCollection, chipData]);

  const handleSelectAll = (e: any) => {
    const isChecked = e.target.checked;
    setSelectAllChecked(isChecked);

    if (isChecked) {
      setSelectedUsers(data?.data || []);
      setSelectedUserId(
        allUsers?.map(user => {
          return user.id;
        }) || [],
      );
    } else {
      setSelectedUsers([]);
    }
  };

  const handleSelectUser = (user: User) => {
    let updatedSelectedUsers;

    if (selectedUsers.includes(user)) {
      updatedSelectedUsers = selectedUsers.filter(selected => selected.id !== user.id);
    } else {
      updatedSelectedUsers = [...selectedUsers, user];
    }

    setSelectedUsers(updatedSelectedUsers);
    setSelectedUserId(updatedSelectedUsers.map(user => user.id));

    if (updatedSelectedUsers.length === allUsers?.length) {
      setSelectAllChecked(true);
    } else {
      setSelectAllChecked(false);
    }
  };

  const handleOpenFIlter = () => {
    setFilterDrawerOpen(true);
  };

  const handleBulkDrawerOpen = () => {
    setBulkDrawerOpen(true);
  };

  const handleChipClick = () => {
    setFilterDrawerOpen(true);
  };

  const handleShowCollectionModal = () => {
    setIsShowCollectionModal(true);
  };

  const handleCollectionBack = () => {
    setSelectedCollection(undefined);
  };

  const handleResetAll = () => {
    setSelectedCollection(undefined);
    setSelectedFilters?.({});
    if (updatedQuery) {
      Object.entries(updatedQuery).forEach(([key, value]) => {
        delete updatedQuery[key];
      });
    }
    handleFilterApply({});
  };

  return (
    <div className={styles.candidate_container}>
      <div className={styles.candidate_header}>
        <div>
          {selectedCollection?.id && (
            <div className={styles.backContainer} onClick={handleCollectionBack}>
              <img
                src={'/icons/candidate/chevron-primary-left.svg'}
                width={16}
                height={16}
                alt="back"

              />
              <p>Go back</p>
            </div>
          )}
          <div className={styles.candidate_header_content}>
            <h3 className={selectedCollection?.id ? styles.selected_collection : ''}>
              {variant === 'Admin' ? 'Candidates' : !!selectedCollection ? selectedCollection.label : 'Resume Search'}
            </h3>
            <span>{selectedCollection?.id ? itemsNumber : paginationMeta?.total}</span>
          </div>
        </div>
        <div className={styles.candidate_header_action}>
          <div>
            <Button onClick={handleBulkDrawerOpen}>Bulk Actions</Button>
          </div>
          <div className={styles.filter_container} onClick={handleOpenFIlter}>
            <div className={styles.filter_icon}>
              {' '}
              <img src={'/icons/candidate/filter.svg'} width={20} height={20} alt="filter" />
              {updatedQuery && Object.keys(updatedQuery).length > 0 && <div className={styles.filter_dot} />}
            </div>
          </div>
        </div>
      </div>

      {chipData.length > 0 && (
        <div className={styles.chip_container}>
          {chipData.map((chip, index) => {
            return (
              <ChipCard
                chip={chip}
                handleChipClick={handleChipClick}
                handleRemoveChip={handleRemoveChip}
                index={index}
                key={index}
              />
            );
          })}
        </div>
      )}

      {variant === 'Employee' && (
        <EmployeeCollection
          variant="dropdown"
          isShowCollectionModal={isShowCollectionModal}
          selectedUsers={selectedUsers}
          setIsShowCollectionModal={setIsShowCollectionModal}
          collection={collection ?? []}
          setCollections={setCollection}
          setSelectedUsers={setSelectedUsers}
          selectedCollection={selectedCollection}
          setSelectedCollection={setSelectedCollection}
          setPaginationMeta={setPaginationMeta}
          setSelectedUser={setSelectedUser}
        />
      )}

      <section className={styles.candidate_section}>
        {
          <div className={styles.candidate_info_header}>
            <div className={styles.select_all_container}>
              <Checkbox checked={selectAllChecked} onChange={handleSelectAll} />
              Select All
            </div>
            {variant === 'Employee' && !selectedCollection?.id && (
              <div className={styles.selectUser_container}>
                <p onClick={handleShowCollectionModal}>Add to collection</p>
                <span>{selectedUsers.length} selected</span>
              </div>
            )}
          </div>
        }

        {allUsers?.length === 0 && !loading && (
          <div className={styles.no_available_candidate}>
            <img src={'/icons/candidate/no_candidate.svg'} height={64} width={64} alt="no_candidate" />
            <div>
              <h3>No Available Candidates to Show</h3>
              {/* <p>Add candidates to this collection by selecting them and clicking on ‘Add to Collection’</p> */}
            </div>
            <Button onClick={handleResetAll}>Go back</Button>
          </div>
        )}

        <div className={styles.candidate_info_container}>
          <div className={styles.candidate_card_container}>
            {loading ? (
              <>
                <CandidateCardSkeleton />
                <CandidateCardSkeleton />
                <CandidateCardSkeleton />
                <CandidateCardSkeleton />
                <CandidateCardSkeleton />
              </>
            ) : allUsers && allUsers?.length > 0 ? (
              allUsers?.map((user, index) => (
                <CandidateCard
                  key={index}
                  user={user}
                  setSelectedUser={setSelectedUser}
                  selectedUser={selectedUser}
                  checked={selectedUsers?.includes(user)}
                  handleSelectUser={handleSelectUser}
                />
              ))
            ) : null}
            {paginationMeta && allUsers && allUsers?.length > 0 && (
              <div className={styles.pagination_container}>
                <Pagination
                  pageSize={paginationMeta.per_page || 1}
                  onChange={onShowSizeChange}
                  total={paginationMeta.total}
                  current={paginationMeta.current_page}
                  showSizeChanger={false}
                  onShowSizeChange={onShowSizeChange}
                  showLessItems
                />
              </div>
            )}
          </div>
          <>
            {selectedUser ? (
              <CandidateDetailCard user={selectedUser} allSkills={skills} />
            ) : (
              allUsers &&
              allUsers?.length > 0 && (
                <div className={styles.no_selected_container}>
                  <div className={styles.no_selected_image}>
                    <img src={'/icons/candidate/no-selected-candidate.svg'} width={80} height={80} alt="no-selected" />
                  </div>
                  <div>
                    <p className={styles.no_selected_title}>No Candidate Selected</p>
                    <p className={styles.no_selected_description}>Select a candidate to view more details</p>
                  </div>
                </div>
              )
            )}
          </>
        </div>
      </section>
      <ActionDrawer
        isOpen={filterDrawerOpen}
        onClose={() => setFilterDrawerOpen(false)}
        filters={filterData}
        onFilterApply={handleFilterApply}
        setIsOpen={setFilterDrawerOpen}
        variant="filter"
        updatedQuery={updatedQuery}
        setUpdatedQuery={setUpdatedQuery}
        selectedFilters={selectedFilters}
        setSelectedFilters={setSelectedFilters}
      />
      <ActionDrawer
        isOpen={bulkDrawerOpen}
        onClose={() => setBulkDrawerOpen(false)}
        onFilterApply={handleFilterApply}
        setIsOpen={setBulkDrawerOpen}
        variant="bulkAction"
        selectedItems={selectedUserId}
        setSelectedItems={setSelectedUserId}
      />
    </div>
  );
};
