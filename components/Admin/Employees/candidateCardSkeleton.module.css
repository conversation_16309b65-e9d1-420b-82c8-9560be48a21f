.candidate_card {
  display: flex;
  justify-content: space-between;
  align-items: start;
  padding: 10px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
  height: 109px;
  width: 414px;
}

@keyframes skeleton-gradient {
  0% {
    background-color: #f0f0f0;
  }
  50% {
    background-color: #e0e0e0;
  }
  100% {
    background-color: #f0f0f0;
  }
}

.candidate_card_checkbox {
  width: 20px;
  height: 20px;
}

.skeleton_checkbox {
  width: 20px;
  height: 20px;
  background-color: #e0e0e0;
  border-radius: 4px;
  animation: skeleton-gradient 1.5s infinite ease-in-out;
}

.candidate_card_content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-left: 10px;
}

.skeleton_text {
  background-color: #e0e0e0;
  border-radius: 4px;
  animation: skeleton-gradient 1.5s infinite ease-in-out;
}

.candidate_card_icons {
  width: 38px;
  height: 30px;
  margin-left: 10px;
  background-color: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  animation: skeleton-gradient 1.5s infinite ease-in-out;
}

.skeleton_icon {
  width: 24px;
  height: 24px;
}
