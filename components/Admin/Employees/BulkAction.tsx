import {Button, notification} from 'antd';
import styles from './BulkAction.module.css';
import {Dispatch, SetStateAction, useEffect, useState} from 'react';
import axios from 'axios';
import moment from 'moment';
import {useExportCandidate} from '@/modules/admin/mutation/useExportCandidate';
import {useDeleteCandidate} from '@/modules/admin/mutation/useDeleteCandidate';
import {useExportEmployer} from '@/modules/admin/mutation/useExportEmployer';
import {useDeleteEmployer} from '@/modules/admin/mutation/useDeleteEmployer';
import {ButtonUi} from '@/ui/Button';

interface BulkActionProps {
  variant: 'download' | 'export' | 'delete';
  selectedItems?: number[];
  setIsOpen: Dispatch<SetStateAction<boolean>>;
  setSelectedItems?: Dispatch<SetStateAction<number[]>>;
  drawerType?: 'candidate' | 'employer';
}

export const BulkAction = ({
  variant,
  selectedItems = [],
  setIsOpen,
  setSelectedItems,
  drawerType = 'candidate',
}: BulkActionProps) => {
  const [selectedId, setSelectedId] = useState<number[]>([]);
  const [selectedAll, setSelectedAll] = useState<boolean>(false);
  const [isSelectedButtonActive, setIsSelectedButtonActive] = useState<boolean | null>(null);
  const [exportFormat, setExportFormat] = useState<'csv' | 'xls'>();
  const {mutate: exportCandidate} = useExportCandidate();
  const {mutate: deleteCandidate} = useDeleteCandidate();
  const {mutate: exportEmployer} = useExportEmployer();
  const {mutate: deleteEmployer} = useDeleteEmployer();

  const handleSelectButtonClick = () => {
    setSelectedId(selectedItems);
    setIsSelectedButtonActive(true);
    setSelectedAll(false);
  };

  const handleAllButtonClick = () => {
    setSelectedId([]);
    setIsSelectedButtonActive(false);
    setSelectedAll(true);
  };
  useEffect(() => {
    if (selectedItems.length > 0 && !selectedAll) {
      setSelectedId(selectedItems);
      setIsSelectedButtonActive(true);
    } else {
      setIsSelectedButtonActive(false);
    }
  }, [selectedItems, selectedAll]);

  const handleExportFile = async (data: any) => {
    if (exportFormat === 'csv') {
      const blob = new Blob([data], {type: 'text/csv'});
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `users.${exportFormat}`;
      a.click();
      return;
    } else if (exportFormat === 'xls') {
      const element = document.createElement('a');
      const file = new Blob([data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      element.href = URL.createObjectURL(file);
      element.download = `users-${moment().format('YYYY-MM-DD-HH-MM')}.xlsx`;
      document.body.appendChild(element);
      element.click();
      return;
    }
  };

  const handleAction = async () => {
    if (variant === 'download') {
      await axios.post('/resume/getuserresume', {
        users: selectedId,
      });
    } else if (variant === 'export') {
      if (drawerType === 'candidate') {
        exportFormat &&
          exportCandidate?.(
            {
              user_ids: selectedId,
              format: exportFormat,
            },
            {
              onSuccess: data => {
                handleExportFile(data);
              },
            },
          );
      } else {
        exportFormat &&
          exportEmployer?.(
            {
              user_ids: selectedId,
              format: exportFormat,
            },
            {
              onSuccess: data => {
                handleExportFile(data);
              },
            },
          );
      }
    } else if (variant === 'delete') {
      if (drawerType === 'candidate') {
        deleteCandidate?.(
          {
            usersId: selectedId,
          },
          {
            onSuccess: () => {
              notification.success({
                message: 'Candidate deleted successfully',
              });
              setIsOpen(false);
              setSelectedItems?.([]);
            },
          },
        );
      } else {
        deleteEmployer?.(
          {
            usersId: selectedId,
          },
          {
            onSuccess: () => {
              notification.success({
                message: 'Employer deleted successfully',
              });
              setIsOpen(false);
              setSelectedItems?.([]);
            },
          },
        );
      }
    }
  };

  const disableButton =
    variant === 'export'
      ? selectedId.length === 0 && exportFormat === null
      : selectedId.length === 0 && isSelectedButtonActive === null;

  return (
    <div className={styles.bulkActionContainer}>
      <div className={styles.buttonSelectContainer}>
        <Button
          className={`${styles.selectableButton} ${
            isSelectedButtonActive && selectedId.length > 0 ? styles.selectedButton : ''
          }`}
          disabled={selectedItems.length === 0}
          onClick={handleSelectButtonClick}>
          Selected ({selectedItems.length})
        </Button>
        <Button
          className={`${isSelectedButtonActive === false ? styles.selectedButton : ''}`}
          onClick={handleAllButtonClick}>
          All
        </Button>
      </div>

      {isSelectedButtonActive === null && (
        <div className={styles.noSelectionMessage}>
          There are no {drawerType === 'candidate' ? 'candidates' : 'employers'} selected. Please select{' '}
          {drawerType === 'candidate' ? 'candidates' : 'employers'} to use bulk actions.
        </div>
      )}

      {variant === 'export' && (
        <div className={styles.buttonSelectContainer}>
          <Button
            className={`${styles.selectableButton} ${exportFormat === 'csv' ? styles.selectedButton : ''}`}
            onClick={() => setExportFormat('csv')}>
            .csv
          </Button>
          <Button
            className={`${styles.selectableButton} ${exportFormat === 'xls' ? styles.selectedButton : ''}`}
            onClick={() => setExportFormat('xls')}>
            .xls
          </Button>
        </div>
      )}

      <div className={styles.applyButton_container}>
        <ButtonUi
          style={{
            width: '100%',
            borderRadius: '8px',
            height: '49px',
          }}
          color={variant === 'export' ? 'primary' : variant === 'delete' ? 'error' : 'primary'}
          variant="contained"
          className={`${styles.applyButton} ${variant === 'delete' ? styles.deleteButton : ''}`}
          onClick={handleAction}
          disabled={disableButton}>
          Apply Action
        </ButtonUi>
      </div>
    </div>
  );
};
