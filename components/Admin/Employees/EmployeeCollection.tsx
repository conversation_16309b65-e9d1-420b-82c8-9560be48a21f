import {Dropdown} from 'react-bootstrap';
import styles from './EmployeeCollection.module.css';
import {Button, Checkbox, Divider, Input, Modal, notification} from 'antd';
import {Dispatch, SetStateAction, useContext, useEffect, useState} from 'react';
import axios from 'axios';
import AuthContext from '@/Context/AuthContext';
import {PaginationMeta, User} from '@/lib/types';
import {useQueryClient} from 'react-query';
import {QUERY_GET_CANDIDATE_LIST} from '@/modules/admin/constants';

export interface Option {
  label: string;
  value: string;
  number: number;
  id: number;
}

export interface EmployeeCollection {
  id: number;
  name: string;
  slug: string;
  created_by: number;
  created_at: string;
  updated_at: string;
  user_ids: number[];
}

interface EmployeeCollectionProps {
  variant: 'dropdown' | 'modal';
  isShowCollectionModal: boolean;
  setIsShowCollectionModal: Dispatch<SetStateAction<boolean>>;
  selectedUsers: User[];
  collection: EmployeeCollection[];
  setCollections: Dispatch<SetStateAction<EmployeeCollection[] | undefined>>;
  setSelectedUsers: Dispatch<SetStateAction<User[]>>;
  selectedCollection?: Option;
  setSelectedCollection: Dispatch<SetStateAction<Option | undefined>>;
  setPaginationMeta: Dispatch<SetStateAction<PaginationMeta | undefined>>;
  setSelectedUser: Dispatch<SetStateAction<User | undefined>>;
}

export const EmployeeCollection = ({
  variant,
  isShowCollectionModal,
  selectedUsers,
  setIsShowCollectionModal,
  collection,
  setCollections,
  setSelectedUsers,
  selectedCollection,
  setSelectedCollection,
  setPaginationMeta,
  setSelectedUser,
}: EmployeeCollectionProps) => {
  const [inputValue, setInputValue] = useState<string>();
  const [collectionAdd, setCollectionAdd] = useState<boolean>(false);
  const queryClient = useQueryClient();
  const [selectedCollectionId, setSelectedCollectionId] = useState<string[]>();
  const {user} = useContext(AuthContext);

  const handleAddCollection = async (collectionIds: number[]) => {
    if (inputValue) {
      const res = await axios.post('/collection/addcollection', {
        id: user?.id,
        collection_id: selectedCollection?.id,
        name: inputValue,
        user_ids: [],
      });
      if (!(res.status === 200)) {
        notification.error({
          message: res?.data?.message,
        });
        return;
      }

      if (!!user?.id) {
        const cancelTokenSource = axios.CancelToken.source();
        const response = await axios.get(`/collection/by-user?user_id=${user?.id}`, {
          cancelToken: cancelTokenSource.token,
        });
        setCollections(response.data.collections);
      }

      notification.success({
        message: res?.data?.message,
      });

      setInputValue('');
      setCollectionAdd(false);
      return;
    }
    collectionIds?.map(async id => {
      try {
        const res = await axios.post('/collection/addcollection', {
          id: user?.id,
          collection_id: id,
          name: collection?.find(el => el.id === id)?.name,
          user_ids: selectedUsers.map(user => user.id),
        });

        if (!(res.status === 200)) {
          notification.error({
            message: res?.data?.message,
          });
        }

        notification.success({
          message: 'Users added to collection successfully',
        });

        if (!!user?.id) {
          const cancelTokenSource = axios.CancelToken.source();
          const response = await axios.get(`/collection/by-user?user_id=${user?.id}`, {
            cancelToken: cancelTokenSource.token,
          });
          setCollections(response.data.collections);
        }
        setInputValue('');
        setCollectionAdd(false);
        setSelectedCollectionId([]);
      } catch (error: any) {
        console.log(error);
      }
    });
  };

  useEffect(() => {
    if (selectedCollection?.id) {
      (async () => {
        try {
          const response = await axios.get(`/collection/user-collection-data?collection_id=${selectedCollection?.id}`);
          if (response) {
            queryClient.invalidateQueries(QUERY_GET_CANDIDATE_LIST);
            setPaginationMeta(response?.data?.meta);
          }
        } catch (error: any) {
          if (error?.response?.status === 404) {
            notification.error({
              message: 'Collection not found',
            });
          }
        }
      })();
    }
  }, [selectedCollection]);

  return (
    <div>
      <Modal
        open={isShowCollectionModal}
        onCancel={() => setIsShowCollectionModal(false)}
        footer={null}
        closeIcon={null}>
        <div className={styles.dropdownHeader}>
          <p>Collection</p>
          <span>{selectedUsers.length} Selected</span>
        </div>
        <Divider className={styles.divider} />
        <p className={styles.choose_text}>Choose the collections to add them to:</p>
        {collection?.map((el, index) => {
          return (
            <div key={index} className={styles.collectionItem}>
              <p>
                <Checkbox
                  checked={selectedCollectionId?.includes(el?.id?.toString())}
                  onChange={e => {
                    if (e.target.checked) {
                      setSelectedCollectionId([...(selectedCollectionId || []), el.id.toString()]);
                    } else {
                      setSelectedCollectionId(selectedCollectionId?.filter(id => id !== el.id.toString()));
                    }
                  }}
                />
                {el.name}
              </p>{' '}
              <span className={styles.number}>{new Set(el?.user_ids).size}</span>
            </div>
          );
        })}
        {collectionAdd && (
          <div className={styles.newCollectionInputContainer}>
            <Input
              onChange={e => setInputValue(e.target.value)}
              value={inputValue}
              onInput={e => {
                e.currentTarget.value = e.currentTarget.value.slice(0, 48);
              }}
            />
            <Button type="primary" onClick={() => handleAddCollection([])} disabled={!inputValue}>
              +
            </Button>
          </div>
        )}
        <p
          className={styles.newCollectionText}
          onClick={() => {
            setCollectionAdd(true);
          }}>
          + New Collection
        </p>
        <div className={styles.modalAction_container}>
          <Button type="default" className={styles.cancel} onClick={() => setIsShowCollectionModal(false)}>
            Cancel
          </Button>
          <Button
            className={`${styles.done} ${
              !selectedCollectionId?.length || selectedUsers.length === 0 ? styles.disabled : ''
            }`}
            type="primary"
            disabled={!selectedCollectionId?.length || selectedUsers.length === 0}
            onClick={() => {
              handleAddCollection(selectedCollectionId?.map(id => parseInt(id)) || []);
              setIsShowCollectionModal(false);
            }}>
            Done
          </Button>
        </div>
      </Modal>
      {variant === 'dropdown' && !selectedCollection?.id && (
        <Dropdown>
          <Dropdown.Toggle className={styles.dropDownButton}>Collections</Dropdown.Toggle>
          <Dropdown.Menu className={styles.dropdownMenu}>
            <Dropdown.Header className={styles.dropdownHeader}>
              <p>Collection</p>
            </Dropdown.Header>
            <Divider className={styles.divider} />

            {collection?.map((el, index) => {
              return (
                <Dropdown.Item
                  key={index}
                  className={styles.dropdownItem}
                  draggable={false}
                  onClick={() => {
                    setSelectedCollection({
                      id: el.id,
                      label: el.name,
                      number: new Set(el?.user_ids).size,
                      value: el.slug,
                    });
                    setSelectedUsers([]);
                    setSelectedUser?.(undefined);
                  }}
                  active={selectedCollection?.label === el.name}>
                  {el.name} <span className={styles.number}>{new Set(el?.user_ids).size}</span>
                </Dropdown.Item>
              );
            })}
            {collectionAdd && (
              <div className={styles.newCollectionInputContainer}>
                <Input
                  onChange={e => setInputValue(e.target.value)}
                  value={inputValue}
                  onInput={e => {
                    e.currentTarget.value = e.currentTarget.value.slice(0, 48);
                  }}
                />
                <Button type="primary" onClick={() => handleAddCollection([])}>
                  +
                </Button>
              </div>
            )}
            <p
              className={styles.newCollectionText}
              onClick={() => {
                setCollectionAdd(true);
              }}>
              + New Collection
            </p>
          </Dropdown.Menu>
        </Dropdown>
      )}
    </div>
  );
};
