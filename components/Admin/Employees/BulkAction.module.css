.bulkActionContainer {
  height: 100%;
}
.buttonSelectContainer {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  padding-bottom: 12px;
}

.buttonSelectContainer button {
  display: flex;
  padding: 8px 16px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  border: 1px solid #bababa;
  color: #4d4d4d;
  font-size: 14px;
}

.noSelectionMessage {
  color: #747474;
  font-size: 16px;
  line-height: 120%;
  padding-bottom: 12px;
}

.selectedButton {
  background: #0055ba !important;
  color: white !important;
}

.buttonSelectContainer button:hover {
  background: #0055ba !important;
  color: white !important;
}

.applyButton_container {
  display: flex;
  position: fixed;
  bottom: 4px;
  width: 368px;
}

.applyButton {
  display: flex;
  padding: 12px 24px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  border-radius: 8px;
  background: #0055ba;
  width: 100%;
  box-shadow: none !important;
  height: 43px;
}

.applyButton:disabled {
  background-color: #61a9ff !important;
  color: #ffffff !important;
}

.deleteButton {
  background-color: #d04e4f !important;
  color: white !important ;
}

.deleteButton:disabled {
  background-color: #ff7b7b !important;
  color: #ffffff !important;
}
