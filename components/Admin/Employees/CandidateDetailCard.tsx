import {City, Country, Skill, User} from '@/lib/types';
import styles from './candidateDetail.module.css';
import Image from 'next/image';
import {birthDateToAge} from '@/utils/birhthDateToAge';
import {capitalizeFirstLetter} from '@/utils/capitallizeFIrstLatter';
import {jobStatusToIconData} from '@/utils/jobStatusToData';
import {Button} from 'antd';
import moment from 'moment';
import {ToolTipUi} from '@/components/Common/TooltipUi';
import {useRouter} from 'next/router';
import {useEffect, useState} from 'react';
import {getCountries} from '@/lib/ApiAdapter';
import {useGetCityNameByCityId} from '@/hooks/useGetCityNameByCityId';
export const CandidateDetailCard = ({user, allSkills}: {user: User; allSkills: Skill[]}) => {
  const [country, setCountry] = useState<Country | null>(null);
  const [city, setCity] = useState<City | null>(null);
  const skills = allSkills?.filter(skill => user?.skills?.split(',').includes(skill.id.toString()));
  const router = useRouter();

  const handleNavigateCandidateProfile = () => {
    router.push(`/candidate-profile/${user.id}`);
  };

  const handleOpenResumes = () => {
    window.open(user?.resumes[0]?.url, '_blank');
  };

  useEffect(() => {
    (async () => {
      try {
        const response = await getCountries();
        const country = response.find(country => country.id.toString() === user.countries);
        if (country) {
          setCountry(country);
        }
      } catch (error) {}
    })();
  }, [user]);

  return (
    <div className={styles.job_detail_section_card}>
      <div className={styles.candidate_detail_header}>
        <div className={styles.candidate_detail_header_info}>
          <div className={styles.candidate_detail_header_personal_info}>
            <div className={styles.header_left}>
              <div className={styles.candidate_detail_profile_image}>
                <img
                  src={user?.profile_image?.source ?? '/images/Avatars-1.png'}
                  width={100}
                  height={124}
                  alt={user.name}
                  style={{objectFit: user?.profile_image?.source ? 'cover' : 'contain'}}
                />
              </div>
              <div className={styles.user_info}>
                {user?.name && (
                  <div title={user?.name} className={styles.user_name}>
                    {user?.name}
                  </div>
                )}
                {user?.current_position && <div className={styles.user_position}>{user?.current_position}</div>}
                {user?.country?.country_name && (
                  <div className={styles.user_location}>
                    <img src="/icons/candidate/location_on.svg" width={18} height={18} alt="location" />
                    <span>{user?.country?.country_name}</span>
                  </div>
                )}
                <div className={styles.user_personal_info}>
                  {user?.education?.[0]?.degree && (
                    <div className={styles.user_education}>
                      <img src={'/icons/candidate/academic-cap.svg'} width={18} height={18} alt="education" />
                      <span>{user?.education?.[0]?.degree}</span>
                    </div>
                  )}
                  {user?.date_of_birth && (
                    <div className={styles.user_dob}>
                      <img src={'/icons/candidate/frame-person.svg'} width={18} height={18} alt="age" />
                      <span>{birthDateToAge(user.date_of_birth)}</span>
                    </div>
                  )}
                  {user?.gender && (
                    <div className={styles.user_gender}>
                      <img src={'/icons/candidate/supervised-user-circle.svg'} width={18} height={18} alt="gender" />
                      <span>{capitalizeFirstLetter(user?.gender)}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className={styles.user_detail_action}>
              <div className={styles.user_status_and_link}>
                <div
                  className={styles.user_status}
                  style={{
                    backgroundColor: jobStatusToIconData(user?.job_status as any)?.bgColor,
                    border: `1px solid ${jobStatusToIconData(user?.job_status as any)?.borderColor}`,
                  }}>
                  <p style={{color: jobStatusToIconData(user?.job_status as any)?.color}}>{jobStatusToIconData(user?.job_status as any)?.name}</p>
                </div>
                <ToolTipUi title="View Profile">
                  <div className={styles.view_profile_link} onClick={handleNavigateCandidateProfile}>
                    <img src={'/icons/candidate/prime_external-link.svg'} width={18} height={18} alt="external-link" />
                  </div>
                </ToolTipUi>
              </div>
              <div className={styles.view_resume_section}>
                <div className={styles.view_resume_button}>
                  <Button disabled={user?.resumes.length === 0} onClick={handleOpenResumes}>
                    View Resume
                  </Button>
                </div>
                <span className={styles.active_status}>Active {moment(user?.updated_at).fromNow()}</span>
              </div>
            </div>
          </div>
          <div className={styles.candidate_detail_header_contact_info}>
            {user?.email && (
              <div className={styles.contact_info_item}>
                <img src={'/icons/candidate/mail.svg'} width={18} height={18} alt="email" />
                {user?.email}
              </div>
            )}
            {user.contact_no && (
              <div className={styles.contact_info_item}>
                <img src={'/icons/candidate/phone-outline-rounded.svg'} width={18} height={18} alt="phone" />
                {user?.contact_no}
              </div>
            )}
          </div>
        </div>
        {user?.bio && <p className={styles.candidate_detail_header_content}>{user?.bio}</p>}
      </div>
      {user.work_experience && user.work_experience.length > 0 && (
        <div className={styles.work_experience_container}>
          <p className={styles.work_experience_title}>Work Experience</p>
          {user.work_experience.map((workExperience, index) => (
            <div key={index} className={styles.candidate_work_experience}>
              <div className={styles.work_experience_header}>
                <div>
                  <p className={styles.work_experience_position}>{workExperience?.title}</p>
                  <p className={styles.work_experience_company}>{workExperience?.company}</p>
                </div>
                <div className={styles.work_experience_duration}>
                  <p>{moment(workExperience?.start_date).format('MM/YYYY')}</p>
                  <p>-</p>
                  <p>{workExperience?.end_date ? moment(workExperience?.end_date).format('MM/YYYY') : 'Present'}</p>
                </div>
              </div>
              <div className={styles.work_experience_content}>
                <p>{workExperience?.description}</p>
              </div>
            </div>
          ))}
        </div>
      )}
      {user.education && user.education.length > 0 && (
        <div className={styles.education_container}>
          <p className={styles.education_text}>Education</p>
          {user.education &&
            user?.education?.length > 0 &&
            user?.education?.map((education, index) => (
              <div key={index} className={styles.candidate_education}>
                <div className={styles.education_header}>
                  <div>
                    <p className={styles.education_degree}>{education?.degree}</p>
                    <p className={styles.education_title}>{education?.education_title}</p>
                  </div>
                  <div className={styles.education_duration}>
                    <p>{moment(education?.start_date).format('MMM YYYY')}</p>
                    <p>-</p>
                    <p>{education?.end_date ? moment(education?.end_date).format('MMM YYYY') : 'Present'}</p>
                  </div>
                </div>
              </div>
            ))}
        </div>
      )}

      {skills && skills.length > 0 && (
        <div className={styles.skills_container}>
          <p className={styles.skill_title}>Skills</p>
          <div className={styles.skills_list}>
            {skills.map((skill, index) => (
              <div key={index} className={styles.skill_item}>
                <p>{skill.skills}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {user.portfolio && user.portfolio.length > 0 && (
        <div className={styles.portfolio_container}>
          <p className={styles.portfolio_title}>Portfolio</p>
          {user.portfolio.map((portfolio, index) => (
            <div key={index} className={styles.candidate_portfolio}>
              <div className={styles.portfolio_header}>
                <div>
                  <p className={styles.portfolio_title}>{portfolio?.title}</p>
                  <p className={styles.portfolio_link}>
                    <img src={'/icons/candidate/link.svg'} width={16} height={16} alt="external-link" />
                    <a href={portfolio?.portfolio_link} target="_blank" rel="noreferrer">
                      {portfolio?.portfolio_link}
                    </a>
                  </p>
                </div>
                <div className={styles.portfolio_duration}>
                  <p>{moment(portfolio?.start_date).format('MMM YYYY')}</p>
                  <p>-</p>
                  <p>{portfolio?.end_date ? moment(portfolio?.end_date).format('MMM YYYY') : 'Present'}</p>
                </div>
              </div>
              <div className={styles.portfolio_content}>
                <p>{portfolio?.description}</p>
              </div>
            </div>
          ))}
        </div>
      )}

      {user.languages && user.languages.length > 0 && (
        <div className={styles.language_container}>
          <p className={styles.language_title}>Languages</p>
          <div className={styles.language_list}>
            {user.languages.map((language, index) => (
              <div key={index} className={styles.language_item}>
                <p className={styles.language_name}>{language.language}</p>
                <p className={styles.language_proficiency}>{language.proficiency}</p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
