.modal_container {
  width: 720px !important;
}
.modal_content {
  padding: 0 !important;
  border-radius: 8px !important;
}

.modal_header {
  border-radius: 8px;
  background: #cfe5ff;
  padding: 24px 24px 12px 24px;
}
.icon_container {
  position: absolute;
  top: 0;
  right: 0;
  cursor: pointer;
}

.modal_header h6 {
  color: #2c2c2c;
  font-size: 26px;
  font-weight: 500;
  line-height: 120%;
}

.modal_header P {
  color: #2c2c2c;
  font-size: 16px;
  line-height: 140%;
}

.modal_body {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 40px;
}
.date_label {
  color: #2c2c2c;
  font-size: 16px;
  font-weight: 700;
  line-height: 140%;
}
.date_value {
  color: #4d4d4d;
  font-size: 16px;
  line-height: 140%;
}
.date_container {
  display: flex;
  align-items: center;
  gap: 44px;
}

.account_info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 24px;
}

.account_info h5 {
  color: #0055ba;
  font-size: 22px;
  font-weight: 700;
  line-height: 120%;
}

.account_info h6 {
  color: #2c2c2c;
  font-size: 18px;
  font-weight: 700;
  line-height: 160%;
}
.team_members h6 {
  color: #2c2c2c;
  font-size: 22px;
  font-weight: 700;
  line-height: 120%;
}
.team_member_detail {
  display: flex;
  align-items: start;
  justify-content: space-between;
}
.team_member_right {
  width: 320px;
}
.team_member_right h6 {
  color: #2c2c2c;
  font-size: 18px;
  font-weight: 600;
  line-height: 160%; /* 28.8px */
}

.team_member_left .cv_views_available,
.jobs_available {
  display: flex;
  align-items: self-end;
  gap: 6px;
}

.cv_views_available h1,
.jobs_available h1 {
  color: #0055ba;
  font-size: 45px;
  font-weight: 700;
  line-height: 120%;
}

.cv_views_available h6,
.jobs_available h6 {
  color: #2c2c2c;
  font-size: 16px;
  font-weight: 600;
  line-height: 140%;
  margin-bottom: 8px;
}

.team_member_left span,
.featured_jobs span {
  color: #747474;
  font-size: 12px;
  line-height: 140%;
}

.team_member_info_container {
  display: flex;
  width: 320px;
  flex-direction: column;
  gap: 8px;
}

.team_member_info_content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
}

.team_user_info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.team_user_info p {
  color: #4d4d4d;
  font-size: 16px;
  line-height: 140%;
}
.team_user_info span {
  color: #747474;
  font-size: 12px;
  line-height: 140%;
}
.feature_jobs h5 {
  color: #2c2c2c;
  font-size: 22px;
  font-weight: 700;
  line-height: 120%;
}
.no_job_count {
  padding-top: 16px;
}
.no_job_count p {
  color: #747474;
  font-size: 12px;
  line-height: 140%;
}
