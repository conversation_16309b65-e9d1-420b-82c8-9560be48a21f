import NiceModal, {useModal} from '@ebay/nice-modal-react';
import {Modal} from 'antd';
import styles from './styles.module.css';
import {User, UserMembership} from '@/lib/types';
import moment from 'moment';
import Image from 'next/image';
import {ModalWrapper} from '@/components/modal';
import {ButtonUi} from '@/ui/Button';
import {ClaimStatus, useClaimCompany} from '@/modules/companies/mutation/useClaimCompany';
import {useAcceptRejectClaimCompany} from '@/modules/admin/mutation/useAcceptRejectClaimCompany';
import {companyClaimMessage} from '../CompanyClaimMessage';

interface CompanyClaimModalProps {
  user: User;
}

export const AccountInfoModal = NiceModal.create(({user}: CompanyClaimModalProps) => {
  const {hide, visible} = useModal();
  const {membership}: {membership: UserMembership} = user;
  const {claim_status} = user;
  const {name, email} = claim_status?.user || {};

  const remainingCV =
    user?.available_resume_count - user?.team_members?.reduce((acc, member) => acc + member.available_resume_count, 0);
  const {mutate: rejectClaim, isLoading} = useAcceptRejectClaimCompany();

  const handleRejectClaim = () => {
    rejectClaim(
      {
        action: 'reject',
        company_id: user?.company?.id as number,
        user_id: claim_status?.user?.id as number,
      },
      {
        onSuccess: () => {},
      },
    );
    hide();

    NiceModal.show(companyClaimMessage, {
      variant: 'rejected',
      isLoading: isLoading,
    });
  };
  return (
    <ModalWrapper
      open={visible}
      onCancel={hide}
      className={styles.modal_container}
      closeIcon={null}
      footer={null}
      headerContent
      classNames={{
        content: styles.modal_content,
      }}>
      <div className={styles.modal_body}>
        <div className={styles.account_info}>
          <h5>{membership?.plan_id === 2 ? 'PRO Account' : 'Free Account'}</h5>
          <div className={styles.date_container}>
            <div>
              <span className={styles.date_label}>From: </span>
              <span className={styles.date_value}>
                {user?.membership?.purchase_at ? moment(user?.membership?.purchase_at).format('DD MMM YYYY') : '-'}
              </span>
            </div>
            <div>
              <span className={styles.date_label}>To: </span>
              <span className={styles.date_value}>
                {user?.membership?.expire_at ? moment(user?.membership?.expire_at)?.format('DD MMM YYYY') : '-'}
              </span>
            </div>
          </div>
          {membership?.plan_id === 2 && (
            <>
              <h6>Payment Details</h6>
              <div className={styles.invoice_number}>
                <span className={styles.date_label}>Invoice Number: </span>
                <span className={styles.date_value}>{membership?.invoice_number}</span>
              </div>
              <div className={styles.information}>
                <span className={styles.date_label}>Information: </span>
                <span className={styles.date_value}>{membership?.information}</span>
              </div>
            </>
          )}
          {claim_status?.status === 'approved' && (
            <>
              <div className={styles.information}>
                <span className={styles.date_label}>Claimed by: </span>
                <span className={styles.date_value}>
                  {name}, {email} {claim_status?.claim_date ? 'on ' : ''}
                  {claim_status?.claim_date ? moment(claim_status?.claim_date).format('DD.MM.YYYY') : ''}
                </span>
              </div>

              <div>
                <ButtonUi color="error" variant="outlined-bg" onClick={handleRejectClaim}>
                  Remove Claim Status
                </ButtonUi>
              </div>
            </>
          )}
        </div>
        <div className={styles.team_members}>
          <h6>Team Members</h6>
          <div className={styles.team_member_detail}>
            <div className={styles.team_member_left}>
              <div className={styles.cv_views_available}>
                <h1>{user?.available_resume_count}</h1>
                <h6>CV Views Available</h6>
              </div>
              <span>
                Available from{' '}
                {user.membership?.purchase_at && moment(user?.membership?.purchase_at).format('DD MMM YYYY')}{' '}
                {user?.membership?.expire_at && user?.membership?.purchase_at && ' - '}
                {user?.membership?.expire_at && moment(user?.membership?.expire_at).format('DD MMM YYYY')}
              </span>
            </div>
            <div className={styles.team_member_right}>
              <h6>
                Team Members <span>({remainingCV} Remaining)</span>
              </h6>
              <div className={styles.team_member_info_container}>
                {user?.team_members?.map((member, index) => {
                  return (
                    <div key={index} className={styles.team_member_info_content}>
                      <div className={styles.team_user_info}>
                        <p>{member?.name}</p>
                        <span>{member?.email}</span>
                      </div>
                      <h6> {member?.available_resume_count} CV’s</h6>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
        <div className={styles.feature_jobs}>
          <h5>Featured Jobs</h5>
          {user?.active_job_count ? (
            <div className={styles.featured_jobs}>
              <div className={styles.jobs_available}>
                <h1>{user?.active_job_count}</h1>
                <h6>featured job posts available</h6>
              </div>
              <span>
                {' '}
                Available from{' '}
                {user.membership?.purchase_at && moment(user?.membership?.purchase_at).format('DD MMM YYYY')}{' '}
                {user?.membership?.expire_at && user?.membership?.purchase_at && ' - '}
                {user?.membership?.expire_at && moment(user?.membership?.expire_at).format('DD MMM YYYY')}
              </span>
            </div>
          ) : (
            <div className={styles.no_job_count}>
              <p>No information to display</p>
            </div>
          )}
        </div>
      </div>
    </ModalWrapper>
  );
});
