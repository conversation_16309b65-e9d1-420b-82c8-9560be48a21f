import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON>, Col, Divider, Form, Input, notification, Row, Select} from 'antd';
import axios from 'axios';
import {FilePond, registerPlugin} from 'react-filepond';
import FilePondPluginImageExifOrientation from 'filepond-plugin-image-exif-orientation';
import FilePondPluginImagePreview from 'filepond-plugin-image-preview';

import 'filepond/dist/filepond.min.css';
import 'filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css';

// import { HtmlEditor } from '@/components/Common/HtmlEditor';
import {Country, File, Sector} from '@/lib/types';
import {getCountries, getSectors} from '@/lib/ApiAdapter';
import ErrorHandler from '@/lib/ErrorHandler';
import {useForm} from 'antd/lib/form/Form';
import Image from 'next/image';
import {modules} from '@/components/Common/QuillHtmlEditor';
import 'react-quill/dist/quill.snow.css';
import dynamic from 'next/dynamic';
import NiceModal, {useModal} from '@ebay/nice-modal-react';
import {ModalWrapper} from '@/components/modal';
import styles from './EmployersForm.module.css';
import {StatusChip} from '@/components/Company/StatusChip';
import { useQueryClient } from 'react-query';
import { QUERY_GET_EMPLOYER_LIST } from '@/modules/admin/constants';

const ReactQuill = dynamic(() => import('react-quill'), {ssr: false});

interface EmployersFormProps {
  employerID?: number;
}

const EmployersForm = NiceModal.create(({employerID}: EmployersFormProps) => {
  const [description, setDescription] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [countries, setCountries] = useState<Country[]>();
  const [sectors, setSectors] = useState<Sector[]>();
  const [search, setSearch] = useState<string>();
  const [loadingCountries, setLoadingCountries] = useState(false);
  const [formRef] = useForm();
  const [selectedEmployer, setSelectedEmployer] = useState<any>();
  const [files, setFiles] = useState<any[]>([]);
  const [logo, setLogo] = useState<File>();
  const [uploadedFile, setUploadedFile] = useState<File>();
  const {remove, visible} = useModal();
  const queryClient = useQueryClient();

  registerPlugin(FilePondPluginImageExifOrientation, FilePondPluginImagePreview);

  useEffect(() => {
    if (selectedEmployer) {
      formRef.resetFields();
    }
  }, [formRef, selectedEmployer]);

  useEffect(() => {
    if (employerID) {
      const cancelTokenSource = axios.CancelToken.source();
      const config = {
        cancelToken: cancelTokenSource.token,
      };

      axios
        .get(`employers/${employerID}`, config)
        .then(response => {
          const employer = response.data.data[0];

          let formData: any = {
            user_name: employer.name,
            user_position: employer.designation,
            user_email: employer.email,
          };
          if (employer.company) {
            formData.company_name = employer.company.company_name;
            formData.slug = employer.company.company_slug;
            formData.location_id = employer.company.country.id ? parseInt(employer.company.country.id) : null;
            formData.sector_id = employer.company.sector.id ? parseInt(employer.company.sector.id) : null;
            formData.description = employer.company.company_description;
            formData.company_email = employer.company.company_email;
            formData.website = employer.company.company_website;
            formData.designation = employer.company.designation;
            formData.no_of_employees = employer.company.no_of_employees;
            formData.linkedin_link = employer.company.linkedin_link;
            formData.twitter_link = employer.company.twitter_link;
            formData.instagram_link = employer.company.instagram_link;
            formData.facebook_link = employer.company.facebook_link;
            formData.membership = employer.membership;
            setLogo(employer.company.logo);
          }
          setSelectedEmployer(formData);
        })
        .catch(e => {
          ErrorHandler.showNotification(e);
        });

      return cancelTokenSource.cancel;
    }
  }, [employerID]);

  useEffect(() => {
    const cancelTokenSource = axios.CancelToken.source();
    setLoadingCountries(true);
    getCountries(search, null, cancelTokenSource)
      .then(response => {
        setLoadingCountries(false);
        setCountries(response);
      })
      .catch(e => {
        setLoadingCountries(false);
        setCountries([]);
        ErrorHandler.showNotification(e);
      });

    getSectors(search, null, cancelTokenSource)
      .then(response => {
        setLoadingCountries(false);
        setSectors(response);
      })
      .catch(e => {
        setLoadingCountries(false);
        setCountries([]);
        ErrorHandler.showNotification(e);
      });
    return cancelTokenSource.cancel;
  }, [search]);

  const onEditorChange = (content: string) => {
    setDescription(content);
  };

  const onSubmitForm = (values: any) => {
    if (employerID) {
      updateEmployer(values);
    } else {
      createEmployer(values);
    }
  };

  const createEmployer = (values: any) => {
    setLoading(true);
    values.description = description;
    axios
      .post('employers', {...values, description, fk_logo_file_uuid: uploadedFile?.uuid})
      .then(() => {
        setLoading(false);
        // Invalidate the employer list query to force a refresh
        queryClient.invalidateQueries(QUERY_GET_EMPLOYER_LIST);
        remove();
      })
      .catch(error => {
        if (error.response) {
          notification.error({message: error?.response?.data?.error});
        }
        setLoading(false);
      });
  };

  const updateEmployer = (values: any) => {
    // Get original values to compare against
    const originalValues = selectedEmployer || {};

    // Create an object to hold only the changed values
    const changedValues: any = {};

    // Compare each field and only include changed ones
    Object.keys(values).forEach(key => {
      // Special handling for logo file
      if (key === 'fk_logo_file_uuid') {
        if (values[key] !== undefined && values[key] !== originalValues[key]) {
          changedValues[key] = values[key];
        } else if (values[key] === undefined && logo?.uuid) {
          changedValues[key] = logo.uuid;
        }
      }
      // Handle description separately since it's managed in state
      else if (key === 'description') {
        if (description !== originalValues.description) {
          changedValues[key] = description;
        }
      }
      // For all other fields, compare directly
      else if (values[key] !== originalValues[key]) {
        changedValues[key] = values[key];
      }
    });

    // Always include description from state
    changedValues.description = description;

    // Only proceed if there are changes
    if (Object.keys(changedValues).length === 0) {
      notification.info({ message: 'No changes detected' });
      remove();
      return;
    }

    setLoading(true);
    axios
      .put(`employers/${employerID}`, changedValues, {headers: {'Content-Type': 'application/json'}})
      .then(() => {
        setLoading(false);
        notification.success({ message: 'Employer updated successfully' });
        // Invalidate the employer list query to force a refresh
        queryClient.invalidateQueries(QUERY_GET_EMPLOYER_LIST);
        remove();
      })
      .catch(error => {
        if (error.response) {
          if (error?.response?.data?.error) {
            notification.error({message: error?.response?.data?.error});
            return;
          }
          Object?.values(error?.response?.data?.errors)?.forEach((err: any) => {
            notification.error({message: err});
          });
        }
        setLoading(false);
      });
  };

  return (
    <ModalWrapper
      open={visible}
      onCancel={remove}
      headerContent
      headerTitle={employerID ? 'Edit Company' : 'Add New Company'}
      closeIcon={null}
      headerDescription={
        employerID
          ? 'Edit company details & manage company profile.'
          : 'Enter company details to create a new company profile.'
      }>
      <div className={styles.employer_form}>
        <Form layout={'vertical'} onFinish={onSubmitForm} form={formRef} initialValues={selectedEmployer}>
          <Form.Item name={'fk_logo_file_uuid'} className="d-none">
            <Input className="d-none" />
          </Form.Item>
          <Divider orientation={'left'}>User information</Divider>
          <Row gutter={15}>
            <Col md={12}>
              <Form.Item label={'Owner Name'} name={'user_name'} rules={[{required: true}]}>
                <Input placeholder="Owner name" />
              </Form.Item>
            </Col>
            <Col md={12}>
              <Form.Item
                label={'Position'}
                name={'user_position'}
                rules={[{message: 'Please input your position!'}]}>
                <Input placeholder="Position" />
              </Form.Item>
            </Col>
            <Col md={12}>
              <Form.Item label={'Owner Email'} name={'user_email'} rules={[{type: 'email'}]}>
                <Input placeholder="email" />
              </Form.Item>
            </Col>
            <Col md={12}>
              <Form.Item label={'Password'} name={'password'} rules={employerID ? undefined : [{required: true}]}>
                <Input.Password placeholder="password" />
              </Form.Item>
            </Col>
          </Row>
          <div className={styles.header_container}>
            <h5>Company information</h5>
            <StatusChip
              variant={
                selectedEmployer?.membership === 'pro'
                  ? 'pro'
                  : selectedEmployer?.membership === 'free'
                    ? 'free'
                    : 'unclaimed'
              }
            />
          </div>
          <Row gutter={15}>
            <Col md={12}>
              {logo && <img src={logo.source} alt={'Logo'} width={365} height={250} style={{borderRadius: '5px'}} />}
              {uploadedFile && (
                <img src={uploadedFile.thumbnail} alt={'Logo'} width={365} height={250} style={{borderRadius: '5px'}} />
              )}
              {/*@ts-ignore*/}
              <FilePond
                files={files}
                onupdatefiles={setFiles}
                allowMultiple={false}
                acceptedFileTypes={['image/*']}
                server={{
                  url: axios.defaults.baseURL + '/file-management/files',
                  process: (fieldName, file, metadata, load, error, progress, abort) => {
                    if (!file.type.startsWith('image/')) {
                      notification.error({message: 'Only image files are allowed!'});
                      abort();
                      return;
                    }

                    const formData = new FormData();
                    formData.append(fieldName, file);

                    axios
                      .post('/file-management/files', formData, {
                        headers: {Authorization: axios.defaults.headers.common.Authorization},
                        onUploadProgress: e => progress(true, e.loaded, e.total ?? 0),
                      })
                      .then(res => {
                        load(res.data);
                        console.log(res, 'res');
                        setUploadedFile(res.data);
                      })
                      .catch(() => {
                        error('Upload failed');
                      });
                  },
                }}
                name="file"
              />
            </Col>
            <Col md={12}>
              <Form.Item label={'Company Name'} name={'company_name'} rules={[{}]}>
                <Input placeholder="Company name" />
              </Form.Item>
            </Col>
            <Col md={12}>
              <Form.Item
                label={'Country location'}
                name={'location_id'}
                rules={[]}>
                <Select
                  loading={loadingCountries}
                  showSearch={true}
                  onSearch={text => {
                    setSearch(text);
                  }}
                  placeholder={'Country'}
                  filterOption={false}
                  options={
                    (countries &&
                      countries?.map(c => {
                        return {label: c.country_name, value: c.id};
                      })) ||
                    []
                  }
                />
              </Form.Item>
            </Col>
            <Col md={12}>
              <Form.Item label={'Sector'} name={'sector_id'} rules={[{required: true}]}>
                <Select
                  loading={loadingCountries}
                  placeholder="sector"
                  showSearch={true}
                  onSearch={text => {
                    setSearch(text);
                  }}
                  filterOption={false}
                  options={
                    (sectors &&
                      sectors?.map(c => {
                        return {label: c.sector_name, value: c.id};
                      })) ||
                    []
                  }
                />
              </Form.Item>
            </Col>
            <Col md={24}>
              {/* <Form.Item label={'Description'}>
            <HtmlEditor value={selectedEmployer?.description} onChange={onDescription} />
          </Form.Item> */}
              <Form.Item label={'Description'} name="description">
                <ReactQuill value={description} onChange={onEditorChange} modules={modules} />
              </Form.Item>
            </Col>
          </Row>
          <div className="row">
            <div className="col-lg-6">
              <Form.Item label={'Company E-mail'} name={'company_email'} rules={[{required: true}]}>
                <Input placeholder="Company email" />
              </Form.Item>
            </div>
            <div className="col-lg-6">
              <Form.Item label={'Website'} name={'website'}>
                <Input placeholder="website" />
              </Form.Item>
            </div>
          </div>
          <div className="row">
            <div className="col-lg-6">
              <Form.Item label={'Designation'} name={'designation'}>
                <Input placeholder="designation" />
              </Form.Item>
            </div>
            <div className="col-lg-6">
              <Form.Item label={'Number of employees'} name={'no_of_employees'}>
                <Input placeholder="50" />
              </Form.Item>
            </div>
          </div>
          <div className="row">
            <div className="col-lg-6">
              <Form.Item label={'Linkedin'} name={'linkedin_link'} rules={[{type: 'url'}]}>
                <Input placeholder="Linkedin url" />
              </Form.Item>
            </div>
            <div className="col-lg-6">
              <Form.Item label={'Twitter'} name={'twitter_link'} rules={[{type: 'url'}]}>
                <Input placeholder="Twitter url" />
              </Form.Item>
            </div>
          </div>

          <div className="row">
            <div className="col-lg-6">
              <Form.Item label={'Instagram'} name={'instagram_link'} rules={[{type: 'url'}]}>
                <Input placeholder="Instagram url" />
              </Form.Item>
            </div>
            <div className="col-lg-6">
              <Form.Item label={'Facebook'} name={'facebook_link'} rules={[{type: 'url'}]}>
                <Input placeholder="Facebook url" />
              </Form.Item>
            </div>
          </div>
          <Button
            loading={loading}
            htmlType={'submit'}
            size={'large'}
            type={'primary'}
            style={{
              width: '100%',
            }}>
            Save
          </Button>
        </Form>
      </div>
    </ModalWrapper>
  );
});

export default EmployersForm;
