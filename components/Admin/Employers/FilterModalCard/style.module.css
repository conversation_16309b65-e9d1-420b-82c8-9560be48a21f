.dropdown {
  position: relative;
  margin-bottom: 20px;
}

.dropdownButton {
  background: #002b5c;
  color: #ffffff;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dropdownButton:hover {
  background: #005bb5;
}

.selectedValue {
  margin-left: 8px;
  font-size: 14px;
  color: #d9d9d9;
}

.caret {
  margin-left: auto;
  font-size: 12px;
}

.dropdownMenu {
  position: absolute;
  top: 35px;
  left: 0;
  display: flex;
  min-width: 160px;
  flex-direction: column;
  justify-content: start;
  align-items: start;
  border-radius: 4px;
  box-shadow:
    0px 1px 3px 0px rgba(21, 21, 21, 0.12),
    0px 2px 5px 0px rgba(21, 21, 21, 0.1),
    0px 4px 12px 0px rgba(21, 21, 21, 0.12);
  background: #fff;
  box-shadow: 0px -1px 0px 0px #edeff5 inset;
  z-index: 9999999;
}

.dropdownItem {
  padding: 8px 12px;
  font-size: 14px;
  color: #333333;
  cursor: pointer;
  transition:
    background 0.3s,
    color 0.3s;
  width: 100%;
}

.dropdownItem:hover {
  background: #f0f0f0;
}

.dropdownItem.selected {
  background: #005bb5;
  color: #ffffff;
}

.dropdownLabel {
  color: #4f4f4f;
  font-size: 12px;
  line-height: 150%;
  letter-spacing: 0.24px;
  padding: 8px 12px;
}
.star_items {
  padding-top: 15px;
  display: inline;
}
.dropdownOption {
  display: flex;
  padding: 8px 12px;
  align-items: center;
  gap: 6px;
  align-self: stretch;
  color: #2c2c2c;
  font-size: 12px;
  line-height: 150%;
  letter-spacing: 0.24px;
}
.start_container {
  display: inline-block;
}
.dropdownItem {
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.dropdownOption:hover,
.start_container:hover {
  background-color: #f0f8ff;
}

.activeItem {
  background-color: #e0f7ff;
}

.label {
  padding: 8px 12px;
  color: #4f4f4f;
  font-size: 12px;
  font-weight: 400;
  line-height: 150%;
  letter-spacing: 0.24px;
  width: 100%;
}
