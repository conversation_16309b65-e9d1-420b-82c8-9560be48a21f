import React, {useState} from 'react';
import styles from './style.module.css';
import {StarSelection} from '@/components/Company/StartSelection';

interface SortingOption {
  label: string;
  value: string;
}

interface SortingFilterProps {
  data: {
    label?: string;
    items: SortingOption[];
  };
  onSelect: (value: string) => void;
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
  type?: 'custom' | 'rating';
  selectedValue: string;
}

const SortingFilter: React.FC<SortingFilterProps> = ({
  data,
  onSelect,
  isOpen,
  setIsOpen,
  type = 'custom',
  selectedValue,
}) => {
  const handleSelect = (value: string) => {
    onSelect(value);
    setIsOpen(false);
  };

  return (
    <>
      <i className="fa-solid fa-align-left"></i>
      {isOpen && (
        <ul className={styles.dropdownMenu}>
          {data?.label && (
            <li
              className={`${styles.dropdownItem} ${selectedValue === '' ? styles.activeItem : ''}`}
              onClick={() => handleSelect('')}>
              {data?.label}
            </li>
          )}
          {type === 'rating'
            ? data?.items.map(option => (
                <li
                  key={option.value}
                  className={`${styles.dropdownItem} ${styles.star_items} ${
                    selectedValue === option.value ? styles.activeItem : ''
                  }`}
                  onClick={() => handleSelect(option.value)}>
                  <div className={styles.start_container}>
                    <StarSelection
                      type="multiple"
                      variant="notSelectable"
                      value={parseInt(option.value)}
                      onChange={() => {}}
                      size="small"
                      startType="blue"
                    />
                  </div>
                  {Number(option.value) <= 4 && ' & Up'}
                </li>
              ))
            : data?.items.map(option => (
                <li
                  key={option.value}
                  className={`${styles.dropdownItem} ${styles.dropdownOption} ${
                    selectedValue === option.value ? styles.activeItem : ''
                  }`}
                  onClick={() => handleSelect(option.value)}>
                  {option.label}
                </li>
              ))}
        </ul>
      )}
    </>
  );
};

export default SortingFilter;
