import React, {useMemo, useState} from 'react';
import {
  deleteuser,
  getStaffMember,
  getSingleStaffMember,
  updateSingleStaffMember,
  getUserAllJobs,
} from '@/lib/adminapi';
import {DeleteOutlined, EditOutlined, EyeOutlined, PlusOutlined} from '@ant-design/icons';
import Image from 'next/image';
import Link from 'next/link';
import {Button, Checkbox, notification, PaginationProps, Popconfirm, Space} from 'antd';
import ModalForm from '@/components/Common/ModalForm';
import EmployersForm from '@/components/Admin/Employers/EmployersForm';
import {PaginationMeta, User} from '@/lib/types';
import styles from './Employer.module.css';
import ActionDrawer from '@/components/Common/FIlterDrawer';
import Search from 'antd/lib/transfer/search';
import {ChipCard} from '@/components/Common/ChipCard';
import {useGetCountries} from '@/modules/common/query/useGetCountries';
import {useHandleAction} from '@/hooks/useHandleFIlter';
import SortingFilter from './FilterModalCard';
import {RatingValue, SortingValue, useGetEmployerList} from '@/modules/admin/query/useGetEmployerList';
import CommonTable from '@/components/Common/CommonTable';
import moment from 'moment';
import {useRouter} from 'next/router';
import NiceModal from '@ebay/nice-modal-react';
import {CompanyClaimModal} from './CompanyClaimModal';
import {AccountInfoModal} from './AccountInfoModal';
import {companyClaimMessage} from './CompanyClaimMessage';

interface FilterModalItems {
  label: string;
  value: string;
}

export enum SortingItem {
  RATING = 'RATING',
  TEAM = 'TEAM',
  JOB_POST = 'JOB POST',
}
export interface FilterModalCardDataProps {
  label?: string;
  items: FilterModalItems[];
  sortingItem: SortingItem;
}

export interface AllEmployer {
  current_page: 1;
  data: User[];
  meta: PaginationMeta;
}

interface Staff {
  email: string;
  name: string;
  id: number;
}

export default function Employers() {
  const [editIndices, setEditIndices] = useState(0);
  const [modalConfirm7, setModalConfirm7] = useState(false);
  const [modalConfirmJobsListing, setModalConfirmJobsListing] = useState(false);
  const [staffmember, setStaffMember] = useState<Staff[]>([]);
  const [showSection, setShowSection] = useState(false);
  const [staffemail, setSaffEmail] = useState('');
  const [getstaffdata, setGetStaffData] = useState({
    id: 0,
    email: '',
    name: '',
  });
  const [errors, setErrors] = useState<any>({});
  const [userAllJobs, setUserAllJobs] = useState([]);
  const [openEmployerForm, setOpenEmployerForm] = useState(false);
  const [reload, setReload] = useState(false);
  const [selectedEmployerID, setSelectedEmployerID] = useState<number>();
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [searchName, setSearchName] = useState<string>();
  const [pageSize, setPageSize] = useState(10);
  const [filterDrawerOpen, setFilterDrawerOpen] = useState(false);
  const [placeholderImage, setPlaceholderImage] = useState('/images/placeholder.jpg');
  const [selectedCompany, setSelectedCompany] = useState<number[]>([]);
  const [selectAllChecked, setSelectAllChecked] = useState(false);

  const [isRatingOpen, setIsRatingOpen] = useState(false);
  const [isTeamOpen, setIsTeamOpen] = useState(false);
  const [isJobPostOpen, setIsJobPostOpen] = useState(false);

  const [selectedRating, setSelectedRating] = useState<any>();
  const [selectedTeam, setSelectedTeam] = useState<any>();
  const [selectedJobPost, setSelectedJobPost] = useState<any>();
  const router = useRouter();

  const {data: countries} = useGetCountries();

  const {
    handleFilterApply,
    updatedQuery,
    setSelectedFilters,
    filterData,
    setUpdatedQuery,
    selectedFilters,
    chipData,
    handleRemoveChip,
  } = useHandleAction({
    countries: countries ?? [],
    variant: 'employers',
  });

  const [bulkDrawerOpen, setBulkDrawerOpen] = useState(false);

  const queryValue = useMemo(() => {
    const ratingMap: Record<any, RatingValue> = {
      '1': '1_and_above',
      '2': '2_and_above',
      '3': '3_and_above',
      '4': '4_and_above',
      '5': '5',
    };

    const sortingMap: Record<any, SortingValue> = {
      asc: 'low_to_high',
      desc: 'high_to_low',
    };

    return {
      rating: selectedRating ? ratingMap[selectedRating] : undefined,
      job_post: selectedJobPost ? sortingMap[selectedJobPost] : undefined,
      team_member: selectedTeam ? sortingMap[selectedTeam] : undefined,
    };
  }, [selectedRating, selectedTeam, selectedJobPost]);

  const {data: allEmployer, isLoading: isLoading, refetch} = useGetEmployerList({
    page,
    pageSize,
    searchName: searchName || '',
    params: {
      ...updatedQuery,
    },
    job_post: queryValue.job_post,
    rating: queryValue.rating,
    team_member: queryValue.team_member,
  }, {
    // Refetch every 30 seconds to catch changes made by other users
    refetchInterval: 30000,
    // Also refetch when the user returns to the tab/window
    refetchOnWindowFocus: true,
  });

  const openModalConfirm7 = () => {
    setModalConfirm7(true);
  };

  const modalConfirmClose7 = () => {
    setModalConfirm7(false);
  };

  const onShowSizeChange: PaginationProps['onShowSizeChange'] = (current, pageSize) => {
    setPage(current);
    setPageSize(pageSize);
  };

  const openModalConfirmJobsListing = (user_id: any) => {
    setModalConfirmJobsListing(true);
    const data = {
      user_id: user_id,
    };
    getUserAllJobs(data)
      .then(res => {
        if (res.status == true) {
          setUserAllJobs(res.data);
        } else {
          setUserAllJobs([]);
        }
      })
      .catch(error => {
        console.error(error);
      });
  };

  const modalConfirmJobsListingClose = () => {
    setModalConfirmJobsListing(false);
  };

  const handleeducationCancel = () => {
    setShowSection(false);
  };

  const handleDelete = (id: number) => {
    deleteuser(id, 'deleted')
      .then(res => {
        if (res.status === true) {
          notification.success({message: 'User has been deleted!'});
          setReload(!reload);
        } else {
          notification.warning({message: res.message});
        }
      })
      .catch(err => {
        notification.error(err.message);
      });
  };

  const getStaffMemberData = async (id: any) => {
    getStaffMember(id)
      .then(res => {
        if (res.status == true) {
          openModalConfirm7();
          setStaffMember(res.data);
        } else {
          notification.info({message: res.message});
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  const getSingleStaffMemberData = async (index: number) => {
    if (editIndices === index) {
      setEditIndices(0);
    } else {
      setEditIndices(index);
    }
    getSingleStaffMember(index)
      .then(res => {
        if (res.status == true) {
          setShowSection(!showSection);
          setGetStaffData(res.data);
          setSaffEmail(res.data.email);
        } else {
          console.log('error');
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  const handleStaffUpdate = (e: any) => {
    e.preventDefault();

    const newErrors: any = {};

    if (staffemail.trim() === '') {
      newErrors.staffemail = 'Email is required';
    }

    setErrors(newErrors);
    if (Object.keys(errors).length === 0) {
      const id = getstaffdata.id;
      const data = {
        email: staffemail,
      };
      updateSingleStaffMember(id, data)
        .then(res => {
          if (res.status == true) {
            modalConfirmClose7();
            handleeducationCancel();
            notification.success({message: res.message});
          } else {
            notification.error({message: res.message});
          }
        })
        .catch(err => {
          console.log(err);
        });
    }
  };

  const handleChipClick = () => {
    setFilterDrawerOpen(true);
  };

  const handleOpenFilterDrawer = () => {
    setFilterDrawerOpen(true);
  };

  const handleSelectAll = (e: any) => {
    const isChecked = e.target.checked;
    setSelectAllChecked(isChecked);

    if (isChecked) {
      setSelectedCompany(
        allEmployer?.data.map(user => {
          return user?.id;
        }) || [],
      );
    } else {
      setSelectedCompany([]);
    }
  };

  const filterModalData = {
    rating: {
      label: 'Star Rating',
      items: [
        {label: '5', value: '5'},
        {label: '4 & Up', value: '4'},
        {label: '3 & Up', value: '3'},
        {label: '2 & Up', value: '2'},
        {label: '1 & Up', value: '1'},
      ],
    },
    jobPost: {
      items: [
        {label: 'Low to High', value: 'asc'},
        {label: 'High to Low', value: 'desc'},
      ],
    },
    team: {
      items: [
        {label: 'Low to High', value: 'asc'},
        {label: 'High to Low', value: 'desc'},
      ],
    },
  };

  const handleSelect = (column: 'rating' | 'jobPost' | 'team', value: any) => {
    switch (column) {
      case 'rating':
        setSelectedRating(value);
        break;
      case 'team':
        setSelectedTeam(value);
        break;
      case 'jobPost':
        setSelectedJobPost(value);
        break;
      default:
        break;
    }
  };

  const handleManageModal = (item: User) => {
    if (item.claim_status?.status === 'requested') {
      NiceModal.show(CompanyClaimModal, {
        user: item,
      });
    } else {
      NiceModal.show(AccountInfoModal, {
        user: item,
      });
    }
  };

  const handleManageCompanyAddEdit = (id?: number) => {
    NiceModal.show(EmployersForm, {
      employerID: id,
      onSuccess: () => {
        // Manually refetch after local changes
        refetch();
      }
    });
  };

  const columns = [
    {
      key: 'company_name',
      title: (
        <th>
          <Checkbox
            onChange={handleSelectAll}
            checked={selectedCompany.length === allEmployer?.data.length && allEmployer?.data.length > 0}
          />
          <span className={styles.company_name}>COMPANY NAME</span>
        </th>
      ),
      render: (item: any, index: number) => (
        <Space>
          <Checkbox
            style={{
              paddingRight: 12,
            }}
            checked={selectedCompany.includes(item.id)}
            onChange={e => {
              const isChecked = e.target.checked;
              if (isChecked) {
                setSelectedCompany([...selectedCompany, item.id]);
              } else {
                setSelectedCompany(selectedCompany.filter(id => id !== item.id));
              }
            }}
          />
          <img
            src={item.company?.logo?.thumbnail || placeholderImage}
            alt="logo"
            width={50}
            height={50}
            className={styles.company_logo}
            onError={e => {
              setPlaceholderImage('/images/placeholder.jpg');
            }}
          />
          <div>
            <a href={'/companies/' + item.company?.id}>{item.company?.company_name}</a>
            <br />
            {item?.company?.status === 'active' && (
              <small className={styles.company_status}>
                {item?.company?.status === 'active' ? 'Active' : 'Inactive'}(
                {item?.company?.status === 'active' && item.updated_at ? moment(item.updated_at).fromNow() : ''})
              </small>
            )}
            <br />
            <p className={item?.claim_status?.status ? styles[item.claim_status?.status] : styles.not_claimed}>
              {item?.claim_status?.status === 'requested'
                ? 'Requested'
                : item?.claim_status?.status === 'approved'
                  ? 'Claimed'
                  : item?.claim_status?.status === 'rejected'
                    ? 'Rejected'
                    : 'Unclaimed'}
            </p>
          </div>
        </Space>
      ),
    },
    {
      key: 'location',
      title: <th> LOCATION</th>,
      render: (item: User, index: number) => (
        <div>
          <h6 className={styles.company_city}></h6>
          <p className={styles.company_country}>
            {countries?.find(el => el.id?.toString() === item?.company?.company_location)?.country_name}
          </p>
        </div>
      ),
    },
    {
      key: 'rating',
      title: (
        <th
          style={{
            position: 'relative',
          }}
          onMouseEnter={() => setIsRatingOpen(true)}
          onMouseLeave={() => setIsRatingOpen(false)}>
          RATING
          <SortingFilter
            data={filterModalData.rating}
            onSelect={value => handleSelect('rating', value)}
            isOpen={isRatingOpen}
            setIsOpen={setIsRatingOpen}
            type="rating"
            selectedValue={selectedRating}
          />
        </th>
      ),
      render: (item: User, index: number) => (
        <div className={styles.rating_card}>
          <div>
            <img
              src={'/icons/employer/yellow-star.svg'}
              height={12}
              width={12}
              alt="star"
              style={{
                marginTop: '-4px',
              }}
            />
            <span>{item.average_rating ?? 0}</span>
            <a
              onClick={() => {
                router.push(`/companies/${item.company?.id}/reviews`);
              }}>
              View
            </a>
          </div>
          <p>({item.total_reviews ?? 0} reviews)</p>
        </div>
      ),
    },
    {
      key: 'team',
      title: (
        <th
          style={{
            position: 'relative',
          }}
          onMouseEnter={() => setIsTeamOpen(true)}
          onMouseLeave={() => setIsTeamOpen(false)}>
          TEAM MEMBERS
          <SortingFilter
            data={filterModalData.team}
            onSelect={value => handleSelect('team', value)}
            isOpen={isTeamOpen}
            setIsOpen={setIsTeamOpen}
            selectedValue={selectedTeam}
          />
        </th>
      ),
      render: (item: any, index: number) => (
        <p className="team-m">
          {item.company?.total_staff ?? 0}{' '}
          <span className="teamm f-12">
            <a onClick={e => getStaffMemberData(item.id)}>(View)</a>
          </span>
        </p>
      ),
    },
    {
      key: 'job_post',
      title: (
        <th
          style={{
            position: 'relative',
          }}
          onMouseEnter={() => setIsJobPostOpen(true)}
          onMouseLeave={() => setIsJobPostOpen(false)}>
          JOB POST
          <SortingFilter
            data={filterModalData.jobPost}
            onSelect={value => handleSelect('jobPost', value)}
            isOpen={isJobPostOpen}
            setIsOpen={setIsJobPostOpen}
            selectedValue={selectedJobPost}
          />
        </th>
      ),
      render: (item: any, index: number) => (
        <p className="team-m">
          {item.company?.total_jobs ?? 0}{' '}
          <span className="f-12">
            <a onClick={e => openModalConfirmJobsListing(item.id)}>(View)</a>
          </span>
        </p>
      ),
    },
    {
      key: 'account',
      title: <th>ACCOUNT</th>,
      render: (item: User, index: number) => {
        return (
          <div className={styles.member_ship}>
            <img
              src={item?.membership?.plan_id === 2 ? '/images/employer/pro-card.png' : '/images/employer/free-card.png'}
              alt="Subscription"
              width={61}
              height={26}
            />
            {item?.membership?.plan_id === 2 ? (
              <span className={styles.member_exp}>
                Exp: {item?.membership?.expire_at ? moment(item?.membership?.expire_at).format('DD.MM.YYYY') : ''}
              </span>
            ) : (
              <></>
            )}
          </div>
        );
      },
    },
    {
      key: 'manage',
      title: <th>MANAGE</th>,
      render: (item: any, index: number) => (
        <Space>
          <div className={styles.manage_account_container} onClick={() => handleManageModal(item)}>
            <img src={'/icons/employer/manage-accounts.svg'} width={16} height={16} alt="eye" />
          </div>
          <div onClick={() => handleManageCompanyAddEdit(item?.id)} className={styles.edit_container}>
            <img src={'/icons/employer/edit-pencil.svg'} width={16} height={16} alt="edit" />
          </div>
          <Popconfirm title={'Are you sure to delete this employer?'} onConfirm={() => handleDelete(item.id)}>
            <div className={styles.delete_container}>
              <img src={'/icons/data-management/delete.svg'} width={16} height={16} alt="delete" />
            </div>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleBulkDrawerOpen = () => {
    setBulkDrawerOpen(true);
  };

  return (
    <>
      <div className="dash-right">
        <div className={styles.candidate_header}>
          <div>
            <div className={styles.candidate_header_content}>
              <h3 className={styles.selected_collection}>Employers</h3>
              <span>{allEmployer?.meta?.total}</span>
            </div>
          </div>
          <div className={styles.candidate_header_action}>
            <Search placeholder={'Search...'} onChange={(e: any) => setSearchName(e.target.value)} />
            <Button
              type={'primary'}
              icon={<PlusOutlined />}
              onClick={() => handleManageCompanyAddEdit()}
              className={styles.add_employer_cta}>
              New Employer
            </Button>
            <div className={styles.filter_container} onClick={handleOpenFilterDrawer}>
              <div className={styles.filter_icon}>
                {' '}
                <img src={'/icons/candidate/filter.svg'} width={20} height={20} alt="filter" />
                {updatedQuery && Object.keys(updatedQuery).length > 0 && <div className={styles.filter_dot} />}
              </div>
            </div>
          </div>
        </div>

        <div className={styles.chip_action_container}>
          {chipData.length > 0 && (
            <div className={styles.chip_container}>
              {chipData.map((chip, index) => {
                return (
                  <ChipCard
                    chip={chip}
                    handleChipClick={handleChipClick}
                    handleRemoveChip={handleRemoveChip}
                    index={index}
                    key={index}
                  />
                );
              })}
            </div>
          )}
          <div className={styles.bulk_action_cta_container}>
            <Button type="primary" ghost className={styles.bulk_action_cta} onClick={handleBulkDrawerOpen}>
              Bulk Actions
              <img
                src={'/icons/employer/prime_chevron_down.svg'}
                width={20}
                height={20}
                alt="arrow-down"
                className={styles.arrow_down_icon}
              />
            </Button>
          </div>
        </div>

        {isLoading ? (
          <div className={styles.center_container}>Loading...</div>
        ) : (
          <CommonTable
            columns={columns}
            data={allEmployer?.data ?? []}
            key={'id'}
            loading={loading}
            pagination={{
              current: allEmployer?.meta?.current_page || 1,
              total: allEmployer?.meta?.total || 0,
              pageSize: allEmployer?.meta?.per_page || 10,
              onChange: (page, pageSize) => {
                setPage(page);
                setPageSize(pageSize ?? 0);
              },
            }}
          />
        )}
        <ModalForm title={'Team Members'} open={modalConfirm7} onCancel={modalConfirmClose7}>
          <section className="container mt-4 mb-4">
            <div className="work-experience-fieild">
              <div className="row">
                {staffmember.length > 0 ? (
                  staffmember.map((staff, index) => (
                    <div className="col-lg-12 col-md-9" key={index}>
                      <div className="right-text-edit mb-3">
                        <div className="row mobile-column-reverse">
                          <div className="col-sm-9">
                            <h6>{staff.name}</h6>
                            <p>
                              <strong>{staff.email}</strong>
                            </p>
                          </div>
                          <div className="col-sm-3 text-right">
                            <div className="edit-pi">
                              <i
                                className="fa-solid fa-square-pen"
                                data-id={'staff_id_' + staff.id}
                                onClick={() => getSingleStaffMemberData(Number(staff.id))}></i>
                            </div>
                          </div>
                        </div>

                        {editIndices === Number(staff.id) && (
                          <form className="form-experience-fieild" onSubmit={handleStaffUpdate}>
                            <div className="form_field_sec">
                              <input
                                type="email"
                                placeholder="Email"
                                name="staffemail"
                                className="fild-des"
                                value={staffemail}
                                onChange={e => setSaffEmail(e.target.value)}
                              />
                              <label>Email*</label>
                            </div>
                            {errors.staffemail && <p className="error">{errors.staffemail}</p>}
                            <div className="text-right mt-3">
                              <a
                                className="cancel cancel-modal"
                                onClick={() => {
                                  modalConfirmClose7();
                                  handleeducationCancel();
                                }}>
                                Cancel
                              </a>
                              <button className="save" type="submit">
                                Update
                              </button>
                            </div>
                          </form>
                        )}
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-center mt-3">No Team Members</p>
                )}
              </div>
            </div>
          </section>
        </ModalForm>
        <ModalForm
          title={'Employers jobs listing'}
          open={modalConfirmJobsListing}
          onCancel={modalConfirmJobsListingClose}>
          <section className="container mt-4 mb-4">
            <div className="work-experience-fieild">
              <div className="row">
                {userAllJobs.length > 0 ? (
                  userAllJobs.map((jobs_data: any, index: any) => (
                    <div className="col-lg-12 col-md-9" key={index}>
                      <div className="right-text-edit mb-3">
                        <div className="row mobile-column-reverse">
                          <div className="col-sm-12">
                            <h6>
                              <a href={'/job/' + jobs_data.job_slug}>{jobs_data.job_title}</a>
                            </h6>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="col-lg-12 col-md-9">
                    <div className="right-text-edit mb-3">
                      <div className="row mobile-column-reverse">
                        <div className="col-sm-12">
                          <p>No any jobs posted</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </section>
        </ModalForm>
        <ActionDrawer
          isOpen={filterDrawerOpen}
          onClose={() => setFilterDrawerOpen(false)}
          filters={filterData}
          onFilterApply={handleFilterApply}
          setIsOpen={setFilterDrawerOpen}
          variant="filter"
          updatedQuery={updatedQuery}
          setUpdatedQuery={setUpdatedQuery}
          selectedFilters={selectedFilters}
          setSelectedFilters={setSelectedFilters}
        />
        <ActionDrawer
          isOpen={bulkDrawerOpen}
          onClose={() => setBulkDrawerOpen(false)}
          onFilterApply={handleFilterApply}
          setIsOpen={setBulkDrawerOpen}
          variant="bulkAction"
          selectedItems={selectedCompany}
          setSelectedItems={setSelectedCompany}
          drawerType="employer"
        />
      </div>
    </>
  );
}
