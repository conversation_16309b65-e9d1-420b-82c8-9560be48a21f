import {ModalWrapper} from '@/components/modal';
import NiceModal, {useModal} from '@ebay/nice-modal-react';
import styles from './style.module.css';
import Image from 'next/image';

interface CompanyClaimModalProps {
  variant: 'accepted' | 'rejected';
  isLoading: boolean;
}

export const companyClaimMessage = NiceModal.create(({variant, isLoading}: CompanyClaimModalProps) => {
  const {hide, visible} = useModal();
  return (
    <ModalWrapper
      open={visible}
      onCancel={hide}
      className={styles.modal_container}
      closeIcon={null}
      headerContent
      classNames={{
        content: styles.modal_content,
      }}>
      {isLoading ? (
        <div className={styles.loading_container}>
          <img src={'/icons/employer/icon_loading.svg'} height={48} width={48} alt="loading" />
          <p>Updating Account Information...</p>
        </div>
      ) : (
        <div className={styles.claim_message}>
          <h6>Company Claim Request {variant === 'accepted' ? 'Approved' : 'Rejected'}</h6>
          <p>
            The account claimant will be promptly notified shortly{' '}
            {variant === 'rejected' && ' and status will revert to ‘Unclaimed’.'}
          </p>
        </div>
      )}
    </ModalWrapper>
  );
});
