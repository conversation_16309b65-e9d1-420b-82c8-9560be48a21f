.candidate_header {
  display: flex;
  gap: 24px;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 24px;
}
.backContainer {
  display: flex;
  align-items: center;
  gap: 4px;
  padding-bottom: 8px;
}
.backContainer p {
  color: #0070f5;
  font-size: 12px;
  font-weight: 600;
  line-height: 140%;
}
.backContainer img {
  cursor: pointer;
}
.candidate_header_content {
  display: flex;
  align-items: center;
  gap: 8px;
}
.candidate_header_content h3 {
  color: #191919;
  font-size: 31px;
  font-weight: 500;
  line-height: 120%;
}

.candidate_header_content span {
  display: flex;
  padding: 2px 8px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 9999px;
  border: 0.5px solid rgba(0, 85, 186, 0.08);
  background: #cfe5ff;
  color: var(--Grayscale-08, #2c2c2c);
  font-size: 12px;
  font-weight: 600;
  line-height: 140%;
}
.bulk_action_cta {
  display: flex;
  height: 40px;
  padding: 11px 12px 11px 16px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  color: #0055ba;
  font-size: 16px;
  font-weight: 500;
  line-height: 120%; /* 19.2px */
  border-radius: 8px;
  border: 1px solid #0055ba;
  background-color: transparent;
  align-items: center;
  gap: 4px;
}
.arrow_down_icon {
  padding-top: 2px;
}
.candidate_header_action {
  display: flex;
  align-items: center;
  gap: 8px;
}
.candidate_header_action input {
  display: flex;
  width: 263px;
  flex-direction: column;
  align-items: flex-start;
  display: flex;
  height: 31px;
  align-items: center;
  border-radius: 4px;
  border: 1px solid #bababa;
  background: #fff;
}

.add_employer_cta {
  display: flex;
  height: 40px;
  padding: 11px 12px 11px 16px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: 8px;
  border: 1px solid #0055ba;
  background: #0055ba;
}

.rating_card div {
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 90px;
}
.rating_card div span {
  color: #0070f5;
  font-size: 14px;
  font-weight: 700;
  line-height: 150%;
  letter-spacing: 0.28px;
}

.rating_card a {
  color: #0070f5 !important;
  font-size: 14px;
  line-height: 150%;
  letter-spacing: 0.28px;
  text-decoration-line: underline !important;
  text-decoration-style: solid;
  text-decoration-skip-ink: none;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
}
.rating_card p {
  overflow: hidden;
  color: #747474;
  text-overflow: ellipsis;
  font-size: 12px;
  font-weight: 400;
  line-height: 150%;
  letter-spacing: 0.24px;
}
.candidate_header_action .filter_container {
  display: flex;
  width: 40px;
  height: 40px;
  padding: 10px;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  border: 1px solid rgba(0, 85, 186, 0.08);
  background: #cfe5ff;
  cursor: pointer;
}
.filter_icon {
  position: relative;
}
.filter_dot {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #fdca40;
  height: 8px;
  width: 8px;
  border-radius: 50%;
}
.chip_container {
  display: flex;
  align-items: flex-start;
  align-content: flex-start;
  gap: 4px;
  flex-wrap: wrap;
  cursor: pointer;
}
.bulk_action_cta_container {
  display: flex;
  justify-self: flex-end;
}
.company_name {
  padding-left: 20px;
}
.center_container {
  min-height: 400px;
  background-color: white;
  display: flex;
  justify-content: center;
  align-items: center;
}
.company_status {
  color: #999;
  font-size: 12px;
  line-height: 140%;
}
.not_claimed,
.rejected {
  color: #4d4d4d;
  font-size: 12px;
  line-height: 140%;
}
.requested {
  color: #d57b11;
  font-size: 12px;
  line-height: 140%;
}
.approved {
  color: #3d9f79;
  font-size: 12px;
  line-height: 140%;
}
.company_country {
  color: #bababa;
  font-size: 12px;
  font-weight: 600;
  line-height: 140%;
}
.member_exp {
  color: #4f4f4f;
  font-size: 12px;
  font-weight: 300;
  line-height: 140%; /* 16.8px */
}
.member_ship {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.manage_account_container,
.edit_container,
.delete_container {
  display: flex;
  padding: 4px;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  background: #ebf4ff;
}

.delete_container {
  background-color: #ffebeb !important;
}
