import {ModalWrapper} from '@/components/modal';
import NiceModal, {useModal} from '@ebay/nice-modal-react';
import styles from './style.module.css';
import {ButtonUi} from '@/ui/Button';
import Image from 'next/image';
import {User} from '@/lib/types';
import {useAcceptRejectClaimCompany} from '@/modules/admin/mutation/useAcceptRejectClaimCompany';
import {useContext, useEffect} from 'react';
import AuthContext from '@/Context/AuthContext';
import CompanyClaim from '@/components/Company/CompanyClaim';
import {companyClaimMessage} from '../CompanyClaimMessage';
import moment from 'moment';

interface CompanyClaimModalProps {
  user: User;
}

export const CompanyClaimModal = NiceModal.create(({user}: CompanyClaimModalProps) => {
  const {hide, visible} = useModal();
  const {mutate: claimAction, isLoading} = useAcceptRejectClaimCompany();

  const handleApproveClaim = () => {
    user?.company?.id &&
      claimAction(
        {user_id: user?.claim_status?.user.id, company_id: user?.company?.id, action: 'approve'},
        {
          onSuccess: () => {
            hide();
          },
        },
      );

    NiceModal.show(companyClaimMessage, {
      variant: 'accepted',
      isLoading: isLoading,
    });
  };

  const handleRejectClaim = () => {
    user?.company?.id &&
      claimAction({user_id: user?.claim_status?.user?.id, company_id: user?.company?.id, action: 'reject'});

    hide();
    NiceModal.show(companyClaimMessage, {
      variant: 'rejected',
      isLoading: isLoading,
    });
  };
  return (
    <ModalWrapper
      open={visible}
      onCancel={hide}
      className={styles.modal_container}
      closeIcon={null}
      headerContent
      classNames={{
        content: styles.modal_content,
      }}>
      <div className={styles.modal_body}>
        <h6>Company Claim Request</h6>
        <p className={styles.claim_message}>
          This company profile has been claimed. Review the information below and proceed with an action.
        </p>

        <div className={styles.user_info_container}>
          <div>
            <div>
              <span className={styles.info_label}>Name: </span>
              <span className={styles.info_value}>{user?.claim_status?.user?.name}</span>
            </div>
          </div>
          <div>
            <div>
              <span className={styles.info_label}>Designation: </span>
              <span className={styles.info_value}>{user?.claim_status?.user?.role}</span>
            </div>
          </div>
          <div className={styles.flex_container}>
            {!!user?.claim_status?.user?.email && (
              <div>
                <span className={styles.info_label}>Email ID: </span>
                <span className={styles.info_value}>{user?.claim_status?.user?.email}</span>
              </div>
            )}

            {/* <div>
              <span className={styles.info_label}>Contact No:</span>
              <span className={styles.info_value}>+91 9876543210</span>
            </div> */}
          </div>
          {!!user?.claim_status?.claim_date && (
            <div>
              <span className={styles.info_label}>Claimed on: </span>
              <span className={styles.info_value}>{moment(user?.claim_status?.claim_date).format('DD.MM.YYYY')}</span>
            </div>
          )}
          {/* <div className={styles.message_container}>
            <span className={styles.info_label}>Message:</span>
            <span className={styles.info_value}>
              Hi, I’m the Head of talent at Connect Resources and I would like to claim our company profile for job
              posting.
            </span>
          </div> */}
        </div>
        <div className={styles.action_button_container}>
          <ButtonUi variant="outlined" color="error" className={styles.reject_button} onClick={handleRejectClaim}>
            <img src={'/icons/employer/icon-cross.svg'} alt="cross" width={16} height={16} />
            Reject
          </ButtonUi>
          <ButtonUi variant="contained" color="primary" className={styles.approve_button} onClick={handleApproveClaim}>
            <img src={'/icons/employer/icon-check.svg'} alt="tick" width={16} height={16} />
            Approve
          </ButtonUi>
        </div>
      </div>
    </ModalWrapper>
  );
});
