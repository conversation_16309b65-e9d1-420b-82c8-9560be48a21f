import { appendValueForJobTags } from "@/lib/constant";
import { HtmlEditor } from "@/components/Common/HtmlEditor";
import { Col, Form, Input, Row, Select, Button } from "antd";
import { PlusCircleOutlined, DeleteOutlined } from '@ant-design/icons';
import React, { Fragment } from "react";
import { Controller, useFieldArray } from "react-hook-form";
// const countryOptions = [
//    {
//  value:"Uae",
//  label:"uae"
// },
// {
//    value:"Argentina",
//    label:"Argentina"
//   },
//   {
//    value:"India",
//    label:"India"
//   },
//   {
//    value:"Austrlia",
//    label:"Austrlia"
//   },
//   {
//    value:"Brazil",
//    label:"Brazil"
//   },
// ]



// const keywordOptions = [
//    {
//  value:"Accounting",
//  label:"Accounting"
// },
// {
//    value:"Graphic designer",
//    label:"Graphic designer"
//   },
//   {
//    value:"Managemnt",
//    label:"Managemnt"
//   },
//   {
//    value:"React",
//    label:"React"
//   },
//   {
//    value:"Fianance",
//    label:"Fianance"
//   },
// ]

interface BlogContentSectionProps {
   jobSearchTags: any,
   control: any,
   register: any,
   errors: any,
   countryData: any;
   keywordData: any;
}
type JobSearchTagsType= {
   keyword: string,
   country: string,
   name?:string
}
const JobSearchFieldSection = ({ control, register, jobSearchTags, errors,countryData, keywordData }:BlogContentSectionProps) => {
   const { fields:searchTagFields, remove:removeJobSearchTag, append:appendJobSearchTag } = useFieldArray({
      control,
      name: "job_searches"
   })

   const countryOptions = countryData?.map((country :any) => ({
      value: country.slug,
      label: country.country_name
  }));

  const keywordOptions = keywordData?.map((keyword :any) => ({
   value: keyword.id,
   label: keyword.job_title
}));
  

   const handleAppend = () => {
      const index = jobSearchTags.findIndex((curElem: any) => curElem?.country === "" || curElem?.keyword === "")
      if (index === -1) {
         // means none of the field is empty and we can append the value
         appendJobSearchTag(appendValueForJobTags)
      }
   }
   console.log(jobSearchTags,"job search tags")

   const handleKeywordChange = (selectedId: number,field:any) => {
      const selectedKeyword = keywordData.find((keyword: any) => keyword.id === selectedId);
      field.onChange(selectedKeyword?.job_title)
    };
   return (
      <>
         {
            searchTagFields.map((field, index) => (
               <Fragment key={field.id}>
                  <Col md={(jobSearchTags?.length !== 1) ? 11 :12}>
                     <label className="blog-label">Job search keyword value</label> 
                     <Controller control={control} name={`job_searches.${index}.keyword`} rules={{required:"Please select keyword"}} render={({field})=>(
                        <Select
                        {...field}
                        onChange={(selectedOption)=>
                           handleKeywordChange(selectedOption,field)}
                           // field.onChange(selectedOption)}
                       className='blog-select'
                       placeholder='Select Keyword'
                       options={keywordOptions}
                            />

                     )}/>    
                     {errors?.job_searches?.[index]?.keyword && (
                        <p
                           className={`error-field`}
                        >
                           {errors?.job_searches?.[index].keyword?.message}
                        </p>
                     )}
                  </Col>
                  <Col md={(jobSearchTags?.length !== 1) ? 11 :12}>
                     <label className="blog-label">Job search keyword country value</label>
                     <Controller control={control} name={`job_searches.${index}.country`} rules={{required:"Please select country"}} render={({field})=>(
                        <Select
                        {...field}
                        onChange={(selectedOption)=>field.onChange(selectedOption)}
                       className='blog-select'
                       placeholder={'Select Country'}
                       options={countryOptions}
                         />

                     )}/> 
                     {errors?.job_searches?.[index]?.country && (
                        <p
                           className={`error-field`}
                        >
                           {errors?.job_searches?.[index].country?.message}
                        </p>
                     )}
                  </Col>
                  {
                     jobSearchTags?.length !== 1 && (
                        <Col md={2}>
                        <Button
                           onClick={() => removeJobSearchTag(index)}
                           className="delete-button"
                           type={'text'}
                           icon={<DeleteOutlined className="delete-icon" />}
                        />
                        </Col>
                     )
                  }
               </Fragment>
            ))
         }
         <Button
            onClick={handleAppend}
            className="add-button"
            type={'text'}
            icon={<PlusCircleOutlined className="plus-icon" />}
         />
      </>
   )

}
export default JobSearchFieldSection
