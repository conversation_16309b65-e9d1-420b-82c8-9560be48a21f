// import { HtmlEditor } from "@/components/Common/HtmlEditor";
import { Col, Form, Input, Row, Select, Button } from "antd";
import { PlusCircleOutlined, DeleteOutlined } from '@ant-design/icons';
import { QuillHtmlEditor } from "@/components/Common/QuillHtmlEditor";

import React, { Fragment } from "react";
import { Controller, useFieldArray } from "react-hook-form";
import { appendValueForBlogContent } from "@/lib/constant";

interface BlogContentSectionProps {
   blogContent: any,
   handleFieldChange: (fieldName: string, key: string, value: any, index: number) => void,
   handleAppend: (fieldName: string) => void,
   handleRemove: (fieldName: string, indexToRemove: number) => void
   control: any,
   register: any,
   errors: any
}
interface BlogContent {
   article_content_link: string,
   article_content_desc: string
}

const BlogContentSection = ({ blogContent, control, handleRemove, handleFieldChange, register, errors }: BlogContentSectionProps) => {
   const { fields, remove, append } = useFieldArray({
      control,
      name: "articles"
   })
   const handleBlogAppend = () => {
      const index = blogContent.findIndex((curElem: any) => curElem?.article_content_link === "" || curElem?.article_content_desc === "")
      const valueToAppend = appendValueForBlogContent
      // if (index === -1) {
      // means none of the field is empty and we can append the value
      append(valueToAppend)
      // }
   }
   return (
      <>
         {
            fields.map((field, index) => (
               <Fragment key={field.id}>
                  {/* <Col md={(blogContent?.length !== 1) ? 11 : 12}>
                     <label className="blog-label">Blog Link</label>
                     <input className={"blog-field ant-input ant-input-outlined"} placeholder="Enter Table of content link " {...register(`articles.${index}.article_content_link`, {
                        required: "Please enter table of content link",
                     })} />
                     {errors?.articles?.[index]?.article_content_link && (
                        <p
                           className={`error-field`}
                        >
                           {errors?.articles?.[index].article_content_link?.message}
                        </p>
                     )}
                  </Col> */}
                  <Col md={24}>
                     <label className="blog-label">Table Of Content Value</label>
                     <Controller control={control} name={`articles.${index}.article_content_desc`} rules={{ required: "Please enter table of content value" }} render={({ field }) => (
                        <QuillHtmlEditor {...field} onChange={(name, value) => field.onChange(value)} />
                     )} />

                     {errors?.articles?.[index]?.article_content_desc && (
                        <p
                           className={`error-field`}
                        >
                           {errors?.articles?.[index].article_content_desc?.message}
                        </p>
                     )}
                  </Col>

                  {/* {
                     blogContent?.length !== 1 && (
                        <Col md={2}>
                           <Button
                              onClick={() => remove(index)}
                              className="delete-button"
                              type={'text'}
                              icon={<DeleteOutlined className="delete-icon" />}
                           />
                        </Col>
                     )
                  } */}
               </Fragment>
            ))
         }
         {/* <Button
            onClick={handleBlogAppend}
            className="add-button"
            type={'text'}
            icon={<PlusCircleOutlined className="plus-icon" />}
         /> */}
      </>
   )
}
export default BlogContentSection
