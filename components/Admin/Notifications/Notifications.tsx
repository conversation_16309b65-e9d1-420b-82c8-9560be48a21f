import React, { useState, useEffect, useContext } from 'react';
import { getCurrentUserData } from "../../../lib/session";
import { getCurrentUserDetails, getNotifications } from '../../../lib/frontendapi';
import moment from 'moment';
import Pagination from "../../../components/Common/Pagination";
import { paginate } from "../../../helpers/paginate";
import AuthContext from '@/Context/AuthContext';
import Image from 'next/image';
export default function Notifications() {
    const [totalNotifications, setTotalNotifications] = useState([]);
    const [userProfileImage, setUserProfileImage] = useState('');
    const [notifications, setNotifications] = useState([]);
    const [currentUserName, setCurrentUsername] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const { user } = useContext(AuthContext);
    const pageSize = 10;

    const onPageChange = (page: any) => {
        setCurrentPage(page);
        const data = {
            notify_to: user?.id
        }
        getNotifications(data)
            .then(res => {
                if (res) {
                    setTotalNotifications(res);
                    const paginatedPosts = paginate(res, page, pageSize);
                    setNotifications(paginatedPosts);
                } else {
                    setNotifications([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
    };
    useEffect(() => {
        getCurrentUserDetails(user?.id)
            .then(res => {
                if (res.status == true) {
                    setUserProfileImage(res.user.profile_image);
                } else {
                    setUserProfileImage('');
                }
            })
            .catch(err => {
                console.log(err);
            });
        const data = {
            notify_to: user?.id
        }
        getNotifications(data)
            .then(res => {
                if (res) {
                    setTotalNotifications(res);
                    const paginatedPosts = paginate(res, currentPage, pageSize);
                    setNotifications(paginatedPosts);
                } else {
                    setNotifications([]);
                }
            })
            .catch(err => {
                console.log(err);
            });
    }, [user, currentPage]);
    return (
        <>
            <div className="dash-right">
                <h1>My <span className='span-color'> Notifications</span></h1>

                <div className='row mt-4 '>
                    <div className='col-sm-6'>
                    </div>
                    <div className='col-sm-6 text-right'>
                        <ul className="blue-text-line mt-4 text-right">
                            {/* <li>
                        <a href="/employees/settings/notifications/">View Public Profile</a>
                    </li> */}
                        </ul>
                    </div>
                </div>

                <div className='data-management  mb-3 m-p-10'>
                    <div className='work-experience-fieild m-p-10'>
                        {notifications.length > 0
                            ?
                            notifications.slice(0, 10).map((notification_data: any, index: any) => {
                                const created_at = moment.utc(notification_data.created_at);
                                const currentTime = moment();
                                const yesterday = moment().subtract(1, 'day');
                                let timeFormatted;

                                if (created_at.isSame(currentTime, 'day')) {
                                    timeFormatted = created_at.local().format('hh:mmA');
                                } else if (created_at.isSame(yesterday, 'day')) {
                                    timeFormatted = 'Yesterday';
                                } else {
                                    timeFormatted = created_at.local().format('MMMM D');
                                }
                                return (
                                    <div className='box-text-img bg-CFE5FF mb-2' key={index}>
                                        <div className='row'>
                                            <div className='col-lg-1 col-md-2 mb-3'>
                                                <p className='f-16'>

                                                    {notification_data.profile_image ? (
                                                        <img
                                                            src={`${process.env.NEXT_PUBLIC_IMAGE_URL}images/userprofileImg/${notification_data.profile_image}`}
                                                            alt="Avatars-notification_data"
                                                            className="w-40 m-none"
                                                        />
                                                    ) : (
                                                        <small
                                                            title={notification_data.name}
                                                            className="text-uppercase w-75 notfication_name_two m-none"
                                                        >
                                                            {notification_data.name.split(" ").length === 1
                                                                ? notification_data.name.substring(0, 2)
                                                                : notification_data.name
                                                                    .split(" ")
                                                                    .map((word: any, index: any) =>
                                                                        index === 0 || index === 1 ? word[0] : ""
                                                                    )
                                                                    .join("")}
                                                        </small>
                                                    )}
                                                </p>
                                            </div>
                                            <div className='col-lg-9 col-md-8'>
                                                <div className="text-align-center m-2">
                                                    <span dangerouslySetInnerHTML={{ __html: notification_data.notification }}></span>
                                                </div>
                                            </div>
                                            <div className='col-sm-2 text-right mt-2'>
                                                <p className='f-12 c-999999  '>{timeFormatted}</p>
                                            </div>
                                        </div>
                                    </div>
                                )
                            })
                            :
                            <>
                                <div className='row'>
                                    <div className='col-sm-12'>
                                        <p className='f-22 m-center'>Inbox</p>
                                    </div>
                                </div>
                                <div className='text-center'>
                                    <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/blank-5.png'} alt="blank-5" />
                                    <p className='f-22 c-BABABA mb-2'>No New Notifications</p>
                                    <p className='f-18 c-BABABA w-400'>Check this section for job updates, and<br /> general notifications. </p>
                                </div>
                            </>
                        }
                        <Pagination
                            items={totalNotifications.length}
                            currentPage={currentPage}
                            pageSize={pageSize}
                            onPageChange={onPageChange}
                        />
                    </div>
                </div>
            </div>
        </>
    )
}
