.tableContainer {
  padding-top: 30px;
}
.table_header {
  display: flex;
  align-items: flex-start;
  gap: 24px;
  align-self: stretch;
  justify-content: space-between;
}
.search_container {
  display: flex;
  width: 320px;
  flex-direction: column;
}
.search_container input {
  height: 32px;
}

.table_header h4 {
  color: #191919;
  font-size: 31px;
  font-weight: 500;
  line-height: 120%;
}

.manageActions {
  display: flex;
  align-items: flex-start;
  gap: 4px;
}

.manageActions div {
  display: flex;
  padding: 4px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 4px;
  background: #ebf4ff;
  cursor: pointer;
}
.rating_top {
  display: flex;
  align-items: center;
  gap: 4px;
}

.rating_top span {
  color: #0070f5;
  font-size: 14px;
  font-weight: 700;
  line-height: 150%;
  letter-spacing: 0.28px;
}
.rating_top a {
  color: #0070f5;
  font-size: 14px;
  font-weight: 400;
  line-height: 150%;
  letter-spacing: 0.28px;
  text-decoration-line: underline !important;
  text-decoration-style: solid;
  text-decoration-skip-ink: none;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
}

.ratingCard p {
  overflow: hidden;
  color: #747474;
  text-overflow: ellipsis;
  font-size: 12px;
  font-weight: 400;
  line-height: 150%;
  letter-spacing: 0.24px;
}

.view_link {
  color: #0070f5;
  font-size: 18px;
  font-weight: 700;
  line-height: 160%;
}

.view_link a {
  color: #0070f5;
  font-size: 12px;
  font-weight: 400;
  line-height: 140%;
  text-decoration-line: underline !important;
  text-decoration-style: solid;
  text-decoration-skip-ink: none;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
}
.approved {
  color: #3d9f79;
  font-size: 12px;
  line-height: 140%;
}

.unclaimed,
.rejected {
  color: #747474;
  font-size: 12px;
  line-height: 140%;
}
.company_content span {
  color: #747474;
  font-size: 12px;
  font-weight: 600;
  line-height: 140%;
}
.company_content p {
  overflow: hidden;
  color: #0070f5;
  text-overflow: ellipsis;
  font-size: 14px;
  font-weight: 600;
  line-height: 140%;
}
.companyName {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}
.expire_text {
  font-weight: 400 !important;
}
