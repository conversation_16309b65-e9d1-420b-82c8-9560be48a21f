.review_manage_container {
  padding-top: 32px;
}
.header_container {
  display: flex;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  padding-bottom: 16px;
}

.back_arrow_container {
  display: flex;
  width: 20px;
  height: 20px;
  padding: 7.5px 3.125px 6.25px 3.125px;
  justify-content: center;
  align-items: center;
  display: flex;
  padding: 2px;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
}
.company_container {
  display: flex;
  padding: 16px 8px 0px 8px;
  flex-direction: column;
  align-items: flex-start;
  gap: 24px;
  align-self: stretch;
  border-radius: 8px 8px 0px 0px;
  background: #fff;
}
.social_link_container {
  display: flex;
  align-items: flex-start;
  gap: 4px;
}
.company_heading {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.heading_left {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  align-self: stretch;
}
.card_container {
  display: flex;
  gap: 16px;
}
.card {
  display: flex;
  width: 240px;
  padding: 15px;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  border-radius: 8px;
  border: 1px solid var(--Grayscale-03, #d9d9d9);
  background: #fff;
  gap: 8px;
  height: fit-content;
}

.card .card_title {
  color: #747474;
  font-size: 12px;
  font-weight: 400;
  line-height: 140%;
  text-transform: uppercase;
}
.card .card_content {
  overflow: hidden;
  color: #2c2c2c;
  text-overflow: ellipsis;
  font-size: 14px;
  font-weight: 600;
  line-height: 140%;
  min-height: 40px;
}
.card_review {
  color: #2c2c2c;
  font-size: 24px;
  font-weight: 600;
  line-height: 140%;
  display: flex;
  align-items: center;
  gap: 4px;
}
.star {
  margin-top: -4px;
}
.no_of_review {
  color: #999;
  font-size: 12px;
  font-weight: 400;
  line-height: 140%;
}
.card_container {
  display: flex;
  gap: 16px;
}
.divider {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  height: 1px;
  width: 100%;
  background-color: #eee;
}
.tabContainer {
  padding-top: 12px;
  width: 100%;
}
.rating_container {
  width: 100%;
}
