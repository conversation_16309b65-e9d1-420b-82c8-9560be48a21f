import {Accordion} from 'react-bootstrap';
import styles from './style.module.css';
import {ManageReview, ManageReviewResponse, WorkExperience} from '@/modules/admin/query/useGetManageReview';
import {useGetCountries} from '@/modules/common/query/useGetCountries';
import {useEffect, useMemo, useRef, useState} from 'react';
import moment from 'moment';
import {StarSelection} from '@/components/Company/StartSelection';
import Image from 'next/image';
import {useRemoveCompany} from '@/modules/admin/mutation/useRemoveCompany';
import {ButtonUi} from '@/ui/Button';
import {useHandleReportedReview} from '@/modules/admin/mutation/useHandleReportedReview';
import {useRouter} from 'next/router';
import {useAcceptRejectClaimCompany} from '@/modules/admin/mutation/useAcceptRejectClaimCompany';
import {useDeleteCompanyReview} from '@/modules/admin/mutation/useDeleteCompanyReview';
import {useApproveRejectReview} from '@/modules/admin/mutation/useApproveRejectReview';

interface AdminReviewCardProps {
  reviewDetails?: ManageReviewResponse;
  review: ManageReview;
}
export const AdminReviewManageCard = ({reviewDetails, review}: AdminReviewCardProps) => {
  const {detailed_review, work_experience} = review;
  const [isExpanded, setIsExpanded] = useState(false);
  const [truncatedText, setTruncatedText] = useState<string | null>(null);
  const toggleReadMore = () => {
    setIsExpanded(prev => !prev);
  };

  const {mutate, isLoading: isReporting} = useHandleReportedReview();
  const router = useRouter();
  const {mutate: actionApproveReject, isLoading: isApproving} = useApproveRejectReview();
  const {mutate: deleteReview} = useDeleteCompanyReview();

  const avgRating = useMemo(() => {
    return (
      (parseInt(detailed_review.work_environment) +
        parseInt(detailed_review.work_life_balance) +
        parseInt(detailed_review.career_growth) +
        parseInt(detailed_review.compensation_benefit) +
        parseInt(detailed_review.job_security)) /
      5
    );
  }, [detailed_review]);

  const reviewContainerData = [
    {
      label: 'Work Environment',
      value: detailed_review.work_environment,
    },
    {
      label: 'Work-Life Balance',
      value: detailed_review.work_life_balance,
    },
    {
      label: 'Career Growth',
      value: detailed_review.career_growth,
    },
    {
      label: 'Compensation & Benefits',
      value: detailed_review.compensation_benefit,
    },
    {
      label: 'Job Security',
      value: detailed_review.job_security,
    },
  ];

  const handleRemoveCompany = () => {
    deleteReview({
      review_id: review.review_id,
    });
  };

  useEffect(() => {
    if (review?.review_description) {
      const charLimit = 220;
      setTruncatedText(
        review.review_description.length > charLimit ? review.review_description.slice(0, charLimit) : null,
      );
    }
  }, [review?.review_description]);

  const checkWorkExperience = (work_experience: WorkExperience, user_work_experiences: WorkExperience[]) => {
    if (work_experience) {
      return user_work_experiences.some(
        el => el.company_name === work_experience?.company_name || el.country === work_experience?.country,
      );
    }
    return false;
  };
  const handleReportedReview = ({
    company_id,
    review_id,
    action,
  }: {
    company_id: number;
    review_id: number;
    action: 'reject' | 'remove';
  }) => {
    mutate({
      company_id,
      review_id,
      action,
    });
  };
  const handleClaimedCompany = (action: 'approve' | 'remove') => {
    review.user_id &&
      actionApproveReject({
        action,
        company_id: Number(router.query.slug),
        review_id: review.review_id,
      });
  };

  const dynamicRatingColor = (values: number) => {
    if (values <= 5 && values >= 4) {
      return 'green';
    } else if (values >= 2.5 && values <= 3.9) {
      return 'yellow';
    } else if (values >= 1 && values <= 2.4) {
      return 'red';
    } else {
      return 'green';
    }
  };
  return (
    <div>
      <Accordion>
        <Accordion.Header className={styles.accordion_header}>
          <div className={styles.header_container}>
            <div className={styles.review_header_content}>
              <h6>{review.review_title}</h6>
              <p>
                <span className={styles.job_title}>{work_experience.job_title}</span> ({' '}
                {moment(work_experience.start_date, 'DD.MM.YYYY').format('DD MMM YYYY')}) in {work_experience.country} -
                {moment(work_experience.end_date, 'DD.MM.YYYY').format('DD MMM YYYY')}{' '}
                {review?.user_name && (
                  <>
                    • Review by <span className={styles.user_name}>{review?.user_name}</span>
                  </>
                )}
              </p>
            </div>
            <div className={`${styles.rating_text} ${styles[dynamicRatingColor(Number(avgRating?.toFixed(1)))]}`}>
              {avgRating?.toFixed(1)}
            </div>
          </div>
        </Accordion.Header>
        <Accordion.Body className={styles.accordion_body}>
          <div className={styles.review_container}>
            {reviewContainerData?.map((el, index) => {
              return (
                <div key={index} className={styles.review_card}>
                  <p>{el.label}</p>
                  <StarSelection value={parseInt(el.value)} variant="notSelectable" />
                </div>
              );
            })}
          </div>
        </Accordion.Body>
      </Accordion>

      <div className={styles.reviewContainer}>
        <div className={styles.reviewDescription}>
          {isExpanded ? review?.review_description : truncatedText || review?.review_description}
          {!isExpanded && truncatedText && (
            <span className={styles.readMore} onClick={toggleReadMore}>
              Read More...
            </span>
          )}
          {isExpanded && (
            <span className={styles.readLess} onClick={toggleReadMore}>
              Read Less
            </span>
          )}
        </div>
      </div>

      <div className={styles.helpful_delete_container}>
        <div className={styles.general_consensus_container}>
          <span>General consensus</span>{' '}
          <div className={styles.button_container}>
            <p>
              Yes <span>{review.helpful_yes_count}</span>
            </p>{' '}
            <p>
              No <span>{review.helpful_no_count}</span>
            </p>
          </div>
        </div>
        <div className={styles.delete_container} onClick={() => handleRemoveCompany()}>
          <img src={'/icons/data-management/delete.svg'} height={24} width={24} alt="delete" />
          <span>Delete</span>
        </div>
      </div>

      {review.user_work_experiences.length > 0 && (
        <div className={styles.work_experience_user_container}>
          <div className={styles.review_header}>
            <img
              src={
                checkWorkExperience(work_experience, review.user_work_experiences)
                  ? '/images/company/check_circle.svg'
                  : '/images/company/cancel.svg'
              }
              height={24}
              width={24}
              alt="check"
            />
            <span>Work Experience (Profile)</span>
          </div>

          {review?.user_work_experiences?.map((el, index) => {
            return (
              <div key={index} className={styles.work_experience_content}>
                {el.job_title} {el.job_title && '@'}
                {el.company_name} {el.country}
                {el.country && ','} {el.city}
                {el.city && ','} {el.country}{' '}
                {el.start_date &&
                  el.end_date &&
                  `(${moment(el.start_date).format('MM.YY')} - ${moment(el.end_date).format('MM.YY')})`}
              </div>
            );
          })}
        </div>
      )}
      {review.is_approve === 0 && (
        <div className={styles.approve_review_container}>
          <p>What do you think of this review?</p>
          <div className={styles.button_container}>
            <ButtonUi
              variant="contained"
              color="primary"
              style={{
                width: '219px',
              }}
              onClick={e => handleClaimedCompany('approve')}>
              Approve
            </ButtonUi>
            <ButtonUi
              variant="outlined"
              color="error"
              style={{
                padding: ' 10px 0px',
                width: '93px',
              }}
              onClick={e => handleClaimedCompany('remove')}>
              Remove
            </ButtonUi>
          </div>
        </div>
      )}
      {review.is_reported === 1 && (
        <div className={styles.report_container}>
          {review.report_reviews.map((report, index) => {
            return (
              <div key={index} className={styles.report_card}>
                <p>
                  Reported By <span>{report.user_id}</span> on <span>{moment().format('DD.MM.YYYY')}</span> for{' '}
                  <span>{report.report_reason}</span>
                </p>
                <div className={styles.button_container}>
                  <ButtonUi
                    variant="contained"
                    color="primary"
                    style={{
                      width: '219px',
                    }}
                    onClick={e =>
                      handleReportedReview({
                        action: 'remove',
                        company_id: Number(router.query.slug),
                        review_id: review?.review_id,
                      })
                    }>
                    Remove this review
                  </ButtonUi>
                  <ButtonUi
                    variant="outlined"
                    color="error"
                    style={{
                      padding: ' 10px 0px',
                      width: '93px',
                    }}
                    onClick={e =>
                      handleReportedReview({
                        action: 'reject',
                        company_id: Number(router.query.slug),
                        review_id: review.review_id,
                      })
                    }>
                    Reject
                  </ButtonUi>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};
