.accordion_header {
  display: flex;
  align-items: flex-start;
  gap: 4px;
  align-self: stretch;
  justify-content: space-between;
  width: 100%;
  min-height: 56px;
  padding-bottom: 16px;
}

.header_container {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
}

.accordion_header button {
  border: none;
  display: flex;
  color: black !important;
  background-color: initial !important;
  width: 100%;
  box-shadow: none !important;
  padding: 0 !important;
}
.green {
  color: #3d9f79 !important;
}
.yellow {
  color: #d57b11 !important;
}
.red {
  color: #d04e4f !important;
}
.review_header_content h6 {
  color: #191919;
  font-size: 22px;
  font-weight: 500;
  line-height: 120%;
}

.review_header_content p {
  color: #747474;
  font-size: 16px;
  line-height: 160%;
}

.job_title {
  text-decoration-line: underline;
  text-decoration-style: solid;
  text-decoration-skip-ink: none;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
}
.rating_text {
  color: #3d9f79;
  font-size: 32px;
  font-weight: 600;
  line-height: 140%;
  padding-right: 4px;
}
.review_container {
  display: flex;
  justify-content: space-between;
  border-radius: 8px;
  border: 1px solid #eee;
  margin-bottom: 16px;
}

.review_card {
  display: flex;
  min-height: 54px;
  padding: 10px 12px;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  flex: 1 0 0;
  text-align: center;
}

.accordion_body {
  padding: 0 !important;
}
.reviewContainer {
  padding-bottom: 16px;
}

.readMore,
.readLess {
  color: #0070f5;
  font-weight: 600;
  cursor: pointer;
  margin-left: 4px;
}

.reviewDescription {
  display: inline;
  font-size: 14px;
  line-height: 1.6;
  position: relative;
  color: #333;
}
.general_consensus_container {
  display: flex;
  gap: 8px;
  align-items: center;
}

.general_consensus_container span {
  color: #747474;
  font-size: 16px;
  line-height: 140%;
}

.button_container {
  display: flex;
  gap: 7px;
  align-items: center;
}

.button_container p {
  display: flex;
  padding: 8px 12px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: 8px;
  background: #eee;
  color: #747474;
  font-size: 14px;
  line-height: 120%;
  align-items: center;
}

.button_container p span {
  color: #999;
  font-size: 12px;
  padding-top: 1px;
}
.user_name {
  color: #0070f5;
  border-bottom: 1px solid #0070f5;
}
.delete_container {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

.delete_container span {
  color: #d04e4f;
  font-weight: 600;
  line-height: 140%;
}

.helpful_delete_container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.work_experience_user_container {
  display: flex;
  padding: 8px 16px;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  align-self: stretch;
  border-radius: 8px;
  background: #f9f9f9;
}
.review_header {
  display: flex;
  align-items: center;
  gap: 4px;
}

.review_header span {
  color: #4d4d4d;
  font-size: 14px;
  font-weight: 600;
  line-height: 160%;
}
.approve_review_container {
  display: flex;
  padding: 8px 16px;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  align-self: stretch;
  border-radius: 8px;
  border: 1px solid #eee;
  margin-top: 16px;
}

.approve_button {
  padding-left: 30px;
}
.report_container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
}
.report_card {
  display: flex;
  padding: 8px 16px;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  align-self: stretch;
  border-radius: 8px;
  border: 1px solid #eee;
}

.report_card p {
  color: #4d4d4d;
  font-size: 16px;
  font-weight: 400;
  line-height: 160%;
}

.report_card span {
  font-weight: 600;
}
