import CustomDropdown from '@/components/Common/Dropdown';
import {filterItems} from '@/components/Frontend/JobOfferPage';
import {useGetCountries} from '@/modules/common/query/useGetCountries';
import {useGetSectors} from '@/modules/common/query/useGetSectors';
import {ManageReviewResponse, useGetCompanyManageReview} from '@/modules/admin/query/useGetManageReview';
import styles from './style.module.css';
import {AdminReviewManageCard} from './ReviewManageCard';
import {useRouter} from 'next/router';
import {useState} from 'react';

export type AdminReviewType = 'all' | 'approved' | 'pending' | 'reported';

interface AdminReviewCardProps {
  reviewDetails?: ManageReviewResponse;
  type: AdminReviewType;
}

export const AdminReviewContainer = ({type}: AdminReviewCardProps) => {
  const {data: countries} = useGetCountries();
  const {data: sectors} = useGetSectors();
  const router = useRouter();

  const [filters, setFilters] = useState({
    sortBy: '',
    dateRange: {startDate: '', endDate: ''},
    rating: '',
    location: '',
    jobTitle: '',
  });

  const updateFilters = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const getFilteredQueryParams = () => {
    const queryParams: Record<string, any> = {};

    if (filters.sortBy) queryParams.sort_by = filters.sortBy;
    if (filters.dateRange.startDate && filters.dateRange.endDate) {
      queryParams['date_range[start]'] = filters.dateRange.startDate;
      queryParams['date_range[end]'] = filters.dateRange.endDate;
    }
    if (filters.rating) queryParams.rating = filters.rating.toString();
    if (filters.location.length) queryParams['location[]'] = filters.location;
    if (filters.jobTitle.length) queryParams['job_title[]'] = filters.jobTitle;

    queryParams.company_id = Number(router.query.slug as string);
    return queryParams;
  };

  const {data: reviewDetails} = useGetCompanyManageReview({
    company_id: Number(router.query.slug as string),
    ...getFilteredQueryParams(),
    type: type,
  });

  console.log(sectors, 'sectors');
  const sortingData: filterItems[] = [
    {
      label: 'Sort By',
      menuProps: [
        {label: 'Highest Rated', value: ['highest_rated']},
        {label: 'Lowest Rated', value: ['lowest_rated']},
        {label: 'Most Recent', value: ['most_recent']},
        {label: 'Oldest', value: ['oldest']},
      ],
      type: 'dropdown-white',
      onSelect: value => updateFilters('sortBy', value[0]),
    },
    {
      label: 'Date Range',
      menuProps: [
        {label: 'Recent', value: ['recent']},
        {label: 'Select range', value: ['select_range'], hasCalender: true},
      ],
      type: 'range-picker',
      onSelect: value => updateFilters('dateRange', value),
    },
    {
      label: 'Rating',
      menuProps: [
        {label: 'High Rating(4.0 - 5.0)', value: ['high']},
        {label: 'Moderate Rating (2.5 - 3.9)', value: ['moderate']},
        {label: 'Low Rating (1.0 - 2.4)', value: ['low']},
      ],
      type: 'checkboxDropdown',
      onSelect: value => updateFilters('rating', value),
    },
    {
      label: 'Location',
      menuProps:
        countries?.map(country => ({
          label: country.country_name,
          value: [country.country_name],
        })) || [],
      type: 'checkboxWithSearch',
      multiSelect: true,
      onSelect: value => updateFilters('location', value),
    },
    {
      label: 'Job Title',
      menuProps: sectors?.map(el => ({
        label: el.sector_name,
        value: [el.id.toString()],
      })),
      type: 'checkboxWithSearch',
      multiSelect: true,
      onSelect: value => updateFilters('jobTitle', value),
    },
  ];

  return (
    <div>
      <div className={styles.sorting_container}>
        {sortingData.map((item, index) => (
          <CustomDropdown
            key={index}
            item={item}
            onSelect={value => item.onSelect?.(value)}
            variant={item.type ?? 'dropdown-white'}
            className="filter-dropdown"
          />
        ))}
      </div>
      <div className={styles.rating_container}>
        {reviewDetails?.reviews?.map((review, index) => (
          <AdminReviewManageCard reviewDetails={reviewDetails} key={index} review={review} />
        ))}
      </div>
    </div>
  );
};
