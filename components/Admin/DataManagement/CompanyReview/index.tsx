import React, {useState} from 'react';
import styles from './style.module.css';
import CommonTable from '@/components/Common/CommonTable';
import Search from 'antd/lib/transfer/search';
import {AllCompaniesReview, useGetCompanyReviewList} from '@/modules/admin/query/useGetComapnyList';
import Image from 'next/image';
import SortingFilter from '../../Employers/FilterModalCard';
import {useRouter} from 'next/router';
import moment from 'moment';

export const CompanyReviewContainer = () => {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchName, setSearchName] = useState<string>();
  const [placeholderImage, setPlaceholderImage] = useState('/images/placeholder.jpg');
  const [reviewModal, setReviewModal] = useState(false);
  const [approvalModal, setApprovalModal] = useState(false);
  const [reportedModal, setReportedModal] = useState(false);

  const [selectedRating, setSelectedRating] = useState<any>();
  const [selectedTeam, setSelectedReported] = useState<any>();
  const [selectedJobPost, setSelectedApproved] = useState<any>();

  const router = useRouter();

  const {data: allCompanies, isLoading: loading} = useGetCompanyReviewList({
    page,
    pageSize,
    searchName: searchName || '',
    star: selectedRating,
    order: selectedJobPost ? selectedJobPost : selectedTeam,
    sort_by: selectedJobPost ? 'approval' : selectedTeam ? 'reported' : 'rating',
  });

  const filterModalData = {
    rating: {
      label: 'Star Rating',
      items: [
        {label: '5', value: '5'},
        {label: '4 & Up', value: '4'},
        {label: '3 & Up', value: '3'},
        {label: '2 & Up', value: '2'},
        {label: '1 & Up', value: '1'},
      ],
    },
    jobPost: {
      items: [
        {label: 'Low to High', value: 'asc'},
        {label: 'High to Low', value: 'desc'},
      ],
    },
    team: {
      items: [
        {label: 'Low to High', value: 'asc'},
        {label: 'High to Low', value: 'desc'},
      ],
    },
  };

  const handleSelect = (column: 'rating' | 'approved' | 'reported', value: string) => {
    switch (column) {
      case 'rating':
        setSelectedRating(value);
        break;
      case 'approved':
        setSelectedApproved(value);
        break;
      case 'reported':
        setSelectedReported(value);
        break;
      default:
        break;
    }
  };

  const columns = [
    {
      key: 'company_name',
      title: <th>COMPANY NAME</th>,
      render: (item: AllCompaniesReview) => (
        <div className={styles.companyName}>
          <img
            src={placeholderImage}
            alt="logo"
            className={styles.companyLogo}
            width={50}
            height={50}
            onError={e => {
              setPlaceholderImage('/images/placeholder.jpg');
            }}
          />
          <div className={styles.company_content}>
            <p>{item.company_name}</p>
            <small className={styles[item?.claim_status]}>
              {item.claim_status === 'approved' ? 'Claimed' : 'Unclaimed'}
            </small>
            <br />
            <span>
              {item.membership === 1 ? 'PRO' : 'FREE'}
              <span className={styles.expire_text}>
                {item?.expire ? ' • ' + 'Exp: ' + moment(item?.expire).format('DD.MM.YYYY') : ''}
              </span>
            </span>
          </div>
        </div>
      ),
    },
    {
      key: 'rating',
      title: (
        <th
          onMouseLeave={() => setReviewModal(false)}
          onMouseEnter={() => setReviewModal(true)}
          style={{
            position: 'relative',
          }}>
          RATING
          <SortingFilter
            data={filterModalData.rating}
            onSelect={(value: string) => handleSelect('rating', value)}
            isOpen={reviewModal}
            setIsOpen={setReviewModal}
            type="rating"
            selectedValue={selectedRating}
          />
        </th>
      ),
      render: (item: AllCompaniesReview) => (
        <div className={styles.ratingCard}>
          <div className={styles.rating_top}>
            <img
              src="/icons/employer/yellow-star.svg"
              alt="star"
              width={12}
              height={12}
              style={{
                marginTop: '-5px',
              }}
            />
            <span>{item.average_rating}</span>
            <a href={`/companies/${item?.company_id}/reviews`}>View</a>
          </div>
          <p>({item.total_reviews} reviews)</p>
        </div>
      ),
    },
    {
      key: 'approval',
      title: (
        <th
          onMouseLeave={() => setApprovalModal(false)}
          onMouseEnter={() => setApprovalModal(true)}
          style={{
            position: 'relative',
          }}>
          APPROVAL
          <SortingFilter
            data={filterModalData.jobPost}
            onSelect={(value: string) => handleSelect('approved', value)}
            isOpen={approvalModal}
            setIsOpen={setApprovalModal}
            selectedValue={selectedJobPost}
          />
        </th>
      ),
      render: (item: AllCompaniesReview) => (
        <span className={styles.view_link}>
          {item.approved_reviews}{' '}
          <a onClick={() => router.push(`/admin/datamanagement/company-review/${item.company_id}/manage?tab=approved`)}>
            View
          </a>
        </span>
      ),
    },
    {
      key: 'reported',
      title: (
        <th
          style={{
            position: 'relative',
          }}
          onMouseLeave={() => setReportedModal(false)}
          onMouseEnter={() => setReportedModal(true)}>
          REPORTED
          <SortingFilter
            data={filterModalData.team}
            onSelect={(value: string) => handleSelect('reported', value)}
            isOpen={reportedModal}
            setIsOpen={setReportedModal}
            selectedValue={selectedTeam}
          />
        </th>
      ),
      render: (item: AllCompaniesReview) => (
        <span className={styles.view_link}>
          {item.reported_reviews}{' '}
          <a onClick={() => router.push(`/admin/datamanagement/company-review/${item.company_id}/manage?tab=reported`)}>
            View
          </a>
        </span>
      ),
    },
    {
      key: 'last_updated',
      title: <th>LAST UPDATED ON</th>,
      render: (item: any) => <span>{item.last_updated_on && moment(item.last_updated_on).format('DD.MM.YYYY')}</span>,
    },
    {
      key: 'manage',
      title: <th>MANAGE</th>,
      render: (item: AllCompaniesReview) => (
        <div className={styles.manageActions}>
          <div>
            <img
              src="/icons/data-management/reviews.svg"
              alt="edit"
              width={24}
              height={24}
              onClick={() => router.push(`/admin/datamanagement/company-review/${item.company_id}/manage`)}
            />
          </div>
          <div>
            {' '}
            <img
              src="/icons/data-management/person.svg"
              alt="person"
              width={24}
              height={24}
              onClick={() => router?.push(`/companies/${item?.company_id}`)}
            />
          </div>
        </div>
      ),
    },
  ];

  return (
    <div className={styles.tableContainer}>
      <div className={styles.table_header}>
        <h4>Company Review</h4>
        <div className={styles.search_container}>
          <Search placeholder={'Search...'} onChange={(e: any) => setSearchName(e.target.value)} />
        </div>
      </div>
      <CommonTable
        columns={columns}
        data={allCompanies?.data || []}
        loading={loading}
        pagination={{
          total: allCompanies?.meta?.total || 0,
          current: allCompanies?.meta?.current_page || 1,
          pageSize: allCompanies?.meta?.per_page || 10,
          onChange: (newPage, newPageSize) => {
            setPage(newPage);
            setPageSize(newPageSize ?? 10);
          },
        }}
      />
    </div>
  );
};
