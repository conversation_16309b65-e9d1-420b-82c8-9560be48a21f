import React, {useState, useEffect} from 'react';
import {
  getAllCitiesForAdmin,
  deleteCity,
  editAndSaveCityData,
  getAllCountries,
  updateCityStatus,
} from '../../../lib/adminapi';
import Pagination from '../../../components/Common/NewPagination';
import {paginate} from '../../../helpers/paginate';
import PopupModal from '../../../components/Common/PopupModal';
import Link from 'next/link';
import swal from 'sweetalert';
import {Switch} from 'antd';
import {ToastContainer, toast} from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import SuccessToast from '../../Common/showSuccessTostrMessage';
import ErrorToast from '../../Common/showErrorTostrMessage';
import {City} from '@/lib/types';

export default function Cities() {
  const [cities, setCities] = useState<City[]>([]);
  const [country, setCountry] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [filteredCity, setFilteredCity] = useState<City[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  const [cityName, setCityName] = useState('');
  const [countryName, setCountryName] = useState('');
  const [id, setCityId] = useState('');
  const [countryId, setCountryId] = useState('');
  const [modalConfirm, setModalConfirm] = useState(false);
  const [showPopupunsave, setShowPopupunsave] = useState(false);
  const [showPopupunsave1, setShowPopupunsave1] = useState(false);
  const [showmessage, setShowmessage] = useState('');
  const [processing, setProcessing] = useState(false);

  const modalConfirmClose = () => {
    setModalConfirm(false);
  };

  const pageSize = 20;

  useEffect(() => {
    getAllCitiesForAdmin()
      .then(response => {
        setCities(response.data);
        filterBlogs(response.data, searchQuery);
      })
      .catch(error => {
        console.error(error);
      });
    getAllCountries()
      .then(res => {
        if (res) {
          setCountry(res);
        } else {
          setCountry([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  }, [searchQuery]);

  const fetchData = async () => {
    try {
      const response = await getAllCitiesForAdmin();
      setCities(response.data);
      filterBlogs(response.data, searchQuery);
    } catch (error) {
      console.error(error);
    }
  };

  const filterBlogs = (cities: City[], searchValue: string) => {
    const filteredCity = cities.filter((citiesData: City) =>
      citiesData.city_name.toLowerCase().includes(searchValue.toLowerCase()),
    );
    setFilteredCity(filteredCity);
    setCurrentPage(1); // Reset to the first page when applying a filter
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchValue = e.target.value.toLowerCase();
    setSearchQuery(searchValue);
    filterBlogs(cities, searchValue);
  };

  const onPageChange = (page: number) => {
    setCurrentPage(page);
  };

  const paginatedCities = paginate(filteredCity, currentPage, pageSize);

  const handlCitySubmit = async (event: any) => {
    event.preventDefault();
    setProcessing(true);

    if (id) {
      const nameExists = cities.some(record => record.city_name == cityName && record.id != id);

      if (nameExists) {
        setShowmessage('City name already in use');
        setShowPopupunsave1(true);
        setTimeout(() => {
          setShowPopupunsave1(false);
        }, 10000);
        return;
      }
    } else {
      const nameExists = cities.some(record => record.city_name == cityName);
      if (nameExists) {
        setShowmessage('City name already in use');
        setShowPopupunsave1(true);
        setTimeout(() => {
          setShowPopupunsave1(false);
        }, 10000);
        return;
      }
    }
    try {
      const data = {
        id: id,
        city_name: cityName,
        country_id: countryId,
      };
      const res = await editAndSaveCityData(data);

      if (res.status == true) {
        setProcessing(false);
        setShowmessage(res.message);
        setShowPopupunsave(true);
        setTimeout(() => {
          setShowPopupunsave(false);
        }, 1000);
        fetchData();
        resetForm();
        setTimeout(() => {
          setModalConfirm(false);
          false;
        }, 1000);
      } else {
        setShowmessage(res.message);
        setShowPopupunsave1(true);
        setTimeout(() => {
          setShowPopupunsave1(false);
        }, 10000);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const handleCancel = () => {
    setModalConfirm(false);
  };

  const resetForm = () => {
    setCityName('');
    setCityId('');
    setCountryId('');
  };

  const handleEdit = (id: any) => {
    const filteredRecords = cities.filter(record => record.id === id);
    if (filteredRecords.length > 0) {
      const {city_name} = filteredRecords[0];
      const {country_id} = filteredRecords[0];
      setCityName(city_name);
      setCityId(id);
      setCountryId(country_id);
      toast.dismiss();

      setModalConfirm(true);
    }
  };

  const handledeleteCity = (id: any) => {
    swal({
      title: 'Are you sure?',
      text: 'You want to delete the City',
      icon: 'warning',
      dangerMode: true,
      buttons: ['Cancel', 'Yes, I am sure!'],
    }).then(willDelete => {
      if (willDelete) {
        const data = {
          id: id,
        };

        deleteCity(data)
          .then(res => {
            if (res.status === true) {
              setShowmessage(res.message);
              setShowPopupunsave(true);
              setTimeout(() => {
                setShowPopupunsave(false);
              }, 10000);

              fetchData();
            } else {
              console.log('Deletion failed');
            }
          })
          .catch(() => {
            // Handle error
          });
      } else {
        console.log('Deletion failed');
      }
    });
  };
  const handleChangeCityStatus = (checked: any, cityId: any) => {
    const data = {
      status: checked,
      cityId: cityId,
    };
    updateCityStatus(data)
      .then(res => {
        if (res.status === true) {
          setShowmessage(res.message);
          setShowPopupunsave(true);
          setTimeout(() => {
            setShowPopupunsave(false);
          }, 10000);
          fetchData();
        } else {
          setShowmessage(res.message);
          setShowPopupunsave1(true);
          setTimeout(() => {
            setShowPopupunsave1(false);
          }, 10000);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  return (
    <>
      <div className="dash-right">
        <h1 className="data">Data Management</h1>
        <div className="data-management">
          <div className="row justify-content-end" id="sector_input">
            <div className="col-lg-4 col-md-12 text-end">
              <div className="d-flex gap-3">
                <div className="input-group mb-2 ">
                  <input
                    type="text"
                    className="form-control"
                    placeholder="Search country here.."
                    value={searchQuery}
                    onChange={handleSearch}
                  />
                </div>
                <div className="btnWidth">
                  <button
                    type="button"
                    className="btn-a primary-size-16 btn-bg-0055BA tab-w-100 w-100"
                    onClick={() => {
                      setModalConfirm(true);
                      resetForm();
                      toast.dismiss();
                    }}>
                    Add City
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div className="table-part mt-4 max-w">
            <table className="rwd-table">
              <tbody>
                <tr>
                  <th>Country Name</th>
                  <th>City Name</th>
                  <th>Status</th>
                  <th className="text-end">MANAGE </th>
                </tr>

                {paginatedCities.map((cities: any, index: any) => (
                  <tr key={index}>
                    <td data-th="Country Name">
                      <p className="location">{cities.country_name}</p>
                    </td>
                    <td data-th="City Name">
                      <p className="location">{cities.city_name}</p>
                    </td>
                    <td>
                      <Switch
                        defaultChecked={cities.status == 'active'}
                        onChange={checked => handleChangeCityStatus(checked, cities.id)}
                        checkedChildren="Enable"
                        unCheckedChildren="Disable"
                      />
                    </td>
                    <td data-th="MANAGE" className="text-end">
                      <i className="fa-solid fa-pencil edit-pencil" onClick={() => handleEdit(cities.id)}></i>
                      <i className="fa-regular fa-trash-can del-trash" onClick={() => handledeleteCity(cities.id)}></i>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="pagination-wrapper mt-4">
            <div className="pagination-wrapper">
              <Pagination
                items={filteredCity}
                currentPage={currentPage}
                pageSize={pageSize}
                onPageChange={onPageChange}
                activePage={currentPage} // Add the activePage property here
              />
            </div>
          </div>
        </div>
      </div>

      <PopupModal
        show={modalConfirm}
        handleClose={modalConfirmClose}
        customclass={'add_company_signup_popup modal-lg body-sp-0 '}
        closebtnclass={'close-x  bg-0055BA border-design close-b-des '}
        closebtnicon={'icon'}>
        <div className="head-box">
          <div className="row">
            <div className="col-sm-10">
              <p className="f-26 mb-2 mt-2"> {id ? 'Edit City' : 'Add City'}</p>
            </div>
            <div className="col-sm-2 text-right">
              <button
                type="button"
                className="close-x  bg-0055BA border-design"
                data-bs-dismiss="modal"
                aria-label="Close">
                <i className="fa-solid fa-xmark"></i>
              </button>
            </div>
          </div>
        </div>
        <div className="p-4">
          <form className="form-experience-fieild" onSubmit={handlCitySubmit}>
            <div className="form_field_sec">
              <select
                className="fild-des form-control"
                value={countryId || ''}
                onChange={e => setCountryId(e.target.value)}>
                <option value="">Select Country</option>
                {country.length > 0 ? (
                  country.map((countryData: any, index) => {
                    if (countryData.status === 'active') {
                      return (
                        <option value={countryData.id || ''} key={index}>
                          {countryData.country_name}
                        </option>
                      );
                    }
                  })
                ) : (
                  <option value="">Select Country</option>
                )}
              </select>
              <label>Country Name*</label>
            </div>
            <div className="form_field_sec">
              <input
                type="text"
                placeholder="United Kingdom"
                className="fild-des"
                name="city_name"
                onChange={e => setCityName(e.target.value)}
                value={cityName}
                required
              />
              <label>City Name*</label>
            </div>
            <div className="text-right mt-3">
              <button type="button" className="cancel" onClick={handleCancel}>
                Cancel
              </button>
              <button type="submit" className="save" disabled={processing}>
                {processing ? 'Please wait...' : 'Save'}
              </button>
            </div>
          </form>
        </div>
      </PopupModal>
      {showPopupunsave && <SuccessToast message={showmessage} />}
      {showPopupunsave1 && <ErrorToast message={showmessage} />}
    </>
  );
}
