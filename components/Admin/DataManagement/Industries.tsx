import React, {useState, useEffect} from 'react';
import {getAllIndustriesForAdmin, deleteIndustries, editAndSaveIndustriesData} from '../../../lib/adminapi';
import {all} from 'axios';
import swal from 'sweetalert';
import {ToastContainer, toast} from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Pagination from '../../../components/Common/NewPagination';
import {paginate} from '../../../helpers/paginate';
import PopupModal from '../../../components/Common/PopupModal';
import Link from 'next/link';
import SuccessToast from '../../Common/showSuccessTostrMessage';
import ErrorToast from '../../Common/showErrorTostrMessage';

interface allIndustries {
  name: string;
  id: string;
}

export default function Industries() {
  const [allIndustries, setAllIndustries] = useState<allIndustries[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [filteredIndustries, setFilteredIndustries] = useState<allIndustries[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  const [name, setSectorIndustries] = useState('');
  const [id, setSectorId] = useState('');

  const [modalConfirm, setModalConfirm] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [showPopupunsave, setShowPopupunsave] = useState(false);
  const [showPopupunsave1, setShowPopupunsave1] = useState(false);
  const [showmessage, setShowmessage] = useState('');

  const modalConfirmClose = () => {
    setModalConfirm(false);
  };

  const pageSize = 20;

  useEffect(() => {
    getAllIndustriesForAdmin()
      .then(response => {
        setAllIndustries(response.data);
        filterBlogs(response.data, searchQuery);
      })
      .catch(error => {
        console.error(error);
      });
  }, [searchQuery]);

  const fetchData = async () => {
    try {
      const response = await getAllIndustriesForAdmin();
      setAllIndustries(response.data);
      filterBlogs(response.data, searchQuery);
    } catch (error) {
      console.error(error);
    }
  };

  const filterBlogs = (allIndustries: allIndustries[], searchValue: string) => {
    const filteredIndustries = allIndustries.filter((industries: allIndustries) =>
      industries.name.toLowerCase().includes(searchValue.toLowerCase()),
    );
    setFilteredIndustries(filteredIndustries);
    setCurrentPage(1); // Reset to the first page when applying a filter
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchValue = e.target.value.toLowerCase();
    setSearchQuery(searchValue);
    filterBlogs(allIndustries, searchValue);
  };

  const onPageChange = (page: number) => {
    setCurrentPage(page);
  };

  const paginatedIndustries = paginate(filteredIndustries, currentPage, pageSize);

  const handlSectorSubmit = async (event: any) => {
    event.preventDefault();
    setProcessing(true);

    if (id) {
      const nameExists = allIndustries.some(record => record.name === name && record.id != id);

      if (nameExists) {
        setShowmessage('Industry name already in use');
        setShowPopupunsave1(true);
        setTimeout(() => {
          setShowPopupunsave1(false);
        }, 10000);
        return;
      }
    } else {
      const nameExists = allIndustries.some(record => record.name === name);

      if (nameExists) {
        setShowmessage('Industry name already in use');
        setShowPopupunsave1(true);
        setTimeout(() => {
          setShowPopupunsave1(false);
        }, 10000);
        return;
      }
    }
    try {
      const data = {
        id: id,
        name: name,
      };
      const res = await editAndSaveIndustriesData(data);

      if (res.status == true) {
        setProcessing(false);

        setShowmessage(res.message);
        setShowPopupunsave(true);
        setTimeout(() => {
          setShowPopupunsave(false);
        }, 1000);
        setTimeout(() => {
          setModalConfirm(false);
        }, 1000);
        fetchData();
        resetForm();
      } else {
        setShowmessage(res.message);
        setShowPopupunsave1(true);
        setTimeout(() => {
          setShowPopupunsave1(false);
        }, 10000);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const handleCancel = () => {
    setModalConfirm(false);
  };

  const resetForm = () => {
    setSectorIndustries('');
    setSectorId('');
  };

  const handleEdit = (id: any) => {
    const filteredRecords = allIndustries.filter(record => record.id === id);
    if (filteredRecords.length > 0) {
      const {name} = filteredRecords[0];
      setSectorIndustries(name);
      setSectorId(id);
      toast.dismiss();
      setModalConfirm(true);
    }
  };

  const handleDeleteIndustries = (id: any) => {
    swal({
      title: 'Are you sure?',
      text: 'You want to delete the industry',
      icon: 'warning',
      //buttons: true,
      dangerMode: true,
      buttons: ['Cancel', 'Yes, I am sure!'],
    }).then(willDelete => {
      if (willDelete) {
        const data = {
          id: id,
        };

        deleteIndustries(data)
          .then(res => {
            if (res.status === true) {
              setShowmessage(res.message);
              setShowPopupunsave(true);
              setTimeout(() => {
                setShowPopupunsave1(false);
              }, 1000);
              fetchData();
            } else {
              console.log('Deletion failed');
            }
          })
          .catch(() => {
            // Handle error
          });
      } else {
        console.log('Deletion failed');
      }
    });
  };

  return (
    <>
      <div className="dash-right">
        <h1 className="data">Data Management</h1>

        <div className="data-management">
          <div className="row justify-content-end" id="sector_input">
            <div className="col-lg-4 col-md-12 text-end">
              <div className="d-flex gap-3">
                <div className="input-group mb-2 ">
                  <input
                    type="text"
                    className="form-control"
                    placeholder="Search country here.."
                    value={searchQuery}
                    onChange={handleSearch}
                  />
                </div>
                <div className="btnWidth">
                  <button
                    type="button"
                    className="btn-a primary-size-16 btn-bg-0055BA tab-w-100 w-100"
                    onClick={() => {
                      setModalConfirm(true);
                      resetForm();
                      toast.dismiss();
                    }}>
                    Add Industry
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div className="table-part mt-4 max-w">
            <table className="rwd-table">
              <tbody>
                <tr>
                  <th>Industry name </th>
                  <th className="text-end">MANAGE </th>
                </tr>

                {paginatedIndustries.map((sector: any, index: any) => (
                  <tr key={index}>
                    <td data-th="Industry name">
                      <p className="location">{sector.name}</p>
                    </td>

                    <td className="text-end" data-th="MANAGE">
                      <i className="fa-solid fa-pencil edit-pencil" onClick={() => handleEdit(sector.id)}></i>
                      <i
                        className="fa-regular fa-trash-can del-trash"
                        onClick={() => handleDeleteIndustries(sector.id)}></i>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="pagination-wrapper mt-4">
            <div className="pagination-wrapper">
              <Pagination
                items={filteredIndustries}
                currentPage={currentPage}
                pageSize={pageSize}
                onPageChange={onPageChange}
                activePage={currentPage} // Add the activePage property here
              />
            </div>
          </div>
        </div>
      </div>

      <PopupModal
        show={modalConfirm}
        handleClose={modalConfirmClose}
        customclass={'add_company_signup_popup modal-lg body-sp-0 '}
        closebtnclass={'close-x  bg-0055BA border-design close-b-des'}
        closebtnicon={'icon'}>
        <div className="head-box">
          <div className="row">
            <div className="col-sm-10">
              <p className="f-26 mb-2 mt-2"> {id ? 'Edit Industry' : 'Add Industry'}</p>
            </div>
            <div className="col-sm-2 text-right">
              <button
                type="button"
                className="close-x  bg-0055BA border-design"
                data-bs-dismiss="modal"
                aria-label="Close">
                <i className="fa-solid fa-xmark"></i>
              </button>
            </div>
          </div>
        </div>
        <div className="p-4">
          <form className="form-experience-fieild" onSubmit={handlSectorSubmit}>
            <div className="form_field_sec">
              <input
                type="text"
                placeholder="Global Finance.."
                className="fild-des"
                name="name"
                onChange={e => setSectorIndustries(e.target.value)}
                value={name}
                required
              />
              <label>Industry Name*</label>
            </div>
            <div className="text-right mt-3">
              <button type="button" className="cancel" onClick={handleCancel}>
                Cancel
              </button>
              <button type="submit" className="save" disabled={processing}>
                {processing ? 'Please wait...' : 'Save'}
              </button>
            </div>
          </form>
        </div>
      </PopupModal>
      {showPopupunsave && <SuccessToast message={showmessage} />}
      {showPopupunsave1 && <ErrorToast message={showmessage} />}
    </>
  );
}
