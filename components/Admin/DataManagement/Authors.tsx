import React, {useState, useEffect, useCallback} from 'react';
import {createOrUpdateAuthor, deleteAuthor} from '../../../lib/adminapi';
import Pagination from '../../../components/Common/NewPagination';
import {paginate} from '../../../helpers/paginate';
import PopupModal from '../../../components/Common/PopupModal';
import Link from 'next/link';
import swal from 'sweetalert';
import {Button, Col, Form, Input, Row, Select, Space, Switch, notification} from 'antd';
import 'react-toastify/dist/ReactToastify.css';
import {getAllAuthors} from '@/lib/frontendapi';
import {HtmlEditor} from '@/components/Common/HtmlEditor';
import Image from 'next/image';
import ErrorHandler from '@/lib/ErrorHandler';
import {PlusOutlined} from '@ant-design/icons';
import ModalForm from '@/components/Common/ModalForm';

export default function Authors() {
  const [authors, setAuthors]: any = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [FilteredAuthors, setFilteredAuthors] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showAuthorAddForm, setShowAuthorAddForm] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [previewAuthorImage, setPreviewAuthorImage] = useState('');
  const [newBlogAuthorImage, setNewBlogAuthorImage] = useState('');
  const [uploadAuthorError, setUploadAuthorError] = useState('');

  const initialFormData = {
    id: '',
    name: '',
    description: '',
    designation: '',
    profile_image: '',
    gender: '',
    linkedin: '',
  };
  const [formData, setFormData] = useState(initialFormData);

  const handleFormChange = (e: any) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const pageSize = 20;

  const fetchData = useCallback(() => {
    getAllAuthors().then(res => {
      setAuthors(res.data);
      handleFilteredAuthors(res.data, searchQuery);
    });
  }, [searchQuery]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleFilteredAuthors = (authors: [], searchValue: string) => {
    const filteredAuthors = authors.filter(
      (author: any) => author.name && author.name.toLowerCase().includes(searchValue.toLowerCase()),
    );
    setFilteredAuthors(filteredAuthors);
    setCurrentPage(1);
  };

  const onPageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleAuthorImageChange = (event: any) => {
    event.preventDefault();
    const selectedFile = event.target.files[0];

    if (selectedFile.size > 1.5 * 1024 * 1024) {
      notification.error({message: 'Image size should be less than or equal to 1.5MB'});
      return;
    }

    setFormData({...formData, profile_image: selectedFile});
    if (selectedFile.type.includes('image')) {
      if (selectedFile) {
        setPreviewAuthorImage(URL.createObjectURL(selectedFile));
        if (newBlogAuthorImage) {
          setNewBlogAuthorImage('');
        }
      }
    } else {
      setUploadAuthorError('Please upload an image file (JPEG,JPG,PNG).');
    }
  };

  const paginatedAuthors = paginate(FilteredAuthors, currentPage, pageSize);

  const handleAuthorSubmit = async (values: any) => {
    setProcessing(true);

    const data = new FormData();
    data.append('id', formData.id);
    data.append('name', values.name);
    data.append('description', formData.description);
    data.append('designation', values.designation);
    data.append('gender', values.gender);
    data.append('linkedin', values.linkedin);
    data.append('profile_image', formData.profile_image);

    createOrUpdateAuthor(data).then(res => {
      if (res.success) {
        notification.success({message: res.message});
        setFormData(initialFormData);
        fetchData();
        setShowAuthorAddForm(false);
      } else {
        // notification.error({ message: res.message });
      }
      setProcessing(false);
    });
  };

  const handleEdit = (id: any) => {
    const filteredRecords: any = authors.filter((record: any) => record.id === id);
    if (filteredRecords.length > 0) {
      setShowAuthorAddForm(true);
      const editedAuthor = filteredRecords[0];
      setFormData({
        id: editedAuthor.id,
        name: editedAuthor.name,
        gender: editedAuthor.gender,
        profile_image: editedAuthor.profile_image,
        designation: editedAuthor.designation,
        description: editedAuthor.description,
        linkedin: editedAuthor.linkedin,
      });
    }
  };

  const handleDeleteAuthor = (id: any) => {
    swal({
      title: 'Are you sure?',
      text: 'You want to delete the Author',
      icon: 'warning',
      dangerMode: true,
      buttons: ['Cancel', 'Yes, I am sure!'],
    }).then(willDelete => {
      if (willDelete) {
        deleteAuthor(id)
          .then(res => {
            if (res.success === true) {
              notification.success({message: res.message});
              fetchData();
            } else {
              console.log('Deletion failed');
            }
          })
          .catch(error => {
            ErrorHandler.showNotification(error);
          });
      } else {
        console.log('Deletion failed');
      }
    });
  };

  return (
    <>
      <div className="dash-right">
        <h1 className="data">Data Management</h1>
        <div className="data-management">
          <Space className="mt-3 float-end mb-3">
            <Input.Search
              size={'large'}
              onChange={e => setSearchQuery(e.target.value)}
              placeholder="Search author here.."
            />
            <Button
              size={'large'}
              type="primary"
              onClick={() => {
                setFormData(initialFormData);
                setShowAuthorAddForm(true);
              }}
              icon={<PlusOutlined />}>
              Add Author
            </Button>
          </Space>
          <div className="table-part mt-4 max-w">
            <div className="">
              <table className="rwd-table">
                <tbody>
                  <tr>
                    <th>Author Name</th>
                    <th>Description</th>
                    <th>Designation</th>
                    <th className="text-end">MANAGE </th>
                  </tr>

                  {paginatedAuthors.map((author: any, index: any) => (
                    <tr key={index}>
                      <td data-th="Author Name">
                        <p className="location text-truncate">{author.name}</p>
                      </td>
                      <td data-th="Description">
                        {author.description ? (
                          <div
                            className="location "
                            style={{ maxWidth: '250px' }}
                            dangerouslySetInnerHTML={{__html: author.description.substring(0, 50) + '...'}}></div>
                        ) : (
                          ''
                        )}
                      </td>
                      <td data-th="Designation">
                        <p className="location text-truncate">{author.designation}</p>
                      </td>
                      <td data-th="MANAGE" className="text-end">
                        <div className="d-flex justify-content-end align-items-center">
                          <i
                            className="fa-solid fa-pencil edit-pencil"
                            onClick={() => handleEdit(author.id)}
                          ></i>
                          <i
                            className="fa-regular fa-trash-can del-trash"
                            onClick={() => handleDeleteAuthor(author.id)}
                          ></i>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          <div className="pagination-wrapper mt-4">
            <div className="pagination-wrapper">
              <Pagination
                items={FilteredAuthors}
                currentPage={currentPage}
                pageSize={pageSize}
                onPageChange={onPageChange}
                activePage={currentPage}
              />
            </div>
          </div>
        </div>
      </div>

      <ModalForm
        open={showAuthorAddForm}
        onCancel={() => setShowAuthorAddForm(false)}
        title={formData.id ? 'Edit Author' : 'Add Author'}>
        <Form size="large" layout="vertical" onFinish={handleAuthorSubmit} initialValues={formData}>
          <Row gutter={10}>
            <Col md={24}>
              <Form.Item name={'name'} label={'Enter author name'} rules={[{required: true}]}>
                <Input placeholder="Enter author name" />
              </Form.Item>
            </Col>
            <Col xl={12}>
              <Form.Item
                name={'gender'}
                label={'Gender'}
                rules={[
                  {
                    required: true,
                  },
                ]}>
                <Select
                  options={[
                    {label: 'Male', value: 'male'},
                    {label: 'Female', value: 'female'},
                    {label: 'Other', value: 'other'},
                  ]}
                />
              </Form.Item>
            </Col>
            <Col xl={12}>
              <Form.Item name={'designation'} label={'Designation'} rules={[{required: true}]}>
                <Input placeholder="Enter author designation" />
              </Form.Item>
            </Col>
            <Col xl={24}>
              <Form.Item name={'linkedin'} label={'Linkedin'} rules={[{type: 'url'}]}>
                <Input placeholder="Enter linkedin url" />
              </Form.Item>
            </Col>
          </Row>
          <div className="form_field_sec">
            <HtmlEditor
              name={'description'}
              value={formData.description}
              onChange={(name, value: any) => setFormData({...formData, description: value})}
            />
            <label>Description*</label>
          </div>
          <label className="f-14 w-700 c-333333 w-100 mb-2 mt-4">Author Image</label>
          <div className="row">
            <div className="col-lg-2 col-md-3 col-3">
              {previewAuthorImage ? (
                <div className="logo-box-data">
                  <img src={previewAuthorImage} alt="Preview" className="" height={70} width={70} />
                </div>
              ) : (
                <div className="logo-box-data">
                  {formData.profile_image != '' ? (
                    <img
                      src={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/blogs/author/' + formData.profile_image}
                      alt="author-profile-image"
                      className=""
                      height={70}
                      width={70}
                    />
                  ) : (
                    <img
                      src={process.env.NEXT_PUBLIC_BASE_URL + 'images/logo-cir.png'}
                      alt="author-profile-image"
                      className=""
                      width={70}
                      height={70}
                    />
                  )}
                </div>
              )}
            </div>
            <div className="col-lg-8 col-md-6 col-sm-12 col-9">
              <a>
                <div className="uploade-btn  max-260">
                  <input type="file" id="logo" onChange={handleAuthorImageChange} accept="image/jpeg, image/png" />
                  <div className="btn-a primary-size-16 upload-text-m-13 btn-bg-0055BA tab-w-100">
                    <i className="fa-solid fa-upload mx-2"></i> Upload Author Image
                  </div>
                </div>
              </a>
              {uploadAuthorError && <p className="error mt-2">{uploadAuthorError}</p>}
            </div>
          </div>
          <div className="text-right mt-3">
            <button type="button" className="cancel" onClick={() => setShowAuthorAddForm(false)}>
              Cancel
            </button>
            <button type="submit" className="save" disabled={false}>
              {processing ? 'Please wait...' : 'Save'}
            </button>
          </div>
        </Form>
      </ModalForm>
    </>
  );
}
