import Search from 'antd/lib/transfer/search';
import {useMemo, useState} from 'react';
import styles from './style.module.css';
import {DataPointTableData, useGetDataPoints} from '@/modules/admin/automation/query/useGetDataPoints';
import CommonTable, {TableColumn} from '@/components/Common/CommonTable';
import {CustomOptionDropdown} from '@/components/Common/CustomOptionDropdown';
import {useGetDataPointOptions} from '@/hooks/useGetDataTypeOptions';
import {IntersectionObserverContainer} from '@/components/Company/InterSectioObserver';
import {Spinner} from 'react-bootstrap';
import {useDebounce} from '@/hooks/useDebounceCallback';

export const AutomationDataPoint = () => {
  const [searchName, setSearchName] = useState('');
  const [name, setName] = useState('');
  const [selectedUserType, setSelectedUserType] = useState<number>();
  const [selectedDataType, setSelectedDataType] = useState<string>('');
  const limit = 20;
  const [userTypeOpen, setUserTypeOpen] = useState(false);
  const [dataTypeOpen, setDataTypeOpen] = useState(false);
  const {data: options} = useGetDataPointOptions();

  const {
    data: tableData,
    fetchNextPage,
    isFetchingNextPage,
    isLoading,
  } = useGetDataPoints({
    search: name,
    selectedUserType,
    selectedDataType,
    per_page: limit,
  });

  const allData = useMemo(() => {
    return tableData?.pages?.map(item => item).flat() || [];
  }, [tableData]);

  const columns: TableColumn[] = [
    {
      key: 'data_point',
      title: <th>DATA POINT</th>,
      dataIndex: 'data_point',
      render: (item: DataPointTableData) => (
        <div className={styles.data_point_name}>
          <span>{item?.data_point_name}</span>
        </div>
      ),
    },
    {
      key: 'user_type',
      title: <th>USER TYPE</th>,
      dataIndex: 'user_type',
      render: (item: DataPointTableData) => (
        <div className={styles.user_type_container}>
          {item?.user_type?.split(',').map((userType: string, index: number) => (
            <span className={styles.user_type} key={index}>
              {userType === '1' ? 'Candidate' : 'Employer'}
            </span>
          ))}
        </div>
      ),
    },
    {
      key: 'data_type',
      title: <th>DATA TYPE</th>,
      dataIndex: 'data_type',
      render: (item: DataPointTableData) => <span>{item?.data_type}</span>,
    },
  ];

  const handleFetchNextPage = (isIntersecting: boolean) => {
    if (isIntersecting && tableData) {
      fetchNextPage();
    }
  };

  const handleSearchName = (e: any) => {
    setName(e.target.value);
  };

  const handleSearch = useDebounce(handleSearchName, 500);

  return (
    <div className={styles.data_point_container}>
      <h4>Data Points</h4>

      <div className={styles.data_header_container}>
        <div className={styles.search_container}>
          <Search
            placeholder="Search..."
            onChange={(e: any) => {
              setSearchName(e);
              handleSearch(e);
            }}
            prefixCls="datapoint_search"
          />
        </div>
        <div className={styles.dropdown_container}>
          <CustomOptionDropdown
            placeholder="User Type"
            items={options?.userTypes ?? []}
            labelExtractor={(item: any) => item?.label}
            onSelect={selected => setSelectedUserType(selected?.value ? Number(selected?.value) : undefined)}
            valueExtractor={(item: any) => item?.value}
            open={dataTypeOpen}
            setOpen={setDataTypeOpen}
          />
          <CustomOptionDropdown
            placeholder="Data Type"
            items={options?.dataTypes ?? []}
            labelExtractor={(item: any) => item?.label}
            onSelect={selected => setSelectedDataType(selected?.value)}
            valueExtractor={(item: any) => item?.value}
            open={userTypeOpen}
            setOpen={setUserTypeOpen}
          />
        </div>
      </div>
      <IntersectionObserverContainer onIntersect={handleFetchNextPage}>
        <CommonTable columns={columns} data={allData || []} loading={isLoading} />
      </IntersectionObserverContainer>
      {isFetchingNextPage && (
        <div style={{textAlign: 'center', marginTop: 16}}>
          <Spinner
            style={{
              color: '#0070F5',
            }}
          />
        </div>
      )}
    </div>
  );
};
