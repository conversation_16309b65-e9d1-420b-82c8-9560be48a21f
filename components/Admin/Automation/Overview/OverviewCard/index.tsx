import {useRouter} from 'next/router';
import styles from './style.module.css';

interface OverviewCardProps {
  title: string;
  value: number;
  tab?: string;
}

export const AutomationOverviewCard = ({title, value, tab}: OverviewCardProps) => {
  const router = useRouter();
  return (
    <div className={styles.overview_card}>
      <h6>{title}</h6>

      <div className={styles.overview_card_value}>
        <h4>{value}</h4>
        <p onClick={() => router.push(`/admin/automation/contacts${tab ? `?tab=${tab}` : ''}`)}>View All</p>
      </div>
    </div>
  );
};
