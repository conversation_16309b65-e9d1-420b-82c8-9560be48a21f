.overview_container {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.overview_top_container {
  display: flex;
  padding: 24px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  gap: 24px;
  background: #0055ba;
}
.overview_header {
  display: flex;
  align-items: flex-end;
  gap: 24px;
  align-self: stretch;
  justify-content: space-between;
}

.overview_header h5 {
  color: #fff;
  font-size: 22px;
  font-weight: 500;
  line-height: 120%;
}

.overview_card_container {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  align-self: stretch;
}
.statistic_header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.overview_statistic {
  display: flex;
  padding: 24px;
  flex-direction: column;
  gap: 4px;
  border-radius: 10px;
  border: 1px solid #eee;
  background: #fff;
}
.statistic_header h2 {
  color: #191d23;
  font-size: 26px;
  font-weight: 700;
  line-height: 120%;
}
.statistic_card_section {
  display: flex;
  align-items: center;
  gap: 40px;
}
.line {
  width: 3px;
  height: 52px;
  background: #d9d9d9;
}
.statistic_card_container {
  display: flex;
  align-items: center;
  gap: 4px;
}
.statistic_card p {
  color: #747474;
  font-weight: 16px;
}

.card_content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card_content h6 {
  color: #191919;
  font-size: 22px;
  font-weight: 700;
}

.card_content span {
  color: #3d9f79;
  font-size: 16px;
  font-weight: 600;
  line-height: 140%;
}
.date_range_container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 0px 12px 12px;
}
.time_period_dropdown {
  width: 320px !important;
}
.loading_container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
