import {
  Chart as ChartJS,
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale,
  Filler,
  Tooltip,
  BarElement,
  Legend,
} from 'chart.js';
import {Bar, Line} from 'react-chartjs-2';
import styles from './style.module.css';
import {OverviewData} from '@/modules/admin/automation/query/useGetEmailStatistic';
import {OverviewGraphEntity} from '@/dto/OverviewGraph/OverviewGraphEntity';

ChartJS.register(LineElement, PointElement, LinearScale, CategoryScale, Filler, Tooltip, Legend, BarElement);

interface EmailStatisticChartProps {
  props: OverviewData;
}

export const EmailStatisticsChart = ({props}: EmailStatisticChartProps) => {
  const {delivered, end_date, graph_data, message, opened, spam, start_date} = props;

  const {data: chartData, labels} = new OverviewGraphEntity(graph_data);
  const data = {
    labels: labels,
    datasets: [
      {
        label: 'Delivered',
        data: chartData?.delivered,
        borderColor: '#FDCA40',
        fill: false,
        tension: 0.5,
      },
      {
        label: 'Opened',
        data: chartData?.opened,
        borderColor: '#48E5C2',
        fill: false,
        tension: 0.5,
      },
      {
        label: 'Spam',
        data: chartData.spam,
        borderColor: '#FD7373',
        fill: false,
        tension: 0.5,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    stacked: false,
    plugins: {
      htmlLegend: {
        containerID: 'legend-container',
      },
      legend: {
        display: false,
      },
    },
    scales: {
      y: {
        type: 'linear' as const,
        display: false,
        position: 'left' as const,
      },
      y1: {
        type: 'linear' as const,
        display: false,
        position: 'right' as const,
        grid: {
          drawOnChartArea: false,
        },
      },
      x: {
        display: true,
        position: 'bottom' as const,
        grid: {
          drawOnChartArea: false,
        },
      },
    },
  };

  const barChartData = {
    labels: labels,
    datasets: [
      {
        label: 'Delivered',
        data: chartData?.delivered,
        backgroundColor: '#FDCA40',
      },
      {
        label: 'Opened',
        data: chartData?.opened,
        backgroundColor: '#48E5C2',
      },
      {
        label: 'Spam',
        data: chartData?.spam,
        backgroundColor: '#FD7373',
      },
    ],
  };

  const barChartOptions = {
    responsive: true,
    plugins: {
      title: {
        display: false,
        text: 'Email Statistics - Stacked Bar Chart',
      },
      legend: {
        display: false,
        position: 'top',
      },
    },
    scales: {
      x: {
        stacked: true,
      },
      y: {
        stacked: true,
        beginAtZero: true,
        grid: {
          drawOnChartArea: false,
        },
      },
    },
  };

  return (
    <>
      <div className={styles.statistic_chart} id="legend-container">
        <div className={styles.statistic_chart_header}></div>
        {Object.entries(graph_data).length > 0 && Object.entries(graph_data).length <= 4 ? (
          <Bar data={barChartData} options={barChartOptions as any} />
        ) : Object.entries(graph_data).length > 4 ? (
          <Line data={data} options={chartOptions} />
        ) : (
          <div className={styles.no_data_found}>No Data Found...</div>
        )}
      </div>
      <div>
        <div className={styles.statistic_chart_footer}>
          <div className={styles.statistic_chart_footer_item}>
            <div className={styles.statistic_chart_footer_item_color} style={{backgroundColor: '#FDCA40'}}></div>
            <span>Delivered</span>
          </div>
          <div className={styles.statistic_chart_footer_item}>
            <div className={styles.statistic_chart_footer_item_color} style={{backgroundColor: '#48E5C2'}}></div>
            <span>Opened</span>
          </div>
          <div className={styles.statistic_chart_footer_item}>
            <div className={styles.statistic_chart_footer_item_color} style={{backgroundColor: '#FD7373'}}></div>
            <span>Spam</span>
          </div>
        </div>
      </div>
    </>
  );
};
