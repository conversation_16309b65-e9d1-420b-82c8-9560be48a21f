import CommonTable, {TableColumn} from '@/components/Common/CommonTable';
import Search from 'antd/lib/transfer/search';
import styles from './style.module.css';
import {ContactTableData} from '@/modules/admin/automation/query/useGetContactLists';
import {IntersectionObserverContainer} from '@/components/Company/InterSectioObserver';
import {Spinner} from 'react-bootstrap';
import {CustomOptionDropdown} from '@/components/Common/CustomOptionDropdown';
import {useGetCountries} from '@/modules/common/query/useGetCountries';
import {useState} from 'react';

interface ContactTableContainerProps {
  columns: TableColumn[];
  data: ContactTableData[];
  setLocation: (location: string) => void;
  onSearch: (search: string) => void;
  fetchNextPage: () => void;
  isFetchingNextPage: boolean;
  loading?: boolean;
}

export const ContactTableContainer = ({
  columns,
  data,
  setLocation,
  onSearch,
  fetchNextPage,
  isFetchingNextPage,
  loading,
}: ContactTableContainerProps) => {
  const handleIntersection = (isIntersecting: boolean) => {
    if (isIntersecting && data) {
      fetchNextPage();
    }
  };
  const {data: countries} = useGetCountries();
  const [isOpen, setIsOpen] = useState(false);
  return (
    <>
      <div className={styles.table_container}>
        <div className={styles.filter_container}>
          <div className={styles.search_container}>
            <Search
              placeholder="Search..."
              onChange={(e: any) => onSearch(e.target.value)}
              prefixCls={`${styles.search_input} datapoint_search`}
            />
          </div>
          <CustomOptionDropdown
            items={
              countries?.map(el => {
                return {
                  label: el.country_name,
                  value: el.id,
                };
              }) ?? []
            }
            placeholder="Location"
            labelExtractor={(item: any) => item.label}
            valueExtractor={(item: any) => item.value}
            onSelect={(selectedOption: {label: string; value: string}) => setLocation(selectedOption?.value)}
            open={isOpen}
            setOpen={setIsOpen}
          />
        </div>
        <IntersectionObserverContainer onIntersect={handleIntersection}>
          <CommonTable columns={columns} data={data ?? []} loading={loading} />
        </IntersectionObserverContainer>
        {isFetchingNextPage && (
          <div style={{textAlign: 'center', marginTop: 16}}>
            <Spinner
              style={{
                color: '#0070F5',
              }}
            />
          </div>
        )}
      </div>
    </>
  );
};
