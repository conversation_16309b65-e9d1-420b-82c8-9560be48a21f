import styles from './style.module.css';
import moment from 'moment';
import {BadgeChip} from '@/components/Common/Badge';
import {ButtonUi} from '@/ui/Button';
import {useClickOutside} from '@/hooks/useOutSideClick';
import {useRef, useState} from 'react';
import {useRouter} from 'next/router';
import {useDeleteTemplateList} from '@/modules/admin/automation/mutation/useDeleteTemplate';

interface TemplateCardProps {
  props: any;
}

export const TemplateCard = ({props}: TemplateCardProps) => {
  const {mutate: deleteTemplate} = useDeleteTemplateList();
  const optionRef = useRef(null);
  const [showOption, setShowOption] = useState(false);
  useClickOutside(optionRef, [], () => {
    setShowOption(false);
  });

  const router = useRouter();

  const handleEditClick = () => {
    router.push(`/admin/automation/template-list/edit-template/${props.id}`);
  };

  const handleDeleteClick = () => {
    deleteTemplate({
      id: props.id,
    });
  };
  return (
    <div className={styles.template_card}>
      <div className={styles.template_card_header}>
        <img src={'/icons/automation/bookmark-primary.svg'} alt="bookmark" width={24} height={24} />
        <div ref={optionRef} className={styles.template_card_option_container}>
          <img
            src={'/icons/automation/menu_primary.svg'}
            alt="menu"
            width={24}
            height={24}
            onClick={() => setShowOption(!showOption)}
          />
          {
            <div className={styles.template_card_option} onClick={() => setShowOption(!showOption)}>
              {showOption && (
                <div className={styles.option_dropdown}>
                  <ul>
                    <li onClick={handleEditClick}>Edit</li>
                    <li>Preview</li>
                    <li>Copy</li>
                    <li
                      onClick={handleDeleteClick}
                      style={{
                        color: '#D04E4F',
                      }}>
                      Delete
                    </li>
                  </ul>
                </div>
              )}
            </div>
          }
        </div>
      </div>
      <div className={styles.template_card_image}>
        {/* <img src={props.thumbnail} alt="thumbnail" width={284} height={315} /> */}
      </div>
      <div className={styles.template_content}>
        <h5>{props.template_name}</h5>
        <p>Updated on {props.updated_at ? moment(props.updated_at).format('DD/MM/YYYY') : 'N/A'}</p>

        <div>
          <BadgeChip
            label={props?.status === 'in_use' ? 'In Use' : 'DRAFT'}
            variant={props?.status === 'in_use' ? 'active' : 'inactive'}
          />
        </div>
      </div>
      <div>
        <ButtonUi
          variant="contained"
          color="primary"
          onClick={handleEditClick}
          style={{
            width: '100%',
          }}>
          Edit
        </ButtonUi>
      </div>
    </div>
  );
};
