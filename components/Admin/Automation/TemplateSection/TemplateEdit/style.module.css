.template_edit_container {
  background-color: white;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  border-radius: 8px;
}

.template_header_container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.template_name_container {
  display: flex;
  align-items: center;
  gap: 8px;
}
.template_name_input,
.template_name_container h5 {
  color: black;
  border: none !important;
  font-size: 22px;
  font-weight: 500;
  width: fit-content;
  max-width: 156px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 !important;
}

.template_name_container img {
  cursor: pointer;
}

.back_container {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}
.back_container p {
  color: #0070f5;
  font-size: 16px;
  font-weight: 600;
  line-height: 140%;
}
.form_container {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 0 24px;
  align-items: center;
}
.address_dropdown {
  height: 56px;
  margin-bottom: 16px;
}
.address_dropdown span {
  color: #2c2c2c;
  font-feature-settings:
    'liga' off,
    'clig' off;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: -0.5px;
}
.preview_header {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.preview_content {
  padding-top: 24px;
}
.button_container button {
  border: none !important;
  box-shadow: none !important;
  background-color: transparent;
}
