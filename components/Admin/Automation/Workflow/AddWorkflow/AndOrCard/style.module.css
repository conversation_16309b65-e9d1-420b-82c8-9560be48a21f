.boolean_card {
  padding: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  align-content: center;
  gap: 8px;
  gap: 8px;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  background: #fff;
}
.boolean_button {
  display: flex;
  width: 90px;
  padding: 8px 0px;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.04);
  background: #fff;
}
.boolean_button:hover,
.selected {
  border: 1px solid #0070f5;
  box-shadow:
    0px 1px 3px 0px rgba(21, 21, 21, 0.12),
    0px 2px 5px 0px rgba(21, 21, 21, 0.1),
    0px 4px 12px 0px rgba(21, 21, 21, 0.12);
  transition: all 0.3s ease;
}
