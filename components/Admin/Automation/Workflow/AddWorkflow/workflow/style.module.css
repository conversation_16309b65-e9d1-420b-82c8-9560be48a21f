.workflow_container {
  background-color: white;
  padding: 24px;
  border-radius: 8px;
}
.workflow_container h1 {
  color: #191919;
  font-size: 22px;
  font-weight: 500;
  line-height: 120%;
  padding-bottom: 40px;
  padding-top: 12px;
}
.workflow_button_container {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  gap: 8px;
  padding-top: 16px;
}
.workflow_create_container {
  gap: 16px;
  width: 100%;
  background: url('/images/automation/dotted-bg.png') no-repeat center;
  background-size: cover;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  height: 100vh;
}
.plus_container {
  display: inline-flex;
  padding: 4px;
  align-items: center;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  background: #f9f9f9;
}
.workflow_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
}
.condition_card {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.workflow_generate_container {
  display: flex;
  align-items: start;
  gap: 8px;
}
