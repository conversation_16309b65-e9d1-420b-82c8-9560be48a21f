import styles from './style.module.css';
import {useMemo, useRef, useState} from 'react';
import {AddWorkflowCard} from '../AddWorkflowCard';
import {BooleanCard} from '../AndOrCard';
import {Handle, Position} from '@xyflow/react';
import {CompletedCard} from './WhenBecomeCard';
import NiceModal from '@ebay/nice-modal-react';
import {AddConditionModal} from '@/components/modal/AddConditionModal';
import {AddContactModal} from '@/components/modal/AddContactModal';
import {FrequencyModal} from '@/components/modal/AddFrequencyModal';
import {useRouter} from 'next/router';
import {useClickOutside} from '@/hooks/useOutSideClick';

interface CustomNodeProps {
  data: any;
  isConnectable: boolean;
  dragging: boolean;
}

export const CustomNodes = ({data, ...props}: CustomNodeProps) => {
  const addNode = data?.addNode;
  const nodes = data?.nodes;
  const [isConditionModalOpen, setConditionModalOpen] = useState(false);
  const [isBooleanModalOpen, setBooleanModalOpen] = useState(false);
  const [selected, setSelected] = useState<'AND' | 'OR' | undefined>();
  const router = useRouter();
  const containerRef = useRef(null);
  const booleanContainerRef = useRef(null);

  const activeIndex = () => {
    const lastNode = nodes[nodes.length - 1];
    switch (lastNode?.type) {
      case 'condition':
        return 1;
      case 'contact':
        return 2;
      case 'frequency':
        return 3;
      case 'templates':
        return 4;
      default:
        return 0;
    }
  };

  const currentActiveIndex = useMemo(() => activeIndex(), [nodes]);

  useClickOutside(containerRef, [], () => {
    console.log('Clicked outside');
    setConditionModalOpen(false);
  });

  const openModal = (type: any) => {
    setConditionModalOpen(true);
  };
  const closeModal = () => {
    setConditionModalOpen(false);
  };

  const openBooleanModal = () => {
    setBooleanModalOpen(true);
  };
  const segmentConditionHasData = useMemo(() => {
    return (
      data?.contact?.condition?.length > 0 &&
      Object.entries(
        data?.contact?.condition[0] as {
          [key: string]: {
            label: string;
            value: string;
          };
        },
      ).every(([key, value]) => value.label !== '' && value.value !== '')
    );
  }, [data]);

  const templateData = [
    {
      key: 'USING',
      value: data?.templates?.name,
    },
  ];

  const contactData = [
    {
      key: 'SEND EMAIL TO',
      value: data?.contact?.sendEmailTo?.label,
    },
    segmentConditionHasData
      ? {
          key: 'SEGMENT',
          value: `${data.contact.condition[0].conditionType === 'include' ? 'including' : 'excluding'} ${
            data?.contact?.condition?.[0]?.conditionKey?.label
          } : ${data?.contact?.condition?.[0]?.conditionValue?.label} ${
            data?.contact?.condition?.length >= 2 ? `and ${data?.contact?.condition?.length - 1} more` : ''
          }`,
        }
      : {},
  ];

  const frequencyData = [
    {
      key: 'EVERY',
      value: `${data?.frequency?.every?.periodValue} ${data?.frequency?.every?.periodType} @ ${data?.frequency?.time?.timeValue} ${data?.frequency?.time?.timeType}`,
    },
    {
      key: 'CONDITION',
      value:
        data?.frequency?.startTime?.option === 'delay'
          ? `After ${data?.frequency?.startTime?.delayTime?.delayValue} ${data?.frequency?.startTime?.delayTime?.delayType}`
          : data?.frequency?.startTime?.option === 'start'
            ? `On ${data?.frequency?.startTime?.dateTime?.date} at ${data?.frequency?.startTime?.dateTime?.time}`
            : 'Immediately',
    },
  ];

  const renderCompletedCard = () => {
    switch (data?.type) {
      case 'condition':
        return (
          <CompletedCard
            data={data}
            items={[
              {
                key: 'WHEN',
                value: data?.condition?.when?.label,
              },
              {
                key: data?.condition?.operator?.label,
                value: `${data?.condition?.becomes?.label}  ${data?.condition?.from?.label} ${
                  data?.condition?.from?.label && data?.condition?.to?.label && ','
                } ${data?.condition?.to?.label}`,
              },
            ]}
            openBooleanModal={openBooleanModal}
          />
        );
      case 'contact':
        return <CompletedCard data={data} items={contactData as any} openBooleanModal={openBooleanModal} />;
      case 'frequency':
        return <CompletedCard data={data} items={frequencyData} openBooleanModal={openBooleanModal} />;
      case 'templates':
        return <CompletedCard data={data} items={templateData} size="small" openBooleanModal={openBooleanModal} />;
    }
  };

  const handleCardClick = (type: 'AND' | 'OR') => {
    setSelected(type);
    if (currentActiveIndex === 4 && data?.isLastNode) {
      setConditionModalOpen(true);
      return;
    }

    switch (currentActiveIndex) {
      case 1:
        NiceModal.show(AddConditionModal, {
          closeModal,
          selectedLogic: type,
          id: 'condition',
        });
        setBooleanModalOpen(false);
        break;
      case 2:
        NiceModal.show(AddContactModal, {
          closeModal,
          selectedLogic: type,
          id: 'contacts',
        });
        setBooleanModalOpen(false);
        break;
      case 3:
        NiceModal.show(FrequencyModal, {
          closeModal,
          selectedLogic: type,
          id: 'condition',
        });
        setBooleanModalOpen(false);
        break;
      case 4:
        router.push('/admin/automation/workflows/template');
        setBooleanModalOpen(false);
      default:
        break;
    }
  };

  return (
    <div>
      <Handle type="target" position={Position.Top} style={{left: 100, opacity: 0}} id="t" />
      <Handle
        type="target"
        position={Position.Left}
        id="l"
        style={{
          opacity: 0,
        }}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        id="b"
        style={{
          bottom: 0,
          opacity: 0,
        }}
      />
      <Handle
        type="source"
        position={Position.Right}
        id="r"
        style={{
          right: 0,
          opacity: 0,
        }}
      />
      {renderCompletedCard()}

      <div className={styles.custom_boolean_add} ref={booleanContainerRef}>
        {isBooleanModalOpen && currentActiveIndex !== 4 && (
          <div className={styles.custom_boolean_card}>
            <BooleanCard selected={selected} onClick={value => handleCardClick(value)} />
          </div>
        )}

        {isBooleanModalOpen && currentActiveIndex === 4 && (
          <div className={styles.new_flow_add_container}>
            <BooleanCard selected={selected} onClick={value => handleCardClick(value)} />
            {isConditionModalOpen && (
              <div className={styles.custom_added_node} ref={containerRef}>
                <div className={styles.modal}>
                  <AddWorkflowCard
                    addNode={addNode}
                    closeModal={closeModal}
                    selectedLogic={selected}
                    nodes={nodes}
                    activeIndex={currentActiveIndex}
                    setBooleanModalOpen={setBooleanModalOpen}
                  />
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      <div className={styles.customNodes_add__container}>
        {data?.isLastNode && currentActiveIndex !== 4 && (
          <div className={styles.plus_container} onClick={() => openModal('add')}>
            <img src={'/icons/automation/prime_plus.svg'} height={24} width={24} alt="prime_plus" />
          </div>
        )}

        {isConditionModalOpen && currentActiveIndex !== 4 && (
          <div className={styles.custom_added_node} ref={containerRef}>
            <div className={styles.modal}>
              <AddWorkflowCard
                addNode={addNode}
                closeModal={closeModal}
                selectedLogic={selected}
                nodes={nodes}
                activeIndex={currentActiveIndex}
                setBooleanModalOpen={setBooleanModalOpen}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
