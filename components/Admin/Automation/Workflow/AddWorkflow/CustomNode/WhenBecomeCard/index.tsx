import {<PERSON><PERSON>abel<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Position} from '@xyflow/react';
import styles from '../style.module.css';
import {useState} from 'react';
import NiceModal from '@ebay/nice-modal-react';
import clsx from 'clsx';
import {FaArrowRight} from 'react-icons/fa';
import {AddConditionModal} from '@/components/modal/AddConditionModal';
import {useWorkflowNodes} from '@/hooks/useAddWorkflowItem';
import {AddContactModal} from '@/components/modal/AddContactModal';
import {NodesData} from '@/modules/admin/automation/types';
import {FrequencyModal} from '@/components/modal/AddFrequencyModal';
import {useRouter} from 'next/router';

interface WhenBecomeCardProps {
  items: {
    key: string;
    value: string;
  }[];
  data: any;
  size?: 'small' | 'medium' | 'large';
  openBooleanModal?: () => void;
}

export const CompletedCard = ({items, data, size = 'medium', openBooleanModal}: WhenBecomeCardProps) => {
  const updateNodes = data?.updateNodes;
  const [showActionCard, setShowActionCard] = useState(false);
  const {deleteNode, nodes} = useWorkflowNodes();
  const currentNodes: NodesData = data?.nodes?.find((node: any) => node.id === data.id.toString());
  const lastNode = nodes[nodes.length - 1];
  const router = useRouter();

  const handleAddNode = () => {
    openBooleanModal?.();
    setShowActionCard(false);
  };

  const handleEditNode = () => {
    switch (currentNodes?.type) {
      case 'condition':
        NiceModal.show(AddConditionModal, {
          nodeId: currentNodes.id,
        });
        break;
      case 'contact':
        NiceModal.show(AddContactModal, {
          nodeId: currentNodes.id,
        });
        break;
      case 'frequency':
        NiceModal.show(FrequencyModal, {
          nodeId: currentNodes.id,
        });
        break;
      case 'templates':
        router.push(`/admin/automation/workflows/template?nodeId=${currentNodes.id}`);
      default:
    }
  };

  const handleDeleteNode = () => {
    deleteNode(currentNodes);
  };

  const handleShowActionCard = () => {
    setShowActionCard(prev => !prev);
  };

  const showPlusIcon =
    lastNode?.id === currentNodes?.id && (currentNodes?.type === 'condition' || currentNodes?.type === 'templates');

  return (
    <div className={styles.customNodes_container}>
      {items.map((item, index) => (
        <>
          <h6>{item.key}</h6>
          {items.every(item => item.value === '') && <div className={styles.line}></div>}
          <p>{item.value}</p>
        </>
      ))}

      <div
        className={`${showActionCard && styles.show_card} ${styles.setting_config_container}`}
        onClick={handleShowActionCard}>
        <FaArrowRight />
      </div>

      <div
        className={clsx(
          styles.customNodes_action_container,
          showActionCard && styles.show_action_card,
          !showPlusIcon && styles.plus_icon_card,
        )}>
        {showPlusIcon && (
          <div className={styles.plus_container} onClick={handleAddNode}>
            <img src={'/icons/automation/prime_plus.svg'} height={18} width={18} alt="prime_plus" />
          </div>
        )}
        <div className={styles.edit_container} onClick={handleEditNode}>
          <img src={'/icons/automation/edit.svg'} height={18} width={18} alt="prime_edit" />
        </div>
        <div className={styles.delete_container} onClick={handleDeleteNode}>
          <img src={'/icons/automation/delete.svg'} height={18} width={18} alt="prime_delete" />
        </div>
      </div>
    </div>
  );
};
