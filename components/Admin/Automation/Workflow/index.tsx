import React, {useRef} from 'react';
import styles from './style.module.css';
import {ButtonUi} from '@/ui/Button';
import {SearchContainer} from '@/components/Common/SearchContainer';
import {useGetWorkflowList, WorkFLowList} from '@/modules/admin/automation/query/useGetWorkflowList';
import CommonTable, {TableColumn} from '@/components/Common/CommonTable';
import {BadgeChip} from '@/components/Common/Badge';
import {useRouter} from 'next/router';
import {WorkflowActionCard} from './WorkflowAction';
import {useWorkflowNodes} from '@/hooks/useAddWorkflowItem';
import {useWorkflowStore} from '@/modules/admin/automation/store';

function WorkflowSection() {
  const {data: Workflow} = useGetWorkflowList();
  const router = useRouter();

  const columns: TableColumn[] = [
    {
      title: <th>TITLE</th>,
      render: (record: WorkFLowList) => (
        <div className={styles.workflow_title_container}>
          {/* <Checkbox /> */}
          <span className={styles.workflow_title}>{record.name}</span>
        </div>
      ),
      key: 'title',
    },
    {
      key: 'status',
      title: <th>STATUS</th>,
      render: (record: WorkFLowList) => (
        <div className={styles.workflow_status_container}>
          <BadgeChip
            label={record.status === 1 ? 'ACTIVE' : 'STOPPED'}
            variant={record.status === 1 ? 'active' : 'inactive'}
          />
        </div>
      ),
    },
    {
      key: 'instance',
      title: <th>INSTANCE</th>,
      render: (record: WorkFLowList) => (
        <div className={styles.workflow_instance_container}>
          <span>{record.instance ?? '-'}</span>
        </div>
      ),
    },
    {
      key: 'email_sent',
      title: <th>EMAIL</th>,
      render: (record: WorkFLowList) => (
        <div className={styles.workflow_email_container}>
          <span>{record.email_sent ?? '-'}</span>
        </div>
      ),
    },
    {
      key: 'sent_on',
      title: <th>SENT ON</th>,
      render: (record: WorkFLowList) => (
        <div className={styles.workflow_sent_on_container}>
          <span>{record.start_date ?? '-'}</span>
        </div>
      ),
    },
    {
      key: 'action',
      title: <th className={styles.workflow_action_header}></th>,
      render: (record: WorkFLowList) => <WorkflowActionCard record={record} />,
    },
  ];
  const {setStoreEdges, setStoreNodes, setBasicInfo} = useWorkflowStore();

  const handleCreateWorkflow = () => {
    setStoreEdges([]);
    setStoreNodes([]);
    setBasicInfo({
      title: '',
      instance: '',
    });
    router.push('/admin/automation/workflows/add/basic_info');
  };
  return (
    <div className={styles.workflow_container}>
      <div className={styles.template_header_container}>
        <h5>Workflows</h5>
        <ButtonUi variant="contained" color="primary" onClick={handleCreateWorkflow}>
          Create Workflow
        </ButtonUi>
      </div>
      <div className={styles.template_filter_container}>
        <div className={styles.filter_left}>
          <div className={styles.search_container}>
            <SearchContainer placeholder="Search..." />
          </div>
          {/* <CustomOptionDropdown placeholder="All" items={[]}  /> */}
        </div>
      </div>

      <div>
        <CommonTable columns={columns} data={Workflow ?? []} />
      </div>
    </div>
  );
}

export default WorkflowSection;
