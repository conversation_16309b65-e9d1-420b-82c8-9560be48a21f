import React, {useState, useMemo, useEffect} from 'react';
import {ReactFlow, Background} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import Image from 'next/image';
import styles from './style.module.css';
import {AddWorkflowCard} from '../AddWorkflow/AddWorkflowCard';
import {useWorkflowNodes} from '@/hooks/useAddWorkflowItem';
import {ButtonUi} from '@/ui/Button';
import {CustomNodes} from '../AddWorkflow/CustomNode';
import {useGetWorkflowById} from '@/modules/admin/automation/query/useGetWorkflowById';
import {useRouter} from 'next/router';

import {useConvertWorkflowToApi} from '@/modules/admin/automation/hooks/useConverWorkflowToApi';
import {useWorkflowStore} from '@/modules/admin/automation/store';
import {useCreateWorkflow} from '@/modules/admin/automation/mutation/useCreateWorkflow';
import {useConvertApiToWorkflow} from '@/modules/admin/automation/hooks/useConvertApiToWorkflow';
import {useUpdateWorkflows} from '@/modules/admin/automation/mutation/useUpdateWorkflow';

export const ReactFlowSection = () => {
  const {addNode, edges, nodes, onEdgesChange, onNodesChange, onConnect, updateNodes, setStoreEdges, setStoreNodes} =
    useWorkflowNodes();
  const router = useRouter();
  const id = router.query.id as string;
  const {data: workflowDetail} = useGetWorkflowById({
    id: Number(id),
  });
  const {basicInfo, reset} = useWorkflowStore();
  const {mutate: createWorkflow} = useCreateWorkflow();
  const {mutate: updateWorkflow} = useUpdateWorkflows();

  const [isModalOpen, setModalOpen] = useState(false);
  const [modalType, setModalType] = useState('');

  const nodeTypes = useMemo(
    () => ({
      custom: CustomNodes,
    }),
    [],
  );

  const openModal = (type: any) => {
    setModalType(type);
    setModalOpen(true);
  };

  const closeModal = () => {
    setModalOpen(false);
  };

  const {nodes: nodesById, edges: edgesById} = useConvertApiToWorkflow({
    workflow: workflowDetail?.workflow,
  });

  const customNodes = nodes?.map((node: any, index: number) => ({
    ...node,
    type: 'custom',
    draggable: false,
    isConnectable: false,
    data: {
      ...node.data,
      isLastNode: index === nodes.length - 1,
      addNode,
      edges,
      onEdgesChange,
      nodes,
      updateNodes,
      id: index + 1,
    },
  }));

  const {workflows} = useConvertWorkflowToApi({
    nodes: nodes,
    basicInfo,
    id: Number(id),
  });

  const handleAddWorkflow = () => {
    createWorkflow(
      {
        workflows: workflows as any,
      },
      {
        onSuccess: () => {
          reset();
          router.push('/admin/automation/workflows');
        },
      },
    );
  };

  useEffect(() => {
    if (id) {
      setStoreNodes(nodesById);
      setStoreEdges(edgesById as any);
    }
  }, []);

  const handleEditWorkflow = () => {
    updateWorkflow(
      {
        id: Number(id),
        workflows: workflows as any,
      },
      {
        onSuccess: () => {
          router.push('/admin/automation/workflows');
          reset();
        },
      },
    );
  };

  return (
    <div style={{width: '100%', height: '90vh', position: 'relative'}}>
      <div className={styles.workflow_header}>
        <div className={styles.workflow_header_left}>
          {nodes.length === 0 && (
            <div className={styles.plus_container} onClick={() => openModal('add')}>
              <img src={'/icons/automation/prime_plus.svg'} height={24} width={24} alt="prime_plus" />
            </div>
          )}

          {isModalOpen && modalType === 'add' && (
            <div className={styles.modal}>
              <AddWorkflowCard addNode={addNode} closeModal={closeModal} activeIndex={0} />
            </div>
          )}
        </div>
        <div className={styles.workflow_header_cta}>
          <ButtonUi color="gray" variant="outlined" size="small" onClick={id ? handleEditWorkflow : handleAddWorkflow}>
            Next <img src={'/icons/automation/arrow_right_alt.svg'} height={24} width={24} alt="arrow_right" />
          </ButtonUi>
        </div>
      </div>

      <ReactFlow
        nodes={customNodes}
        edges={edges}
        snapToGrid={true}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        nodeTypes={nodeTypes}
        onConnect={onConnect}>
        <Background />
      </ReactFlow>
    </div>
  );
};
