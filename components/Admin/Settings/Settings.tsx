import React, { useState, useEffect, useContext } from 'react';
import { updateLogoAndFavicon, getAllNewAdminSettings, UpdateAdminSettings } from '../../../lib/adminapi';
import { getSingleUserDetails, updateTwoFactorAuth } from '../../../lib/frontendapi';
import SuccessToast from '@/components/Common/showSuccessTostrMessage';
import ErrorToast from '@/components/Common/showErrorTostrMessage';
import AuthContext from '@/Context/AuthContext';
import { Settings as AdminSettings } from '@/lib/types';
import { Form, Input } from 'antd';
import { FaLinkedinIn } from 'react-icons/fa6';
import { FaXTwitter } from 'react-icons/fa6';
import { FaInstagram } from 'react-icons/fa';
import { FaFacebookF } from 'react-icons/fa';
import { FaGlobe } from 'react-icons/fa';
import { FaStripe } from 'react-icons/fa6';
import { FaSquarePollHorizontal } from 'react-icons/fa6';
import Image from 'next/image';

interface Settings {
  key: string;
  value: string;
}

export default function Profile() {
  const [previewLogoImage, setPreviewLogoImage] = useState('');
  const [previewFavImage, setPreviewFavImage] = useState('');
  const [logoUploadError, setLogoUploadError] = useState('');
  const [ficonUploadError, setFiconUploadError] = useState('');
  const [companyLogo, setCompanyLogo] = useState('');
  const [settings, SetAdminSetting] = useState<AdminSettings>();

  const [twofactorauth, setTwoFactorAuth] = useState<any>('');
  const [showPopupunsave, setShowPopupunsave] = useState(false);
  const [showPopupunsave1, setShowPopupunsave1] = useState(false);
  const [showmessage, setShowmessage] = useState([]);
  const { user } = useContext(AuthContext);

  useEffect(() => {
    fetchAdminSettingData();
    getSingleUserDetails(user?.id)
      .then(res => {
        if (res.status == true) {
          setTwoFactorAuth(res.user.is2FA);
        } else {
          setTwoFactorAuth('');
        }
      })
      .catch(err => {
        console.log(err);
      });
  }, [user]);

  const fetchAdminSettingData = async () => {
    try {
      const response = await getAllNewAdminSettings();
      SetAdminSetting(response);
    } catch (error) {
      console.error(error);
    }
  };

  const handleImage = (e: any) => {
    const file = e.target.files[0];
    setCompanyLogo(file);

    if (file) {
      if (file.type.includes('image')) {
        const reader = new FileReader();
        reader.onloadend = () => {
          if (reader.result !== null) {
            // Check which input triggered the event
            if (e.target.name == 'logo') {
              setPreviewLogoImage(reader.result.toString());
              setLogoUploadError('');
            } else {
              setPreviewFavImage(reader.result.toString());
              setFiconUploadError('');
            }
          }
        };
        reader.readAsDataURL(file);
        const data = {
          key: e.target.name,
        };
        updateLogoAndFavicon(data, e.target.files[0]).then(res => {
          if (res.status == true) {
            setShowmessage(res.message);
            setShowPopupunsave(true);
            setTimeout(() => {
              setShowPopupunsave(false);
            }, 3000);
            window.location.reload();
          }
        });
      } else {
        if (e.target.name === 'logo') {
          setLogoUploadError('Please upload an image file (JPEG,JPG,PNG) for the logo');
        } else {
          setFiconUploadError('Please upload an image file (JPEG,JPG,PNG) for the favicon');
        }
      }
    }
  };

  const handleChangeSingleValue = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    SetAdminSetting((prevSettings: any) => ({
      ...prevSettings,
      [name]: value,
    }));
  };

  const handleBlurSingleValue = async (e: any) => {
    const data = {
      settings: settings,
    };
    UpdateAdminSettings(data).then(res => {
      if (res.status == true) {
        // toast.success(res.message, {
        //     position: toast.POSITION.TOP_RIGHT
        // });
      }
    });
  };

  const handleToggle2Fa = () => {
    const updatedValue: any = !twofactorauth;
    const data = {
      userId: user?.id,
      is2FA: updatedValue ? 1 : 0,
    };
    updateTwoFactorAuth(data)
      .then(response => {
        setTwoFactorAuth(updatedValue);
        //localStorage.setItem('displayEmail', String(updatedValue));
      })
      .catch(error => {
        console.error('Error setting default resume:', error);
      });
  };

  return (
    <>
      <div className="dash-right">
        <h1>
          Admin <span className="span-color">Settings</span>
        </h1>

        <div className="data-management m-p-10">
          <div className="work-experience-fieild m-p-10">
            <div className="row">
              <div className="col-lg-2 col-md-6 col-sm-12">
                <div className="left-text-fieild">
                  <h3 className=" m-center">Logo and Favicon</h3>
                </div>
              </div>
              <div className="col-lg-5 col-md-6 col-sm-12">
                <div className="row align-items-center">
                  <div className="col-lg-4 col-md-3 col-12">
                    <div className="dash-profile-img mb-4 m-auto" id="image_admin_logo">
                      <p className="f-12 c-2C2C2C m-center">Logo</p>
                      {previewLogoImage ? (
                        <img src={previewLogoImage} alt="Profile Image" className="img-circle" />
                      ) : settings?.logo ? (
                        <img
                          src={'/images/' + settings.logo}
                          alt="Profile Imag"
                          className="img-circle"
                        />
                      ) : (
                        <div className="dash-profile-img">
                          <img src={'/images/Avatars-4.png'} alt="Avatars-4" />
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="col-lg-8 col-md-6 col-sm-12">
                    <a>
                      <div className="uploade-btn">
                        <input
                          type="file"
                          name="logo"
                          id="logo"
                          onChange={handleImage}
                          accept="image/jpeg, image/png"
                        />
                        <div className="btn-a primary-size-16 btn-bg-0055BA mt-5 mb-4 mobile-m-0 max-340">
                          <i className="fa-solid fa-upload mx-2"></i> Upload Logo
                        </div>
                        {logoUploadError && <p className="error">{logoUploadError}</p>}
                      </div>
                    </a>
                  </div>
                </div>
              </div>
              <div className="col-lg-5 col-md-6">
                <div className="row align-items-center">
                  <div className="col-lg-4 col-md-4 col-12">
                    <div className="dash-profile-img mb-4 m-auto" id="image_admin_logo">
                      <p className="f-12 c-2C2C2C m-center">Favicon</p>

                      {previewFavImage ? (
                        <img src={previewFavImage} alt="Profile Image" className="img-circle" />
                      ) : settings?.favicon ? (
                        <img
                          src={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/' + settings.favicon}
                          alt="Profile Imag"
                          className="img-circle"
                        />
                      ) : (
                        <div className="dash-profile-img">
                          <img src={'/images/Avatars-4.png'} alt="Avatars-4" />
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="col-lg-8 col-md-9 col-12">
                    <a>
                      <div className="uploade-btn">
                        <input
                          type="file"
                          name="favicon"
                          id="doctor_profile"
                          onChange={handleImage}
                          accept="image/jpeg, image/png"
                        />
                        <div className="btn-a primary-size-16 btn-bg-0055BA mt-5 mb-4 mobile-m-0 max-340">
                          <i className="fa-solid fa-upload mx-2"></i> Upload Favicon
                        </div>
                        {ficonUploadError && <p className="error">{ficonUploadError}</p>}
                      </div>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="work-experience-fieild mt-3 m-p-10 mb-3 rounded">
          <div className="row">
            <div className="col-lg-3 col-md-3">
              <div className="left-text-fieild">
                <h3>Social Links</h3>
              </div>
            </div>
            <div className="col-lg-9 col-md-9">
              <form className="form-experience-fieild">
                <Form layout="vertical">
                  <Form.Item label={'LinkedIn*'}>
                    <Input
                      placeholder="LinkedIn"
                      prefix={<FaLinkedinIn />}
                      name="linkedin_link"
                      value={settings?.linkedin_link}
                      onBlur={handleBlurSingleValue}
                      onChange={handleChangeSingleValue}
                    />
                  </Form.Item>
                  <Form.Item label={'Twitter*'}>
                    <Input
                      placeholder="Twitter"
                      prefix={<FaXTwitter />}
                      name="twitter_link"
                      value={settings?.twitter_link}
                      onBlur={handleBlurSingleValue}
                      onChange={handleChangeSingleValue}
                    />
                  </Form.Item>
                  <Form.Item label={'Instagram*'}>
                    <Input
                      placeholder="Instagram"
                      prefix={<FaInstagram />}
                      name="instagram_link"
                      value={settings?.instagram_link}
                      onBlur={handleBlurSingleValue}
                      onChange={handleChangeSingleValue}
                    />
                  </Form.Item>
                  <Form.Item label={'Facebook*'}>
                    <Input
                      placeholder="Facebook"
                      prefix={<FaFacebookF />}
                      name="facebook_link"
                      value={settings?.facebook_link}
                      onBlur={handleBlurSingleValue}
                      onChange={handleChangeSingleValue}
                    />
                  </Form.Item>
                  <Form.Item label={'Website*'}>
                    <Input
                      placeholder="Website"
                      prefix={<FaGlobe />}
                      name="website_link"
                      value={settings?.facebook_link}
                      onBlur={handleBlurSingleValue}
                      onChange={handleChangeSingleValue}
                    />
                  </Form.Item>
                </Form>
              </form>
            </div>
          </div>
        </div>

        <div className="work-experience-fieild mt-3 m-p-10 mb-3 rounded">
          <div className="row">
            <div className="col-lg-3 col-md-3">
              <div className="left-text-fieild">
                <h3 className="mx-2">Payment Mode : </h3>
                <div className="row">
                  <div className="col-md-6 col-sm-12">
                    <select
                      className="form-control"
                      name="payment_mode"
                      value={settings?.payment_mode}
                      onBlur={handleBlurSingleValue}
                      onChange={handleChangeSingleValue}>
                      <option value="testing">Testing</option>
                      <option value="live">Live</option>
                    </select>
                  </div>
                  <div className="col-md-6 col-sm-12">
                    <button type="button" className="btn btn-success">
                      update
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-9 col-md-9 form-experience-fieild" id="stripe_data">
              <div className="row">
                <h4>Stripe test key</h4>
                <div className="col-lg-6 col-md-12 col-sm-12">
                  <Form layout="vertical">
                    <Form.Item label={'Secret Key*'}>
                      <Input
                        placeholder="Secret Key"
                        prefix={<FaStripe fontSize={25} />}
                        name="stripe_test_secret_key"
                        value={settings?.stripe_test_secret_key}
                        onBlur={handleBlurSingleValue}
                        onChange={handleChangeSingleValue}
                      />
                    </Form.Item>
                  </Form>
                </div>
                <div className="col-lg-6 col-md-12 col-sm-12">
                  <Form layout="vertical">
                    <Form.Item label={'Publish Key*'}>
                      <Input
                        placeholder="Publish Key"
                        prefix={<FaStripe fontSize={25} />}
                        value={settings?.stripe_test_publish_key}
                        onBlur={handleBlurSingleValue}
                        onChange={handleChangeSingleValue}
                      />
                    </Form.Item>
                  </Form>
                </div>
              </div>

              <div className="row mt-2">
                <h4>Stripe live key</h4>
                <div className="col-lg-6 col-md-12 col-sm-12">
                  <Form layout="vertical">
                    <Form.Item label={'Secret Key*'}>
                      <Input
                        placeholder="Secret Key"
                        prefix={<FaStripe fontSize={25} />}
                        name="stripe_test_secret_key"
                        value={settings?.stripe_live_secret_key}
                        onBlur={handleBlurSingleValue}
                        onChange={handleChangeSingleValue}
                      />
                    </Form.Item>
                  </Form>
                </div>
                <div className="col-lg-6 col-md-12 col-sm-12">
                  <Form layout="vertical">
                    <Form.Item label={'Publish Key*'}>
                      <Input
                        placeholder="Publish Key"
                        prefix={<FaStripe fontSize={25} />}
                        value={settings?.stripe_live_publish_key}
                        onBlur={handleBlurSingleValue}
                        onChange={handleChangeSingleValue}
                      />
                    </Form.Item>
                  </Form>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="cdata-management m-p-10 mt-2">
          <div className="work-experience-fieild m-p-10">
            <div className="row">
              <div className="col-lg-3 col-md-3">
                <div className="left-text-fieild">
                  <h3>Home Page</h3>
                </div>
              </div>
              <div className="col-lg-9 col-md-9 form-experience-fieild">
                <Form layout="vertical">
                  <Form.Item label={'Meta Title*'}>
                    <Input
                      placeholder="Meta Title"
                      prefix={<FaSquarePollHorizontal />}
                      value={settings?.homepage_meta_title}
                      onBlur={handleBlurSingleValue}
                      onChange={handleChangeSingleValue}
                    />
                  </Form.Item>
                </Form>
                <Form layout="vertical">
                  <Form.Item label={'Meta Description*'}>
                    <Input
                      placeholder="Meta Description"
                      prefix={<FaSquarePollHorizontal />}
                      value={settings?.homepage_meta_description}
                      onBlur={handleBlurSingleValue}
                      onChange={handleChangeSingleValue}
                    />
                  </Form.Item>
                </Form>
              </div>
            </div>
          </div>
        </div>

        <div className="cdata-management m-p-10 mt-2">
          <div className="work-experience-fieild m-p-10">
            <div className="row">
              <div className="col-lg-3 col-md-3">
                <div className="left-text-fieild">
                  <h3>Job Page</h3>
                </div>
              </div>
              <div className="col-lg-9 col-md-9 form-experience-fieild">
                <Form layout="vertical">
                  <Form.Item label={'Meta Title*'}>
                    <Input
                      placeholder="Meta Title"
                      prefix={<FaSquarePollHorizontal />}
                      value={settings?.jobs_meta_title}
                      onBlur={handleBlurSingleValue}
                      onChange={handleChangeSingleValue}
                    />
                  </Form.Item>
                </Form>
                <Form layout="vertical">
                  <Form.Item label={'Meta Description*'}>
                    <Input
                      placeholder="Meta Description"
                      prefix={<FaSquarePollHorizontal />}
                      value={settings?.jobs_meta_description}
                      onBlur={handleBlurSingleValue}
                      onChange={handleChangeSingleValue}
                    />
                  </Form.Item>
                </Form>
              </div>
            </div>
          </div>
        </div>

        <div className="cdata-management m-p-10 mt-2">
          <div className="work-experience-fieild m-p-10">
            <div className="row">
              <div className="col-lg-3 col-md-3">
                <div className="left-text-fieild">
                  <h3>Career Page</h3>
                </div>
              </div>
              <div className="col-lg-9 col-md-9 form-experience-fieild">
                <Form layout="vertical">
                  <Form.Item label={'Meta Title*'}>
                    <Input
                      placeholder="Meta Title"
                      prefix={<FaSquarePollHorizontal />}
                      value={settings?.carrer_meta_title}
                      onBlur={handleBlurSingleValue}
                      onChange={handleChangeSingleValue}
                    />
                  </Form.Item>
                </Form>
                <Form layout="vertical">
                  <Form.Item label={'Meta Description*'}>
                    <Input
                      placeholder="Meta Description"
                      prefix={<FaSquarePollHorizontal />}
                      value={settings?.carrer_meta_title}
                      onBlur={handleBlurSingleValue}
                      onChange={handleChangeSingleValue}
                    />
                  </Form.Item>
                </Form>
              </div>
            </div>
          </div>
        </div>

        <div className="cdata-management m-p-10 mt-2">
          <div className="work-experience-fieild m-p-10">
            <div className="row">
              <div className="col-lg-3 col-md-3">
                <div className="left-text-fieild">
                  <h3>About Page</h3>
                </div>
              </div>
              <div className="col-lg-9 col-md-9 form-experience-fieild">
                <Form layout="vertical">
                  <Form.Item label={'Meta Title*'}>
                    <Input
                      placeholder="Meta Title"
                      prefix={<FaSquarePollHorizontal />}
                      value={settings?.about_meta_title}
                      onBlur={handleBlurSingleValue}
                      onChange={handleChangeSingleValue}
                    />
                  </Form.Item>
                </Form>
                <Form layout="vertical">
                  <Form.Item label={'Meta Description*'}>
                    <Input
                      placeholder="Meta Description"
                      prefix={<FaSquarePollHorizontal />}
                      value={settings?.about_meta_description}
                      onBlur={handleBlurSingleValue}
                      onChange={handleChangeSingleValue}
                    />
                  </Form.Item>
                </Form>
              </div>
            </div>
          </div>
        </div>

        <div className="cdata-management m-p-10 mt-2">
          <div className="work-experience-fieild m-p-10">
            <div className="row">
              <div className="col-lg-3 col-md-3">
                <div className="left-text-fieild">
                  <h3>Employeer Page</h3>
                </div>
              </div>
              <div className="col-lg-9 col-md-9 form-experience-fieild">
                <Form layout="vertical">
                  <Form.Item label={'Meta Title*'}>
                    <Input
                      placeholder="Meta Title"
                      prefix={<FaSquarePollHorizontal />}
                      value={settings?.employer_meta_title}
                      onBlur={handleBlurSingleValue}
                      onChange={handleChangeSingleValue}
                    />
                  </Form.Item>
                </Form>
                <Form layout="vertical">
                  <Form.Item label={'Meta Description*'}>
                    <Input
                      placeholder="Meta Description"
                      prefix={<FaSquarePollHorizontal />}
                      value={settings?.employer_meta_description}
                      onBlur={handleBlurSingleValue}
                      onChange={handleChangeSingleValue}
                    />
                  </Form.Item>
                </Form>
              </div>
            </div>
          </div>
        </div>

        <div className="cdata-management m-p-10 mt-2">
          <div className="work-experience-fieild m-p-10">
            <div className="row">
              <div className="col-lg-3 col-md-3">
                <div className="left-text-fieild">
                  <h3>Employee Page</h3>
                </div>
              </div>
              <div className="col-lg-9 col-md-9 form-experience-fieild">
                <Form layout="vertical">
                  <Form.Item label={'Meta Title*'}>
                    <Input
                      placeholder="Meta Title"
                      prefix={<FaSquarePollHorizontal />}
                      value={settings?.employee_meta_title}
                      onBlur={handleBlurSingleValue}
                      onChange={handleChangeSingleValue}
                    />
                  </Form.Item>
                </Form>
                <Form layout="vertical">
                  <Form.Item label={'Meta Description*'}>
                    <Input
                      placeholder="Meta Description"
                      prefix={<FaSquarePollHorizontal />}
                      value={settings?.employee_meta_description}
                      onBlur={handleBlurSingleValue}
                      onChange={handleChangeSingleValue}
                    />
                  </Form.Item>
                </Form>
              </div>
            </div>
          </div>
        </div>

        <div className="cdata-management m-p-10 mt-2">
          <div className="work-experience-fieild m-p-10">
            <div className="row">
              <div className="col-lg-3 col-md-3">
                <div className="left-text-fieild">
                  <h3>Pricing Page</h3>
                </div>
              </div>
              <div className="col-lg-9 col-md-9 form-experience-fieild">
                <Form layout="vertical">
                  <Form.Item label={'Meta Title*'}>
                    <Input
                      placeholder="Meta Title"
                      prefix={<FaSquarePollHorizontal />}
                      value={settings?.pricing_meta_title}
                      onBlur={handleBlurSingleValue}
                      onChange={handleChangeSingleValue}
                    />
                  </Form.Item>
                </Form>
                <Form layout="vertical">
                  <Form.Item label={'Meta Description*'}>
                    <Input
                      placeholder="Meta Description"
                      prefix={<FaSquarePollHorizontal />}
                      value={settings?.pricing_meta_description}
                      onBlur={handleBlurSingleValue}
                      onChange={handleChangeSingleValue}
                    />
                  </Form.Item>
                </Form>
              </div>
            </div>
          </div>
        </div>

        <div className="cdata-management m-p-10 mt-2">
          <div className="work-experience-fieild m-p-10">
            <div className="row">
              <div className="col-lg-3 col-md-3">
                <div className="left-text-fieild">
                  <h3>Blog Page</h3>
                </div>
              </div>
              <div className="col-lg-9 col-md-9 form-experience-fieild">
                <Form layout="vertical">
                  <Form.Item label={'Meta Title*'}>
                    <Input
                      placeholder="Meta Title"
                      prefix={<FaSquarePollHorizontal />}
                      value={settings?.blog_listing_meta_title}
                      onBlur={handleBlurSingleValue}
                      onChange={handleChangeSingleValue}
                    />
                  </Form.Item>
                </Form>
                <Form layout="vertical">
                  <Form.Item label={'Meta Description*'}>
                    <Input
                      placeholder="Meta Description"
                      prefix={<FaSquarePollHorizontal />}
                      value={settings?.blog_listing_meta_description}
                      onBlur={handleBlurSingleValue}
                      onChange={handleChangeSingleValue}
                    />
                  </Form.Item>
                </Form>
              </div>
            </div>
          </div>
        </div>
        <div className="cdata-management m-p-10 mt-2">
          <div className="work-experience-fieild m-p-10">
            <div className="row">
              <div className="col-sm-12">
                <div className="form-experience-fieild">
                  <div className="row mt-4">
                    <div className="col-sm-10 col-8">
                      <p className="f-22 c-2C2C2C w-500 mb-1">Two-Factor Authentication (2FA)</p>
                      <p className="f-16 c-747474">
                        2FA adds an extra layer of protection, making it more challenging for hackers to compromise user
                        accounts.
                      </p>
                    </div>
                    <div className="col-sm-2 col-4 text-right">
                      <label className="switch btn-swith">
                        <input type="checkbox" checked={twofactorauth} onChange={handleToggle2Fa} />
                        <span className="slider round"></span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {showPopupunsave && <SuccessToast message={showmessage} />}
        {showPopupunsave1 && <ErrorToast message={showmessage} />}
      </div>
    </>
  );
}
