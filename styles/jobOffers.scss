.job-search-top {
  background: #ffffff;
  padding: 15px;
  border-radius: 20px;
  margin: 0 auto;
  box-shadow: 2px 2px 20px rgba(0, 0, 0, 0.1);

  .fa-solid {
    font-size: 23px;
    color: var(--primary-color);
  }

  .field-container {
    display: flex;
    align-items: center;
    padding: 0 5px 0 15px;
    overflow: hidden;
    border: 1px solid #b6c8e2;
    border-radius: 25px;
  }
}

.sidebar-filters {
  position: sticky;
  top: 20px;

  .sidebar-filters-form > div {
    background: var(--color_4);
    border: 1px solid var(--color_12);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 20px;
  }
}

.jobs-results {
  .job-item {
    border-radius: 12px;
    border: 1px solid #dfdfdf;
    padding: 15px;
    margin-bottom: 20px;
  }
}
.job-title {
  margin: 0;
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_700);
  font-size: var(--font_18);
  line-height: var(--font_18_line-height);
  color: var(--color_9);
}
.company-name {
  color: #0055ba;
  font-size: 16px;
  font-weight: 600;
  line-height: 140%; /* 22.4px */
  margin-top: 4px;
}
.average-base-salary {
  border-radius: 16px;
  border: 1px solid #eee;
  background: #fff;
  margin: 0 0 24px;
  padding: 12px;
  h4 {
    color: #747474;
    font-family: var(--opensans-font);
    font-size: 16px;
    font-weight: 600;
    line-height: 140%; /* 22.4px */
    margin: 0 0 8px;
  }
  h3 {
    color: #191919;
    font-size: 22px;
    font-weight: 500;
    line-height: 120%; /* 26.4px */
    margin: 0 0 12px;
  }
  a {
    color: #0055ba;
    font-family: var(--opensans-font);
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 140%; /* 22.4px */
  }
}

.salary-insights {
  border: 1px solid #eeeeee !important;
  background: #ebf4ff !important;
  p {
    font-size: 16px;
    font-weight: 600;
    line-height: 22px;
    letter-spacing: 0em;
    text-align: left;
    color: #747474;
    margin: 0 0 8px;
  }
  h3 {
    font-size: 22px;
    font-weight: 500;
    line-height: 26px;
    letter-spacing: 0em;
    text-align: left;
    color: #191919;
    margin: 0 0 12px;
  }
  a,
  span {
    font-size: 16px;
    font-weight: 700;
    line-height: 22px;
    letter-spacing: 0em;
    text-align: left;
    color: #0055ba;
    cursor: pointer;
    &:hover {
      text-decoration: underline;
    }
  }
}

.no-result-found {
  h3 {
    font-size: 22px;
    font-weight: 500;
    line-height: 26px;
    letter-spacing: 0em;
    text-align: left;
    color: #191919;
    margin: 0 0 8px;
  }
  p {
    font-size: 16px;
    font-weight: 600;
    line-height: 22px;
    letter-spacing: 0em;
    text-align: left;
    color: #747474;
    margin: 0;
  }
}

.salary-insights.average-base {
  background: #fff !important;
}

@media (max-width: 767px) {
  .jobs-results {
    .job-item {
      border-radius: 16px;
      border: 1px solid #d9d9d9;
      padding: 12px;
      margin-bottom: 8px;
      .job-title {
        line-height: 20px;
        margin: 8px 0 4px;
      }
      .company-name {
        color: #0055ba;
        font-size: 13px;
        line-height: 16px;
        margin: 0 0 16px !important;
      }
      .featured-sec {
        display: flex;
        align-items: center;
        .pro {
          display: flex;
          align-items: center;
        }
      }
      ul.full-time {
        margin-top: 16px;
        li {
          font-size: 13px;
          line-height: 16px;
        }
      }
      button {
        padding: 10px 0;
        font-size: 16px;
        border-radius: 4px;
      }
      button.download.mt-3 {
        margin-top: 12px !important;
      }
      small.posted {
        color: #999;
      }
    }
  }
  .pagination-tab {
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid #cfe5ff;
    ul {
      text-align: center;
    }
    li.ant-pagination-item-active {
      background: #cfe5ff !important;
      border-color: #cfe5ff !important;
      color: #0055ba;
    }
  }
  .sidebar-filters {
    .sidebar-filters-form > div {
      padding: 12px;
      border-radius: 8px;
      border: 1px solid #eee;
      h4 {
        font-size: 16px;
        line-height: 22px;
      }
      h5 {
        font-size: 16px;
        line-height: 22px;
      }
      .ant-checkbox-group {
        border-radius: 4px;
        border: 1px solid #eee;
        label {
          width: 100%;
          display: flex;
          align-items: center;
          box-shadow: 0px -1px 0px 0px #edeff5 inset;
          padding: 10px;
        }
      }
      .ant-slider-track {
        background: #0055ba;
      }
      .ant-slider-handle::after {
        background: #0055ba;
      }
    }
    .ant-btn {
      height: 43px;
    }
  }
  .uae-new {
    padding: 40px 0;
    text-align: center;
    h1 {
      margin: 0;
      font-size: 40px;
      line-height: 120%;
    }
  }
  .jobs-by-location {
    ul.uae-part {
      margin-bottom: 16px;
      column-count: 1 !important;
    }
  }
  .salary-insights {
    border-radius: 16px !important;
    padding: 8px !important;
    h3 {
      font-size: 19px;
    }
  }
  .no-result-found {
    h3 {
      font-size: 19px;
    }
  }
}
