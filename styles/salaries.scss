/* Salaries */
.salaries-top {
  p {
    color: #fff;
    font-size: 18px;
  }
  .ant-input {
    padding: 13px 12px;
  }
  .ant-select {
    height: 46px;
  }
  button.ant-btn {
    height: 46px;
    border-radius: 8px;
    font-size: 16px;
  }
}

.salaries-main {
  background: #f5f9ff;
}
.slick-track {
  margin-left: 0 !important;
}
.scroll-block {
  max-height: 530px;
  overflow: hidden;
  overflow-y: auto;
}
.salary-based-block {
  border-radius: 8px;
  border: 1px solid #ebf4ff;
  background: #fff;
  padding: 32px 16px;
  p {
    margin: 0;
    color: #2c2c2c;
    font-size: 18px;
    font-weight: 400;
    line-height: 21.6px;
  }
  h3 {
    font-size: 31px;
    font-weight: 700;
    line-height: 37px;
    color: #151515;
    margin: 0 0 16px;
    span {
      color: #0055ba;
    }
  }
  span.last-update {
    color: #747474;
    font-size: 18px;
    font-weight: 400;
    line-height: 22px;
    margin-right: 15px;
  }
  select.form-select {
    color: #999;
    font-size: 14px;
    font-weight: 400;
    line-height: 18px;
    letter-spacing: -0.5px;
    width: 96px;
  }
  .average-salary {
    border-radius: 16px;
    border: 1px solid #eee;
    background: #fff;
    padding: 16px;
    h5 {
      color: #747474;
      font-size: 16px;
      font-weight: 600;
      line-height: 22.4px;
      margin: 0 0 12px;
      span {
        display: block;
        color: #747474;
        font-size: 12px;
        font-weight: 300;
        line-height: 14px;
      }
    }
    h4 {
      color: #2c2c2c;
      font-size: 26px;
      font-weight: 700;
      line-height: 31.2px;
      margin: 0;
    }
  }
  .average-range {
    border-radius: 16px;
    border: 1px solid #eee;
    background: #fff;
    padding: 16px;
    .average-bottom {
      color: #2c2c2c;
      font-size: 16px;
      font-weight: 400;
      line-height: 140%; /* 22.4px */
      span {
        font-weight: 600;
      }
    }
    .rangeslider {
      margin-top: 50px;
      .rangeslider__handle {
        width: 22px;
        height: 16px;
        box-shadow: unset;
        background: #0055ba;
        border-radius: 4px;
        &::after {
          display: none;
        }
      }
      .rangeslider__handle-tooltip {
        background: none;
        color: #2c2c2c;
        font-size: 14px;
        font-weight: 600;
        line-height: 140%;
        width: auto;
        height: auto;
        top: -40px;
        &::after {
          display: none;
        }
      }
    }
    .rangeslider-horizontal {
      height: 16px;
      background: #cfe5ff;
      box-shadow: none;
      border-radius: 4px;
    }
    .rangeslider__fill {
      background: none;
      box-shadow: unset;
    }
  }
  .view-all {
    color: #0070f5;
    font-size: 18px !important;
    font-weight: 700;
    line-height: 120% !important; /* 21.6px */
  }
  ul.search-list {
    margin: 0;
    padding: 0;
    list-style-type: none;
    li {
      margin: 0;
      a {
        color: #747474;
        font-size: 18px;
        font-weight: 400;
        line-height: 180%; /* 32.4px */
        &:hover {
          color: #0070f5;
        }
      }
    }
  }
  ul.search-list.salaries-search-list {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 10px;
    li {
      width: calc(50% - 5px);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

// For Overflowing List
.overflow-list {
  li {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 20px;
  }
}

.review-info {
  border-radius: 8px;
  border: 1px solid #ebf4ff;
  background: #fff;
  padding: 16px;
  h3 {
    margin: 0;
    color: #151515;
    font-size: 22px;
    font-weight: 500;
    line-height: 120%; /* 26.4px */
  }
  .choice {
    width: 115px;
    a {
      width: 40px;
      height: 40px;
      text-align: center;
      line-height: 40px;
      font-size: 20px;
      border-radius: 50%;
      transition: all 0.3s;
    }
    a.thumbsUp {
      background: #dcf2ea;
      color: #3d9f79;
      &:hover {
        background: #b0f9de;
      }
    }
    a.thumbsDown {
      background: #ffebeb;
      color: #fd7373;
      &:hover {
        background: #ffcbcb;
      }
    }
  }
}

.side-panel {
  border-radius: 16px;
  border: 1px solid #ebf4ff;
  background: #fff;
  padding: 32px 16px;
  h3 {
    color: #151515;
    font-size: 22px;
    font-weight: 700;
    line-height: 26.4px;
    margin: 0 0 24px;
  }
  .career-path-block {
    padding: 16px;
    border-radius: 16px;
    border: 1px solid #eee;
    background: #fff;
    margin: 0 0 16px;
    transition: all 0.3s;
    &:hover {
      box-shadow:
        0px 4px 12px 0px rgba(21, 21, 21, 0.12),
        0px 2px 5px 0px rgba(21, 21, 21, 0.1),
        0px 1px 3px 0px rgba(21, 21, 21, 0.12);
    }
    h5 {
      color: #2c2c2c;
      font-size: 18px;
      font-weight: 600;
      line-height: 120%; /* 21.6px */
      margin: 0 0 12px;
      span {
        display: block;
        color: #747474;
        font-size: 16px;
        font-weight: 400;
        line-height: 140%; /* 22.4px */
      }
    }
    h4 {
      color: #2c2c2c;
      font-size: 26px;
      font-weight: 700;
      line-height: 120%; /* 31.2px */
      margin: 0 0 12px;
      span {
        font-size: 18px;
        font-weight: 400;
      }
    }
    a {
      color: #0055ba;
      font-size: 16px;
      font-weight: 700;
      line-height: 140%; /* 22.4px */
      img {
        vertical-align: middle;
      }
    }
  }
  .latest-opening-block {
    padding: 16px;
    border-radius: 16px;
    border: 1px solid #eee;
    background: #fff;
    margin: 0 0 16px;
    transition: all 0.3s;
    &:hover {
      box-shadow:
        0px 4px 12px 0px rgba(21, 21, 21, 0.12),
        0px 2px 5px 0px rgba(21, 21, 21, 0.1),
        0px 1px 3px 0px rgba(21, 21, 21, 0.12);
    }
    h5 {
      color: #2c2c2c;
      font-size: 18px;
      font-weight: 600;
      line-height: 120%; /* 21.6px */
      margin-bottom: 4px;
      span {
        display: block;
        margin-top: 4px;
        color: #0055ba;
        font-size: 16px;
        font-weight: 600;
        line-height: 140%; /* 22.4px */
      }
    }
    p {
      color: #999;
      font-size: 16px;
      font-weight: 400;
      line-height: 140%; /* 22.4px */
      span {
        display: block;
        margin-bottom: 4px;
      }
    }
    a {
      display: block;
      color: #0055ba;
      font-size: 16px;
      font-weight: 700;
      line-height: 140%; /* 22.4px */
      img {
        vertical-align: middle;
      }
    }
  }
}

.slider-main {
  position: relative;
  width: 100%;
  padding-bottom: 30px;
  button.slick-arrow {
    position: absolute;
    bottom: -35px;
    right: 10px;
    top: auto;
    margin: 0;
    transform: unset;
    left: auto;
  }
  .slick-prev {
    background: url(../public/images/slider-arrow.svg) center center no-repeat !important;
    width: 32px;
    height: 32px;
    opacity: 0.9;
    transform: rotate(180deg) !important;
    right: 50px !important;
    &:hover {
      opacity: 1;
    }
  }
  .slick-next {
    background: url(../public/images/slider-arrow.svg) center center no-repeat !important;
    width: 32px;
    height: 32px;
    opacity: 0.9;
    &:hover {
      opacity: 1;
    }
  }
  .slick-prev:before,
  .slick-next:before {
    display: none;
  }
  .slick-slide > div {
    margin-right: 10px;
  }
  .experience-by-block {
    border-radius: 16px;
    border: 1px solid #eee;
    background: #fff;
    padding: 16px;
    margin-right: 8px;
    transition: all 0.3s;
    &:hover {
      border: 1px solid #000;
    }
    h4 {
      color: #2c2c2c;
      font-size: 16px;
      font-weight: 600;
      line-height: 140%; /* 22.4px */
      margin-bottom: 12px;
      span {
        display: block;
        color: #747474;
        font-size: 12px;
        font-weight: 300;
        line-height: 140%; /* 16.8px */
        margin-top: 4px;
      }
    }
    h3 {
      color: #2c2c2c;
      font-size: 26px;
      font-weight: 700;
      line-height: 120%; /* 31.2px */
      margin: 0;
    }
  }
  .company-block {
    border-radius: 16px;
    border: 1px solid #eee;
    background: #fff;
    padding: 16px;
    margin-right: 8px;
    transition: all 0.3s;
    &:hover {
      border: 1px solid #000;
    }
    .company-logo {
      display: flex;
      width: 72px;
      height: 72px;
      padding: 0px 6.3px;
      justify-content: center;
      align-items: center;
      border-radius: 3.6px;
      border: 1px solid #eee;
      margin-bottom: 12px;
      img {
        height: auto !important;
        max-height: 68px;
      }
    }
    h4 {
      color: #0070f5;
      font-size: 18px;
      font-weight: 700;
      line-height: 120%; /* 21.6px */
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      span {
        display: block;
        margin-top: 4px;
        color: #4d4d4d;
        font-size: 16px;
        font-weight: 600;
        line-height: 140%; /* 22.4px */
      }
    }
    h5 {
      margin: 0;
      color: #2c2c2c;
      font-size: 22px;
      font-weight: 700;
      line-height: 120%; /* 26.4px */
      span {
        font-size: 12px;
        font-weight: 600;
      }
    }
  }
}
.jobs-by-location h4 span {
  color: #0055ba;
}
.locations-new {
  padding: 40px 0 120px;
}

.looking-for {
  h3 {
    font-size: 31px;
    font-weight: 700;
    line-height: 37px;
    color: #151515;
    margin: 0 0 16px;
    span {
      color: #0055ba;
    }
  }
  p {
    margin: 0;
    color: #2c2c2c;
    font-size: 18px;
    font-weight: 400;
    line-height: 21.6px;
  }
}

// Responsive
@media (max-width: 767.98px) {
  .uae-new.salaries-top {
    text-align: left;
    h1 {
      font-size: 33px;
    }
    button.ant-btn {
      height: 43px;
      border-radius: 4px;
    }
  }

  .salaries-main {
    padding: 24px 0 !important;
    button.slick-arrow {
      display: none !important;
    }
    .salary-based-block {
      padding: 16px 8px;
      h3 {
        font-size: 28px;
        line-height: 33px;
      }
      select.form-select {
        margin-top: 24px;
      }
    }
    .average-salary {
      h4 {
        font-size: 23px;
      }
    }
    .slider-main {
      padding: 0 !important;
      button.slick-arrow {
        display: none !important;
      }
      .experience-by-block {
        h3 {
          font-size: 23px;
        }
      }
    }
    .review-info {
      h3 {
        font-size: 19px;
      }
    }
  }

  .side-panel {
    margin-top: 24px;
    padding: 16px 8px;
    h3 {
      font-size: 19px;
    }
    .career-path-block {
      h4 {
        font-size: 23px;
      }
    }
  }

  .locations-new {
    padding: 40px 0 60px;
  }
  ul.search-list.salaries-search-list {
    li {
      width: 100% !important;
    }
  }
}

.cs-search .ant-select {
  width: 100%;
  .ant-select-selector {
    border: 0;
    outline: none;
    box-shadow: unset;
    :focus {
      outline: none;
      box-shadow: unset;
    }
  }
}
.cs-search
  .ant-select-focused:where(.css-dev-only-do-not-override-17cb29s).ant-select:not(.ant-select-disabled):not(
    .ant-select-customize-input
  ):not(.ant-pagination-size-changer)
  .ant-select-selector {
  box-shadow: unset;
}
