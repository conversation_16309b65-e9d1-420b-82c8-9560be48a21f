:root {
  --max-width: 1100px;
  --border-radius: 12px;
  --font-mono: ui-monospace, Menlo, Monaco, 'Cascadia Mono', 'Segoe UI Mono', 'Roboto Mono', 'Oxygen Mono',
    'Ubuntu Monospace', 'Source Code Pro', 'Fira Mono', 'Droid Sans Mono', 'Courier New', monospace;

  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;

  --primary-glow: conic-gradient(
    from 180deg at 50% 50%,
    #16abff33 0deg,
    #0885ff33 55deg,
    #54d6ff33 120deg,
    #0071ff33 160deg,
    transparent 360deg
  );
  --secondary-glow: radial-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));

  --tile-start-rgb: 239, 245, 249;
  --tile-end-rgb: 228, 232, 233;
  --tile-border: conic-gradient(#00000080, #00000040, #00000030, #00000020, #00000010, #00000010, #00000080);

  --callout-rgb: 238, 240, 241;
  --callout-border-rgb: 172, 175, 176;
  --card-rgb: 180, 185, 188;
  --card-border-rgb: 131, 134, 135;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;

    --primary-glow: radial-gradient(rgba(1, 65, 255, 0.4), rgba(1, 65, 255, 0));
    --secondary-glow: linear-gradient(to bottom right, rgba(1, 65, 255, 0), rgba(1, 65, 255, 0), rgba(1, 65, 255, 0.3));

    --tile-start-rgb: 2, 13, 46;
    --tile-end-rgb: 2, 5, 19;
    --tile-border: conic-gradient(#ffffff80, #ffffff40, #ffffff30, #ffffff20, #ffffff10, #ffffff10, #ffffff80);

    --callout-rgb: 20, 20, 20;
    --callout-border-rgb: 108, 108, 108;
    --card-rgb: 100, 100, 100;
    --card-border-rgb: 200, 200, 200;
  }
}

html,
body {
  font-family: var(--archivo-font), sans-serif !important;
  margin: 0;
}

h1,
h2,
h3,
h4,
h5,
h6,
button {
  font-family: var(--archivo-font), sans-serif !important;
}

a {
  color: #1c6ccb;
  transition: all 0.3s;
  text-decoration: none !important;
}

a:hover {
  color: #1976e3;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

/**
TODO: move this to the ant theme configuration
 */
.ant-form-item-label {
  padding: 2px !important;
}

.ant-form-item-label > label {
  height: auto !important;
}

.filepond--credits {
  display: none !important;
}

.btnPositionBottom {
  position: absolute;
  bottom: 0;
  left: 18px;
  width: 90%;
}

.nav_item_new {
  background: var(--color_3);
  border-radius: 4px;
  padding: 12px 24px;
  margin: 0 8px;
  color: var(--color_1);
}

/* Override dark mode checkbox styling */
@media (prefers-color-scheme: dark) {
  input[type="checkbox"] {
    background-color: white !important;
    border-color: #d9d9d9 !important;
  }

  input[type="checkbox"]:checked {
    background-color: #0055BA !important;
    border-color: #0055BA !important;
  }

  /* Ant Design checkboxes */
  .ant-checkbox-input {
    background-color: white !important;
  }

  .ant-checkbox-checked .ant-checkbox-inner {
    background-color: #0055BA !important;
    border-color: #0055BA !important;
  }

  .ant-checkbox-inner {
    background-color: white !important;
  }
}

/* Force white background on checkboxes in dark mode */
@media (prefers-color-scheme: dark) {
  input[type="checkbox"] {
    filter: invert(100%) hue-rotate(180deg) brightness(1.7) !important;
  }

  input[type="checkbox"]:checked {
    filter: invert(0%) !important;
    accent-color: #0055BA !important;
  }
}

/* Base checkbox styling regardless of color scheme */
input[type="checkbox"] {
  accent-color: #0055BA;
  cursor: pointer;
}

input[type="checkbox"]:checked {
  background-color: #0055BA;
  border-color: #0055BA;
}
