.ourAuthors {
  background-image: url('/images/section-plan-back.png');
  background-size: cover;
  background-repeat: no-repeat;
  padding: 40px 0;
  background-position: right;

  h5 {
    color: #FDCA40;
    font-size: 19px;
    font-style: normal;
    font-weight: 500;
    line-height: 150%;
  }

  h1 {
    color: #fff;
    font-size: 45px;
    font-style: normal;
    font-weight: 700;
    line-height: 120%;
    margin-bottom: 20px;
  }

  p {
    color: #EBF4FF;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
  }
}

.authorsCardSection {
  background: #FCFCFC;
  padding: 50px 0;
  width: 100%;

  img {
    width: 100%;
    object-fit: contain;
    height: 240px;
  }

}

.authorsCard {
  background: #FFF;
  padding: 0 16px;
  transition: 0.6s;
  margin-bottom: 25px;

  img {
    border-radius: 18.301px;
    margin-bottom: 15px;
  }

  h4 {
    color: #0055BA;
    font-size: 22px;
    font-style: normal;
    font-weight: 700;
    line-height: 140%;
    margin: 0 0 2px 0;
  }

  h5 {
    color: #2C2C2C;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
  }

  p {
    overflow: hidden;
    color: #4D4D4D;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
  }
}

.iconBrand {
  i {
    width: 28px;
    height: 28px;
    background: #e3eefb;
    text-align: center;
    padding: 7px 0;
    border-radius: 100px;
    color: #0055BA;
    font-size: 14px;
  }
}

.align {
  align-items: center;
}

.singleAuthor {
  border-radius: 0px 0px 80px 80px;
  background: #FCFCFC;
  padding: 50px 0;

  h1 {
    color: #0055BA;
    font-size: 37px;
    font-style: normal;
    font-weight: 600;
    line-height: 120%;
  }

  h6 {
    color: #4F4F4F;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 150%;
  }

  p {
    color: #666;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
  }

  h5 {
    color: #4F4F4F;
    font-size: 22px;
    font-style: normal;
    font-weight: 700;
    line-height: 140%;
    margin-bottom: 15px;
  }
}

.tagList {
  li {
    list-style: none;
    display: inline-block;
    margin-bottom: 30px;
    color: #4D4D4D;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px;
    letter-spacing: -0.5px;
    margin-right: 6px;

    i {
      margin-left: 7px;
    }

    a {
      color: #0070F5;
      font-size: 12px;
      font-style: normal;
      font-weight: 700;
      line-height: 18px;
      letter-spacing: -0.5px;
      text-decoration: none;
    }
  }
}

ul.tagButtons {
  li {
    a {
      border-radius: 28px;
      border: 1px solid #0055BA;
      background: rgba(46, 157, 255, 0.06);
      color: #0055BA;
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 150%;
      text-decoration: none;
      padding: 4px 8px;
    }

    list-style: none;
    display: inline-block;
    margin-right: 10px;
  }
}

.authorsImage {
  img {
    border-radius: 16px;
  }
}

@media(max-width:767px) {
  .authorsCard {
    img {
      width: 70%;
    }

    h4 {
      font-size: 19px;
    }
  }

  .ourAuthors {
    h1 {
      font-size: 40px;
    }

    p {
      font-size: 16px;
    }
  }

  .MobileNone {
    display: none;
  }

  .authorsImage {
    margin: 0 auto;
    width: 215.686px;
  }

  .singleAuthor {
    h1 {
      font-size: 32px;
      margin: 10px 0 5px 0;
    }

    .iconBrand {
      margin-bottom: 8px;
    }
  }

  .mobileViewCenter {
    text-align: center;
  }

  .pr-0 {
    padding-right: 0;
  }

  .pl-0 {
    padding-left: 0;
  }
}

@media (min-width:768px) {
  .dasktopAndTabNone {
    display: none;
  }
}
