import React, {createContext, useEffect, useState} from 'react';
import axios from 'axios';
import Cookies from 'js-cookie';
import {signOut} from 'next-auth/react';
import <PERSON>rror<PERSON>andler from '@/lib/ErrorHandler';
import {Settings, User} from '@/lib/types';
import {useRouter} from 'next/router';
import {notification} from 'antd';
import { getCurrentUserDetails } from '@/lib/frontendapi';

interface AuthContextDefaults {
  user?: User;
  settings?: Settings;
  memberPlanInfo?: {amount?: number; plan_id?: number};
  candidateTempId?: {user_id?: number};
  setUser: (user: any) => void;
  logout: () => Promise<any>;
  login: (username: string, password: string, is2FA?: boolean) => Promise<any>;
  refreshSignUpSession: () => void;
  refreshUserData: () => Promise<void>;
  registerSessionToken: (token: string) => void;
  setMemberTempPlanInfo: (amount: number, plan_id: number) => void;
  setUserTempId: (user_id: number) => void;
  hasSession?: boolean;
}

interface AuthContextProp {
  children?: React.ReactNode;
}

const AuthContext = createContext<AuthContextDefaults>({
  logout(): Promise<any> {
    return Promise.resolve(undefined);
  },
  login(): Promise<any> {
    return Promise.resolve(undefined);
  },
  setUser(): void {},
  refreshSignUpSession: () => {},
  registerSessionToken: () => {},
  setMemberTempPlanInfo(): void {},
  memberPlanInfo: {},
  setUserTempId(): void {},
  candidateTempId: {},
  user: undefined,
  refreshUserData(): Promise<any> {
    return Promise.resolve(undefined);
  },
});

const AuthContextProvider = ({children}: AuthContextProp) => {
  const [user, setUser] = useState<User>();
  const router = useRouter();
  const [settings, setSettings] = useState<Settings>();
  const [reloadSignUp, setReloadSignUp] = useState(false);
  const [memberPlanInfo, setMemberShipPlan] = useState<{amount?: number; plan_id?: number}>({});
  const [candidateTempId, setCandidateTempId] = useState<{user_id?: number}>({});
  const [hasSession, setHasSession] = useState<boolean>();
  const [initialized, setInitialized] = useState(false);

  useEffect(() => {
    if (hasSession !== undefined) {
      console.log('[AUTH] Route on AUTHContext', router.route);
      if (
        router.route.startsWith('/employer') ||
        router.route.startsWith('/employee') ||
        router.route.startsWith('/admin')
      ) {
        if (!hasSession) {
          console.error('[AUTH] Unauthenticated');
          router.push('/auth/login').then();
          return;
        }

        if (
          user &&
          !user?.signup_completed &&
          !router.route.startsWith('/admin') &&
          !router.route.startsWith('/employees/dashboard')
        ) {
          console.error('[AUTH] Signup not completed');
          // router.push('/auth/signup').then();
          return;
        }
      }

      if (user) {
        if (
          (router.route.startsWith('/employer') && user?.role !== 'employer') ||
          (router.route.startsWith('/employee') && user?.role !== 'employee') ||
          (router.route.startsWith('/admin') && user?.role !== 'admin')
        ) {
          console.error('[AUTH] Unauthorized');
          // router.push(`/${user.role}/dashboard`).then();
          return;
        }

        setInitialized(true);
      }
    }
  }, [router.route, hasSession, user]);

  useEffect(() => {
    const token = Cookies.get('session_token');
    if (token) {
      const cancelTokenSource = axios.CancelToken.source();
      const config = {cancelToken: cancelTokenSource.token};
      axios
        .get(`authentication/session`, config)
        .then(response => {
          if (response) {
            setUser(response.data);
            setHasSession(true);
          }
        })
        .catch(e => {
          console.log('session error', e, e.code);
          if (e.code !== 'ERR_CANCELED') {
            console.log('Session canceled');
            setHasSession(false);
            setInitialized(true);
          }
          ErrorHandler.showNotification(e);
        });

      setHasSession(true);
      return cancelTokenSource.cancel;
    } else {
      setUser(undefined);
      setInitialized(true);
      setHasSession(false);
    }
  }, [reloadSignUp]);

  useEffect(() => {
    axios.get('/settings/public').then(res => {
      setSettings(res.data);
    });
  }, []);

  const refreshSignUpSession = () => {
    setReloadSignUp(!reloadSignUp);
  };

  const registerSessionToken = (token: string) => {
    axios.defaults.headers.common.Authorization = 'Bearer ' + token;
    Cookies.set('session_token', token);
  };

  const login = async (email: string, password: string, is2FA?: boolean) => {
    try {
      const response = await axios.post('authenticate', {email, password});
      if (response) {
        if (response?.data?.token?.original?.token) {
          //according to the new response
          Cookies.set('temp_token', response?.data?.token?.original?.token);
        } else {
          registerSessionToken(response?.data?.token);
        }
        if (response?.data?.user) {
          setUser(response?.data?.user);
        }
        // according to the new response
        // registerSessionToken(response?.data?.token?.original?.token);
        setReloadSignUp(!reloadSignUp);
        return response.data;
      }
    } catch (error: any) {
      if (error.response && error.response.data.error === 'pending_verification') {
        axios.defaults.baseURL = process.env.NEXT_PUBLIC_API_URL;
        Cookies.set('2fa', error.response.data.data.hint);
        throw new Error('pending_verification');
      } else {
        let message = error.message;

        if (error.response) {
          message = error.response.data.message || error.response.data.error;
        }
        notification.error({
          message: message,
        });
      }
    }
  };

  const logout = async () => {
    await axios.get('authentication/logout');
    setUser(undefined);
    setHasSession(false);
    Cookies.remove('session_token');
    Cookies.remove('user_id');
    Cookies.remove('temp_token');

    await signOut();
    window.location.href = '/auth/login';
  };

  const setMemberTempPlanInfo = (amount: number, plan_id: number) => {
    setMemberShipPlan({amount, plan_id});
  };

  const setUserTempId = (user_id: number) => {
    setCandidateTempId({user_id});
  };

  const refreshUserData = async () => {
    const UserID = Cookies.get("user_id");
    if (user?.id || UserID) {
      try {
        const response = await getCurrentUserDetails(user?.id ? user.id : Number(UserID));
        if (response.status === true) {
          setUser({...user, ...response.user});
        }
      } catch (error) {
        console.error('Error refreshing user data:', error);
      }
    }
  };

  // if (!initialized) {
  //   return (
  //     <Flex align="center" gap="middle" justify="center" style={{height: '100vh'}}>
  //       <Spin
  //         size="large"
  //         indicator={
  //           <Triangle
  //             visible={true}
  //             height="80"
  //             width="80"
  //             color="#0055BA"
  //             ariaLabel="triangle-loading"
  //             wrapperStyle={{}}
  //             wrapperClass=""
  //           />
  //         }
  //       />
  //     </Flex>
  //   );
  // }

  return (
    <AuthContext.Provider
      value={{
        user,
        settings,
        memberPlanInfo,
        candidateTempId,
        setUser,
        logout,
        login,
        refreshSignUpSession,
        refreshUserData,
        registerSessionToken,
        setMemberTempPlanInfo,
        setUserTempId,
        hasSession,
      }}>
      {children}
    </AuthContext.Provider>
  );
};

export {AuthContextProvider};
export default AuthContext;
