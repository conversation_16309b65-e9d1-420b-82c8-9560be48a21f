import React from 'react';
import axios from 'axios';

import FrontendCandidateProfile from '../../components/Frontend/CandidateProfile';
import NotFound from 'next/dist/client/components/not-found-error';

export default function CandidateProfile({user}: any) {
  if (!user) {
    return <NotFound />;
  }
  return (
    <>
      <FrontendCandidateProfile user={user} />
    </>
  );
}

export async function getServerSideProps({params}: any) {
  console.log('slug', params.slug);

  if (!params.slug) {
    return {
      redirect: {
        destination: '/',
        permanent: false,
      },
    };
  }
  try {
    const response = await axios.get(`users/employees/${params.slug}`);

    return {
      props: {
        user: response.data,
      },
    };
  } catch (error) {
    return {
      props: {
        user: null,
      },
    };
  }
}
