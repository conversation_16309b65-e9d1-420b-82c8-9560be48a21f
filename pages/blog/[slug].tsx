import React from 'react';
import Head from 'next/head';
import { getSingleBlogBySlug } from '@/lib/frontendapi';
import SingleBlog from '@/components/Frontend/SingleBlog';
import cache from 'memory-cache'

import { Blogs } from '@/lib/types';
import { setCacheHeaders } from '@/utils';
import { GetServerSidePropsContext } from 'next';

interface SingleCountryPageProps {
  blogPost: Blogs;
  blog: Blogs[];
}

export default function SingleCountryPage({ blogPost, blog }: SingleCountryPageProps) {
  return (
    <>
      <Head>
        <title>{blogPost?.meta_tag ? blogPost?.meta_tag : 'Talent Point'}</title>
        <meta name="description" content={blogPost?.meta_desc ? blogPost?.meta_desc : ''} />
      </Head>
      <SingleBlog blogPost={blogPost} blog={blog} />
    </>
  );
}

export async function getServerSideProps({ params, res }: GetServerSidePropsContext) {
  const { slug = '' } = params ?? {};
  const cacheKey = `blog-${slug}`;
  const cachedData = cache.get(cacheKey);

  setCacheHeaders(res);

  if (cachedData) {
    if (cachedData.status === false) {
      return {
        redirect: {
          destination: '/',
          permanent: false,
        },
      };
    }
  }

  if (cachedData) {
    return {
      props: {
        blogPost: cachedData.entry,
        blog: cachedData.last_entries,
      },
    };
  }

  try {
    const postResponse = await getSingleBlogBySlug(slug);

    if (postResponse) {
      if (postResponse.status === false) {
        return {
          redirect: {
            destination: '/',
            permanent: false,
          },
        };
      }
    }

    if (!postResponse || !postResponse.entry) {
      return {
        notFound: true,
      };
    }

    // Cache the response data for 15 seconds
    cache.put(cacheKey, postResponse, 15 * 1000);

    return {
      props: {
        blogPost: postResponse.entry,
        blog: postResponse.last_entries,
      },
    };
  } catch (error) {
    console.error('Error fetching data:', error);
    return {
      props: {
        blogPost: null,
        blog: null,
      },
    };
  }
}

