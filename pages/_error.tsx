// import { NextPageContext } from "next";
// import { useEffect } from 'react';
// import { useRouter } from 'next/router';

// const Error = ({ statusCode }: any) => {

//   const router = useRouter();

//   // useEffect(() => {
//   //   router.replace('/');
//   // }, [router]);

//   useEffect(() => {
//     if (router.asPath.includes('wp-content')) {
//       router.replace('/');
//     }
//   }, [router]);

//   return (
//     <p style={{ textAlign: "center", display: 'flex', fontWeight: '800', justifyContent: 'center', alignItems: 'center', fontSize: '3rem', color: 'var(--color_1)', marginTop: '15%' }}>
//       {statusCode
//         ? `An error ${statusCode} occurred on server`
//         : "An error occurred on client"}
//       <br /><br /><br />
//     </p>
//   );
// };

// Error.getInitialProps = ({ res, err }: NextPageContext) => {
//   const statusCode = res ? res.statusCode : err ? err.statusCode : 404;
//   return { statusCode };
// };

// export default Error;


import { NextPageContext } from "next";

const Error = ({ statusCode }: any) => {
  return (
    <p style={{ textAlign: "center", display: 'flex', fontWeight: '800', justifyContent: 'center', alignItems: 'center', fontSize: '3rem', color: 'var(--color_1)', marginTop: '15%' }}>
      {statusCode
        ? `An error ${statusCode} occurred on server`
        : "An error occurred on client"}
      <br /><br /><br />
    </p>
  );
};

Error.getInitialProps = async ({ res, err, req }: NextPageContext) => {
  const statusCode = res ? res.statusCode : err ? err.statusCode : 404;

  // if (statusCode === 404 && req?.url?.includes('wp-content')) {
  // if (statusCode === 404 && (req?.url?.startsWith('/wp-content') ||asdddadasqweqeqe req?.url?.startsWith('/skill'))) {
  if (statusCode === 404) {
    if (res) {
      res.writeHead(302, {
        Location: '/'
      });
      res.end();
    } else {
      if (typeof window !== 'undefined') {
        window.location.href = '/';
      }
    }
  }

  return { statusCode };
};

export default Error;

