import React, {useState, useEffect} from 'react'; 
import { useRouter } from "next/router";
import EmployeesArchivedNamePosition from '../../../../components/Employees/Messages/ArchivedNamePosition'
export default function ArchivedNamePosition({id}:any) {
    return (
        <>
            <EmployeesArchivedNamePosition userId={id}/>
        </>
    )

}
export async function getServerSideProps({ params }:any) {
    return {
        props: { id: params.id }
    }
}