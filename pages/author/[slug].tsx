import BlogCard from '@/components/Blog/BlogCard';
import Footer from '@/components/Frontend/layouts/footer';
import {getAuthorsBySlug} from '@/lib/frontendapi';
import {Col, Row} from 'antd';
import Head from 'next/head';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

interface AuthorProps {
  author?: any;
  writeAbout?: any;
}

export default function page({author, writeAbout}: AuthorProps) {
  const uniqueWriteAbout: any = [];
  writeAbout.forEach((item: any) => {
    if (!uniqueWriteAbout.includes(item)) {
      uniqueWriteAbout.push(item);
    }
  });
  return (
    <div className="single-author-wrapper">
      {/* <Head>
        <title>{'Talent Point'}</title>
        <meta name="description" content={''} />
      </Head> */}
      <section className="singleAuthor">
        <div className="container">
          <ul className="tagList">
            <li>
              <a href="/author">
                {' '}
                Our Authors <i className="fa-solid fa-caret-right" />{' '}
              </a>
            </li>
            <li>
              <a href="#">{author.name}</a>
            </li>
          </ul>
          <div className="row mt-4">
            <div className="col-sm-4">
              <div className="authorsImage">
                <img
                  src={
                    author.profile_image !== null || ''
                      ? process.env.NEXT_PUBLIC_IMAGE_URL + 'images/blogs/author/' + author.profile_image
                      : '/images/users.jpg'
                  }
                  alt="author-Profile_image"
                  height={400}
                  width={300}
                />
                <div className="dasktopAndTabNone text-center">
                  <h1>{author.name}</h1>
                  <h6>{author.designation}</h6>
                  <div className="iconBrand">
                    <i className="fa-brands fa-linkedin-in" />
                  </div>
                </div>
              </div>
            </div>
            <div className="col-sm-8 mobileViewCenter">
              <div className="row align MobileNone">
                <div className="col-10">
                  <h1>{author.name}</h1>
                </div>
                <div className="col-2 text-end">
                  <div className="iconBrand">
                    {author.linkedin && (
                      <Link target="_blank" href={author.linkedin}>
                        <i className="fa-brands fa-linkedin-in" />
                      </Link>
                    )}
                  </div>
                </div>
              </div>
              <h6 className="MobileNone">{author.designation}</h6>
              <div dangerouslySetInnerHTML={{__html: author.description}}></div>
              <h5>Writes about:</h5>
              <ul className="tagButtons m-0 p-0">
                {uniqueWriteAbout?.map((item: any, index: number) => (
                  <li key={index} className="mx-2 mb-3">
                    <a href="#" className="p-2">
                      {item}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>
      <section className="author-related-blogs container">
        <h2 className="text-center text-dark" style={{color: '#0055BA', fontWeight: '600', fontSize: '32px'}}>
          All posts by <span className="text-capitalize">{author.name}</span>
        </h2>
        <Row gutter={15}>
          {author.blogs?.length > 0 ? (
            author.blogs.map((blog: any, index: number) => (
              <Col xl={8} key={`blog-${index}`} className="mt-3">
                <BlogCard
                  post={blog}
                  authorName={author.name}
                  authorImage={author.profile_image}
                  authorSlug={author.slug}
                />
              </Col>
            ))
          ) : (
            <div className="col-12 text-center mt-2 mb-2">
              <p className="text-muted" style={{fontSize: '16px'}}>
                It seems like there are no blogs available at the moment. Check back later or explore more content on
                our platform.
              </p>
            </div>
          )}

          <Col xl={8}></Col>
        </Row>
      </section>
      <Footer />
    </div>
  );
}

export async function getServerSideProps({params}: any) {
  const {slug} = params;
  const response = await getAuthorsBySlug(slug);
  return {
    props: {author: response.data, writeAbout: response.writeAbout},
  };
}
