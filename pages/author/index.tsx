import Author from '@/components/Frontend/Author';
import Footer from '@/components/Frontend/layouts/footer';
import {getAllAuthors} from '@/lib/frontendapi';
import {Col, Row} from 'antd';
import Head from 'next/head';
import React from 'react';

interface AuthorProps {
  authors?: any;
}

export default function page({authors}: AuthorProps) {
  return (
    <div className="author-wrapper">
      <Head>
        <title>{'Talent Point'}</title>
        <meta name="description" content={''} />
      </Head>
      <section className="ourAuthors">
        <div className="container">
          <div className="row">
            <div className="col-sm-7">
              <h5>Meet our expert authors</h5>
              <h1>Our Authors</h1>
              <p>
                Discover diverse insights from our skilled authors, each bringing valuable expertise to our professional
                blog.
              </p>
            </div>
          </div>
        </div>
      </section>
      <section className="authorsCardSection">
        <div className="container">
          <Row gutter={10}>
            {authors?.length > 0 ? (
              authors.map((author: {}, index: number) => (
                <Col xl={6} lg={8} md={2} sm={1} xs={1} key={`author-${index}`}>
                  <div>
                    <Author author={author} />
                  </div>
                </Col>
              ))
            ) : (
              <center style={{padding: '50px 0'}}>
                <h3>No authors found!</h3>
              </center>
            )}
          </Row>
        </div>
      </section>
      <Footer />
    </div>
  );
}

export async function getServerSideProps() {
  const response = await getAllAuthors();
  return {
    props: {authors: response.data},
  };
}
