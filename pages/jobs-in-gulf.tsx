import React from 'react';
import Head from 'next/head';

import { getAllNewAdminSettings } from '@/lib/adminapi';
import { Job, Settings } from '@/lib/types';

import { useRouter } from 'next/router';
import JobOffersPage from '@/components/Frontend/JobOfferPage';
import { NextSeo } from 'next-seo';


interface JobSearchPageProps {
   jobs: Job[];
   settings: Settings;
   trendingJobs: any;
   locationFaq: any;
   hasQuery: boolean;
}

export default function JobSearchPage({ jobs = [], settings, trendingJobs = [], locationFaq = [], hasQuery }: JobSearchPageProps) {
   const router = useRouter();

   return (
      <>
         <Head>
            <title>Jobs in Gulf</title>
            <meta name="description" content="Browse a variety of jobs in Gulf, Middle East, and UAE on The Talent Point. Explore listings from top companies in the Gulf and find the perfect fit for your schedule and career goals." />
            <meta
               property="og:title"
               content="Jobs in Gulf"
            />
            <meta
               property="og:description"
               content="Browse a variety of jobs in Gulf, Middle East, and UAE on The Talent Point. Explore listings from top companies in the Gulf and find the perfect fit for your schedule and career goals."
            />
            <meta property="og:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.png`} />
            <meta property="og:url" content={new URL(router.asPath, process.env.NEXT_PUBLIC_BASE_URL).href} />
            <meta property="og:type" content="website" />

            <meta name="twitter:card" content="summary_large_image" />
            <meta name="twitter:site" content="@theTalentPoint" />
            <meta
               name="twitter:title"
               content="Jobs in Gulf"
            />
            <meta
               name="twitter:description"
               content="Browse a variety of jobs in Gulf, Middle East, and UAE on The Talent Point. Explore listings from top companies in the Gulf and find the perfect fit for your schedule and career goals."
            />

            <meta property="twitter:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.png`} />
         </Head>
         <NextSeo noindex={hasQuery} nofollow={hasQuery} />
         {jobs && (
            <JobOffersPage ssrJobs={jobs} trendingJobs={trendingJobs} locationFaq={locationFaq} />
         )}

      </>
   );
}

export async function getServerSideProps({ query }: any) {
   const response = await getAllNewAdminSettings();

   try {

      return {
         // props: { jobs: jobsResponse?.data, settings: response, trendingJobs: jobsResponse?.trending, locationFaq: jobsResponse?.faq },
         props: {
            jobs: [],
            settings: response,
            trendingJobs: [],
            locationFaq: [],
            hasQuery: Object.keys(query || {}).length > 0,
         }
      };
   } catch (error) {
      return {
         props: {
            jobs: [],
            settings: response,
            trendingJobs: [],
            locationFaq: [],
            hasQuery: false,
         }
      };
   }
}
