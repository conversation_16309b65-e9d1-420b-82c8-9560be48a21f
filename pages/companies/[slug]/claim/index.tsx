import CompanyClaim from '@/components/Company/CompanyClaim';
import {getSingleEmployerCompanyDetails} from '@/lib/frontendapi';
import {Company} from '@/lib/types';
import {notFound} from 'next/navigation';
import React from 'react';

interface CompanyClaimProps {
  company: Company;
}

export default function CompanyClaimPage({company}: CompanyClaimProps) {
  if (!company) {
    return notFound();
  }
  return <CompanyClaim company={company} />;
}

export async function getServerSideProps({params}: any) {
  try {
    const companyResponse = await getSingleEmployerCompanyDetails(params.slug);
    return {
      props: {company: companyResponse?.data?.company},
    };
  } catch (error) {
    return {
      props: {
        company: {},
      },
    };
  }
}
