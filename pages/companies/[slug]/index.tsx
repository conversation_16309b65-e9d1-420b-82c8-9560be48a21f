import React from 'react';
import Head from 'next/head';
import {type Company} from '@/lib/types';
import {notFound} from 'next/navigation';
import {getSingleEmployerCompanyDetails} from '@/lib/frontendapi';
import CompanyProfile from '@/components/Frontend/CompanyProfile';
import CompanyDetailContainer from '@/components/Company/CompanyDetails';

interface CompanyProps {
  company: Company;
}

export default function Company({company}: CompanyProps) {
  if (!company) {
    return notFound();
  }

  return (
    <>
      <Head>
        <title>{company.meta_tag ? company.meta_tag : 'Talent Point'}</title>
        <meta name="description" content={company.meta_desc ? company.meta_desc : ''} />
      </Head>
      <CompanyDetailContainer />
    </>
  );
}

export async function getServerSideProps({params}: any) {
  try {
    const companyResponse = await getSingleEmployerCompanyDetails(params.slug);
    return {
      props: {company: companyResponse?.data?.company},
    };
  } catch (error) {
    return {
      props: {
        company: {},
      },
    };
  }
}
