import React from "react";
import Head from "next/head";

import { getAllNewAdminSettings } from "@/lib/adminapi";
import { Settings } from "@/lib/types";

import FrontendAbout from "../components/Frontend/NewAbout";
import {useRouter} from 'next/router';
interface AboutProps {
  settings: Settings;
}

export default function About({ settings }: AboutProps) {
 
  const router = useRouter();
  return (
    <>
      <Head>
        <title>
          {settings.about_meta_title
            ? settings.about_meta_title
            : "Talent Point"}
        </title>
        <meta
          name="description"
          content={
            settings.about_meta_description
              ? settings.about_meta_description
              : ""
          }
          
        />
    {/* Open Graph tags */}
    <meta property="og:title" content={settings.carrer_meta_title ? settings.carrer_meta_title : `Talent Point`} />
        <meta
          property="og:description"
          content={
            settings.carrer_meta_description ? settings.carrer_meta_description : `Get Expert Career  tips, guides, and to clarify your doubts, map out your future aspirations, and uncover exciting career opportunities.`
          }
        />
        <meta property="og:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.png`} />
        <meta property="og:url" content={new URL(router.asPath, process.env.NEXT_PUBLIC_BASE_URL).href} />
        <meta property="og:type" content="website" />

        {/* Twitter card */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:site" content="@theTalentPoint" />
        <meta name="twitter:title" content={settings.carrer_meta_title ? settings.carrer_meta_title : `Talent Point`} />
        <meta
          name="twitter:description"
          content={
            settings.carrer_meta_description ? settings.carrer_meta_description : `Get Expert Career  tips, guides, and to clarify your doubts, map out your future aspirations, and uncover exciting career opportunities.`
          }
        />
        <meta property="twitter:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.png`} />
      </Head>
      <FrontendAbout />
    </>
  );
}

export async function getServerSideProps() {
  const response = await getAllNewAdminSettings();
  return {
    props: { settings: response },
  };
}
