import React, { useContext } from "react";
import Head from "next/head";
import { Job, Settings } from "@/lib/types";
import FrontendJobsInCity from "../components/Frontend/JobsInCity";
import FrontendJobsInSkill from "../components/Frontend/jobsInSkill";
import { searchJobs } from "@/lib/ApiAdapter";
import { getSingleCityByName } from "@/lib/frontendapi";
import AuthContext from "@/Context/AuthContext";
import { useRouter } from 'next/router';
import JobOffersPage from "@/components/Frontend/JobOfferPage";
import { NextSeo } from "next-seo";

interface JobSearchPageProps {
  jobInCityName: '';
  jobs: Job[];
  locationFaq: any;
  settings: Settings;
  finalCityName: '';
  cityCountryName: '';
  keywordName: ''
}

export default function JobsInCity({ jobInCityName, jobs, finalCityName, cityCountryName, locationFaq, keywordName }: JobSearchPageProps) {
  const { user } = useContext(AuthContext);
  const inCityName = jobInCityName;
  const splitText = inCityName.split("-");
  const final = splitText[0] === 'jobs' ? true : false

  const router = useRouter();
  const date = new Date();
  var months = ['Jan', 'Feb', 'March', 'April', 'May', 'June', 'July', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  var content = "Apply for " + jobs?.length + "Jobs in " + finalCityName + ". Apply for Multiple  Vacancies in " + finalCityName + "on thetalentpoint.com by Top employers  in multiple industries & sectors";



  return (
    <>
      <Head>
        <title>{check(jobInCityName)}</title>
        <meta name="description" content={`Browse a variety of  ${check(jobInCityName)} on The Talent Point. Explore listings from top companies in ${finalCityName} and find the perfect fit for your schedule and career goals`} />
        <meta
          property="og:title"
          content={`Jobs in ${finalCityName}`}
        />
        <meta
          property="og:description"
          content={`Browse a variety of jobs in ${check(jobInCityName)} on The Talent Point. Explore listings from top companies in ${finalCityName} and find the perfect fit for your schedule and career goals`}
        />
        <meta property="og:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.png`} />
        <meta property="og:url" content={new URL(router.asPath, process.env.NEXT_PUBLIC_BASE_URL).href} />
        <meta property="og:type" content="website" />

        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:site" content="@theTalentPoint" />
        <meta
          name="twitter:title"
          content={`Jobs in ${finalCityName}`}
        />
        <meta
          name="twitter:description"
          content={`Browse a variety of jobs in ${check(jobInCityName)} on The Talent Point. Explore listings from top companies in ${finalCityName} and find the perfect fit for your schedule and career goals`}
        />

        <meta property="twitter:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.png`} />

      </Head>
      <NextSeo
        nofollow={true}
        noindex={true}
      />
      {/* {jobs && final ? (
        <FrontendJobsInCity jobInCityName={jobInCityName} cityCountryName={cityCountryName} ssrJobs={jobs} locationFaq={locationFaq} skillJobInCityName={jobInCityName} />
      ) : <FrontendJobsInSkill skillJobInCityName={jobInCityName} cityCountryName={cityCountryName} ssrJobs={jobs} locationFaq={locationFaq} keywordName={keywordName} finalCityName={finalCityName} />
      } */}

      {
        jobs && <JobOffersPage cityCountryName={cityCountryName} jobInCityName={jobInCityName} locationFaq={
          locationFaq
        }
          ssrJobs={jobs}
        />
      }

    </>
  );
}

function check(name: string = '') {
  const newCityName = name.replace(/-/g, ' ');
  const newCityArr = newCityName.split(" ");
  for (let i = 0; i < newCityArr.length; i++) {
    newCityArr[i] = newCityArr[i].charAt(0).toUpperCase() + newCityArr[i].slice(1);
  }
  return newCityArr.join(" ");
}

function checkNewCity(newCityName: string = '') {
  const newCityArr = newCityName.split(" ");
  for (let i = 0; i < newCityArr.length; i++) {
    newCityArr[i] = newCityArr[i].charAt(0).toUpperCase() + newCityArr[i].slice(1);
  }
  return newCityArr.join(" ");
}

export async function getServerSideProps({ params, user }: any) {
  const jobInCityName = params.jobsInCity;
  const splitText = jobInCityName.split("jobs-in-");
  if (splitText[0] === '') {
    const CityName = splitText[splitText.length - 1];
    const finalCityName = check(CityName);
    let cityData = '';
    if (finalCityName) {
      cityData = finalCityName;
    } else {
      cityData = '';
    }

    const cityDatas = {
      city_name: cityData
    }
    const cityResponse = await getSingleCityByName(cityDatas);
    const cityCountryName = cityResponse?.country_name || null;
    const data = {
      country: cityCountryName ? cityCountryName === 'Uae' ? 'United Arab Emirates' : cityCountryName : finalCityName === 'Uae' ? 'United Arab Emirates' : finalCityName,
      keywords: '',
      user_id: user?.id || null,
      city: cityCountryName === null ? '' : cityData,
      faqLocation: params.jobsInCity,
    };
    const jobsResponse = await searchJobs(data);
    return {
      props: { jobInCityName: jobInCityName, jobs: jobsResponse?.data, locationFaq: jobsResponse?.faq, finalCityName: finalCityName, cityCountryName: cityCountryName },
    }
  } else {
    let finalCityName = '';
    let keywordName = '';
    const splitInText = jobInCityName.split("-in-");

    if (splitInText.length === 1) {

      const splitInText = jobInCityName.split("-jobs");
      let newCityName;
      if (jobInCityName === 'transguard-online-job-apply') {
        finalCityName = ''
        keywordName = checkNewCity(jobInCityName.replace(/-/g, ' '))
      }
      else if (splitInText[0] === 'dubai') {
        newCityName = splitInText[0];
        finalCityName = checkNewCity(newCityName)
        keywordName = checkNewCity(splitInText[1].replace(/-/g, ' '))
      } else {
        if (splitInText[1] !== '') {
          newCityName = splitInText[1]?.replace(/-/g, ' ');
          finalCityName = checkNewCity(newCityName)
          keywordName = checkNewCity(splitInText[0].replace(/-/g, ' '))
        } else {
          finalCityName = ''
          keywordName = checkNewCity(splitInText[0].replace(/-/g, ' '))
        }
      }


    } else {
      let CityName = splitInText[splitInText.length - 1];
      finalCityName = check(CityName);
      keywordName = check(splitInText[0]).split(' Job')[0] || '';
    }


    let cityData = '';
    if (finalCityName) {
      cityData = finalCityName;
    } else {
      cityData = '';
    }

    const cityDatas = {
      city_name: cityData
    }
    const cityResponse = cityDatas.city_name !== '' && await getSingleCityByName(cityDatas);
    console.log(cityResponse, cityDatas, "cityDatas")
    const cityCountryName = cityResponse?.country_name || null;

    const data = {
      country: cityCountryName ? cityCountryName === 'Uae' ? 'United Arab Emirates' : cityCountryName : finalCityName === 'Uae' ? 'United Arab Emirates' : finalCityName,
      keywords: keywordName ? keywordName : '',
      user_id: user?.id || null,
      city: cityCountryName === null ? '' : cityCountryName === finalCityName ? '' : cityData,
      faqLocation: params.jobsInCity,
    };

    const jobsResponse = await searchJobs(data);
    return {
      props: { jobInCityName: jobInCityName, jobs: jobsResponse?.data, locationFaq: jobsResponse?.faq, finalCityName: cityData, cityCountryName: cityCountryName, keywordName: keywordName },
    }
  }
}
