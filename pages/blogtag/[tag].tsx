import React from 'react';
import BlogTag from '../../components/Frontend/Blogtag'
import { getBlogByTag } from '../../lib/frontendapi';

export default function BlogTagPage({ blogtag, tag }: any) {
  return (
    <>
      <BlogTag blogtag={blogtag} tag={tag} />
    </>
  );
}

export async function getServerSideProps({ params }: any) {
  const { tag } = params;
  const blogtagdata = await getBlogByTag(tag);

  return {
    props: {
      blogtag: blogtagdata,
      tag: tag
    },
  };
}
