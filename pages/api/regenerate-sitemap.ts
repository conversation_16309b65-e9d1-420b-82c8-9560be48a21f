import type {NextApiRequest, NextApiResponse} from 'next';
import {getServerSideProps} from '../sitemap.xml';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({message: 'Method not allowed'});
  }

  try {
    // Create a mock response object that mimics the structure needed by getServerSideProps
    const mockRes: any = {
      setHeader: () => {},
      write: () => {},
      end: () => {},
    };

    // Call the same function used by the sitemap.xml page
    await getServerSideProps({res: mockRes});

    // Return success response
    return res.status(200).json({success: true, message: 'Sitemap regenerated successfully'});
  } catch (error) {
    console.error('Error regenerating sitemap:', error);
    return res.status(500).json({success: false, message: 'Failed to regenerate sitemap'});
  }
}
