const SchemaOrg = () => {
  const schema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "The Talent Point",
    "alternateName": "The Talent Point",
    "url": "https://www.thetalentpoint.com/",
    "logo": "https://www.thetalentpoint.com/images/Avatars-4.png",
    "sameAs": [
      "https://www.facebook.com/thetalentpoint",
      "https://www.instagram.com/thetalentpoint/",
 
    ]
  };

  return (
   
      <script type="application/ld+json">{JSON.stringify(schema)}</script>
  
  );
};

export default SchemaOrg;
