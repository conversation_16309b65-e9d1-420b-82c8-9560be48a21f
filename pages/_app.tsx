import React, {lazy, useEffect, useState} from 'react';
import type {AppProps} from 'next/app';
import {SessionProvider} from 'next-auth/react';
import {useRouter} from 'next/router';
import <PERSON><PERSON> from 'js-cookie';
import axios from 'axios';
import {ConfigProvider} from 'antd';
import Head from 'next/head';
import Script from 'next/script';
import AntdConfig from '@/lib/AntdConfig';
import dynamic from 'next/dynamic';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import NextNProgress from 'nextjs-progressbar';

import {AuthContextProvider} from '@/Context/AuthContext';
import SchemaOrg from './common/SchemaOrg';
// import HeadLayout from '@/components/Common/Layout/HeadLayout';
const HeadLayout = dynamic(() => import('@/components/Common/Layout/HeadLayout'));
// import FrontendHeader from "../components/Frontend/layouts/header";
const FrontendHeader = dynamic(() => import('../components/Frontend/layouts/header'));
// import FrontendHeader from "../components/Frontend/layouts/header";

// import FrontendFooter from '../components/Frontend/layouts/footer';
const FrontendFooter = dynamic(() => import('../components/Frontend/layouts/footer'));

// import AdminLayout from '../components/Admin/layouts/layout';
const AdminLayout = dynamic(() => import('../components/Admin/layouts/layout'));

// import FrontendLayout from '../components/Frontend/layouts/layout';
const FrontendLayout = dynamic(() => import('../components/Frontend/layouts/layout'));

// import EmployeesLayout from '../components/Employees/layouts/layout';
const EmployeesLayout = dynamic(() => import('../components/Employees/layouts/layout'));

// import EmployerLayout from '../components/Employer/layouts/layout';
const EmployerLayout = dynamic(() => import('../components/Employer/layouts/layout'));

// import StaffLayout from '../components/Staff/layouts/layout';
const StaffLayout = dynamic(() => import('../components/Staff/layouts/layout'));

import '@/styles/globals.css';
import '@/styles/admin.scss';
import '@/styles/bootstrap.css';
import '@/styles/style.css';
import '@/styles/header.scss';
import '@/styles/blog.scss';
import '@/styles/author.scss';
import '@/styles/jobsSearchBy.scss';
import '@/styles/jobOffers.scss';
import '@/styles/salaries.scss';
import '@/styles/EmployerDashboard/overview.scss';
import './../components/Common/editorStyles.css';
import './../components/Common/modalAdminStyles.css';
import './../components/Common/LoadingIndicator/styles.css';
import '@fortawesome/fontawesome-free/css/all.min.css';
import {NextSeo} from 'next-seo';
import {FontLoader} from '@/fonts';
import {QueryClientProvider} from 'react-query';
import {queryClient} from '@/utils/queryClient';
import NiceModal from '@ebay/nice-modal-react';

// import 'slick-carousel/slick/slick.css';
// import 'slick-carousel/slick/slick-theme.css';
// import 'react-rangeslider/lib/index.css';
const token = Cookie.get('session_token');
axios.defaults.baseURL = process.env.NEXT_PUBLIC_API_URL;
axios.defaults.headers.common.Authorization = 'Bearer ' + token;

export default function App({Component, pageProps}: AppProps) {
  const router = useRouter();
  const [noindex, setNoindex] = useState(false);

  useEffect(() => {
    if (typeof window !== 'undefined' && window.location.href.includes('https://env-dev.thetalentpoint.com/')) {
      // Check if the meta tag already exists
      const existingMeta = document.querySelector('meta[name="robots"][content="noindex"]');
      if (!existingMeta) {
        setNoindex(true);
      }
    }
  }, [router]);

  let Layout = FrontendLayout;
  if (router.pathname.startsWith('/admin')) {
    Layout = AdminLayout;
  } else if (router.pathname.startsWith('/employees')) {
    Layout = EmployeesLayout;
  } else if (router.pathname.startsWith('/employer')) {
    Layout = EmployerLayout;
  } else if (router.pathname.startsWith('/staff')) {
    Layout = StaffLayout;
  }

  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL?.replace(/\/$/, '');
  const canonicalUrl = `${baseUrl || ''}${router.asPath || ''}`;

  const isDevelopment = process.env.NEXT_PUBLIC_APP_ENV === 'DEV';

  return (
    <>
      <NextNProgress color="#0070F5" />
      <FontLoader />
      <Script async id="tag-manager" strategy="lazyOnload">
        {`(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':` +
          `new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],` +
          `j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=` +
          `'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);` +
          `})(window,document,'script','dataLayer','GTM-NX4VWT66');`}
      </Script>
      <NextSeo noindex={isDevelopment} nofollow={isDevelopment} />
      {
        // router.pathname === '/' ||
        router.pathname === '/blog' ||
        router.pathname === '/blog/[slug]' ||
        router.pathname === '/career-tips' ||
        router.pathname === '/about-us' ||
        router.pathname === '/for-employers' ? (
          <>
            <HeadLayout settingsData={pageProps} />
            <Head>
              <link rel="canonical" href={canonicalUrl} />
              <SchemaOrg />
            </Head>
            <ConfigProvider
              theme={{
                hashed: false,
                token: {
                  fontFamily: 'var(--archivo-font), sans-serif',
                  colorPrimary: '#0253b3',
                  borderRadius: 4,
                },
              }}>
              <SessionProvider session={pageProps.session} refetchOnWindowFocus={false}>
                <AntdConfig>
                  <AuthContextProvider>
                    <FrontendHeader />
                  </AuthContextProvider>
                  <Component {...pageProps} />
                  <FrontendFooter />
                </AntdConfig>
              </SessionProvider>
            </ConfigProvider>
          </>
        ) : (
          <>
            <HeadLayout settingsData={pageProps} />
            <Head>
              <link rel="canonical" href={canonicalUrl} />
              <SchemaOrg />
            </Head>
            <AuthContextProvider>
              <ConfigProvider
                theme={{
                  hashed: false,
                  token: {
                    fontFamily: 'var(--archivo-font), sans-serif',
                    colorPrimary: '#0253b3',
                    borderRadius: 4,
                  },
                }}>
                <QueryClientProvider client={queryClient}>
                  <SessionProvider session={pageProps.session} refetchOnWindowFocus={false}>
                    <AntdConfig>
                      <Layout>
                        <NiceModal.Provider>
                          <Component {...pageProps} />
                        </NiceModal.Provider>
                      </Layout>
                    </AntdConfig>
                  </SessionProvider>
                </QueryClientProvider>
              </ConfigProvider>
            </AuthContextProvider>
          </>
        )
      }
    </>
  );
}
