// Import necessary modules
import { useRouter } from 'next/router';

// Define your component
const JobSearchPage = () => {
  // Get the router instance
  const router = useRouter();

  // This component doesn't render anything as it will be redirected on the server-side
  return null;
};

// Implement server-side redirection logic
export async function getServerSideProps() {
  // Redirect to '/jobs-in-gulf'
  return {
    redirect: {
      destination: '/jobs-in-gulf',
      permanent: false, // Set this to true if the redirection is permanent
    },
  };
}

// Export the component
export default JobSearchPage;
