import {EmployeeCandidate} from '@/components/Admin/Employees/Candidate';
import {getAllSkillsForAdmin} from '@/lib/adminapi';
import {getCountries} from '@/lib/ApiAdapter';
import {Country, Skill} from '@/lib/types';
import axios from 'axios';
import React from 'react';

interface EmployeesProps {
  countries: Country[];
  skills: Skill[];
}
export default function Candidates({countries, skills}: EmployeesProps) {
  return (
    <>
      <EmployeeCandidate countries={countries} skills={skills} variant="Employee" />
    </>
  );
}

export async function getServerSideProps({query}: any) {
  const cancelTokenSource = axios.CancelToken.source();
  const response = await getCountries(undefined, undefined, cancelTokenSource);
  const skillResponse = await getAllSkillsForAdmin();

  return {
    props: {
      countries: response,
      skills: skillResponse?.data ?? [],
    },
  };
}
