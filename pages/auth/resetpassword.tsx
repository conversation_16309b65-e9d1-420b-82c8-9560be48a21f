import React, { useState } from 'react';
import { resetPassword } from '@/lib/frontendapi';
import { useRouter } from 'next/router';
import { Form, Input, Button, notification } from 'antd';
import Image from 'next/image';

export default function ResetPassword() {
  const router = useRouter();
  const { token } = router.query;
  const [isLoading, setIsLoading] = useState(false);

  const onFinish = async (values: any) => {
    const { email, password, passwordConfirm } = values;

    const user = {
      token: token,
      email: email,
      password: password,
      password_confirmation: passwordConfirm,
    };
    try {
      const response = await resetPassword(user);
      if (response.success === true) {
        setIsLoading(false);
        notification.success({ message: 'Password reset successful' });
        router.push('/auth/login');
      } else {
        setIsLoading(false);
        notification.error({ message: 'Password Reset Failed' });
      }
    } catch (error) {
      setIsLoading(false);
      notification.error({ message: 'Password Reset Failed' });
    }
  };
  return (
    <>
      <section className="banner-part-home">
        <div className="container-fluid">
          <div className="row">
            <div className="col-lg-5 col-md-12 tab-none pl-0">
              <img src={process.env.NEXT_PUBLIC_BASE_URL + 'images/form-img.png'} alt="form-img" className="w-100" />
            </div>
            <div className="col-lg-7 col-md-12">
              <div className="form-pages top-m-sp">
                <h2>Forgot Your Password?</h2>
                <h5>Let’s get you connected.</h5>
                <Form className="form-get mt-4" onFinish={onFinish}>
                  <Form.Item
                    name="email"
                    rules={[
                      { required: true, message: 'Email is required' },
                      { type: 'email', message: 'Email is invalid' },
                    ]}>
                    <Input type="email" className="" id="email" name="email" size="large" placeholder="Email ID*" />
                  </Form.Item>
                  <Form.Item name="password" rules={[{ required: true, message: 'Password is required' }]}>
                    <Input
                      type="password"
                      className=""
                      id="password"
                      name="password"
                      size="large"
                      placeholder="Password*"
                    />
                  </Form.Item>
                  <Form.Item
                    name="passwordConfirm"
                    rules={[
                      { required: true, message: 'Confirm Password is required' },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (!value || getFieldValue('password') === value) {
                            return Promise.resolve();
                          }
                          return Promise.reject(new Error('Passwords do not match'));
                        },
                      }),
                    ]}>
                    <Input
                      type="password"
                      className=""
                      id="passwordConfirm"
                      name="passwordConfirm"
                      size="large"
                      placeholder="Confirm Password*"
                    />
                  </Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    className="btn-a primary-size-16 b-0 btn-bg-0055BA w-100"
                    style={{ height: 'auto' }}
                    loading={isLoading}>
                    Reset Password
                  </Button>
                </Form>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
