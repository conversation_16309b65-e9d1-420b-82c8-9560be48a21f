import React, {useContext, useEffect, useState} from 'react';
import axios from 'axios';
import {getServerSession} from 'next-auth';
import {getCsrfToken} from 'next-auth/react';
import {useRouter} from 'next/router';
// import AuthContext from '@/Context/AuthContext';
import AreYouStep from '@/components/Singup/AreYouStep';
import CreatePassword from '@/components/Singup/CreatePassword';
import TellUsAboutYourSelf from '@/components/Singup/TellUsAboutYourSelf';
import SetCompanyProfile from '@/components/Singup/SetCompanyProfile';
import AddCompany from '@/components/Singup/AddCompany';
import PublishJob from '@/components/Singup/PublishJob';
import CreateNumber from '@/components/Frontend/CreateNumber';
import GetStartedStep from '../../../components/Singup/GetStartedStep';
import authOption from '../../api/auth/[...nextauth]';
import ClaimCompany from '@/components/Singup/ClaimCompany';

interface SignupProps {
  sessionToken?: string;
}

export default function Slug({sessionToken}: SignupProps) {
  const router = useRouter();
  if (router.query.slug === undefined) {
    return <AreYouStep />;
  }
  switch (router.query.slug[0]) {
    case 'employer-step-1':
    case 'candidate-step-1':
      // setTimeout(() => {
      //   return <GetStartedStep sessionToken={sessionToken} />;
      // }, 100);
      return <GetStartedStep sessionToken={sessionToken} />;
    case 'employer-step-2':
    case 'candidate-step-2':
      return <CreatePassword />;
    case 'employer-step-3':
    case 'candidate-step-3':
      return <TellUsAboutYourSelf />;
    case 'employer-step-4':
      return <AddCompany />;
    case 'employer-step-5':
      return <SetCompanyProfile />;
    case 'employer-step-6':
      return <PublishJob />;
    case 'employer-step-7':
      return <ClaimCompany />;
    case 'employer-step-8':
    case 'candidate-step-4':
      return <CreateNumber />;
  }
}

export async function getServerSideProps(context: any) {
  // @ts-ignore
  const session = await getServerSession(context.req, context.res, authOption);
  console.log('getServerSideProps', session);
  console.log('hiii');
  let sessionToken: string | null = null;
  if (session && session?.user) {
    const nextSessionToken = await getCsrfToken();
    const data = {
      token: nextSessionToken,
      name: session.user.name,
      email: session.user.email,
      image: session.user.image,
    };
    try {
      const authResponse = await axios.post('social-authenticate', data);
      sessionToken = authResponse.data.token;
      console.log(sessionToken, 'authResponse', authResponse);
    } catch (error) {
      console.error('Error during social-authenticate request:', error);
    }
  }
  return {
    props: {sessionToken},
  };
}
