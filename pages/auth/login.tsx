import React from 'react';
import FrontendLogin from '../../components/Frontend/Login';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { getServerSession } from 'next-auth';
import axios from 'axios';
import authOption from '../api/auth/[...nextauth]';
import { getCsrfToken } from 'next-auth/react';

interface LoginProps {
  sessionToken?: string;
}

export default function Login({ sessionToken }: LoginProps) {
  const router = useRouter();
  return (
    <>
      <Head>
        <title>The Talent Point - Login</title>
        <meta
          name="description"
          content="Welcome to The Talent Point - where opportunities are crafted! Log in to access your dream job hub in Dubai. Don't wait for opportunities; create them. Explore 4000+ job listings and connect with top employers."
        />
        <meta
          name="keywords"
          content="The Talent Point, login, user login, job hub, Dubai jobs, job listings, top employers"
        />

        {/* Open Graph tags */}
        <meta property="og:title" content="The Talent Point - Login" />
        <meta
          property="og:description"
          content="Welcome to The Talent Point - where opportunities are crafted! Log in to access your dream job hub in Dubai. Don't wait for opportunities; create them. Explore 1000+ job listings and connect with top employers."
        />
        <meta property="og:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.png`} />
        <meta property="og:url" content={new URL(router.asPath, process.env.NEXT_PUBLIC_BASE_URL).href} />
        <meta property="og:type" content="website" />

        {/* Twitter card */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:site" content="@TheTalentPoint" />
        <meta name="twitter:title" content="The Talent Point - Login" />
        <meta
          name="twitter:description"
          content="Welcome to The Talent Point - where opportunities are crafted! Log in to access your dream job hub in Dubai. Don't wait for opportunities; create them. Explore 4000+ job listings and connect with top employers."
        />
        <meta property="twitter:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.png`} />
      </Head>
      <FrontendLogin sessionToken={sessionToken} />
    </>
  );
}

export async function getServerSideProps(context: any) {
  // @ts-ignore
  const session = await getServerSession(context.req, context.res, authOption);
  console.log("getServerSideProps", session);
  console.log("hiii");
  let sessionToken: string | null = null;
  if (session && session?.user) {
    const nextSessionToken = await getCsrfToken();
    const data = {
      token: nextSessionToken,
      name: session.user.name,
      email: session.user.email,
      image: session.user.image,
    };
    try {
      const authResponse = await axios.post('social-authenticate', data);
      sessionToken = authResponse.data.token;
      console.log(sessionToken, "authResponse", authResponse);
    } catch (error) {
      console.error("Error during social-authenticate request:", error);
    }
  }
  return {
    props: { sessionToken },
  };
}
