import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import FilterSearch from '@/components/Frontend/JobSalaries/FilterSearch';
import Carrer from '@/components/Frontend/JobSalaries/carrer';
import {
   getCountries,
   getSectorsList,
   getSalaryInsights,
   getSimilarCarrerPaths,
   getLatestJobOpenings,
} from '@/lib/ApiAdapter';
import { Country, Sector } from '../../lib/types';
import { useRouter } from 'next/router';
import FrontendSalaries from '@/components/Frontend/salaries';
import Footer from '@/components/Frontend/layouts/footer'
import { NextSeo } from 'next-seo';
interface CountryIndustryProps {
   countries: Country;
   industries: Sector;
}

interface ResultState {
   sector?: {
      sector_name: string;
   };
   country?: {
      country_name: string;
   };
   based_experience?: {
      time: string;
      count: number;
   }[];
   salary?: {
      average: number;
   };
}



export default function Salaries({ countries, industries }: CountryIndustryProps) {
   //console.log("countries", countries);
   const router = useRouter();
   const { slug } = router.query;
   const [shouldReloadFilterSearch, setShouldReloadFilterSearch] = useState(false);
   const [frontendSalaries, setFrontendSalaries] = useState(false);
   const [resultState, setResultState] = useState({});
   const [name, setName] = useState({});
   const [latestJobsOpenings, setLatestJobsOpenings] = useState([]);
   const [notFoundMessage, setNotFoundMessage] = useState(false);
   const [similarCareerPaths, setSimilarCareerPaths] = useState([]);
   const ApiCall = async (countryId: any, industryId: any) => {
      if (countryId && industryId) {
         try {
            const insightsRes = await getSalaryInsights(countryId.id, industryId.id);
            const latestOpeningsRes = await getLatestJobOpenings(countryId.id, industryId.id);
            const similarCareerPaths = await getSimilarCarrerPaths(countryId.id, industryId.id);
            if (insightsRes.salary.total > 0) {
               setResultState(insightsRes);
            } else {
               setResultState({});
               setNotFoundMessage(true);
               const name = {
                  countryName: '',
                  industryName: ''
               };
               name.countryName = countryId.country_name;
               name.industryName = industryId.sector_name;
               setName(name);
            }

            setLatestJobsOpenings(latestOpeningsRes);
            setSimilarCareerPaths(similarCareerPaths);
         } catch (error) {
            setNotFoundMessage(true);
            const name = {
               countryName: '',
               industryName: ''
            };
            name.countryName = countryId.country_name;
            name.industryName = industryId.sector_name;
            setName(name);
         }
      }
   };
   useEffect(() => {
      setShouldReloadFilterSearch(true);
      setResultState({});
      setNotFoundMessage(false);

      if (slug != undefined) {
         if (slug.length > 1) {
            setFrontendSalaries(true);

            const countryId = countries.find((res: any) => res.slug === slug[0]);
            const industryId = industries.find((res: any) => res.sector_name.replace(/\s+/g, '-').toLowerCase() === slug[1]);
            ApiCall(countryId, industryId);
         } else {
            setFrontendSalaries(false);
         }
      }

   }, [slug, countries, industries]);
   const typedResultState: ResultState = resultState || {};
   const currentYear = new Date().getFullYear();
   return (
      <>
         <Head>
            <title>{typedResultState?.sector?.sector_name
               ? `${typedResultState?.sector?.sector_name} Salary in ${typedResultState?.country?.country_name} ${currentYear}`
               : 'Talent Point'}
            </title>
            <meta

               content={`${typedResultState?.sector?.sector_name ? `The average salary for ${typedResultState?.sector?.sector_name} Salary in` : ''
                  } ${typedResultState?.country?.country_name ? typedResultState?.country?.country_name : 'Talent Point'
                  } ${typedResultState?.based_experience &&
                     typedResultState.based_experience.length > 0
                     ? `${currentYear} years is ${typedResultState?.salary?.average || 'Talent Point'} per month. Salaries are based on ${typedResultState?.based_experience[0].count
                        ? `${typedResultState?.based_experience[0].count} reported on The Talentpoint by employers`
                        : 'Talent Point'
                     }`
                     : ''
                  }`
               }
               name="description"
            />
         </Head>
         <NextSeo noindex nofollow />

         <FilterSearch
            key={shouldReloadFilterSearch ? Date.now() : undefined}
            countries={countries}
            industries={industries}
         />
         {
            frontendSalaries ? (
               <FrontendSalaries
                  resultState={resultState}
                  latestJobsOpenings={latestJobsOpenings}
                  similarCareerPaths={similarCareerPaths}
                  notFoundMessage={notFoundMessage}
                  name={name}
                  countries={countries}
               />
            ) :
               (<Carrer industries={industries} />)

         }
         <Footer />
      </>
   );
}
export async function getServerSideProps(context: any) {
   const { slug } = context.query;
   const response1 = await getCountries();
   //const res1 = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/countries`);
   //const response1 = await res1.json();
   const response2 = await getSectorsList();
   // console.log(response2);
   // const countryId = response1.filter(function (res) {
   //    return res.slug === slug[0];
   // });
   // const industryId = response2.filter(function (res) {
   //    // return res.sector_name.replace(/\s+/g, '-').toLowerCase() === slug[1];
   // });
   //const res2 = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/sectors/searchallsectors?order_by=desc`);
   //const response2 = await res2.json();
   // const countryId = response1.find((res: any) => res.slug === slug[0]);
   // const industryId = response2.find((res: any) => res.sector_name.replace(/\s+/g, '-').toLowerCase() === slug[1]);

   // console.log(industryId);

   // let insightsRes = {};
   // if (slug != undefined) {
   //    if (slug.length > 1) {
   //       insightsRes = await getSalaryInsights(countryId?.id, industryId?.id);
   //    }
   // }
   return {
      props: {
         countries: response1,
         industries: response2,
         //resultState: insightsRes,
      },
   };
}
