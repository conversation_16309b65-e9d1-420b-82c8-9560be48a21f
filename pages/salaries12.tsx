
import FrontendSalaries from '../components/Frontend/salaries'
import React from 'react'
import <PERSON> from 'next/head';
const Salaries = () => {
  return (
    <>
      <Head>
        <title>The Talent Point - Salaries</title>
        <meta
          name="description"
          content="Find a career that's best for you. Discover careers offering top job satisfaction, competitive salaries, and more."
        />
        <meta
          name="keywords"
          content="The Talent Point, salaries, job satisfaction, competitive salaries, career"
        />

        {/* Open Graph tags */}
        <meta property="og:title" content="The Talent Point - Salaries" />
        <meta
          property="og:description"
          content="Find a career that's best for you. Discover careers offering top job satisfaction, competitive salaries, and more."
        />
        <meta property="og:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.png`} />
        <meta property="og:url" content={`${process.env.NEXT_PUBLIC_BASE_URL}/salaries`} />
        <meta property="og:type" content="website" />

        {/* Twitter card */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:site" content="@TheTalentPoint" />
        <meta name="twitter:title" content="The Talent Point - Salaries" />
        <meta
          name="twitter:description"
          content="Find a career that's best for you. Discover careers offering top job satisfaction, competitive salaries, and more."
        />
        <meta property="twitter:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.png`} />
      </Head>
      {/* <FrontendSalaries resultState={''} similarCareerPaths={''} latestJobsOpenings={''} notFoundMessage={''} name={''} /> */}
    </>

  )
}

export default Salaries
