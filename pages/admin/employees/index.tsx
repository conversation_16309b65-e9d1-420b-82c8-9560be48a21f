import React from 'react';
import { EmployeeCandidate } from '@/components/Admin/Employees/Candidate';
import { Country, Skill } from '@/lib/types';
import axios from 'axios';
import { getCountries } from '@/lib/ApiAdapter';
import { getAllSkillsForAdmin } from '@/lib/adminapi';

interface EmployeesProps {
  countries: Country[];
  skills: Skill[];
}

export default function Employees({ countries, skills }: EmployeesProps) {
  return (
    <>
      <EmployeeCandidate countries={countries} skills={skills} variant='Admin' />
    </>
  );
}
export async function getServerSideProps({ query }: any) {
  const cancelTokenSource = axios.CancelToken.source();
  const response = await getCountries(undefined, undefined, cancelTokenSource);
  const skillResponse = await getAllSkillsForAdmin();

  return {
    props: {
      countries: response,
      skills: skillResponse?.data ?? [],
    },
  };
}
