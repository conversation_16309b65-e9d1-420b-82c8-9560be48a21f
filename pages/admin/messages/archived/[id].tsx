import React, {useState, useEffect} from 'react'; 
import { useRouter } from "next/router";
import AdminArchivedNamePosition from '../../../../components/Admin/Messages/ArchivedNamePosition'
export default function ArchivedNamePosition({id}:any) {
    return (
        <>
            <AdminArchivedNamePosition userId={id}/>
        </>
    )

}
export async function getServerSideProps({ params }:any) {
    return {
        props: { id: params.id }
    }
}