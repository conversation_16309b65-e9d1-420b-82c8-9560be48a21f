export const jobStatusToIconData = (status: 'ready_to_interview' | 'open_to_offer' | 'not_looking') => {
  switch (status) {
    case 'ready_to_interview':
      return {
        href: '/icons/candidate/job_ready.svg',
        bgColor: '#DCF2EA',
        name: 'Ready to Interview',
        borderColor: '#2E8B57',
        color: '#2E8B57',
      };

    case 'open_to_offer':
      return {
        href: '/icons/candidate/job_open.svg',
        bgColor: '#FFFCEB',
        name: 'Open to offers',
        borderColor: '#FFC107',
        color: '#FFA110',
      };
    case 'not_looking':
      return {
        href: '/icons/candidate/job_not_looking.svg',
        bgColor: '#FFE5E5',
        name: 'Not looking',
        borderColor: '#FF0000',
        color: '#FF0000',
      };
    default:
      break;
  }
};
