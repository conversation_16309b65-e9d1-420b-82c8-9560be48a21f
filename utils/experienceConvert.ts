export const experienceConvert = (experience: number) => {
  if (experience === 0) {
    return 'Internship / No Experience';
  } else if (experience > 0 && experience <= 1) {
    return 'Entry Level';
  } else if (experience >= 2 && experience <= 3) {
    return 'Junior Level';
  } else if (experience >= 4 && experience <= 5) {
    return 'Mid Level';
  } else if (experience >= 6 && experience <= 8) {
    return 'Senior Level';
  } else if (experience >= 9 && experience <= 11) {
    return 'Lead / Management Level';
  } else if (experience >= 12) {
    return 'Executive / Director Level';
  }
};
