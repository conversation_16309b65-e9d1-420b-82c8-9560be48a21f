import {type ServerResponse} from 'http';

export const setCacheHeaders = (response: ServerResponse, age: number = 300) => {
  response.setHeader('Cache-Control', `public, s-maxage=${age}, stale-while-revalidate=59`);
};

export const setNoCacheHeaders = (response: ServerResponse) => {
  response.setHeader(
    'Cache-Control',
    'private, must-revalidate, max-age=0, no-store, no-cache, must-revalidate, post-check=0, pre-check=0',
  );
};
